package com.trs.police.comparison.api.entity.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Collections;
import java.util.Map;

/**
 * 依图人脸信息
 *
 * <AUTHOR>
 * @date 2023/1/31 14:08
 **/
@Data
public class YituFaceInfo {

  /**
   * 人脸库id
   */
  @NotEmpty
  @JsonProperty("repository_id")
  @JSONField(name = "repository_id")
  private String repositoryId;
  /**
   * 姓名
   */
  private String name;
  /**
   * 证件号码
   */
  @JsonProperty("person_id")
  @JSONField(name = "person_id")
  @NotNull
  private String personId;
  /**
   * 图片列表，单次传输总大小小于16M，单张图片建 议小于2M (对于人脸，单张图片小于1M，图片数量 限制为<=128张)
   */
  @JsonProperty("picture_image_content_base64")
  @JSONField(name = "picture_image_content_base64")
  @NotEmpty
  private String pictureBase64;

  @JsonProperty("extra_meta")
  @JSONField(name = "extra_meta")
  private Map<String, String> extraMeta = Collections.emptyMap();

}
