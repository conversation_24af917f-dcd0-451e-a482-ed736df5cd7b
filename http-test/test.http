###
## 登录
POST {{baseUrl}}/authorize/login/pwd
Content-Type: application/json

{
  "username": "admin",
  "password": "dHJzYWRtaW4=",
  "defaultUnitId": 1
}

> {%client.global.set("access_token", response.body.access_token);
    client.global.set("cookie", response.headers.valueOf("Set-Cookie"));%}

###
GET http://localhost:5000/login/cas?ticket=123456


###
## switch-dept
PUT {{baseUrl}}/authorize/user/switch-dept?deptId=2
Authorization: bearer {{access_token}}
Cookie: {{cookie}}

###
## 登出
GET {{baseUrl}}/authorize/logout
Authorization: bearer {{access_token}}

###
## 获取用户信息
GET {{baseUrl}}/permission/user/current
Authorization: bearer {{access_token}}

###
## 获取用户信息
GET {{baseUrl}}/permission/dept/2
Authorization: bearer {{access_token}}

###
## 获取用户信息
GET {{baseUrl}}/authorize/dept/tree
Authorization: bearer {{access_token}}


###
## 获取用户信息
GET {{baseUrl}}/demo/me
Authorization: bearer {{access_token}}

###
## 获取用户信息
GET {{baseUrl}}/demo/user
Authorization: bearer {{access_token}}

###
## 获取用户信息
GET {{baseUrl}}/demo/dept
Authorization: bearer {{access_token}}

###
## 获取用户信息
GET {{baseUrl}}/demo/test
Authorization: bearer {{access_token}}

###
## 登出
GET {{baseUrl}}/authorize/logout
Authorization: bearer {{access_token}}

###
GET {{baseUrl}}/fight/test
Authorization: bearer {{access_token}}

###
GET {{baseUrl}}/demo/test
Authorization: bearer {{access_token}}

###
POST {{baseUrl}}/demo/upload
Content-Type: multipart/form-data; boundary=WebAppBoundary
Authorization: bearer {{access_token}}

--WebAppBoundary
Content-Disposition: form-data; name="file"; filename="test.txt"
Content-Type: application/json

< ./test.txt
--WebAppBoundary--

###
GET {{baseUrl}}/demo/download/1
Authorization: bearer {{access_token}}

###
DELETE {{baseUrl}}/demo/delete
Authorization: bearer {{access_token}}

###
GET {{baseUrl}}/fight/composite/plan/level
Authorization: bearer {{access_token}}

###
GET {{baseUrl}}/fight/composite/case/list
Authorization: bearer {{access_token}}

###
POST {{baseUrl}}/fight/event
Authorization: bearer {{access_token}}
Content-Type: application/json

{
  "number": "AJ20210408",
  "name": "新增案件1",
  "detail": "新增案件详情",
  "dataSource": 6,
  "address": "新增案件地址",
  "dateTime": 1649841637000,
  "type": 2
}

###
GET {{baseUrl}}/global/user/current
Authorization: bearer {{access_token}}

###
GET {{baseUrl}}/profile/dynamic/list/3/schema
Authorization: bearer {{access_token}}

###
GET {{baseUrl}}/statistic/security-situation/list-params
Authorization: bearer {{access_token}}

###
GET {{baseUrl}}/statistic/security-situation/district/list
Authorization: bearer {{access_token}}

###
POST localhost:5506/security-situation/overview
Authorization: bearer {{access_token}}
Content-Type: application/json

{
  "deptCode": "51050345000",
  "timeParams": {
    "range": "13"
  },
  "category": "警情",
  "caseType": "刑事案件"
}

###
POST localhost:5506/security-situation/trend-analysis
Authorization: bearer {{access_token}}
Content-Type: application/json

{
  "caseType": "刑事案件",
  "category": "警情",
  "timeParams": {
    "range": "2"
  },
  "deptCode": "510500000000",
  "childDepts": [],
  "subCaseTypes": []
}
###
POST localhost:5506/security-situation/trend-analysis
Authorization: bearer {{access_token}}
Content-Type: application/json

{
  "deptCode": "510500000000",
  "timeParams": {
    "range": "2"
  },
  "category": "警情",
  "caseType": "刑事案件",
  "subCaseTypes": [],
  "childDepts": [{"code":"510502000000","name":"江阳"},{"code":"510504000000","name":"龙马潭"},{"code":"510503000000","name":"纳溪"},{"code":"510521000000","name":"泸县"},{"code":"510522000000","name":"合江"},{"code":"510524000000","name":"叙永"},{"code":"510525000000","name":"古蔺"}]
}

###
POST {{baseUrl}}/statistic/security-situation/district-analysis
Authorization: bearer {{access_token}}
Content-Type: application/json

{"caseType":"刑事案件","category":"警情","timeParams":{"range":"2"},"deptCode":"510522000000","subCaseTypes":[{"code":301,"name":"技术开锁"},{"code":302,"name":"溜门"},{"code":303,"name":"攀爬"}],"childDepts":[{"code":"510502000000","name":"江阳"},{"code":"510504000000","name":"龙马潭"},{"code":"510503000000","name":"纳溪"},{"code":"510521000000","name":"泸县"},{"code":"510522000000","name":"合江"},{"code":"510524000000","name":"叙永"},{"code":"510525000000","name":"古蔺"}]}

###
POST localhost:5506/security-situation/trend-analysis
Authorization: bearer {{access_token}}
Content-Type: application/json

{
  "caseType": "刑事案件",
  "category": "立案",
  "timeParams": {
    "range": "99",
    "beginTime": 1619798400000,
    "endTime": 1685721599999
  },
  "deptCode": "510500000000",
  "childDepts": [
    {
      "code": "510502000000",
      "name": "江阳"
    },
    {
      "code": "510504000000",
      "name": "龙马潭"
    },
    {
      "code": "510503000000",
      "name": "纳溪"
    },
    {
      "code": "510521000000",
      "name": "泸县"
    },
    {
      "code": "510522000000",
      "name": "合江"
    },
    {
      "code": "510524000000",
      "name": "叙永"
    },
    {
      "code": "510525000000",
      "name": "古蔺"
    }
  ]
}

###
GET {{baseUrl}}/statistic/security-situation/list-params
Authorization: bearer {{access_token}}

###

GET {{baseUrl}}/statistic/security-situation/test
Authorization: bearer {{access_token}}

###
POST localhost:5506/security-situation/detail-table/jq
Authorization: bearer {{access_token}}
Content-Type: application/json

{
    "pageParams": {
        "pageNumber": 1,
        "pageSize": 5,
        "total": 0
    },
    "filterParams": {
        "caseType": "刑事案件",
        "category": "警情",
        "timeParams": {
            "range": "2"
        },
        "deptCode": "510502000000",
        "subCaseTypes": [
            {
                "name": "刑事案件",
                "code": 1
            }
        ]
    }
}
