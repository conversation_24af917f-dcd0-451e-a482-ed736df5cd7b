package com.trs.police.statistic.converter;

import com.trs.common.utils.TimeUtils;
import com.trs.police.statistic.domain.entity.Ajxgry;
import com.trs.police.statistic.domain.entity.Xsaj;
import com.trs.police.statistic.domain.entity.Xyr;
import com.trs.police.statistic.domain.entity.Zaaj;
import com.trs.police.statistic.domain.vo.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.sql.Date;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface NcInterFaceDataConvert {

    NcInterFaceDataConvert INSTANCE = Mappers.getMapper(NcInterFaceDataConvert.class);


    /**
     * 将 NcXsAjBaseDataVO 转换为 Xsaj
     *
     * @param vo vo
     * @return Xsaj
     */
    @Mapping(target = "jd", source = "asjfsddDqjd")
    @Mapping(target = "wd", source = "asjfsddDqwd")
    @Mapping(target = "unitCode",source = "badwGajgjgdm")
    @Mapping(target = "larq",source = "larq", qualifiedByName = "dateToString")
    Xsaj toXsaj(NcXsAjBaseDataVO vo);

    /**
     * 将NcXzAjBaseDataVO 转换为 Zaaj
     *
     * @param vo vo
     * @return Zaaj
     */
    @Mapping(target = "zj", source = "xxzjbh")
    @Mapping(target = "jd", source = "asjfsddDqjd")
    @Mapping(target = "wd", source = "asjfsddDqwd")
    @Mapping(target = "slsj",source = "jjsj")
    Zaaj toZaaj(NcXzAjBaseDataVO vo);

    /**
     * 将 NcXsAjXyrVO 转换为 Xyr
     *
     * @param vo vo
     * @return Xyr
     */
    @Mapping(target = "fzxyrSg", expression = "java(vo.getFzxyrSg() != null ? vo.getFzxyrSg().intValue() : null)")
    @Mapping(target = "fzxyrTz", expression = "java(vo.getFzxyrTz() != null ? vo.getFzxyrTz().intValue() : null)")
    @Mapping(target = "fzxyrZc", expression = "java(vo.getFzxyrZc() != null ? vo.getFzxyrZc().toString() : null)")
    @Mapping(target = "xyrbh", source = "asjxgrybh")
    @Mapping(target = "zjhm", source = "fzxyrCyzjZjhm")
    @Mapping(target = "zj", source = "xxzjbh")
    @Mapping(target = "lrsj", source = "xtLrsj")
    @Mapping(target = "rsqzcs", source = "fzxyrRsqzcsdm")
    @Mapping(target = "xm", source = "fzxyrXm")
    Xyr toXsajXyr(NcXsAjXyrVO vo);

    /**
     * 将 NcXzAjXyrVO 转换为 Xyr
     *
     * @param vo vo
     * @return Xyr
     */
    @Mapping(target = "xyrbh", source = "asjxgrybh")
    @Mapping(target = "zjhm", source = "cyzjZjhm")
    @Mapping(target = "zj", source = "xxzjbh")
    @Mapping(target = "lrsj", source = "xtLrsj")
    Xyr toZaajXyr(NcXzAjXyrVO vo);

    /**
     * 将 NcXsAjXgRyVO 转换为 Ajxgry
     *
     * @param vo vo
     * @return Ajxgry
     */

    @Mappings({
            @Mapping(target = "role", source = "asjxgryjsdm"),
            @Mapping(target = "xgrybh", source = "asjxgrybh"),
            @Mapping(target = "zjhm", source = "cyzjZjhm"),
            @Mapping(target = "zj", source = "xxzjbh"),
            @Mapping(target = "lrsj", source = "xtLrsj"),
            @Mapping(target = "xzzJd", source = "xzzJd"),
            @Mapping(target = "xzzWd", source = "xzzWd")
    })
    Ajxgry toXsajAjxgry(NcXsAjXgRyVO vo);

    /**
     * 将 NcXzAjXgRyVO 转换为 Ajxgry
     *
     * @param vo vo
     * @return Ajxgry
     */
    @Mapping(target = "xgrybh", source = "asjxgrybh")
    @Mapping(target = "zjhm", source = "cyzjZjhm")
    @Mapping(target = "zj", source = "xxzjbh")
    @Mapping(target = "lrsj", source = "xtLrsj")
    Ajxgry toZaajAjxgry(NcXzAjXgRyVO vo);


    /**
     * 日期转换
     *
     *  @param date date
     *  @return string
     */
    @Named("dateToString")
    default String dateToString(Date date) {
        return date == null ? null : TimeUtils.dateToString(date,TimeUtils.YYYYMMDD);
    }
}
