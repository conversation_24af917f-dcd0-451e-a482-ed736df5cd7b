package com.trs.police.statistic.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 南充 行政案件-基本信息 接口返回信息
 * *@author:tang.shuai
 * *@create 2024-06-05 16:37
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NcXzAjBaseDataVO {
    /**
     * 信息主键编号
     */
    private String xxzjbh;

    /**
     * 案事件编号(业务)
     */
    private String asjbh;

    /**
     * 接警编号(业务)
     */
    private String jjbh;

    /**
     * 现场勘验编号
     */
    private String xckybh;

    /**
     * 案事件来源代码(业务)
     */
    private String asjlydm;

    /**
     * 行政警情类别代码(业务)
     */
    private String xzjqlbdm;

    /**
     * 案件类别代码(业务)
     */
    private String ajlbdm;

    /**
     * 作案特征_简要情况(业务)
     */
    private String zatzJyqk;

    /**
     * 案件名称（华南）
     */
    private String ajmc;

    /**
     * 发现案事件时间(业务)
     */
    private Date fxasjsj;

    /**
     * 发现案事件地点_行政区划代码(业务)
     */
    private String fxasjddXzqhdm;

    /**
     * 发现案事件地点_地址名称(业务)
     */
    private String fxasjddDzmc;

    /**
     * 发现案事件地点_地址ID
     */
    private String fxasjddDzid;

    /**
     * 案事件发生时间_案事件发生开始时间(业务)
     */
    private Date asjfssjAsjfskssj;

    /**
     * 案事件发生时间_案事件发生结束时间(业务)
     */
    private Date asjfssjAsjfssjssj;

    /**
     * 案事件发生地点_行政区划代码(业务)
     */
    private String asjfsddXzqhdm;

    /**
     * 案事件发生地点_地址名称(业务)
     */
    private String asjfsddDzmc;

    /**
     * 案事件发生地点_经度
     */
    private Double asjfsddDqjd;

    /**
     * 案事件发生地点_纬度
     */
    private Double asjfsddDqwd;

    /**
     * 案事件发生地点_地址ID
     */
    private String asjfsddDzid;

    /**
     * 案事件发生地点_地域类别代码（华南）
     */
    private String asjfsddDylbdm;

    /**
     * 案事件发生地点_涉案场所_涉案场所类别代码（华南）
     */
    private String asjfsddSacsSacsLbdm;

    /**
     * 案事件发生地点_涉案场所_代码补充描述（华南）
     */
    private String asjfsddSacsDmbcms;

    /**
     * 简要案情(业务)
     */
    private String jyaq;

    /**
     * 是否涉枪_判断标识(业务)
     */
    private String sfsqPdbz;

    /**
     * 是否涉爆_判断标识(业务)
     */
    private String sfsbPdbz;

    /**
     * 是否涉外_判断标识(业务)
     */
    private String sfswPdbz;

    /**
     * 涉外案事件涉外情况(业务)
     */
    private String swasjswqk;

    /**
     * 损失物品去向_简要情况
     */
    private String sswpqxJyqk;

    /**
     * 现场可疑物品_简要情况
     */
    private String xckywpJyqk;

    /**
     * 发现线索_简要情况
     */
    private String fxxsJyqk;

    /**
     * 案事件受伤人员_人数(业务)
     */
    private Integer asjssryRs;

    /**
     * 案事件损失财物_简要情况(业务)
     */
    private String asjsscwJyqk;

    /**
     * 案事件管制物品_简要情况(业务)
     */
    private String asjgzwpJyqk;

    /**
     * 损失价值（人民币元）
     */
    private Double ssjzrmby;

    /**
     * 受理时间
     */
    private Date slsj;

    /**
     * 受理单位_公安机关机构代码
     */
    private String sldwGajgjgdm;

    /**
     * 受理单位_公安机关名称
     */
    private String sldwGajgmc;

    /**
     * 收缴财物价值（人民币元）
     */
    private Double sjcwjzrmby;

    /**
     * 移送日期（华南）
     */
    private Date ysscqsrq;

    /**
     * 主办人_姓名
     */
    private String zbrXm;

    /**
     * 主办人_公民身份号码
     */
    private String zbrGmsfhm;

    /**
     * 主办人_联系电话
     */
    private String zbrLxdh;

    /**
     * 协办人_姓名
     */
    private String xbrXm;

    /**
     * 协办人_公民身份号码
     */
    private String xbrGmsfhm;

    /**
     * 协办人_联系电话
     */
    private String xbrLxdh;

    /**
     * 案件业务状态(业务)
     */
    private String ajywztdm;

    /**
     * 是否串并
     */
    private String sfcb;

    /**
     * 接警时间（华南）
     */
    private Date jjsj;

    /**
     * 接警方式（华南）
     */
    private String jjfs;

    /**
     * 作案状态（华南）
     */
    private String zazt;

    /**
     * 所属社区（华南）
     */
    private String sssq;

    /**
     * 涉案总值（华南）
     */
    private BigDecimal sazz;

    /**
     * 结案时间（华南）
     */
    private Date jasj;

    /**
     * 密级
     */
    private String securitygrade;

    /**
     * 适用法律（华南）
     */
    private String syfl;

    /**
     * 录入时间
     */
    private String xtLrsj;

    /**
     * 录入人姓名
     */
    private String xtLrrxm;

    /**
     * 录入人ID
     */
    private String xtLrrid;

    /**
     * 录入人部门
     */
    private String xtLrrbm;

    /**
     * 录入人部门ID
     */
    private String xtLrrbmid;

    /**
     * 录入IP
     */
    private String xtLrip;

    /**
     * 最后修改时间
     */
    private String xtZhxgsj;

    /**
     * 最后修改人姓名
     */
    private String xtZhxgrxm;

    /**
     * 最后修改人ID
     */
    private String xtZhxgrid;

    /**
     * 最后修改人部门
     */
    private String xtZhxgrbm;

    /**
     * 最后修改人部门ID
     */
    private String xtZhxgrbmid;

    /**
     * 最后修改IP
     */
    private String xtZhxgip;


    /**
     * 注销标志
     */
    private String xtZxbz;

    /**
     * 注销原因
     */
    private String xtZxyy;

    /**
     * 注销原因备注
     */
    private String xtZxyybz;

    /**
     * 涉爆情况
     */
    private String sbqk;

    /**
     * 涉枪情况
     */
    private String sqqk;

    /**
     * 所属派出所代码
     */
    private String gxpcsdm;

    /**
     * 所属分县局代码
     */
    private String gxfxjdm;

    /**
     * 所属市局代码
     */
    private String gxsjdm;

    /**
     * 老警综-B_ASJ_AJ表-系统编号
     */
    private String oldSystemid;

    /**
     * 案件业务状态明细代码
     */
    private String ajywztmxdm;
    /**
     * 报警时间
     */
    private Date bjsj;

    /**
     * 案件结案代码
     */
    private String ajjadm;

    /**
     * 案发地点是否标准地址
     */
    private String asjfsddSfbz;

    /**
     * 办案单位_公安机关机构代码
     */
    private String badwGajgjgdm;

    /**
     * 办案单位_公安机关名称
     */
    private String badwGajgmc;

    /**
     * 是否转案
     */
    private String sfza;

    /**
     * 老数据入库标识
     */
    private String lsjrkbz;


    private String depActionFlag;


    private Date depActionTime;


    private Date depFirstenterTime;

}
