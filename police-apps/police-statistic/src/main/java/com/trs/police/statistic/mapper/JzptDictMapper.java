package com.trs.police.statistic.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.trs.police.statistic.domain.entity.JzptDict;
import com.trs.police.statistic.domain.vo.NcDictCategoryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024/4/18
 */
@DS("mysql")
@Mapper
public interface JzptDictMapper {

    /**
     * 案件类别名称
     *
     * @return 结果
     */
    List<JzptDict> selectAjlbmc();

    /**
     * 案件小类别名称
     *
     * @return 结果
     */
    List<JzptDict> selectAjxlbmc();

    /**
     * 行政案件类别名称
     *
     * @return 结果
     */
    List<JzptDict> selectXzajlbdm();

    /**
     * 警情类别名称
     *
     * @return 结果
     */
    List<JzptDict> selectJqlbdm();

    /**
     * 插入字典类别
     *
     * @param vo 字典类别
     */
    void insert(@Param("vo")NcDictCategoryVO vo);
}
