package com.trs.police.statistic.mapper.download;

import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2023/08/15
 */
@Component
public class JzptMapperFactory {

    private final Map<String, JzptMapper> mappers = new ConcurrentHashMap<>();

    JzptMapperFactory(JzptPostgresMapper postgresMapper,
                      JzptOracleMapper oracleMapper,
                      JzptMysqlMapper mysqlMapper,
                      JzptHttpNcMapper ncHttpMapper,
                      JzptNjMapper njMapper) {
        this.mappers.put("postgres", postgresMapper);
        this.mappers.put("oracle", oracleMapper);
        this.mappers.put("mysql", mysqlMapper);
        this.mappers.put("NcHttp", ncHttpMapper);
        this.mappers.put("nj", njMapper);
    }

    /**
     * getMapper
     *
     * @param driverClassName 数据库驱动类名
     * @return mapper
     */
    public JzptMapper getMapper(String driverClassName) {
        return this.mappers.get(driverClassName);
    }
}
