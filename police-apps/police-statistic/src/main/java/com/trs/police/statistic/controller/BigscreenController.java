package com.trs.police.statistic.controller;


import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.statistic.domain.vo.AbstractDataListVO;
import com.trs.police.statistic.service.AnalysisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 大屏
 *
 * <AUTHOR>
 * @date 2024/6/6
 */

@RestController
@RequestMapping
public class BigscreenController {
    @Autowired
    private AnalysisService analysisService;

    /**
     * 自贡大屏-警情统计
     *
     * @param request 过滤参数
     * @return 结果
     */
    @PostMapping("/zgdp/jqtj")
    public AbstractDataListVO getDataVO(@RequestBody ListParamsRequest request) {
        return analysisService.jqtjWithZgdp(request);
    }
}
