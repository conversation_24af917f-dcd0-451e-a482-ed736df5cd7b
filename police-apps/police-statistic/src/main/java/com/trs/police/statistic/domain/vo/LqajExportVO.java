package com.trs.police.statistic.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/11/7
 */
@Data
public class LqajExportVO {

    @ExcelProperty(value = {"单位"},index = 0)
    private String deptName;
    @ExcelProperty(value = {"{month月}","抢劫","立案"},index = 1)
    private Integer qjla;
    @ExcelProperty(value = {"{month月}","抢劫","破案"},index = 2)
    private Integer qjpa;
    @ExcelProperty(value = {"{month月}","抢劫","破案率"},index = 3)
    private Double qjpal;
    @ExcelProperty(value = {"{month月}","抢夺","立案"},index = 4)
    private Integer qdla;
    @ExcelProperty(value = {"{month月}","抢夺","破案"},index = 5)
    private Integer qdpa;
    @ExcelProperty(value = {"{month月}","抢夺","破案率"},index = 6)
    private Double qdpal;


    @ExcelProperty(value = {"{month月}","两抢","立案"},index = 7)
    private Integer lqla;
    @ExcelProperty(value = {"{month月}","两抢","破案"},index = 8)
    private Integer lqpa;
    @ExcelProperty(value = {"{month月}","两抢","破案率"},index = 9)
    private Double lqpal;

    @ExcelProperty(value = {"1-{month月}","抢劫","立案"},index = 10)
    private Integer qjlaYear;
    @ExcelProperty(value = {"1-{month月}","抢劫","破案"},index = 11)
    private Integer qjpaYear;
    @ExcelProperty(value = {"1-{month月}","抢劫","破案率"},index = 12)
    private Double qjpalYear;
    @ExcelProperty(value = {"1-{month月}","抢夺","立案"},index = 13)
    private Integer qdlaYear;
    @ExcelProperty(value = {"1-{month月}","抢夺","破案"},index = 14)
    private Integer qdpaYear;
    @ExcelProperty(value = {"1-{month月}","抢夺","破案率"},index = 15)
    private Double qdpalYear;
    @ExcelProperty(value = {"1-{month月}","两抢","立案"},index = 16)
    private Integer lqlaYear;
    @ExcelProperty(value = {"1-{month月}","两抢","破案"},index = 17)
    private Integer lqpaYear;
    @ExcelProperty(value = {"1-{month月}","两抢","破案率"},index = 18)
    private Double lqpalYear;
}
