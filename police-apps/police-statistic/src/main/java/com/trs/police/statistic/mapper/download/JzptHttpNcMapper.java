package com.trs.police.statistic.mapper.download;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.statistic.converter.NcInterFaceDataConvert;
import com.trs.police.statistic.domain.entity.*;
import com.trs.police.statistic.domain.vo.*;
import com.trs.police.statistic.helper.JzptNcRequestHelper;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 警综平台数据库查询接口 南充警综 http版
 *
 * <AUTHOR>
 */
@Component
public class JzptHttpNcMapper implements JzptMapper{

    @Autowired
    private NcInterFaceDataConvert dataConvert;

    private final JzptNcRequestHelper ncRequestHelper;

    public JzptHttpNcMapper(JzptNcRequestHelper ncRequestHelper) {
        this.ncRequestHelper = ncRequestHelper;
    }


    /**
     * 查询刑事案件信息
     *
     * @param page 分页
     * @param dateTime 时间
     * @param tableName 表名
     * @return 刑事案件信息
     */
    @Override
    public IPage<Xsaj> selectXsaj(@Param("page") IPage page, @Param("dateTime") String dateTime, @Param("tableName") String tableName){
        PageResult<NcXsAjBaseDataVO> ncXsAjBaseDataList = ncRequestHelper.requestNcXsAjBaseData(page, dateTime);
        IPage<Xsaj> xsajPage = new Page<>(page.getCurrent(), page.getSize()
                , ncXsAjBaseDataList.getTotal());
        // 转换 ncXsAjBaseDataList 列表为 Xsaj 列表
        List<Xsaj> xsajList = ncXsAjBaseDataList.getItems().stream()
                .map(vo -> dataConvert.toXsaj(vo))
                .collect(Collectors.toList());
        xsajPage.setRecords(xsajList);
        return xsajPage;
    }

    /**
     * 查询治安案件信息
     *
     * @param page 分页
     * @param dateTime 时间
     * @param tableName 表名
     * @return 治安案件信息
     */
    @Override
    public IPage<Zaaj> selectZaaj(@Param("page") IPage page, @Param("dateTime") String dateTime, @Param("tableName") String tableName){
        PageResult<NcXzAjBaseDataVO> ncXzAjBaseDataList = ncRequestHelper.requestNcXzAjBaseData(page, dateTime);
        IPage<Zaaj> zaajPage = new Page<>(ncXzAjBaseDataList.getPageNumber(),ncXzAjBaseDataList.getPageSize()
                , ncXzAjBaseDataList.getTotal());
        // 转换 ncXsAjBaseDataList 列表为 zaaj 列表
        List<Zaaj> xsajList = ncXzAjBaseDataList.getItems().stream()
                .map(vo -> dataConvert.toZaaj(vo))
                .collect(Collectors.toList());
        zaajPage.setRecords(xsajList);
        return zaajPage;
    }

    /**
     * 查询警情信息
     *
     * @param page 分页
     * @param dateTime 时间
     * @param tableName 表名
     * @return 警情信息watch com.trs.police.statistic.mapper.download.JzptHttpNcMapper selectJqxx '{params,returnObj,throwExp}'  -n 5  -x 3
     */
    @Override
    public IPage<Jqxx> selectJqxx(@Param("page") IPage page, @Param("dateTime") String dateTime, @Param("tableName") String tableName){
        PageResult<NcJqxxVO> ncJqxxList = ncRequestHelper.requestNcJqxx(page, dateTime);
        IPage<Jqxx> jqxxPage = new Page<>(page.getCurrent(), page.getSize(), ncJqxxList.getTotal());
        Map<String, String> map = ncRequestHelper.requestNcDictCategory(page, "BD_D_JQLBDM")
                .stream()
                .collect(Collectors.toMap(NcDictCategoryVO::getDm,
                        NcDictCategoryVO::getCt,
                        (existingValue, newValue) -> existingValue));
        // 转换 NcJqxxVO 列表为 Jqxx 列表
        List<Jqxx> jqxxList = ncJqxxList.getItems().stream()
                .map(vo -> {
                    Jqxx jqxx = new Jqxx();
                    jqxx.setJd(vo.getSfbzdzDqjd());
                    jqxx.setWd(vo.getSfbzdzDqwd());
                    jqxx.setBjsj(TimeUtils.dateToString(vo.getBjsjRqsj(), TimeUtils.YYYYMMDD_HHMMSS));
                    jqxx.setFsddmc(vo.getSfdzDzmc());
                    jqxx.setJqbh(vo.getJqbh());
                    jqxx.setJjdbh(vo.getJjdbh());
                    jqxx.setJqlbdm(vo.getJqlbdm());
                    jqxx.setJqlbmc(map.get(vo.getJqlbdm()));
                    jqxx.setJyaq(vo.getJyaq());
                    jqxx.setCjdwGajgjgdm(vo.getCjdwgajgjgdm());
                    jqxx.setCjdwGajgmc(vo.getCjdwgajgmc());
                    jqxx.setJjdwdm(vo.getJjdwGajgjgdm());
                    jqxx.setJjdwmc(vo.getJjdwGajgmc());
                    return jqxx;
                })
                .collect(Collectors.toList());
        jqxxPage.setRecords(jqxxList);
        return jqxxPage;
    }

    /**
     * 查询刑事案件嫌疑人
     *
     * @param page 分页
     * @param dateTime 时间
     * @param tableName 表名
     * @return 刑事案件嫌疑人
     */
    @Override
    public IPage<Xyr> selectXsajXyr(@Param("page") IPage page, @Param("dateTime") String dateTime, @Param("tableName") String tableName){
        PageResult<NcXsAjXyrVO> ncXsAjXyrList = ncRequestHelper.requestNcXsAjXyr(page, dateTime);
        IPage<Xyr> xyrPage = new Page<>(page.getCurrent(), page.getSize()
                , ncXsAjXyrList.getTotal());
        // 转换 ncXsAjBaseDataList 列表为 zaaj 列表
        List<Xyr> xsajList = ncXsAjXyrList.getItems().stream()
                .map(vo -> dataConvert.toXsajXyr(vo))
                .collect(Collectors.toList());
        xyrPage.setRecords(xsajList);
        return xyrPage;
    }

    /**
     * 查询治安案件嫌疑人
     *
     * @param page 分页
     * @param dateTime 时间
     * @param tableName 表名
     * @return 治安案件嫌疑人
     */
    @Override
    public IPage<Xyr> selectZaajXyr(@Param("page") IPage page, @Param("dateTime") String dateTime, @Param("tableName") String tableName){
        PageResult<NcXzAjXyrVO> ncXsAjXyrList = ncRequestHelper.requestNcXzAjXyr(page, dateTime);
        IPage<Xyr> xyrPage = new Page<>(page.getCurrent(), page.getSize()
                , ncXsAjXyrList.getTotal());
        // 转换 ncXsAjXyrList 列表为 Xyr 列表
        List<Xyr> xsajList = ncXsAjXyrList.getItems().stream()
                .map(vo -> dataConvert.toZaajXyr(vo))
                .collect(Collectors.toList());
        xyrPage.setRecords(xsajList);
        return xyrPage;
    }

    /**
     * 查询刑事案件关联人
     *
     * @param page 分页
     * @param dateTime 时间
     * @param tableName 表名
     * @return 刑事案件关联人
     */
    @Override
    public IPage<Ajxgry> selectXsajAjxgry(@Param("page") IPage page, @Param("dateTime") String dateTime, @Param("tableName") String tableName){
        PageResult<NcXsAjXgRyVO> ncXsAjXgRyList = ncRequestHelper.requestNcXsAjXgRy(page, dateTime);
        IPage<Ajxgry> xgryPage = new Page<>(page.getCurrent(), page.getSize()
                , ncXsAjXgRyList.getTotal());
        // 转换 ncXsAjXyrList 列表为 Xyr 列表
        List<Ajxgry> ajxgriesList = ncXsAjXgRyList.getItems().stream()
                .map(vo -> dataConvert.toXsajAjxgry(vo))
                .collect(Collectors.toList());
        xgryPage.setRecords(ajxgriesList);
        return xgryPage;
    }

    /**
     * 查询治安案件关联人
     *
     * @param page 分页
     * @param dateTime 时间
     * @param tableName 表名
     * @return 治安案件关联人
     */
    @Override
    public IPage<Ajxgry> selectZaajAjxgry(@Param("page") IPage page, @Param("dateTime") String dateTime, @Param("tableName") String tableName){
        PageResult<NcXzAjXgRyVO> ncXzAjXgRyList = ncRequestHelper.requestNcXzAjXgRy(page, dateTime);
        IPage<Ajxgry> xgryPage = new Page<>(page.getCurrent(), page.getSize()
                , ncXzAjXgRyList.getTotal());
        // 转换 ncXsAjXyrList 列表为 Xyr 列表
        List<Ajxgry> ajxgriesList = ncXzAjXgRyList.getItems().stream()
                .map(vo -> dataConvert.toZaajAjxgry(vo))
                .collect(Collectors.toList());
        xgryPage.setRecords(ajxgriesList);
        return xgryPage;
    }


}
