package com.trs.police.statistic.domain.vo;

import com.fasterxml.jackson.databind.JsonNode;
import javax.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2023/8/18 14:17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExportChartVO {

    /**
     * 导出excelName
     */
    @NotBlank(message = "导出excelName不能为空")
    private String tableName;
    /**
     * 需要导出的数据
     */
    private JsonNode tableData;
}
