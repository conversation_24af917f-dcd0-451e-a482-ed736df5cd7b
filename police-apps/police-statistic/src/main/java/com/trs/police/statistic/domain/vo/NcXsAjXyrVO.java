package com.trs.police.statistic.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 南充 刑事案件-嫌疑人 接口返回信息
 * *@author:tang.shuai
 * *@create 2024-06-05 16:37
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NcXsAjXyrVO {

    /**
     * 信息主键编号
     */
    private String xxzjbh;

    /**
     * 案事件编号
     */
    private String asjbh;

    /**
     * 案事件相关人员编号
     */
    private String asjxgrybh;

    /**
     * 人员ID
     */
    private String ryid;

    /**
     * 侦查行为代码（刑专）
     */
    private String zcxwdm;

    /**
     * 嫌疑人_嫌疑人到案状态代码GA_D_DAZTDM
     */
    private String fzxyrXyrdaztdm;

    /**
     * 嫌疑人_人身强制措施代码GA_D_RSQZCSDM
     */
    private String fzxyrRsqzcsdm;

    /**
     * 嫌疑人_采取强制措施时间
     */
    private Date fzxyrCqqzcssj;

    /**
     * 嫌疑人_采取强制措施结束时间
     */
    private Date fzxyrCqqzcsjssj;

    /**
     * 嫌疑人_共同犯罪犯罪嫌疑人涉案地位作用代码GA_D_GTFZFZXYRSADWZYDM
     */
    private String fzxyrGtfzfzxyrsadwzydm;

    /**
     * 嫌疑人_在逃人员编号
     */
    private String fzxyrZtrybh;

    /**
     * 嫌疑人_是否收赃人员_判断标识BD_D_SFDM
     */
    private String fzxyrSfszryPdbz;

    /**
     * 嫌疑人_是否投案自首_判断标识BD_D_SFDM
     */
    private String fzxyrSftazsPdbz;

    /**
     * 嫌疑人_是否上网追逃_判断标识BD_D_SFDM
     */
    private String fzxyrSfswztPdbz;

    /**
     * 嫌疑人_英文名
     */
    private String fzxyrYwm;

    /**
     * 嫌疑人_英文性
     */
    private String fzxyrYwx;

    /**
     * 嫌疑人_姓名
     */
    private String fzxyrXm;

    /**
     * 嫌疑人_曾用名
     */
    private String fzxyrCym;

    /**
     * 嫌疑人_别名
     */
    private String fzxyrBmch;

    /**
     * 嫌疑人_常用证件_常用证件代码KX_D_CYZJDM
     */
    private String fzxyrCyzjCyzjdm;

    /**
     * 嫌疑人_常用证件_代码补充描述
     */
    private String fzxyrCyzjDmbcms;

    /**
     * 嫌疑人_常用证件_证件号码
     */
    private String fzxyrCyzjZjhm;

    /**
     * 嫌疑人_出生日期
     */
    private Date fzxyrCsrq;

    /**
     * 嫌疑人_性别代码GB_D_XBDM
     */
    private String fzxyrXbdm;

    /**
     * 嫌疑人_国籍代码GB_D_GJHDQDM
     */
    private String fzxyrGjdm;

    /**
     * 嫌疑人_籍贯省市县代码GB_D_XZQHDMNEW
     */
    private String fzxyrJgdm;

    /**
     * 嫌疑人_民族代码GB_D_MZDM
     */
    private String fzxyrMzdm;

    /**
     * 嫌疑人_出生地_国家和地区代码GB_D_GJHDQDM
     */
    private String fzxyrCsdGjhdqdm;

    /**
     * 嫌疑人_出生地_行政区划代码GB_D_XZQHDMNEW
     */
    private String fzxyrCsdXzqhdm;

    /**
     * 嫌疑人_出生地_地址名称
     */
    private String fzxyrCsdDzmc;

    /**
     * 嫌疑人_户籍地址_行政区划代码GB_D_XZQHDMNEW
     */
    private String fzxyrHjdzXzqhdm;

    /**
     * 嫌疑人_户籍地址_地址名称
     */
    private String fzxyrHjdzDzmc;

    /**
     * 嫌疑人_现住址_地址ID
     */
    private String fzxyrXzzDzid;

    /**
     * 嫌疑人_现住址_行政区划代码GB_D_XZQHDMNEW
     */
    private String fzxyrXzzXzqhdm;

    /**
     * 嫌疑人_现住址_地址名称
     */
    private String fzxyrXzzDzmc;

    /**
     * 嫌疑人_境外住址_国家和地区代码GB_D_GJHDQDM
     */
    private String fzxyrJwzzGjhdqdm;

    /**
     * 嫌疑人_境外住址_地址名称
     */
    private String fzxyrJwzzDzmc;

    /**
     * 嫌疑人_身高
     */
    private Double fzxyrSg;

    /**
     * 嫌疑人_体重
     */
    private Double fzxyrTz;

    /**
     * 嫌疑人_足长
     */
    private Double fzxyrZc;

    /**
     * 嫌疑人_体貌特征描述
     */
    private String fzxyrTmtzms;

    /**
     * 嫌疑人_体表标记描述
     */
    private String fzxyrTbbjms;

    /**
     * 嫌疑人_人其他特征_简要情况
     */
    private String fzxyrRqttzJyqk;

    /**
     * 嫌疑人_工作单位
     */
    private String fzxyrGzdw;

    /**
     * 嫌疑人_宗教信仰代码ZA_D_ZJXYDM
     */
    private String fzxyrZjxydm;

    /**
     * 嫌疑人_政治面貌代码GB_D_ZZMMDM
     */
    private String fzxyrZzmmdm;

    /**
     * 嫌疑人_学历代码GB_D_XLDM
     */
    private String fzxyrXldm;

    /**
     * 嫌疑人_婚姻状况代码GB_D_HYZKDM
     */
    private String fzxyrJyzkdm;

    /**
     * 嫌疑人_兵役状况代码ZA_D_BYQKDM
     */
    private String fzxyrByzkdm;

    /**
     * 嫌疑人_职业_职业类别代码KX_D_ZYLBDM
     */
    private String fzxyrZylbdm;

    /**
     * 嫌疑人_职业_代码补充描述
     */
    private String fzxyrZydmBcms;

    /**
     * 嫌疑人_案事件相关人员身份_案事件相关人员身份代码GA_D_ASJXGRYSFDM
     */
    private String fzxyrAsjxgrysfsAsjxgrysfdm;

    /**
     * 嫌疑人_案事件相关人员身份_代码补充描述
     */
    private String fzxyrAsjxgrysfsDmbcms;

    /**
     * 嫌疑人_犯罪嫌疑人特殊专长_犯罪嫌疑人特殊专长代码GA_D_FZXYRTSZCDM
     */
    private String fzxyrFzxyrtszcFzxyrtszcdm;

    /**
     * 嫌疑人_犯罪嫌疑人特殊专长_代码补充描述
     */
    private String fzxyrFzxyrtszcDmbcms;

    /**
     * 嫌疑人_简历
     */
    private String fzxyrJl;

    /**
     * 嫌疑人_个人爱好及活动特点_简要情况
     */
    private String fzxyrGrahjhdtJyqk;

    /**
     * 嫌疑人_违法犯罪经历描述
     */
    private String fzxyrWffzjlms;

    /**
     * 嫌疑人_是否有吸毒史_判断标识BD_D_SFDM
     */
    private String fzxyrSfyxdsPdbz;

    /**
     * 嫌疑人_滥用毒品_涉案物品代码GA_D_SAWPFLDM
     */
    private String fzxyrLydpSawpdm;

    /**
     * 嫌疑人_滥用毒品_代码补充描述
     */
    private String fzxyrLydpDmbcms;

    /**
     * 嫌疑人_是否怀孕_判断标识
     */
    private String fzxyrSfhyPdbz;

    /**
     * 嫌疑人_毒品尿检是否阳性_判断标识
     */
    private String fzxyrDpnjsfyxPdbz;

    /**
     * 嫌疑人_是否艾滋病病毒携带者_判断标识
     */
    private String fzxyrSfazbbdxdzPdbz;

    /**
     * 嫌疑人_是否重大疾病患者_判断标识
     */
    private String fzxyrSfzdjbhzPdbz;

    /**
     * 嫌疑人_是否精神病人_判断标识
     */
    private String fzxyrSfjsbrPdbz;

    /**
     * 嫌疑人_携带物品_简要情况
     */
    private String fzxyrXdwpJyqk;

    /**
     * 嫌疑人_十指指纹编号
     */
    private String fzxyrSzzwbh;

    /**
     * 嫌疑人_人员DNA编号
     */
    private String fzxyrRydnabh;

    /**
     * 嫌疑人_备注
     */
    private String fzxyrBz;

    /**
     * 嫌疑人_联系电话
     */
    private String fzxyrLxdh;

    /**
     * 嫌疑人_血型
     */
    private String fzxyrXxdm;

    /**
     * 所属派出所代码
     */
    private String gxpcsdm;

    /**
     * 所属分县局代码
     */
    private String gxfxjdm;

    /**
     * 所属市局代码
     */
    private String gxsjdm;

    /**
     * 录入时间
     */
    private String xtLrsj;

    /**
     * 录入人姓名
     */
    private String xtLrrxm;

    /**
     * 录入人ID
     */
    private String xtLrrid;

    /**
     * 录入人部门
     */
    private String xtLrrbm;

    /**
     * 录入人部门ID
     */
    private String xtLrrbmid;

    /**
     * 录入IP
     */
    private String xtLrip;

    /**
     * 最后修改时间
     */
    private String xtZhxgsj;

    /**
     * 最后修改人姓名
     */
    private String xtZhxgrxm;

    /**
     * 最后修改人ID
     */
    private String xtZhxgrid;

    /**
     * 最后修改人部门
     */
    private String xtZhxgrbm;

    /**
     * 最后修改人部门ID
     */
    private String xtZhxgrbmid;

    /**
     * 最后修改IP
     */
    private String xtZhxgip;

    /**
     * 注销标志
     */
    private String xtZxbz;

    /**
     * 注销原因
     */
    private String xtZxyy;

    /**
     * 注销原因备注
     */
    private String xtZxyybz;

    /**
     * 老警综系统编号（数据抽取）
     */
    private String oldSystemid;

    /**
     * 老警综涉案情况表系统编号（数据抽取）
     */
    private String oldRysaqkSystemid;

    /**
     * 嫌疑人_出生地_地址ID
     */
    private String fzxyrCsdDzid;

    /**
     * 嫌疑人_户籍地址_地址ID
     */
    private String fzxyrHjdzDzid;

    /**
     * 老警综-人口systemid（数据抽取）
     */
    private String oldRyid;

    /**
     * 犯罪嫌疑人人身措施明细代码
     */
    private String fzxyrRsqzcsdmx;

    /**
     * 老数据入库标识（数据抽取）
     */
    private String lsjrkbz;

    /**
     * 刑专人员编号
     */
    private String xzrybh;

    /**
     * 来源表_主键
     */
    private String lyZj;

    /**
     * 是否已出具吸毒文书
     */
    private String sfycjxdws;

    private String depActionFlag;

    private Date depActionTime;

    private Date depFirstenterTime;
}
