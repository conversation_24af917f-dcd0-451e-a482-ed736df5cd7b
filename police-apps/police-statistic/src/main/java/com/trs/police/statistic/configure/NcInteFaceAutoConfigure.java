package com.trs.police.statistic.configure;

import com.trs.police.statistic.config.NcInterfaceConfig;
import com.trs.police.statistic.helper.JzptNcRequestHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/06/27
 */
@Configuration
@Slf4j
public class NcInteFaceAutoConfigure{

    /**
     * 南充警综接口信息
     *
     * @return NcInterfaceConfig
     */
    @Bean
    @ConfigurationProperties(prefix = "nc.jwzh.interface.info")
    public NcInterfaceConfig ncInterfaceConfig() {
        return new NcInterfaceConfig();
    }


    /**
     * 南充警综接口工具类
     *
     * @param ncInterfaceConfig 南充警综接口信息
     * @return JzptNcRequestHelper
     */
    @Bean
    public JzptNcRequestHelper jzptNcRequestHelper(NcInterfaceConfig ncInterfaceConfig) {
        return new JzptNcRequestHelper(ncInterfaceConfig);
    }
}
