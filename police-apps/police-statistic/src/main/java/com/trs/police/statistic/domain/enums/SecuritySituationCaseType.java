package com.trs.police.statistic.domain.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.jackson.annotate.JsonCreator;

/**
 * 案件类别枚举
 *
 * <AUTHOR>
 * @date 2023/04/30
 */
public enum SecuritySituationCaseType {

    TOTAL_CASE("总数"),
    /**
     * 有效案件
     */
    EFFECTIVE_CASE("有效类"),
    /**
     * 刑事案件
     */
    CRIMINAL_CASE("刑事类"),
    /**
     * 治安案件
     */
    ADMINISTRATIVE_CASE("治安类");

    @Getter
    @JsonValue
    private final String name;

    SecuritySituationCaseType(String name) {
        this.name = name;
    }

    /**
     * 名称转换枚举
     *
     * @param name 名称
     * @return 枚举
     */
    @JsonCreator
    public static SecuritySituationCaseType nameOf(String name) {
        if (StringUtils.isNotBlank(name)) {
            for (SecuritySituationCaseType type : SecuritySituationCaseType.values()) {
                if (type.getName().equals(name)) {
                    return type;
                }
            }
        }
        return null;
    }
}
