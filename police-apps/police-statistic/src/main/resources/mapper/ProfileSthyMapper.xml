<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.statistic.mapper.ProfileSthyMapper">
    <select id="selectCountByGroup" resultType="com.trs.police.statistic.domain.vo.NamedIndexVO">
        select ${groupField} name, count(1) count
        from t_profile_sthy t
        <include refid="where"></include>
        group by ${groupField}
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        select count(1)
        from t_profile_sthy t
        <include refid="where"></include>
    </select>

    <select id="selectCountByJqlbdms" resultType="java.lang.Integer">
        select count(1)
        from t_profile_sthy t
        <include refid="where"></include>
        <include refid="jqlbdms"></include>
    </select>

    <sql id="where">
        <where>
            t.bjsj between #{timeParams.beginTime,jdbcType=TIMESTAMP} and #{timeParams.endTime,jdbcType=TIMESTAMP}
            <if test="filterParams!=null and filterParams.size()>0">
                <foreach collection="filterParams" item="filterParam">
                    <if test="filterParam.key == 'jqlbmc'">
                        <foreach collection="filterParam.value" item="value" separator="," open="and t.jqlbmc in (" close=")">
                            #{value}
                        </foreach>
                    </if>
                    <if test="filterParam.key == 'areaCode'">
                        and t.gxdwdm like '${@com.trs.police.common.core.utils.StringUtil@getShortAreaCode(filterParam.value)}%'
                    </if>
                </foreach>
            </if>
        </where>
    </sql>

    <sql id="jqlbdms">
        <if test="jqlbdms != null and jqlbdms!=''">
            <foreach collection="jqlbdms.split(',')" item="value" separator="or" open="and (" close=")">
                <choose>
                    <when test="value.length()==2">
                        t.jqlbdm = concat(#{value},'000000')
                    </when>
                    <when test="value.length()==4">
                        t.jqlxdm = concat(#{value},'0000')
                    </when>
                    <when test="value.length()==6">
                        t.jqxldm = concat(#{value},'00')
                    </when>
                    <otherwise>
                        t.jqzldm = #{value}
                    </otherwise>
                </choose>
            </foreach>
        </if>
    </sql>

    <select id="getJqczqk" resultType="com.trs.police.statistic.domain.vo.JqczqkVO">
        SELECT
            COALESCE(sum(case when aj.asjbh is not null then 1 else 0 end), 0) as laCount,
            COALESCE(sum(case when jq.JQCLZTDM != 1 then 1 else 0 end), 0) as cjCount,
            COALESCE(sum(case when jq.JQLBDM = '01000000' then 1 else 0 end), 0) as xsCount,
            COALESCE(sum(case when jq.JQLBDM = '02000000' then 1 else 0 end), 0) as xzCount,
            COALESCE(sum(case when jq.JQLBDM = '03000000' then 1 else 0 end), 0) as jtCount,
            COALESCE(sum(case when jq.JQLBDM = '08000000' then 1 else 0 end), 0) as jfCount
        from t_profile_sthy jq
                 left join JWZH_XSAJ_AJ aj on jq.JJDBH = aj.jjbh
        where WXBS = 0
            and jq.bjsj between #{timeParams.beginTime,jdbcType=TIMESTAMP} and #{timeParams.endTime,jdbcType=TIMESTAMP}
        <if test="areaCode!=null and areaCode!=''">
            and jq.gxdwdm like '${@com.trs.police.common.core.utils.StringUtil@getShortAreaCode(areaCode)}%'
        </if>
    </select>

    <select id="getYxWxCount" resultType="com.trs.police.statistic.domain.vo.JqczqkVO">
        SELECT
            COALESCE(sum(case when WXBS = 1 then 1 else 0 end), 0) as wxCount,
            COALESCE(sum(case when WXBS = 0 then 1 else 0 end), 0) as yxCount
        from t_profile_sthy jq
        where
            bjsj between #{timeParams.beginTime,jdbcType=TIMESTAMP} and #{timeParams.endTime,jdbcType=TIMESTAMP}
        <if test="areaCode!=null and areaCode!=''">
            and gxdwdm like '${@com.trs.police.common.core.utils.StringUtil@getShortAreaCode(areaCode)}%'
        </if>
    </select>
    <select id="countGroupByDate" resultType="com.trs.police.common.core.vo.KeyValueVO">
        SELECT
        DATE_FORMAT(jjsj, '%m-%d') as `key`,
        COUNT(1) as `value`
        FROM t_profile_sthy
        <where>
            DELETED = 0
            <if test="startTime != null and startTime != ''">
                and jjsj >= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and jjsj &lt;= #{endTime}
            </if>
        </where>
        GROUP BY DATE_FORMAT(jjsj, '%m-%d')
    </select>
    <select id="countRecent24Hours" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM t_profile_sthy
        <where>
            DELETED = 0
            <if test="startTime != null and startTime != ''">
                and jjsj >= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and jjsj &lt;= #{endTime}
            </if>
        </where>
    </select>
</mapper>