<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.statistic.mapper.JzZaajAjMapper">
    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM t_statistic_jz_zaaj t
        <include refid="where"/>
    </select>

    <select id="selectPage" resultType="com.trs.police.statistic.domain.vo.page.AjPageListVO">
        <include refid="select"/>
    </select>

    <select id="selectList" resultType="com.trs.police.statistic.domain.vo.page.AjPageListVO">
        <include refid="select"/>
    </select>

    <insert id="insert">
        INSERT IGNORE INTO t_statistic_jz_zaaj
            (asjbh, ajmc, ajlbmc, jyaq, slsj, badw_gajgmc, badw_gajgjgdm, ajlbdm,jd,wd)
        VALUES (#{zaaj.asjbh,jdbcType=VARCHAR},
                #{zaaj.ajmc,jdbcType=VARCHAR},
                #{zaaj.ajlbmc,jdbcType=VARCHAR},
                #{zaaj.jyaq,jdbcType=VARCHAR},
                #{zaaj.slsj,jdbcType=TIMESTAMP},
                #{zaaj.badwGajgmc,jdbcType=VARCHAR},
                #{zaaj.badwGajgjgdm,jdbcType=VARCHAR},
                #{zaaj.ajlbdm,jdbcType=VARCHAR},
                #{zaaj.jd,jdbcType=DOUBLE},
                #{zaaj.wd,jdbcType=DOUBLE}) ON DUPLICATE KEY UPDATE
            ajmc = #{zaaj.ajmc,jdbcType=VARCHAR},
            ajlbmc = #{zaaj.ajlbmc,jdbcType=VARCHAR},
            jyaq = #{zaaj.jyaq,jdbcType=VARCHAR},
            slsj = #{zaaj.slsj,jdbcType=VARCHAR},
            badw_gajgmc = #{zaaj.badwGajgmc,jdbcType=VARCHAR},
            badw_gajgjgdm = #{zaaj.badwGajgjgdm,jdbcType=VARCHAR},
            ajlbdm = #{zaaj.ajlbdm,jdbcType=VARCHAR},
            jd = #{zaaj.jd,jdbcType=DOUBLE},
            wd = #{zaaj.wd,jdbcType=DOUBLE};
    </insert>

    <select id="selectHeatList" resultType="com.trs.police.statistic.domain.vo.HeatMapVO">
        select t.JD as lng,
               t.WD as lat,
               1    as count
        from t_statistic_jz_zaaj t
        <include refid="where"/>
    </select>

    <sql id="select">
        SELECT t.asjbh       as code,
        t.ajmc        as name,
        t.jyaq        as content,
        t.ajlbmc      as caseType,
        t.slsj        as time,
        t.badw_gajgmc as dept
        FROM t_statistic_jz_zaaj t
        <include refid="where"/>
        order by t.slsj
    </sql>

    <sql id="where">
        <!--@sql SELECT COUNT(1) FROM t_statistic_jz_zaaj t-->
        <where>
            t.slsj between #{beginTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
            <choose>
                <when test="deptCode.equals('510599')">
                    AND left(t.badw_gajgjgdm, 6) in ('510502', '510503', '510504')
                </when>
                <otherwise>
                    AND t.badw_gajgjgdm like concat(#{deptCode,jdbcType=VARCHAR}, '%')
                </otherwise>
            </choose>
            <include refid="lb"/>
        </where>
    </sql>

    <sql id="lb">
        <if test="!subCaseTypes.empty">
            <!--@sql SELECT COUNT(1) FROM t_statistic_jz_zaaj t WHERE t.slsj > 0 -->
            AND
            <foreach collection="subCaseTypes" open="(" close=")" item="subCaseType" separator="OR">
                <choose>
                    <!--所有治安-->
                    <when test="subCaseType.startsWith('治安')">
                        <!--@ignoreSql-->
                        1=1
                    </when>
                    <when test="subCaseType.equals('涉黄')">
                        t.ajlbdm IN
                        ('03040045', '03040046', '03040047', '03040048', '03040049', '03040050', '03040051',
                        '03040052', '03040053', '03040054', '03040055', '11000000', '11000001', '24000003',
                        '24000004', '24000005', '24000006', '24000007', '24000008', '24000009')
                    </when>
                    <when test="subCaseType.equals('涉毒')">
                        <!--@ignoreSql-->
                        t.ajlbdm IN
                        ('03040058'
                        , '03040059'
                        , '03040061'
                        , '03040062'
                        , '03040063'
                        , '03040065'
                        , '24000001'
                        , '24000002'
                        , '65000001'
                        , '65000002')
                    </when>
                    <when test="subCaseType.equals('涉赌')">
                        <!--@ignoreSql-->
                        t.ajlbdm IN
                        ('03040056'
                        , '03040057'
                        , '24000010'
                        , '24000011'
                        , '24000021')
                    </when>
                    <when test="subCaseType.equals('殴打他人')">
                        <!--@ignoreSql-->
                        t.ajlbdm IN ('03030015')
                    </when>
                    <when test="subCaseType.equals('殴打')">
                        <!--@ignoreSql-->
                        t.ajlbdm IN ('03030015')
                    </when>
                    <when test="subCaseType.equals('主要治安案件')">
                        <!--@ignoreSql-->
                        t.ajlbdm IN ('03040056', '03040057', '24000010', '24000011', '24000021',
                        '03040058' , '03040059' , '03040061' , '03040062' , '03040063' , '03040065' ,
                        '24000001' , '24000002' , '65000001' , '65000002','03040045', '03040046', '03040047',
                        '03040048', '03040049', '03040050', '03040051','03040052', '03040053', '03040054', '03040055',
                        '11000000', '11000001', '24000003','24000004', '24000005', '24000006', '24000007', '24000008',
                        '24000009')
                    </when>
                    <otherwise>
                        <!--@ignoreSql-->
                        1 = 0
                    </otherwise>
                </choose>
            </foreach>
        </if>
    </sql>

</mapper>