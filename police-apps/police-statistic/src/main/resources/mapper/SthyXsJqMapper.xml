<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.statistic.mapper.SthyXsajJqMapper">
    <select id="selectCount" resultType="java.lang.Integer">
        SELECT count(1)
        from t_statistic_sthy_jqxx t
        <include refid="where"/>
    </select>

    <insert id="insert">
        INSERT IGNORE INTO t_statistic_sthy_jqxx
        (jqbh, jjdbh, jyaq, jqlbdm, bjsj, cjdw_gajgjgdm, cjdw_gajgmc, jd, wd, lhlx, fsddmc, wxbs)
        VALUES (#{sthy.jqbh,jdbcType=VARCHAR},
                #{sthy.jjdbh,jdbcType=VARCHAR},
                #{sthy.jyaq,jdbcType=VARCHAR},
                #{sthy.jqlbdm,jdbcType=VARCHAR},
                #{sthy.bjsj,jdbcType=VARCHAR},
                #{sthy.cjdwGajgjgdm,jdbcType=VARCHAR},
                (select name from t_dept d where d.code = #{sthy.cjdwGajgjgdm,jdbcType=VARCHAR}),
                #{sthy.jd,jdbcType=DOUBLE},
                #{sthy.wd,jdbcType=DOUBLE},
                #{sthy.lhlx,jdbcType=VARCHAR},
                #{sthy.fsddmc,jdbcType=VARCHAR},
                #{sthy.wxbs,jdbcType=TINYINT}) ON DUPLICATE KEY UPDATE
            jyaq = #{sthy.jyaq,jdbcType=VARCHAR},
            jqlbdm = #{sthy.jqlbdm,jdbcType=VARCHAR},
            bjsj = #{sthy.bjsj,jdbcType=VARCHAR},
            cjdw_gajgjgdm = #{sthy.cjdwGajgjgdm,jdbcType=VARCHAR},
            cjdw_gajgmc = (select name from t_dept d where d.code = #{sthy.cjdwGajgjgdm,jdbcType=VARCHAR}),
            jd = #{sthy.jd,jdbcType=DOUBLE},
            wd = #{sthy.wd,jdbcType=DOUBLE},
            lhlx = #{sthy.lhlx,jdbcType=VARCHAR},
            fsddmc = #{sthy.fsddmc,jdbcType=VARCHAR},
            wxbs = #{sthy.wxbs,jdbcType=TINYINT};
    </insert>

    <select id="selectPage" resultType="com.trs.police.statistic.domain.vo.page.JqPageListVO">
        <include refid="select"/>
    </select>


    <select id="selectList" resultType="com.trs.police.statistic.domain.vo.page.JqPageListVO">
        <include refid="select"/>
    </select>

    <sql id="select">
        SELECT t.JQBH        as code,
               t.jjdbh       as jjdCode,
               t.JYAQ        as content,
               t.JQLBMC      as caseType,
               t.fsddmc      as address,
               t.BJSJ        as time,
               t.CJDW_GAJGMC as dept
        FROM t_statistic_sthy_jqxx t
        <include refid="where"/>
    </sql>

    <sql id="where">
        <!--@sql SELECT count(1) from t_statistic_sthy_jqxx t -->
        <where>
            t.bjsj between #{beginTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
            <choose>
                <when test="deptCode.equals('510599')">
                    AND left(t.CJDW_GAJGJGDM, 6) in ('510502', '510503', '510504')
                </when>
                <otherwise>
                    AND t.CJDW_GAJGJGDM like concat(#{deptCode,jdbcType=VARCHAR}, '%')
                </otherwise>
            </choose>
              AND t.JQLBDM like '01%'
        </where>
    </sql>
</mapper>