<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.statistic.mapper.download.JzptMysqlMapper">
    <select id="selectXsaj" resultType="com.trs.police.statistic.domain.entity.Xsaj">
        select  t.*,
                case when t.asjfsdd_dqjd = '无' then null else t.asjfsdd_dqjd end as JD,
                case when t.asjfsdd_dqwd = '无' then null else t.asjfsdd_dqwd end as WD
        from ${tableName} t
        <where>
            t.xt_zxbz = '1'
            and case_type = 0
            <choose>
                <when test="dateTime == null">
                    and t.xt_zhxgsj > '20200101'
                </when>
                <otherwise>
                    and t.xt_zhxgsj > #{dateTime}
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="selectZaaj" resultType="com.trs.police.statistic.domain.entity.Zaaj">
        select  t.*,
                case when t.asjfsdd_dqjd = '无' then null else t.asjfsdd_dqjd end as JD,
                case when t.asjfsdd_dqwd = '无' then null else t.asjfsdd_dqwd end as WD
        from ${tableName} t
        <where>
            t.xt_zxbz = '1'
            and case_type = 1
            <choose>
                <when test="dateTime == null">
                    and t.xt_zhxgsj > '20200101'
                </when>
                <otherwise>
                    and t.xt_zhxgsj > #{dateTime}
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="selectJqxx" resultType="com.trs.police.statistic.domain.entity.Jqxx">
        select t.jqbh                                                                           as JQBH,
               t.jjdbh                                                                          as JJDBH,
               t.cjdw_gajgjgdm                                                                  AS CJDW_GAJGJGDM,
               t.bjsj_rqsj                                                                      as bjsj,
               t.jqlbdm                                                                         AS JQLBDM,
               t.cjdw_gajgmc                                                                    AS cjdw_gajgmc,
               t.jyaq                                                                           as jyaq,
               t.sfbzdz_dqjd                                                                    as JD,
               t.sfbzdz_dqwd                                                                    as WD,
               t.sfbzdz_dzmc                                                                    as FSDDMC,
               t.jjdw_gajgjgdm                                                                  AS jjdwdm,
               t.jjdw_gajgmc                                                                    AS jjdwmc
        from ${tableName} t
        <where>
            t.cjdw_gajgjgdm is not null
              and t.jqztdm not in ('04', '05')
            <choose>
                <when test="dateTime == null">
                    and t.xt_zhxgsj > '20200101'
                </when>
                <otherwise>
                    and t.xt_zhxgsj > #{dateTime}
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="selectXsajXyr" resultType="com.trs.police.statistic.domain.entity.Xyr">
        select t.*,
               t.jsdm  as rsqzcs
        from ${tableName} t
        <where>
            <choose>
                <when test="dateTime == null">
                    and t.lrsj > '20200101'
                </when>
                <otherwise>
                    and t.lrsj > #{dateTime}
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="selectZaajXyr" resultType="com.trs.police.statistic.domain.entity.Xyr">
        select t.*,
               t.jsdm  as rsqzcs
        from ${tableName} t
        <where>
            <choose>
                <when test="dateTime == null">
                    and t.lrsj > '20200101'
                </when>
                <otherwise>
                    and t.lrsj > #{dateTime}
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="selectXsajAjxgry" resultType="com.trs.police.statistic.domain.entity.Ajxgry">
        select t.*,
        t.cyzj_zjhm       as ZJHM,
        t.asjxgrybh       AS xgrybh,
        t.xt_lrsj         AS LRSJ,
        t.XXZJBH          as zj,
        t.ASJXGRYJSDM     as role
        from ${tableName} t
        <where>
            <choose>
                <when test="dateTime == null">
                    and t.xt_zhxgsj > '20200101'
                </when>
                <otherwise>
                    and t.xt_zhxgsj > #{dateTime}
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="selectZaajAjxgry" resultType="com.trs.police.statistic.domain.entity.Ajxgry">
        select t.*,
        t.cyzj_zjhm       as ZJHM,
        t.asjxgrybh       AS xgrybh,
        t.xt_lrsj         AS LRSJ,
        t.XXZJBH          as zj
        from ${tableName} t
        <where>
            <choose>
                <when test="dateTime == null">
                    and t.xt_zhxgsj > '20200101'
                </when>
                <otherwise>
                    and t.xt_zhxgsj > #{dateTime}
                </otherwise>
            </choose>
        </where>
    </select>
</mapper>