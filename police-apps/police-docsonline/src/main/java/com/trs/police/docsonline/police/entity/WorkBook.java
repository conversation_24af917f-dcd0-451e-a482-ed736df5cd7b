package com.trs.police.docsonline.police.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.docsonline.authapi.User;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@TableName(value = "tb_workbook")
public class WorkBook implements Serializable {

    /**
     * list_id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    @TableField(value = "title")
    private String title;

    @TableField(value = "create_user_id")
    private Long createUserId;

    @TableField(value = "create_user_real_name")
    private String createUserRealName;

    @TableField(value = "create_user_dept_id")
    private Long createUserDeptId;

    @TableField(value = "create_user_dept_name")
    private String createUserDeptName;

    @TableField(value = "create_time")
    private Date createTime;

    public WorkBook(String title, User user) {
        this.title = title;
        this.createUserId = user.getUserId();
        this.createUserRealName = user.getUserRealName();
        this.createUserDeptId = user.getUserDeptId();
        this.createUserDeptName = user.getUserDeptName();
        this.createTime = new Date();
    }
}
