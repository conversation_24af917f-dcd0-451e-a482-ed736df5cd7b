package com.trs.police.log.domain.dto;

import com.trs.common.exception.ServiceException;
import com.trs.police.common.core.dto.BaseListDTO;
import lombok.Data;

/**
 * 登录日志dto
 *
 * <AUTHOR>
 * @date 2024/8/6
 */
@Data
public class LoginLogDTO extends BaseListDTO {
    /**
     * 用户名
     */
    private String userName;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    private String deptInfo;

    private String keyword;

    private String keywordType;

    @Override
    protected boolean checkParams() throws ServiceException {
        return true;
    }
}
