<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.incident.analysis.mapper.RiskCompanyMapper">

    <select id="getRiskCompanyIncidentCount" resultType="java.lang.Integer">
        SELECT
            count(1)
        from
        (
            select
                 IFNULL(c.company_name,b.gsmc) as companyName
            FROM t_jjdb as a
            JOIN t_jjdbtj as b ON a.jjdbh = b.jjdbh
            LEFT JOIN t_company as c on FIND_IN_SET(b.gsmc, c.company_aliases) > 0
            <include refid="com.trs.police.incident.analysis.mapper.SqlSnippetMapper.deptJoinAndBjrqCondition">
                <property name="mainTableAlias" value="a"/>
            </include>
            and b.gsmc != '' and b.gsmc != '未知公司'
            and b.gsmc not in (select excluded_name from t_company_excluded)
            <choose>
                <when test="type == 'incident'">
                    <!--警情数量，无附加条件-->
                </when>
                <when test="type == 'company'">
                    <!--涉及公司数量-->
                    GROUP BY companyName
                </when>
                <when test="type == 'risk'">
                    <!--风险公司数量，在涉及公司的基础上，涉及警情指定次数及以上-->
                    GROUP BY companyName
                    HAVING COUNT(1) >= 3
                </when>
                <when test="type == 'specify'">
                   and IFNULL(c.company_name,b.gsmc) = #{dto.companyName}
                </when>
            </choose>
        ) as t
    </select>
    
    
    <select id="getCompanyList"
            resultType="com.trs.police.incident.analysis.domain.response.RiskCompanyGetCompanyListResponse">
        SELECT
        companyName,
        incidentCount,
        isRisk,
        CASE
        WHEN companyName is null
        THEN 1
        ELSE 0
        END AS isNew
        FROM
        (
            select
                 IFNULL(c.company_name,b.gsmc) as companyName,
                 count(1) as incidentCount,
                 CASE
                     WHEN count(1) >= 3 then 1
                     ELSE 0
                     END AS isRisk
            FROM t_jjdb AS a
            JOIN t_jjdbtj AS b ON a.jjdbh = b.jjdbh
            LEFT JOIN t_company AS c ON FIND_IN_SET(b.gsmc, c.company_aliases) > 0
            <include refid="com.trs.police.incident.analysis.mapper.SqlSnippetMapper.deptJoinAndBjrqCondition">
                <property name="mainTableAlias" value="a"/>
            </include>
            AND b.gsmc != '' AND b.gsmc != '未知公司'
            AND b.gsmc NOT IN (SELECT excluded_name FROM t_company_excluded)
            GROUP BY companyName
            <if test="dto.getOnlyRisk() != null and dto.getOnlyRisk()">
                <!--只看风险公司列表-->
                HAVING incidentCount >= 3
            </if>
             ORDER BY incidentCount DESC
        ) as t
        <if test="dto.getCompanyName() != null">
            <!--公司名称模糊查询-->
            WHERE t.companyName LIKE CONCAT('%',#{dto.companyName}, '%')
        </if>
    </select>


</mapper>
