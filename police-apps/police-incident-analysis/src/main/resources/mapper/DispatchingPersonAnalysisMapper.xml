<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.incident.analysis.mapper.DispatchingPersonAnalysisMapper">

    <select id="getPoliceManCount" resultType="java.lang.Integer">
        SELECT
        count(DISTINCT c.cjybh)
        from t_jjdb as a
        LEFT JOIN t_cjdb as c ON c.jjdbh = a.jjdbh and (c.czlbbh = '6' or czlbmc like '%到达%')
        <include refid="com.trs.police.incident.analysis.mapper.SqlSnippetMapper.PoliceSituationCommonQueryCondition">
            <property name="mainTableAlias" value="a"/>
        </include>
        <if test="dto.sfyxjq != null">
            and
            <if test="dto.sfyxjq == 1">
                b.sfyxjq = 1
            </if>
            <if test="dto.sfyxjq == 0">
                b.sfyxjq = 0
            </if>
        </if>
        and c.cjybh is not null
    </select>
    <select id="getDispatchingPersonAnalysis" resultType="com.trs.police.incident.analysis.domain.response.DispatchingPersonCountResponse">
        SELECT
        c.cjyxm,
        c.cjdwbm,
        cjdw.name as cjdwmc,
        count(1) as cjzs
        from t_jjdb as a
        LEFT JOIN t_cjdb as c ON c.jjdbh = a.jjdbh and (c.czlbbh = '6' or czlbmc like '%到达%')
        LEFT JOIN t_dept as cjdw ON cjdw.code = c.cjdwbm
        <include refid="com.trs.police.incident.analysis.mapper.SqlSnippetMapper.PoliceSituationCommonQueryCondition">
            <property name="mainTableAlias" value="a"/>
        </include>
        <if test="dto.sfyxjq != null">
            and
            <if test="dto.sfyxjq == 1">
                b.sfyxjq = 1
            </if>
            <if test="dto.sfyxjq == 0">
                b.sfyxjq = 0
            </if>
        </if>
        and c.cjybh is not null
        group by c.cjybh,c.cjyxm,c.cjdwbm,c.cjdwmc
        order by cjzs desc
        <if test="dto.start != null and dto.end != null">
            limit #{dto.start},#{dto.end}
        </if>
    </select>
</mapper>