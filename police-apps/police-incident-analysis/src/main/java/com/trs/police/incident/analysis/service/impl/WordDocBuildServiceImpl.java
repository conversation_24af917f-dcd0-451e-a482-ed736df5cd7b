package com.trs.police.incident.analysis.service.impl;

import com.trs.police.incident.analysis.conf.properties.WordDocIncidentProperties;
import com.trs.police.incident.analysis.constants.DocBuildConstants;
import com.trs.police.incident.analysis.converter.WordDocQueryConverter;
import com.trs.police.incident.analysis.domain.dto.WordDocQueryDTO;
import com.trs.police.incident.analysis.domain.entity.DeptEntity;
import com.trs.police.incident.analysis.domain.request.WordDocBuildRequest;
import com.trs.police.incident.analysis.domain.response.PoliceSituationDeptCountResponse;
import com.trs.police.incident.analysis.mapper.DeptMapper;
import com.trs.police.incident.analysis.mapper.WordDocBuildMapper;
import com.trs.police.incident.analysis.service.WordDocBuildService;
import com.trs.police.incident.analysis.util.HierarchicalUtil;
import com.trs.police.incident.analysis.util.PoiTlUtils;
import com.trs.police.incident.analysis.util.TimeUtil;
import io.vavr.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 报告文档生成服务实现
 */

@Slf4j
@Service
public class WordDocBuildServiceImpl implements WordDocBuildService {

    @Resource
    private DeptMapper deptMapper;

    @Resource
    private WordDocBuildMapper wordDocBuildMapper;

    @Resource
    private WordDocQueryConverter wordDocQueryConverter;

    @Resource
    private WordDocIncidentProperties wordDocIncidentProperties;

    @Override
    public String getIncidentSummaryTable(HttpServletResponse response, WordDocBuildRequest request) {
        Map<String, Object> resultMap = new HashMap<>();
        String startMonth = StringUtils.substring(request.getStartTime(),5,7).replaceFirst("^0+(?!$)", "");
        String endMonth = StringUtils.substring(request.getEndTime(),5,7).replaceFirst("^0+(?!$)", "");
        String startDay = StringUtils.substring(request.getStartTime(),8,10).replaceFirst("^0+(?!$)", "");
        String endDay = StringUtils.substring(request.getEndTime(),8,10).replaceFirst("^0+(?!$)", "");
        String startHour = StringUtils.substring(request.getStartTime(),11,13).replaceFirst("^0+(?!$)", "");
        String endHour = StringUtils.substring(request.getEndTime(),11,13).replaceFirst("^0+(?!$)", "");
        resultMap.put("startMonth",startMonth);
        resultMap.put("endMonth",endMonth);
        resultMap.put("startDay",startDay);
        resultMap.put("endDay",endDay);
        resultMap.put("startHour",startHour);
        resultMap.put("endHour",endHour);
        // 获取当前日期时间
        LocalDateTime now = LocalDateTime.now();
        // 格式化日期时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String curMonth  =  StringUtils.substring(now.format(formatter),5,7).replaceFirst("^0+(?!$)", "");
        String curDay  =  StringUtils.substring(now.format(formatter),8,10).replaceFirst("^0+(?!$)", "");
        resultMap.put("curMonth",curMonth);
        resultMap.put("curDay",curDay);

        switch (request.getAreaCode().length()){
            case 2:
                resultMap.put("areaName", DocBuildConstants.DOC_AREA_PROVINCE);
                break;
            case 4:
                resultMap.put("areaName", DocBuildConstants.DOC_AREA_CITY);
                break;
            case 6:
                resultMap.put("areaName", DocBuildConstants.DOC_AREA_COUNTY);
                break;
            default:
                resultMap.put("areaName", DocBuildConstants.DOC_AREA_PLACE);
                break;
        }
        String deptName = deptMapper.getDeptName(request.getDeptCode());
        if(StringUtils.isNotEmpty(deptName)){
            resultMap.put("deptName", deptName);
        }
        List<DeptEntity> deptList = deptMapper.getSubDeptList(request.getAreaCode());
        //警情统计表格构建
        totalIncidentStatisticBuild(deptList,resultMap,request);
        PoiTlUtils.exportBrowser(response,resultMap,null,DocBuildConstants.DOC_TEMPLATE_PATH,DocBuildConstants.OUT_FILE_NAME,null);
        log.info("生成word文档成功");
        return "生成word文档成功";
    }

    private void totalIncidentStatisticBuild(List<DeptEntity> deptList, Map<String, Object> resultMap, WordDocBuildRequest request){

        //获取当日各部门警情
        request.setEndDate(StringUtils.substring(request.getEndTime(),0,10));
        request.setStartDate(StringUtils.substring(request.getStartTime(),0,10));
        WordDocQueryDTO queryDTO = wordDocQueryConverter.toDTO(request);
        List<PoliceSituationDeptCountResponse> curDeptCountList = wordDocBuildMapper.getDeptCount(queryDTO);
        //获取当日各部门刑事警情
        List<String> criminalLbCodeList =  HierarchicalUtil.getEffectiveList(wordDocIncidentProperties.getCriminalIncidentCodes());
        queryDTO.setLbCodes(criminalLbCodeList);
        List<PoliceSituationDeptCountResponse> curCriminalDeptCountList = wordDocBuildMapper.getDeptCount(queryDTO);
        //获取当日各部门治安警情
        List<String> securityLbCodeList =  HierarchicalUtil.getEffectiveList(wordDocIncidentProperties.getSecurityIncidentCodes());
        queryDTO.setLbCodes(securityLbCodeList);
        List<PoliceSituationDeptCountResponse> curSecurityDeptCountList = wordDocBuildMapper.getDeptCount(queryDTO);
        //获取当日各部门交通警情
        List<String> trafficLbCodeList =  HierarchicalUtil.getEffectiveList(wordDocIncidentProperties.getTrafficIncidentCodes());
        queryDTO.setLbCodes(trafficLbCodeList);
        List<PoliceSituationDeptCountResponse> curTrafficDeptCountList = wordDocBuildMapper.getDeptCount(queryDTO);

        //获取昨日各部门警情
        Tuple2<String,String> lastDayTimeRange = TimeUtil.getLastDayTimeRange(queryDTO.getStartTime(),queryDTO.getEndTime());
        queryDTO.setStartTime(lastDayTimeRange._1);
        queryDTO.setEndTime(lastDayTimeRange._2);
        queryDTO.setEndDate(StringUtils.substring(queryDTO.getEndTime(),0,10));
        queryDTO.setStartDate(StringUtils.substring(queryDTO.getStartTime(),0,10));
        queryDTO.setLbCodes(null);
        List<PoliceSituationDeptCountResponse> lastDayDeptCountList = wordDocBuildMapper.getDeptCount(queryDTO);
        //获取昨日各部门刑事警情
        queryDTO.setLbCodes(criminalLbCodeList);
        List<PoliceSituationDeptCountResponse> lastDayCriminalDeptCountList = wordDocBuildMapper.getDeptCount(queryDTO);
        //获取昨日各部门治安警情
        queryDTO.setLbCodes(securityLbCodeList);
        List<PoliceSituationDeptCountResponse> lastDaySecurityDeptCountList = wordDocBuildMapper.getDeptCount(queryDTO);
        //获取昨日各部门交通警情
        queryDTO.setLbCodes(trafficLbCodeList);
        List<PoliceSituationDeptCountResponse> lastDayTrafficDeptCountList = wordDocBuildMapper.getDeptCount(queryDTO);

        int i = 1;
        Integer totalCurCount = 0;
        Integer totalLastDayCount = 0;
        Integer totalCurCriminalCount = 0;
        Integer totalLastDayCriminalCount = 0;
        Integer totalCurSecurityCount = 0;
        Integer totalLastDaySecurityCount = 0;
        Integer totalCurTrafficCount = 0;
        Integer totalLastDayTrafficCount = 0;
        for (DeptEntity dept:deptList){
            //填入部门
            resultMap.put("area"+i, dept.getName());
            Integer curCount = 0;
            Integer lastDayCount = 0;
            Integer curCriminalCount = 0;
            Integer lastDayCriminalCount = 0;
            Integer curSecurityCount = 0;
            Integer lastDaySecurityCount = 0;
            Integer curTrafficCount = 0;
            Integer lastDayTrafficCount = 0;

            //当前部门的今日警情数量
            PoliceSituationDeptCountResponse curDeptCount = curDeptCountList.stream()
                    .filter(r -> r.getBmbh().equals(dept.getCode()))
                    .findFirst().orElse(null);
            if(curDeptCount != null){
                curCount = curDeptCount.getCount();
            }
            resultMap.put("totalNum"+i, curCount);
            //总数统计
            totalCurCount += curCount;

            //当前部门的今日刑事警情数量
            PoliceSituationDeptCountResponse curCriminalDeptCount = curCriminalDeptCountList.stream()
                    .filter(r -> r.getBmbh().equals(dept.getCode()))
                    .findFirst().orElse(null);
            if (curCriminalDeptCount != null){
                curCriminalCount = curCriminalDeptCount.getCount();
            }
            resultMap.put("criminalNum"+i, curCriminalCount);
            //刑事总数统计
            totalCurCriminalCount += curCriminalCount;

            //当前部门的今日治安警情数量
            PoliceSituationDeptCountResponse curSecurityDeptCount = curSecurityDeptCountList.stream()
                    .filter(r -> r.getBmbh().equals(dept.getCode()))
                    .findFirst().orElse(null);
            if (curSecurityDeptCount != null){
                curSecurityCount = curSecurityDeptCount.getCount();
            }
            resultMap.put("securityNum"+i, curSecurityCount);
            //治安总数统计
            totalCurSecurityCount += curSecurityCount;

            //当前部门的今日交通警情数量
            PoliceSituationDeptCountResponse curTrafficDeptCount = curTrafficDeptCountList.stream()
                    .filter(r -> r.getBmbh().equals(dept.getCode()))
                    .findFirst().orElse(null);
            if (curTrafficDeptCount != null){
                curTrafficCount = curTrafficDeptCount.getCount();
            }
            resultMap.put("trafficNum"+i, curTrafficCount);
            //交通总数统计
            totalCurTrafficCount += curTrafficCount;

            //当前部门的昨日警情数量
            PoliceSituationDeptCountResponse lastDayDeptCount = lastDayDeptCountList.stream()
                    .filter(r -> r.getBmbh().equals(dept.getCode()))
                    .findFirst().orElse(null);
            if(lastDayDeptCount != null){
                lastDayCount = lastDayDeptCount.getCount();
            }
            resultMap.put("last_totalNum"+i,lastDayCount);
            //昨日总数统计
            totalLastDayCount += lastDayCount;

            //当前部门的昨日刑事警情数量
            PoliceSituationDeptCountResponse lastDayCriminalDeptCount = lastDayCriminalDeptCountList.stream()
                    .filter(r -> r.getBmbh().equals(dept.getCode()))
                    .findFirst().orElse(null);
            if (lastDayCriminalDeptCount != null){
                lastDayCriminalCount = lastDayCriminalDeptCount.getCount();
            }
            resultMap.put("last_criminalNum"+i, lastDayCriminalCount);
            //昨日刑事总数统计
            totalLastDayCriminalCount += lastDayCriminalCount;

            //当前部门的昨日治安警情数量
            PoliceSituationDeptCountResponse lastDaySecurityDeptCount = lastDaySecurityDeptCountList.stream()
                    .filter(r -> r.getBmbh().equals(dept.getCode()))
                    .findFirst().orElse(null);
            if (lastDaySecurityDeptCount != null){
                lastDaySecurityCount = lastDaySecurityDeptCount.getCount();
            }
            resultMap.put("last_securityNum"+i, lastDaySecurityCount);
            //昨日治安总数统计
            totalLastDaySecurityCount += lastDaySecurityCount;

            //当前部门的昨日交通警情数量
            PoliceSituationDeptCountResponse lastDayTrafficDeptCount = lastDayTrafficDeptCountList.stream()
                    .filter(r -> r.getBmbh().equals(dept.getCode()))
                    .findFirst().orElse(null);
            if (lastDayTrafficDeptCount != null){
                lastDayTrafficCount = lastDayTrafficDeptCount.getCount();
            }
            resultMap.put("last_trafficNum"+i, lastDayTrafficCount);
            //昨日交通总数统计
            totalLastDayTrafficCount += lastDayTrafficCount;

            //当前部门警情数环比
            if(curCount >= lastDayCount){
                int delta = curCount - lastDayCount;
                resultMap.put("totalRatio"+i,getPercentageNum((double) delta,lastDayCount));
            }else{
                int delta = lastDayCount - curCount;
                resultMap.put("totalRatio"+i,"-"+getPercentageNum((double) delta,lastDayCount));
            }

            //当前部门刑事警情数环比
            if(curCriminalCount >= lastDayCriminalCount){
                int delta = curCriminalCount - lastDayCriminalCount;
                resultMap.put("criminalRatio"+i,getPercentageNum((double) delta,lastDayCriminalCount));
            }else{
                int delta = lastDayCriminalCount - curCriminalCount;
                resultMap.put("criminalRatio"+i,"-"+getPercentageNum((double) delta,lastDayCriminalCount));
            }

            //当前部门治安警情数环比
            if(curSecurityCount >= lastDaySecurityCount){
                int delta = curSecurityCount - lastDaySecurityCount;
                resultMap.put("securityRatio"+i,getPercentageNum((double) delta,lastDaySecurityCount));
            }else{
                int delta = lastDaySecurityCount - curSecurityCount;
                resultMap.put("securityRatio"+i,"-"+getPercentageNum((double) delta,lastDaySecurityCount));
            }

            //当前部门交通警情数环比
            if(curTrafficCount >= lastDayTrafficCount){
                int delta = curTrafficCount - lastDayTrafficCount;
                resultMap.put("trafficRatio"+i,getPercentageNum((double) delta,lastDayTrafficCount));
            }else {
                int delta = lastDayTrafficCount - curTrafficCount;
                resultMap.put("trafficRatio"+i,"-"+getPercentageNum((double) delta,lastDayTrafficCount));
            }
            i++;
        }
        //填入总计
        resultMap.put("totalNum0", totalCurCount);
        resultMap.put("last_totalNum0", totalLastDayCount);
        resultMap.put("criminalNum0", totalCurCriminalCount);
        resultMap.put("last_criminalNum0", totalLastDayCriminalCount);
        resultMap.put("securityNum0", totalCurSecurityCount);
        resultMap.put("last_securityNum0", totalLastDaySecurityCount);
        resultMap.put("trafficNum0", totalCurTrafficCount);
        resultMap.put("last_trafficNum0", totalLastDayTrafficCount);
        //总警情环比
        if(totalCurCount >= totalLastDayCount){
            int delta = totalCurCount - totalLastDayCount;
            resultMap.put("totalRatio0",getPercentageNum((double) delta,totalLastDayCount));
            if(totalCurCount.equals(totalLastDayCount)){
                resultMap.put("totalTrend","持平");
            }else {
                resultMap.put("totalTrend","上升");
                resultMap.put("totalRatio",getPercentageNum((double) delta,totalLastDayCount));
            }
        }else{
            int delta = totalLastDayCount - totalCurCount;
            resultMap.put("totalRatio0","-"+getPercentageNum((double) delta,totalLastDayCount));
            resultMap.put("totalTrend","下降");
            resultMap.put("totalRatio",getPercentageNum((double) delta,totalLastDayCount));
        }

        //总刑事警情环比
        if(totalCurCriminalCount >= totalLastDayCriminalCount) {
            int delta = totalCurCriminalCount - totalLastDayCriminalCount;
            resultMap.put("criminalRatio0",getPercentageNum((double) delta,totalLastDayCriminalCount));
            if(totalCurCriminalCount.equals(totalLastDayCriminalCount)) {
                resultMap.put("criminalTrend", "持平");
            }else{
                resultMap.put("criminalTrend", "上升");
                resultMap.put("criminalRatio", getPercentageNum((double) delta, totalLastDayCriminalCount));
            }
        }else{
            int delta = totalLastDayCriminalCount - totalCurCriminalCount;
            resultMap.put("criminalRatio0","-"+getPercentageNum((double) delta,totalLastDayCriminalCount));
            resultMap.put("criminalTrend", "下降");
            resultMap.put("criminalRatio", getPercentageNum((double) delta, totalLastDayCriminalCount));
        }

        //总治安警情环比
        if(totalCurSecurityCount >= totalLastDaySecurityCount) {
            int delta = totalCurSecurityCount - totalLastDaySecurityCount;
            resultMap.put("securityRatio0",getPercentageNum((double) delta,totalLastDaySecurityCount));
            if(totalCurSecurityCount.equals(totalLastDaySecurityCount)) {
                resultMap.put("securityTrend", "持平");
            }else{
                resultMap.put("securityTrend", "上升");
                resultMap.put("securityRatio", getPercentageNum((double) delta, totalLastDaySecurityCount));
            }
        }else{
            int delta = totalLastDaySecurityCount - totalCurSecurityCount;
            resultMap.put("securityRatio0","-"+getPercentageNum((double) delta,totalLastDaySecurityCount));
            resultMap.put("securityTrend", "下降");
            resultMap.put("securityRatio", getPercentageNum((double) delta, totalLastDaySecurityCount));
        }

        //总交通警情环比
        if(totalCurTrafficCount >= totalLastDayTrafficCount) {
            int delta = totalCurTrafficCount - totalLastDayTrafficCount;
            resultMap.put("trafficRatio0", getPercentageNum((double) delta, totalLastDayTrafficCount));
            if(totalCurTrafficCount.equals(totalLastDayTrafficCount)) {
                resultMap.put("trafficTrend", "持平");
            }else{
                resultMap.put("trafficTrend", "上升");
                resultMap.put("trafficRatio", getPercentageNum((double) delta, totalLastDayTrafficCount));
            }
        }else{
            int delta = totalLastDayTrafficCount - totalCurTrafficCount;
            resultMap.put("trafficRatio0","-"+getPercentageNum((double) delta,totalLastDayTrafficCount));
            resultMap.put("trafficTrend", "下降");
            resultMap.put("trafficRatio", getPercentageNum((double) delta, totalLastDayTrafficCount));
        }
    }


    /**
     * 计算百分比
     *
     * @param num 数据
     * @param total 总数
     * @return 百分比字符串
     */
    public String getPercentageNum(Double num, Integer total) {
        if(total == 0 && num != 0){
            return "净增"+num.intValue()+"件";
        } else if(total == 0 && num == 0){
            return "0.0%";
        } else {
            double percentage = num / total * 100;

            return String.format("%.1f", percentage) + "%";
        }
    }
}
