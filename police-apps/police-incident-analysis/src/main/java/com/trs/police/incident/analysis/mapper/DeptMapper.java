package com.trs.police.incident.analysis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.incident.analysis.domain.entity.DeptEntity;
import com.trs.police.incident.analysis.domain.response.DeptTreeResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * Description: 组织机构统计
 *
 * <AUTHOR>
 * @create 2024-11-11 18:59
 */
@Mapper
public interface DeptMapper extends BaseMapper<DeptEntity> {

    /**
     * 根据区域编码获取下属部门列表
     *
     * @param areaCode 区域编码
     * @return 部门列表
     */
    List<DeptEntity> getSubDeptList(@Param("areaCode") String areaCode);

    /**
     * 根据区域便能吗获取派出所列表
     *
     * @param areaCode 区域编码
     * @return 派出所列表
     */
    List<DeptEntity> getPoliceStationList(@Param("areaCode") String areaCode);

    /**
     * 根据部门编码获取部门名称
     *
     * @param deptCode 部门编码
     * @return 部门名称
     */
    String getDeptName(@Param("deptCode") String deptCode);

    /**
     * 获取部门列表
     *
     * @return 部门列表
     */
    @Select("select *,case when code in (select parent_code from t_dept group by parent_code) then 1 else 0 end as has_child from t_dept")
    List<DeptTreeResponse> getDeptList();

    /**
     * 根据部门编号获取部门列表
     *
     * @param deptCode 部门编号
     * @return 部门列表
     */
    @Select("select *,case when code in (select parent_code from t_dept group by parent_code) then 1 else 0 end as has_child from t_dept where code like concat(#{deptCode},'%')")
    List<DeptTreeResponse> getDeptListByDeptCode(@Param("deptCode") String deptCode);
}
