package com.trs.police.incident.analysis.domain.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 警情类别树返回
 *
 * <AUTHOR>
 * @date 2024年11月18日 10:25
 */

@Data
@ApiModel("警情类别树返回")
public class PoliceSituationTypeTreeResponse implements Serializable {

    private static final long serialVersionUID = -119201014345120065L;
    /**
     * 警情类别代码
     */
    @ApiModelProperty("警情类别代码")
    private String code;
    /**
     * 警情类别名称
     */
    @ApiModelProperty("警情类别名称")
    private String name;
    /**
     * 警情类别级别
     */
    @ApiModelProperty("警情类别级别")
    private String level;
    /**
     * 警情类别父类代码
     */
    @ApiModelProperty("警情类别父类代码")
    private String parentCode;

    /**
     * 是否含有子类别
     */
    @ApiModelProperty("是否含有子类别")
    private Integer hasChild;
}
