package com.trs.police.incident.analysis.util;

import com.trs.police.incident.analysis.constants.PoliceSituationConstants;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * 处理有层级关系的一类数据的工具类
 *
 * <AUTHOR> && zhu.kaize
 */
public class HierarchicalUtil {

    /**
     * 获取有效检索的层级关系代码code值：02130000的有效code为0213，05000000的有效code为05
     *
     * @param sourceCode sourceCode
     * @return 有效code
     */
    public static String getEffectiveCode(String sourceCode) {
        if (StringUtils.isBlank(sourceCode)) {
            return sourceCode;
        }
        int pointer = sourceCode.length();

        while (pointer >= 0 && sourceCode.substring(pointer - 2, pointer).equalsIgnoreCase("00")) {
            pointer -= 2;
        }
        return sourceCode.substring(0, pointer);
    }

    /**
     * 获取有效检索的层级关系代码code值：02130000的有效code为0213，05000000的有效code为05
     *
     * @param list list
     * @return 有效值
     */
    public static List<String> getEffectiveListAndFilterInvalid(List<String> list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        } else {
            return list.stream()
                    .filter(HierarchicalUtil::isValid)
                    .map(HierarchicalUtil::getEffectiveCode)
                    .collect(Collectors.toList());
        }
    }

    /**
     * 获取有效值
     *
     * @param list list
     * @return 有效值
     */
    public static List<String> getEffectiveList(List<String> list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        } else {
            return list.stream()
                    .map(HierarchicalUtil::getEffectiveCode)
                    .collect(Collectors.toList());
        }
    }

    private static final Pattern NUMBER_CHECK_PATTERN = Pattern.compile("^\\d+$");

    /**
     * 是否是有效的类别编码
     * 非空 且 纯数字 且 长度为8或者12
     *
     * @param sourceCode 源
     * @return 是否合规
     */
    public static boolean isValid(String sourceCode) {
        if (StringUtils.isEmpty(sourceCode)) {
            return false;
        }
        return !StringUtils.isEmpty(sourceCode)
                && NUMBER_CHECK_PATTERN.matcher(sourceCode).matches()
                && (sourceCode.length() == PoliceSituationConstants.PS_TYPE_LENGTH
                || sourceCode.length() == PoliceSituationConstants.PS_DEPT_CODE_LENGTH);
    }
}