package com.trs.police.incident.analysis.controller;

import com.alibaba.fastjson.JSONObject;
import com.trs.police.incident.analysis.service.EnvironmentParameterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 环境参数控制器
 */
@Api(value = "环境参数", tags = "环境参数")
@RestController
@RequestMapping(value = "/environment-parameter", produces = "application/json")
public class EnvironmentParameterController {

    private final EnvironmentParameterService environmentParameterService;

    public EnvironmentParameterController(EnvironmentParameterService environmentParameterService) {
        this.environmentParameterService = environmentParameterService;
    }

    /**
     * 获取环境参数
     *
     * @return {@link JSONObject}
     */
    @ApiOperation(value = "获取环境参数")
    @GetMapping()
    JSONObject getEnvironmentParameters() {
        return environmentParameterService.getEnvironmentParameter();
    }

}
