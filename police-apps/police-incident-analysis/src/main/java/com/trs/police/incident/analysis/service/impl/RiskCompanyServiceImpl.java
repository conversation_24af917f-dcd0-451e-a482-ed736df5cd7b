package com.trs.police.incident.analysis.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.excpetion.SystemException;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.incident.analysis.constants.ReportConstants;
import com.trs.police.incident.analysis.converter.CompanyLibraryResponseConverter;
import com.trs.police.incident.analysis.converter.RiskCompanyQueryConverter;
import com.trs.police.incident.analysis.domain.dto.RiskCompanyListQueryDTO;
import com.trs.police.incident.analysis.domain.dto.RiskCompanyQueryDTO;
import com.trs.police.incident.analysis.domain.entity.CompanyEntity;
import com.trs.police.incident.analysis.domain.entity.CompanyExcludedEntity;
import com.trs.police.incident.analysis.domain.request.*;
import com.trs.police.incident.analysis.domain.response.*;
import com.trs.police.incident.analysis.mapper.CompanyExcludedMapper;
import com.trs.police.incident.analysis.mapper.CompanyMapper;
import com.trs.police.incident.analysis.mapper.DeptMapper;
import com.trs.police.incident.analysis.mapper.RiskCompanyMapper;
import com.trs.police.incident.analysis.service.CodeSnippetService;
import com.trs.police.incident.analysis.service.RiskCompanyService;
import com.trs.police.incident.analysis.util.PageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 风险公司分析服务实现
 */

@Slf4j
@Service
public class RiskCompanyServiceImpl implements RiskCompanyService {

    //风险公司统计相关mapper
    private final RiskCompanyMapper riskCompanyMapper;

    //公司库mapper
    private final CompanyMapper companyMapper;

    //公司库排除词mapper
    private final CompanyExcludedMapper companyExcludedMapper;

    private final DeptMapper deptMapper;

    private final CodeSnippetService codeSnippetService;

    private final RiskCompanyQueryConverter riskCompanyQueryConverter;

    private final CompanyLibraryResponseConverter companyLibraryResponseConverter;

    public RiskCompanyServiceImpl(RiskCompanyMapper riskCompanyMapper,
                                  CompanyMapper companyMapper,
                                  CompanyExcludedMapper companyExcludedMapper,
                                  DeptMapper deptMapper,
                                  CodeSnippetService codeSnippetService,
                                  RiskCompanyQueryConverter riskCompanyQueryConverter,
                                  CompanyLibraryResponseConverter companyLibraryResponseConverter) {
        this.riskCompanyMapper = riskCompanyMapper;
        this.companyMapper = companyMapper;
        this.companyExcludedMapper = companyExcludedMapper;
        this.deptMapper = deptMapper;
        this.codeSnippetService = codeSnippetService;
        this.riskCompanyQueryConverter = riskCompanyQueryConverter;
        this.companyLibraryResponseConverter = companyLibraryResponseConverter;
    }

    @Override
    public RiskCompanyGetIncidentCountResponse getIncidentCount(RiskCompanyGetIncidentCountRequest request) {
        RiskCompanyQueryDTO queryDTO = riskCompanyQueryConverter.toDTO(request);

        //涉及公司警情数
        final Integer incidentCount = riskCompanyMapper.getRiskCompanyIncidentCount(queryDTO, "incident");
        //涉及公司数
        final Integer companyCount = riskCompanyMapper.getRiskCompanyIncidentCount(queryDTO, "company");
        //风险公司数
        final Integer riskCompanyCount = riskCompanyMapper.getRiskCompanyIncidentCount(queryDTO, "risk");

        return new RiskCompanyGetIncidentCountResponse(incidentCount, companyCount, riskCompanyCount);
    }

    @Override
    public PageResult<RiskCompanyGetCompanyListResponse> getRiskCompanyList(RiskCompanyGetRiskCompanyListRequest request) {
        RiskCompanyListQueryDTO queryDTO = riskCompanyQueryConverter.toDTO(request);

        Page<RiskCompanyGetCompanyListResponse> companyList = riskCompanyMapper.getCompanyList(
                request.getPageParams().toPage(),
                queryDTO);
        if(!CollectionUtils.isEmpty(companyList.getRecords())){
            for(RiskCompanyGetCompanyListResponse companyResponse : companyList.getRecords()){
                //查询公司名称是否在公司库中，不在则为新公司
                List<String> companyName = companyMapper.getStandardName(companyResponse.getCompanyName());
                companyResponse.setIsNew(CollectionUtils.isEmpty(companyName));
            }
        }
        return PageUtil.toPageResult(companyList);
    }

    @Override
    public RiskCompanyGetCompanyDetailResponse getRiskCompanyDetail(RiskCompanyGetRiskCompanyDetailRequest request) {
        RiskCompanyQueryDTO queryDTO = riskCompanyQueryConverter.toDTO(request);

        return codeSnippetService.getMultiPeriodData(
                queryDTO,
                (dto) -> riskCompanyMapper.getRiskCompanyIncidentCount(dto, "specify"),
                RiskCompanyGetCompanyDetailResponse.class
        );
    }

    @Override
    public PageResult<GetCompanyLibraryListResponse> getCompanyLibraryList(GetCompanyLibraryListRequest request) {
//        if (!request.getSearchColumn().equals("companyName") && !request.getSearchColumn().equals("companyAlias")) {
//            throw new ParamValidationException("检索字段非法");
//        }
        Page<CompanyEntity> selectedPage = companyMapper.selectPage(
                request.getPageParams().toPage(),
                Wrappers.<CompanyEntity>query()
                        .like(
                                StringUtils.isNotBlank(request.getSearchColumn()),
                                request.getSearchColumn(),
                                request.getSearchValue()));
        return PageUtil.toPageResult(selectedPage, companyLibraryResponseConverter::toResponse);
    }

    @Override
    public void addCompanyLibrary(AddCompanyLibraryRequest request) {
        final String companyName = request.getCompanyName();
        final String companyAlias = request.getCompanyAlias();
        final String[] companyAliasesArray = StringUtils.isBlank(companyAlias) ? new String[0] : companyAlias.split(",");

        //新增模式下校验公司名称是否重复，更新模式不校验
        if (Objects.isNull(request.getId()) && companyMapper.exists(
                Wrappers.<CompanyEntity>lambdaQuery()
                        .eq(CompanyEntity::getCompanyName, companyName))) {
            throw new TRSException("公司名称已经存在");
        }
        LambdaQueryWrapper<CompanyEntity> checkQueryWrapper = Wrappers.lambdaQuery();
        checkQueryWrapper.nested(companyAliasesArray.length > 0, c -> {
            for (int i = 0; i < companyAliasesArray.length; i++) {
                c.eq(CompanyEntity::getCompanyAliases, companyAliasesArray[i])
                        .or(i != companyAliasesArray.length - 1);
            }
        }).ne(Objects.nonNull(request.getId()), CompanyEntity::getId, request.getId());

        if (companyAliasesArray.length > 0 && companyMapper.exists(checkQueryWrapper)) {
            throw new TRSException("公司别名已经存在");
        }

        CompanyEntity companyEntity = new CompanyEntity();
        companyEntity.setCompanyName(companyName);
        companyEntity.setCompanyAliases(companyAlias);

        if (Objects.nonNull(request.getId())) {
            companyEntity.setId(request.getId());
            companyEntity.setUpdateTime(LocalDateTime.now());
            companyMapper.updateById(companyEntity);
        } else {
            companyEntity.setCreateTime(LocalDateTime.now());
            companyEntity.setUpdateTime(LocalDateTime.now());
            companyMapper.insert(companyEntity);
        }
    }

    @Override
    public void deleteCompanyLibrary(Long id) {
        companyMapper.deleteById(id);
    }

    @Override
    public PageResult<GetCompanyLibraryExcludedListResponse> getCompanyLibraryExcludedList(GetCompanyLibraryExcludedListRequest request) {
        Page<CompanyExcludedEntity> selectedPage = companyExcludedMapper.selectPage(
                request.getPageParams().toPage(),
                Wrappers.query()
        );
        return PageUtil.toPageResult(selectedPage, companyLibraryResponseConverter::toResponse);
    }

    @Override
    public void addCompanyLibraryExcluded(AddCompanyLibraryExcludedRequest request) {
        Arrays.stream(request.getExcludedName().split(","))
                .filter(s -> !s.isEmpty())
                .filter(s -> !companyExcludedMapper.exists(Wrappers.<CompanyExcludedEntity>lambdaQuery().eq(CompanyExcludedEntity::getExcludedName, s)))
                .distinct()
                .map(s -> {
                    CompanyExcludedEntity entity = new CompanyExcludedEntity();
                    entity.setExcludedName(s);
                    entity.setCreateTime(LocalDateTime.now());
                    return entity;
                })
                .forEach(companyExcludedMapper::insert);
    }

    @Override
    public void deleteCompanyLibraryExcluded(DelCompanyLibraryExcludedRequest request) {
        companyExcludedMapper.deleteById(request.getId());
    }

    @Override
    public CompanyReportResponse getCompanyReport(GetCompanyReportRequest request) {
        CompanyReportResponse response = new CompanyReportResponse();
        response.setTitle(String.format(ReportConstants.REPORT_CREATE_TITLE,
                LocalDate.now().format(DateTimeFormatter.ofPattern("MMdd"))));
        String deptName = Optional.ofNullable(deptMapper.selectById(request.getDeptCode()))
                .orElseThrow(() -> new SystemException("组织机构代码非法"))
                .getName();
        response.setContentTitle(deptName);
        response.setSubContentTitle(ReportConstants.REPORT_SUB_TITLE);

        //查询排名前10公司
        RiskCompanyListQueryDTO queryDTO = new RiskCompanyListQueryDTO(request.getAreaCode(),
                request.getStartDate(),
                request.getEndDate(),
                true);
        List<RiskCompanyGetCompanyListResponse> companyList = riskCompanyMapper.getCompanyList(new Page<>(1, 10), queryDTO).getRecords();

        StringBuilder gsInfo = new StringBuilder();
        for (int i = 0; i < companyList.size(); i++) {
            RiskCompanyGetCompanyListResponse companyResponse = companyList.get(i);
            gsInfo.append(companyResponse.getCompanyName()).append("-").append(companyResponse.getIncidentCount()).append("件");
            if (i == companyList.size() - 1) {
                gsInfo.append("。");
            }else{
                gsInfo.append(",");
            }
        }
        String startDate = LocalDate.parse(request.getStartDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")).format(DateTimeFormatter.ofPattern("yyyy年M月d日"));
        String endDate = LocalDate.parse(request.getEndDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")).format(DateTimeFormatter.ofPattern("yyyy年M月d日"));

        RiskCompanyGetIncidentCountResponse incidentCount = this.getIncidentCount(new RiskCompanyGetIncidentCountRequest(request.getDeptCode(), request.getAreaCode(), request.getStartDate(), request.getEndDate()));

        String content = String.format(ReportConstants.REPORT_CONTENT_TEMPLATE,
                startDate,
                endDate,
                deptName,
                incidentCount.getIncidentCount(),
                incidentCount.getCompanyCount(),
                incidentCount.getRiskCompanyCount(),
                gsInfo);
        response.setContent(content);
        return response;
    }
}
