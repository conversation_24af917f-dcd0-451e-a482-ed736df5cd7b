package com.trs.police.incident.analysis.domain.response;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * 涉及警员数量返回
 *
 * <AUTHOR>
 * @date 2024年12月06日 10:25
 */

@Data
@ApiModel("涉及警员数量返回")
public class PoliceManCountResponse implements Serializable {
    private static final long serialVersionUID = -7967595253876994196L;
    /**
     * 警情数量
     */
    @ApiModelProperty(value = "警情数量")
    private Integer incidentCount;

    /**
     * 涉及警员数量
     */
    @ApiModelProperty(value = "涉及警员数量")
    private Integer policeManCount;
}
