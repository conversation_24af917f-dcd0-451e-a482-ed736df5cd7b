package com.trs.police.incident.analysis.domain.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 重复报警获取满足条件的人员列表返回
 */

@Data
@ApiModel("重复报警获取人员列表返回")
public class DuplicateIncidentGetPersonListResponse {

    /**
     * 电话号码
     */
    @ApiModelProperty("电话号码")
    private String phoneNumber;

    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    private String name;

    /**
     * 标签
     */
    @ApiModelProperty("标签")
    private String tag;

    /**
     * 警情数
     */
    @ApiModelProperty("警情数")
    private Integer countOfIncident;

    /**
     * 12345数量
     */
    @ApiModelProperty("12345数量")
    private Integer countOf12345;

}
