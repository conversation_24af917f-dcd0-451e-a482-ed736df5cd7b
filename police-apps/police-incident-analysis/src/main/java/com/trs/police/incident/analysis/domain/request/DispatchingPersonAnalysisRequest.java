package com.trs.police.incident.analysis.domain.request;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 按出警人员分析请求参数
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("按出警人员分析请求参数")
public class DispatchingPersonAnalysisRequest extends PopDetailTopRequest{
    @ApiModelProperty("前多少条-开始")
    private Integer start;

    @ApiModelProperty("前多少条-结束")
    private Integer end;
}
