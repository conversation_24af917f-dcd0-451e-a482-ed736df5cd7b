package com.trs.police.incident.analysis.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 按照下属单位分组统计通用查询返回DTO
 */

@Data
public class SubDeptCountCommonDTO {

    /**
     * 单位代码
     */
    @ApiModelProperty("单位代码")
    private String deptCode;

    /**
     * 单位名称
     */
    @ApiModelProperty("单位名称")
    private String deptName;

    /**
     * 警情数量
     */
    @ApiModelProperty("警情数量")
    private Integer count;

}
