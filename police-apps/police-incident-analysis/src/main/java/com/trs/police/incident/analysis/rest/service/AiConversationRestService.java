package com.trs.police.incident.analysis.rest.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.police.incident.analysis.rest.domain.AiConversationAskRequest;
import retrofit2.Call;
import retrofit2.http.*;

/**
 * AI对话rest服务
 */
public interface AiConversationRestService {

    /**
     * 发送提问
     *
     * @param userName    userName
     * @param accessToken accessToken
     * @param appId       appId
     * @param askRequest  请求参数
     * @return {@link JsonNode}
     */
    @Headers("Content-Type: application/json;charset=UTF-8")
    @POST("/llmops/chat/apps/{appId}/conversations/info")
    Call<JsonNode> ask(@Header("Username") String userName,
                       @Header("Access-Token") String accessToken,
                       @Path("appId") String appId,
                       @Body AiConversationAskRequest askRequest);

    /**
     * 获取回答
     * 1. 获取回答不是eventstream，是普通json请求
     * 2. 请求参数body必须传递一个空json，否则报错（牛逼）
     *
     * @param userName       userName
     * @param accessToken    accessToken
     * @param appId          appId
     * @param conversationId conversationId
     * @param qaId           qaId
     * @param chatLogId      chatLogId
     * @param body           body
     * @return {@link JsonNode}
     */
    @Headers("Content-Type: application/json; charset=utf-8")
    @POST("/llmops/chat/apps/{appId}/conversations/{conversationId}/qas/{qaId}")
    Call<JsonNode> answer(@Header("Username") String userName,
                          @Header("Access-Token") String accessToken,
                          @Path("appId") String appId,
                          @Path("conversationId") String conversationId,
                          @Path("qaId") String qaId,
                          @Query("chatLogId") String chatLogId,
                          @Body JsonNode body);


}
