package com.trs.police.incident.analysis.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Description: 词云排除词实体
 *
 * @author: yang.pengfei
 * @create: 2024-11-21 10:02
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "t_cy_excluded", autoResultMap = true)
public class WordCloudExcludedEntity {

    @TableField("id")
    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("word")
    private String word;

    @TableField("create_time")
    private LocalDateTime createTime;
}
