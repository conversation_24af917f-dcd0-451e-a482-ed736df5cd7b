package com.trs.police.incident.analysis.service;

import com.trs.police.incident.analysis.domain.request.GetIncidentTypeStatisticsRequest;
import com.trs.police.incident.analysis.domain.request.GetSubDeptProportionRequest;
import com.trs.police.incident.analysis.domain.response.GetIncidentTypeStatisticsResponse;
import com.trs.police.incident.analysis.domain.response.GetSubdeptProportionResponse;


/**
 * 虚拟警情统计服务
 */
public interface VirtualIncidentStatisticsService {

    /**
     * 根据警情类别进行统计
     *
     * @param request 请求参数
     * @return {@link GetIncidentTypeStatisticsResponse}
     */
    GetIncidentTypeStatisticsResponse getIncidentTypeStatistics(GetIncidentTypeStatisticsRequest request);

    /**
     * 根据警情类别按下属单位分类统计
     *
     * @param request 请求参数
     * @return {@link GetSubdeptProportionResponse}
     */
    GetSubdeptProportionResponse getSubDeptProportion(GetSubDeptProportionRequest request);

}
