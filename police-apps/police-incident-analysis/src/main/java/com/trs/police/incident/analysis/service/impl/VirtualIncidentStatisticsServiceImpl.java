package com.trs.police.incident.analysis.service.impl;

import com.trs.police.incident.analysis.conf.properties.VirtualIncidentProperties;
import com.trs.police.incident.analysis.converter.VirtualIncidentStatisticsQueryConverter;
import com.trs.police.incident.analysis.domain.request.GetIncidentTypeStatisticsRequest;
import com.trs.police.incident.analysis.domain.request.GetSubDeptProportionRequest;
import com.trs.police.incident.analysis.domain.response.GetIncidentTypeStatisticsResponse;
import com.trs.police.incident.analysis.domain.response.GetSubdeptProportionResponse;
import com.trs.police.incident.analysis.mapper.VirtualIncidentStatisticsMapper;
import com.trs.police.incident.analysis.service.CodeSnippetService;
import com.trs.police.incident.analysis.service.VirtualIncidentStatisticsService;
import com.trs.police.incident.analysis.util.JqCodeUtil;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 虚拟警情统计服务实现
 */

@Service
public class VirtualIncidentStatisticsServiceImpl implements VirtualIncidentStatisticsService {

    /**
     * 查询mapper
     */
    private final VirtualIncidentStatisticsMapper mapper;

    /**
     * 虚拟警情配置
     */
    private final VirtualIncidentProperties properties;

    private final CodeSnippetService codeSnippetService;

    private final VirtualIncidentStatisticsQueryConverter converter;

    public VirtualIncidentStatisticsServiceImpl(
            VirtualIncidentStatisticsMapper mapper,
            VirtualIncidentProperties properties,
            CodeSnippetService codeSnippetService,
            VirtualIncidentStatisticsQueryConverter converter) {
        this.mapper = mapper;
        this.properties = properties;
        this.codeSnippetService = codeSnippetService;
        this.converter = converter;
    }

    @Override
    public GetIncidentTypeStatisticsResponse getIncidentTypeStatistics(GetIncidentTypeStatisticsRequest request) {
        Map<Integer, List<String>> classifiedJqCodes = JqCodeUtil.classifyJqCodes(request.getIncidentType(), properties);

        GetIncidentTypeStatisticsResponse response = codeSnippetService.getMultiPeriodData(
                converter.toDTO(request, classifiedJqCodes),
                mapper::getVirtualIncidentTypeStatistics,
                GetIncidentTypeStatisticsResponse.class
        );
        response.setLbCodes(JqCodeUtil.translateVirtualIncident(request.getIncidentType(), properties.getVirtualIncidents()));
        return response;
    }

    @Override
    public GetSubdeptProportionResponse getSubDeptProportion(GetSubDeptProportionRequest request) {
        Map<Integer, List<String>> classifiedLbCodes = JqCodeUtil.classifyJqCodes(request.getIncidentType(), properties);

        return new GetSubdeptProportionResponse(
                mapper.getSubDeptProportion(converter.toDTO(request, classifiedLbCodes)),
                JqCodeUtil.translateVirtualIncident(request.getIncidentType(), properties.getVirtualIncidents()));
    }
}
