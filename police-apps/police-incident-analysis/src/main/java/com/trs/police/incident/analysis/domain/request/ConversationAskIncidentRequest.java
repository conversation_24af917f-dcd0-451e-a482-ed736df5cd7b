package com.trs.police.incident.analysis.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 警情数据提问请求参数
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("警情数据提问请求参数")
public class ConversationAskIncidentRequest extends ConversationAskRequest {

    @ApiModelProperty("数据查询参数")
    private PoliceSituationListRequest dataParams;

}
