package com.trs.police.incident.analysis.domain.dto;

import lombok.Data;

/**
 * 各年龄段平均出警数SQL返回DTO
 */

@Data
public class DispatchingAnalysisGetAgeGroupDTO {

    /**
     * 年龄在21到25之间的人数
     */
    private Integer age21To25;

    /**
     * 年龄在26到30之间的人数
     */
    private Integer age26To30;

    /**
     * 年龄在31到35之间的人数
     */
    private Integer age31To35;

    /**
     * 年龄在36到40之间的人数
     */
    private Integer age36To40;

    /**
     * 年龄在41到45之间的人数
     */
    private Integer age41To45;

    /**
     * 年龄在46到50之间的人数
     */
    private Integer age46To50;

    /**
     * 年龄在51到55之间的人数
     */
    private Integer age51To55;

    /**
     * 年龄在56到60之间的人数
     */
    private Integer age56To60;

    /**
     * 年龄在61到65之间的人数
     */
    private Integer age61To65;

}
