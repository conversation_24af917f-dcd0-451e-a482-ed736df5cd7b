package com.trs.police.incident.analysis.mapper;

import com.trs.police.incident.analysis.domain.dto.DispatchingPersonQueryDTO;
import com.trs.police.incident.analysis.domain.response.DispatchingPersonCountResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 按出警人员分析 Mapper
 */
@Mapper
public interface DispatchingPersonAnalysisMapper {
    /**
     * 获取涉及出警人员数量
     *
     * @param dispatchingPersonQueryDTO 查询条件
     * @return 出警人员数量
     */
    Integer getPoliceManCount(@Param("dto")DispatchingPersonQueryDTO dispatchingPersonQueryDTO);

    /**
     * 获取出警人员分析数据
     *
     * @param dispatchingPersonQueryDTO 查询条件
     * @return 出警人员分析数据
     */
    List<DispatchingPersonCountResponse> getDispatchingPersonAnalysis(@Param("dto") DispatchingPersonQueryDTO dispatchingPersonQueryDTO);
}
