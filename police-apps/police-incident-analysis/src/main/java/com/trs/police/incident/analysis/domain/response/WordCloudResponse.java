package com.trs.police.incident.analysis.domain.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 词云返回
 */
@Data
@ApiModel("词云统计返回")
public class WordCloudResponse  implements Serializable {
    private static final long serialVersionUID = 3148966969992089874L;

    /**
     * 词名
     */
    @ApiModelProperty("词名")
    private String wordname;
    /**
     * 词频
     */
    @ApiModelProperty("词频")
    private Integer count;
}
