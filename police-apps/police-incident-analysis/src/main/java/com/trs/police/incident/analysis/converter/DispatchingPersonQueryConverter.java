package com.trs.police.incident.analysis.converter;

import com.trs.police.incident.analysis.domain.dto.DispatchingPersonQueryDTO;
import com.trs.police.incident.analysis.domain.request.DispatchingPersonAnalysisRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

/**
 * Description: 按出警人员分析查询转换类
 *
 * @author: yang.pengfei
 * @create: 2024-11-21 10:02
 */
@Mapper(componentModel = "spring")
public interface DispatchingPersonQueryConverter {
    /**
     * 转换查询对象
     *
     * @param request 请求对象
     * @return 查询对象
     */

    @Mappings({
            @Mapping(target = "effectiveJq", expression = "java(request.getSfyxjq() == null)"),
    })
    DispatchingPersonQueryDTO toDto(DispatchingPersonAnalysisRequest request);
}
