package com.trs.police.incident.analysis.converter;

import com.trs.police.incident.analysis.domain.dto.AlarmPeopleAnalysisQueryDTO;
import com.trs.police.incident.analysis.domain.dto.IncidentAnalysisQueryDTO;
import com.trs.police.incident.analysis.domain.request.AlarmPeopleAnalysisRequest;
import com.trs.police.incident.analysis.domain.request.IncidentAnalysisRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

/**
 * Description: 警情分析查询转换类
 *
 * @author: yang.pengfei
 * @create: 2024-11-21 10:02
 */
@Mapper(componentModel = "spring")
public interface IncidentAnalysisQueryConverter {
    /**
     * 请求参数转换查询dto
     *
     * @param request 请求参数
     * @return 查询dto
     */
    @Mappings({
            @Mapping(target = "lbCodes", expression = "java(com.trs.police.incident.analysis.util.HierarchicalUtil.getEffectiveListAndFilterInvalid(request.getLbCodes()))"),
            @Mapping(target = "effectiveJq", expression = "java(request.getSfyxjq() == null)"),
    })
    IncidentAnalysisQueryDTO toDto(IncidentAnalysisRequest request);

    /**
     * 报警人员分析请求参数转换查询dto
     *
     * @param request 请求参数
     * @return 查询dto
     */
    @Mappings({
            @Mapping(target = "lbCodes", expression = "java(com.trs.police.incident.analysis.util.HierarchicalUtil.getEffectiveListAndFilterInvalid(request.getLbCodes()))"),
            @Mapping(target = "effectiveJq", expression = "java(request.getSfyxjq() == null)"),
    })
    AlarmPeopleAnalysisQueryDTO toDto(AlarmPeopleAnalysisRequest request);
}
