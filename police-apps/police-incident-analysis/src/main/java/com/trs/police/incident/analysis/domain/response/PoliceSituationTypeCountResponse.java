package com.trs.police.incident.analysis.domain.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Description:
 *
 * @author: yang.pengfei
 * @create: 2024-11-08 14:50
 */
@Data
@ApiModel("警情类别分布返回")
public class PoliceSituationTypeCountResponse {

    /**
     *  类别代码
     */
    @ApiModelProperty(value = "类别代码")
    private String jqlbdm;

    /**
     *  类别名称
     */
    @ApiModelProperty(value = "类别名称")
    private String jqlbmc;

    /**
     *  警情总数
     */
    @ApiModelProperty(value = "警情总数")
    private Integer count;

    /**
     * 是否有子节点 0:无 1：有
     */
    @ApiModelProperty(value = "是否有子节点 0:无 1：有")
    private Integer hasChild;
}
