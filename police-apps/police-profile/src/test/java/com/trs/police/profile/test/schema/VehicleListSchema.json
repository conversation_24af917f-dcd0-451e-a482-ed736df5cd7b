{"name": "车辆信息", "type": "LIST_SCHEMA", "table": "t_profile_vehicle", "selectable": false, "searchFields": [], "fields": [{"db": {"table": "t_profile_vehicle", "column": "type", "mapping": "dict_code_to_name", "jdbcType": "json_object_array"}, "name": "type", "dict": {"type": "profile_vehicle_type"}, "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "类型"}, "properties": {"copyable": false, "required": false, "sortable": false, "editable": true}}}, {"db": {"table": "t_profile_vehicle", "column": "car_number", "jdbcType": "string"}, "name": "carNumber", "listSchema": {"schema": {"title": "车牌号", "type": "select"}, "style": {"align": "center"}, "properties": {"copyable": true, "sortable": false, "required": false, "editable": true}}}, {"db": {"table": "t_profile_vehicle", "column": "owner", "jdbcType": "string"}, "name": "owner", "listSchema": {"schema": {"title": "车辆所有人", "type": "select"}, "style": {"align": "center"}, "properties": {"copyable": false, "sortable": false, "required": false, "editable": true}}}, {"db": {"table": "t_profile_vehicle", "column": "source", "mapping": "vehicle_source_to_name", "jdbcType": "json"}, "name": "source", "listSchema": {"schema": {"title": "来源", "type": "select"}, "style": {"align": "center"}, "properties": {"copyable": false, "sortable": false, "required": false, "editable": false, "defaultValue": "手动录入"}}}]}