package com.trs.police.profile.test;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.profile.ProfileApp;
import com.trs.police.profile.domain.entity.JbxxXp;
import com.trs.police.profile.mapper.yq.JbxxXpMapper;
import com.trs.police.profile.service.HandleVirtualInfoService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@SpringBootTest(classes = ProfileApp.class)
public class HanleVirtualInfoTest {

    @Autowired
    private HandleVirtualInfoService handleVirtualInfoService;

    @Autowired
    private JbxxXpMapper jbxxXpMapper;

    @Test
    public void test() {
        List<Long> list1 = new ArrayList<>();
        list1.add(1l);
        list1.add(2l);
        List<Long> list2 = new ArrayList<>();
        list2.add(1l);
        list2.add(2l);
        List<List<Long>> list = new ArrayList<>();
        list.add(list1);list.add(list2);
        Object recordValue = list;
        List<Object> deptIds = JsonUtil.objectToArray(recordValue, Object.class);
        List<Long> deptIdsa = deptIds.stream().map(e -> {
            List<Long> result = JsonUtil.objectToArray(e, Long.class);
            return CollectionUtils.isEmpty(result) ? null : result.get(result.size() - 1);
        }).filter(e -> Objects.nonNull(e)).collect(Collectors.toList());
        System.out.println(deptIdsa);
    }

    @Test
    public void setBlob() {
        File file = new File("C:/Users/<USER>/Pictures/Screenshots/sss.jpg");
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(file);
            byte[] imageData = new byte[fis.available()];
            fis.read(imageData);
            fis.close();
            JbxxXp jbxxXp = jbxxXpMapper.selectOne(new QueryWrapper<JbxxXp>().eq("GMSFHM", "450800198601231757"));
            jbxxXp.setXp(imageData);
            jbxxXpMapper.update(jbxxXp,new QueryWrapper<JbxxXp>().eq("GMSFHM", "450800198601231757"));
            jbxxXp.setGmsfhm("450800198601231758");
            jbxxXp.setXm("成刘翔");
            jbxxXpMapper.insert(jbxxXp);
        } catch (Exception e) {
            log.error("插入blob失败：{}",e.getMessage());
        }
    }
}
