package com.trs.police.profile.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.exception.ServiceException;
import com.trs.police.profile.domain.entity.JuvenilesScoreEntity;
import com.trs.police.profile.domain.entity.RiskScoreItemEntity;
import com.trs.police.profile.domain.entity.person.JuvenilesEntity;
import com.trs.police.profile.domain.vo.JuvenilesScoreVo;
import com.trs.police.profile.mapper.JuvenilesLabelMapper;
import com.trs.police.profile.mapper.JuvenilesScoreMapper;
import com.trs.police.profile.mapper.RiskScoreItemMapper;
import com.trs.police.profile.mapper.person.JuvenilesOracleMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@SpringBootTest
public class JuvenilesRiskScoreTotalCountServiceImplTest {

    @InjectMocks
    private JuvenilesRiskScoreTotalCountServiceImpl service;

    @Mock
    private JuvenilesOracleMapper juvenilesMapper;

    @Mock
    private JuvenilesScoreMapper juvenilesScoreMapper;

    @Mock
    private JuvenilesLabelMapper juvenilesLabelMapper;

    @Mock
    private RiskScoreItemMapper riskScoreItemMapper;

    private JuvenilesEntity testJuvenile;
    private List<RiskScoreItemEntity> testScoreItems;

    @BeforeEach
    void setUp() {
        testJuvenile = new JuvenilesEntity();
        testJuvenile.setZjhm("test123456");
        testJuvenile.setName("Test User");

        testScoreItems = new ArrayList<>();
        RiskScoreItemEntity scoreItem = new RiskScoreItemEntity();
        scoreItem.setItemScore(25.0);
        scoreItem.setItemType("juvenilesScore");
        scoreItem.setRecordId("test123456");
        testScoreItems.add(scoreItem);

    }

    @Test
    void testCountTargetItemScore() throws ServiceException {
        // Setup
        when(juvenilesMapper.findCountData(any(), eq("test123456")))
                .thenReturn(new Page<JuvenilesEntity>(1, 1)
                        .setRecords(List.of(testJuvenile)));

        when(riskScoreItemMapper.selectList(any())).thenReturn(testScoreItems);

        // Create score details for testing
        List<JuvenilesScoreVo.ScoreDetailVo> scoreDetails = new ArrayList<>();
        JuvenilesScoreVo.ScoreDetailVo detail = new JuvenilesScoreVo.ScoreDetailVo();
        detail.setScore(25L);
        detail.setSecondCategory("TestCategory");
        scoreDetails.add(detail);

        JSONObject other = new JSONObject();
        other.put("scoreDetail", scoreDetails);

        // Execute
        service.countTargetItemScore("test123456");

        // Verify
        verify(juvenilesMapper).findCountData(any(), eq("test123456"));
        verify(riskScoreItemMapper).selectList(any());
        verify(juvenilesScoreMapper, times(1)).updateById(any(JuvenilesScoreEntity.class));

        // Additional verifications can be added based on specific business logic
    }

    @Test
    void testCountTargetItemScoreWithInvalidId() {
        // Test with invalid ID
        assertThrows(ServiceException.class, () -> {
            service.countTargetItemScore(null);
        });
    }

    @Test
    void testCountTargetItemScoreWithNoScoreItems() throws ServiceException {
        // Setup
        when(juvenilesMapper.findCountData(any(), eq("test123456")))
                .thenReturn(new Page<JuvenilesEntity>(1, 1).setRecords(List.of(testJuvenile)));

        when(riskScoreItemMapper.selectList(any())).thenReturn(new ArrayList<>());

        // Execute
        service.countTargetItemScore("test123456");

        // Verify that no score update was performed
        verify(juvenilesScoreMapper, never()).updateById(any(JuvenilesScoreEntity.class));
    }

    @Test
    void testCountTargetItemScoreWithHighRisk() throws ServiceException {
        // Setup
        testScoreItems.get(0).setItemScore(50.0); // Set score high enough for high risk

        when(juvenilesMapper.findCountData(any(), eq("test123456")))
                .thenReturn(new Page<JuvenilesEntity>(1, 1).setRecords(List.of(testJuvenile)));

        when(riskScoreItemMapper.selectList(any())).thenReturn(testScoreItems);

        // Execute
        service.countTargetItemScore("test123456");

        // Verify high risk processing
        verify(juvenilesLabelMapper).selectCount(any());
        verify(juvenilesScoreMapper).updateById(any(JuvenilesScoreEntity.class));
    }
}