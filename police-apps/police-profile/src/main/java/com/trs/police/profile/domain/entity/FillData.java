package com.trs.police.profile.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 事件实体类对象
 *
 * <AUTHOR>
 * @date 2024/04/07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FillData{

    /**
     * 日期
     */
    private String date;

    /**
     * 首页信息
     */
    private String info;

    /**
     * 总人数
     */
    private Long count;

    /**
     * 人数
     */
    private String controlPoliceInfo;

    /**
     * 人数
     */
    private String areaCountInfo;

    /**
     * 人数
     */
    private String groupInfo;
}
