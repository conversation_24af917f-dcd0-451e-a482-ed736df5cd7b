package com.trs.police.profile.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.CodeNameVO;
import com.trs.police.profile.domain.entity.SearchFootholdEntity;
import com.trs.police.profile.domain.vo.FootholdListVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 落脚点mapper
 */

@Mapper
public interface SearchFootholdMapper extends BaseMapper<SearchFootholdEntity> {

    /**
     * 落脚点列表
     *
     * @param ids    身份标识
     * @param params 筛选参数
     * @return 落脚点列表
     */
    List<FootholdListVO> getFootholdList(@Param("ids") List<CodeNameVO> ids,
                                         @Param("params") ListParamsRequest params);

}
