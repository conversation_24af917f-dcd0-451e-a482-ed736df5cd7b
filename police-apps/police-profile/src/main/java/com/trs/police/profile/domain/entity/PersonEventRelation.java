package com.trs.police.profile.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> lv.bo
 * @version 1.0
 * @date 创建时间：2024/2/18 14:53
 * @since 1.0
 */
@Data
@TableName("t_profile_person_event_relation")
@AllArgsConstructor
@NoArgsConstructor
public class PersonEventRelation extends AbstractBaseEntity {

    private static final long serialVersionUID = -156618590633730434L;

    private Long eventId;

    private Long personId;

    private String riskLabelIds;

    /**
     * 事件编号
     */
    private String eventCode;

    /**
     * 身份证号
     */
    private String idNumber;

    /**
     * 是否劝返
     */
    private Integer isPersuaded;

    /**
     * 劝返情况
     */
    private Integer persuasionSituation;
}