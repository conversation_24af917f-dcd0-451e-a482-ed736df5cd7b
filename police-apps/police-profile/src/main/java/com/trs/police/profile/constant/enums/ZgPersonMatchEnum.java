package com.trs.police.profile.constant.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 自贡事件档案人员匹配情况
 */
public enum ZgPersonMatchEnum {

    MATCHED(1,"已匹配"),
    UN_MATCHED(2,"未匹配");
    @Getter
    private int code;
    @Getter
    private String name;

    ZgPersonMatchEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * xx
     *
     * @param code xx
     * @return xx
     */
    public static String codeOf(Integer code) {
        if (Objects.nonNull(code)) {
            for (ZgPersonMatchEnum typeEnum : ZgPersonMatchEnum.values()) {
                if (code.equals(typeEnum.getCode())) {
                    return typeEnum.getName();
                }
            }
        }
        return "";
    }
}
