package com.trs.police.profile.schema.vo;

import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.profile.schema.field.ListField;
import com.trs.police.profile.schema.module.ListModuleSchema;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 动态列表行查询结果
 *
 * <AUTHOR>
 * @date 2022/12/09
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ListRowResultVO {

    /**
     * 主键
     */
    private Object id;
    /**
     * 是否允许编辑，用于自增列表
     */
    private Boolean editable;
    /**
     * 列
     */
    private List<ListColumnResultVO> columns;
    /**
     * 是否可跳转
     */
    private Boolean canJump;

    /**
     * 一行原始数据处理成列表数据
     *
     * @param dbRecord 原始数据
     * @param schema 配置
     * @return {@link ListRowResultVO}
     */
    public static ListRowResultVO getRecordValue(Map<String, Object> dbRecord, ListModuleSchema schema) {
        List<ListField> fields = Arrays.asList(schema.getFields());
        List<ListColumnResultVO> columns = fields.stream()
            .map(field -> field.toListSchemaResultVO(dbRecord))
            .collect(Collectors.toList());
        return new ListRowResultVO(dbRecord.get("id"), true, columns, true);
    }

    /**
     * 生成导出字段
     *
     * @param heads 列表标题
     * @return 结果
     */
    public List<Object> getExportValue(List<String> heads) {
        return columns.stream()
            .filter(vo -> checkColumnExist(vo.getKey(), heads))
            .map(vo -> generateValue(vo.getValue()))
            .collect(Collectors.toList());
    }

    private String generateValue(Object value) {
        if (value == null) {
            return "--";
        } else {
            if (value instanceof List) {
                List<String> list = JsonUtil.castList(value, String.class);
                return String.join(",", list);
            } else {
                return value.toString();
            }
        }
    }

    private boolean checkColumnExist(String column, List<String> heads) {
        for (String head : heads) {
            if (head.equals(column)) {
                return true;
            }
        }
        return false;
    }
}
