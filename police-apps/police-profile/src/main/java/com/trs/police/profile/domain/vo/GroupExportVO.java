package com.trs.police.profile.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2023/10/19 9:50
 */
@Data
public class GroupExportVO {
    @ExcelProperty("地区")
    private String controlBureau;
    @ExcelProperty("风险类别")
    private String fxlb;
    @ExcelProperty("风险细类")
    private String fxxl;
    @ExcelProperty("群体名称")
    private String name;
    @ExcelProperty("主责警种")
    private String controlPolice;
    @ExcelProperty("风险背景")
    private String mainDemand;
    @ExcelProperty("化解难点")
    private  String petitionInfo;
    @ExcelProperty("现实动向")
    private String realtimeTrend;
    @ExcelProperty("工作目标")
    private String workTarget;
    @ExcelProperty("工作措施")
    private String workMeasures;
    @ExcelProperty("党政管控")
    private String governmentControl;
    @ExcelProperty(value = {"任务清单","党政主管部门"})
    private String controlGovernment;
    @ExcelProperty(value = {"任务清单","责任领导职务"})
    private String zrldzw;
    @ExcelProperty(value = {"任务清单","责任领导"})
    private String zrld;
    @ExcelProperty(value = {"任务清单","党政责任领导电话"})
    private String dzzrlddh;
    @ExcelProperty(value = {"任务清单","公安包案领导职务"})
    private String gabaldyw;
    @ExcelProperty(value = {"任务清单","领导姓名"})
    private String ldxm;
    @ExcelProperty(value = {"任务清单","公安包案领导电话"})
    private String gabalddh;
    @ExcelProperty(value = {"任务清单","公安责任人"})
    private String gazry;
    @ExcelProperty(value = {"任务清单","公安责任人电话"})
    private String gazrydh;
    @ExcelProperty(value = {"督查清单","专班运行情况"})
    private String zbyxqk;
    @ExcelProperty(value = {"督查清单","责任人落实情况"})
    private String zrrlsqk;
    @ExcelProperty(value = {"督查清单","人员稳控情况"})
    private String rywkqk;
    @ExcelProperty(value = {"督查清单","化解成效情况"})
    private String  hjcxqk;
    @ExcelIgnore
    private String groupLabels;
    @ExcelIgnore
    private Integer xh;
    @ExcelIgnore
    private Long id;
}
