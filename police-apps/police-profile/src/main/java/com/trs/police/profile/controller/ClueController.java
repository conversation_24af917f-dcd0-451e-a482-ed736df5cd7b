package com.trs.police.profile.controller;

import com.trs.police.common.core.annotation.SkipResponseBodyAdvice;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.TrendVO;
import com.trs.police.common.core.vo.profile.ClueListVO;
import com.trs.police.profile.domain.dto.TimeRequest;
import com.trs.police.profile.domain.vo.AbstractDataVO;
import com.trs.police.common.core.vo.ImportResultVO;
import com.trs.police.profile.excel.ImportService;
import com.trs.police.profile.excel.ImportVO;
import com.trs.police.profile.service.ClueService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * 线索查询
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = {"/clue", "/public/clue"})
public class ClueController {

    @Resource
    ImportService importService;

    @Resource
    private ClueService clueService;


    /**
     * 查询线索列表
     *
     * @param request 参数
     * @return 结果
     */
    @PostMapping("/list")
    public PageResult<ClueListVO> getClueList(@RequestBody ListParamsRequest request) {
        return clueService.selectClueList(request);
    }

    /**
     * 根据线索编号查询线索id
     *
     * @param code 线索编号
     * @return 线索id
     */
    @GetMapping("/id/{code}")
    Long getIdByCode(@PathVariable("code") String code) {
        return clueService.getIdByCode(code);
    }

    /**
     * 下载线索导入模版
     *
     * @param response 模版
     */
    @GetMapping("/download/template")
    @SkipResponseBodyAdvice
    void downloadTemplate(HttpServletResponse response) throws IOException {
        importService.downloadClueTemplate(response);
    }

    /**
     * 批量导入
     *
     * @param vo 导入参数
     * @return 导入结果
     */
    @PostMapping("/import")
    public ImportResultVO importClue(ImportVO vo) {
        return importService.importClue(vo);
    }

    /**
     * 判断线索编号是否已经存在
     *
     * @param code 线索编号
     * @param clueId 线索id
     * @return 是否已经存在-已经存在：true；不存在：false
     */
    @GetMapping("/checkExist")
    boolean checkExist(String code, Long clueId) {
        return clueService.checkExist(code,clueId);
    }

    /**
     * 指向地点分析
     *
     * @param request 时间参数
     * @return 结果
     */
    @PostMapping("/targetLocationAnalysis")
    public List<AbstractDataVO> targetLocationAnalysis(@Valid @RequestBody TimeRequest request) {
        return clueService.targetLocationAnalysis(request);
    }

    /**
     * 趋势分析
     *
     * @param request 时间参数
     * @return 结果
     */
    @PostMapping("/trend")
    public List<TrendVO> trend(@Valid @RequestBody TimeRequest request) {
        return clueService.trend(request);
    }
}
