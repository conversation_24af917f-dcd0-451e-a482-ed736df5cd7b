package com.trs.police.profile.configure;

import com.trs.police.profile.config.StInterfaceConfig;
import com.trs.police.profile.util.StRequestHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/06/27
 */
@Configuration
@Slf4j
public class StInterFaceAutoConfigure {

    /**
     * st警情接口信息
     *
     * @return NcInterfaceConfig
     */
    @Bean
    @ConfigurationProperties(prefix = "st.case.interface.info")
    public StInterfaceConfig stInterfaceConfig() {
        return new StInterfaceConfig();
    }


    /**
     * st警情接口工具类
     *
     * @param stInterfaceConfig 南充警综接口信息
     * @return StRequestHelper
     */
    @Bean
    public StRequestHelper jzptNcRequestHelper(StInterfaceConfig stInterfaceConfig) {
        return new StRequestHelper(stInterfaceConfig);
    }
}
