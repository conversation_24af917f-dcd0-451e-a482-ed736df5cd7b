package com.trs.police.profile.strategy.promotion.model;

import com.trs.police.profile.constant.enums.PoliceRankEnum;
import com.trs.police.profile.domain.entity.zhzg.PoliceRankRelation;

import java.time.LocalDate;
import java.time.ZoneId;

/**
 * 职级任职信息实现类
 * 
 * 实现 TenureInfo 接口，专门处理警员职级相关的任职信息。
 * 封装了职级关系记录和职级枚举，提供统一的任职信息访问接口。
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
public class RankTenureInfo implements TenureInfo {
    
    /**
     * 职级关系记录
     */
    private final PoliceRankRelation rankRelation;
    
    /**
     * 职级枚举
     */
    private final PoliceRankEnum rankEnum;

    /**
     * 构造方法
     *
     * @param rankRelation 职级关系记录
     * @param rankEnum 职级枚举
     */
    public RankTenureInfo(PoliceRankRelation rankRelation, PoliceRankEnum rankEnum) {
        this.rankRelation = rankRelation;
        this.rankEnum = rankEnum;
    }

    /**
     * 获取职级任职开始时间
     *
     * @return LocalDate 职级开始时间，如果无法获取返回null
     */
    @Override
    public LocalDate getStartDate() {
        if (rankRelation == null || rankRelation.getStartTime() == null) {
            return null;
        }
        return rankRelation.getStartTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    /**
     * 获取职级名称
     *
     * @return String 职级名称（如"三级警长"）
     */
    @Override
    public String getTenureName() {
        return rankEnum != null ? rankEnum.getName() : null;
    }

    /**
     * 获取任职类型描述
     *
     * @return String 固定返回"职级"
     */
    @Override
    public String getTenureType() {
        return "职级";
    }

    /**
     * 获取职级关系记录
     *
     * @return PoliceRankRelation 职级关系记录
     */
    public PoliceRankRelation getRankRelation() {
        return rankRelation;
    }

    /**
     * 获取职级枚举
     *
     * @return PoliceRankEnum 职级枚举
     */
    public PoliceRankEnum getRankEnum() {
        return rankEnum;
    }

    @Override
    public String toString() {
        return String.format("RankTenureInfo{rankName='%s', startDate=%s}", 
                           getTenureName(), getStartDate());
    }
}
