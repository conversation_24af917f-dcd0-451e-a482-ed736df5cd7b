package com.trs.police.profile.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import com.trs.police.common.core.handler.typehandler.JsonToLongListHandler;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 人员档案公安管控
 *
 * <AUTHOR>
 * @since 2022/11/17 10:51
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_profile_group_police_control", autoResultMap = true)
public class ProfileGroupPoliceControlEntity extends AbstractBaseEntity {

    private static final long serialVersionUID = -6608037492973471568L;

    /**
     * 群体id
     */
    private Long groupId;

    /**
     * 责任分局code
     */
    private String controlBureau;
    /**
     * 责任分局领导id
     */
    private Long controlBureauLeader;
    /**
     * 责任警种部门id
     */
    private String controlPolice;

    /**
     * 责任警种部门领导id
     */
    private Long controlPoliceLeader;

    /**
     * 责任派出所id
     */
    private String controlStation;
    /**
     * 责任派出所领导id
     */
    private Long controlStationLeader;
    /**
     * 责任民警
     */
    @TableField(typeHandler = JsonToLongListHandler.class)
    private List<Long> controlPerson;

    /**
     * 责任分局联系方式
     */
    private String controlBureauContact;

    /**
     * 责任派出所联系方式
     */
    private String controlPoliceContact;

    /**
     * 警种
     */
    private Integer policeKind;
}
