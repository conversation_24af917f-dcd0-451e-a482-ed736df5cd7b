package com.trs.police.profile.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.profile.domain.entity.ProfileVirtualIdentityEntity;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 虚拟身份查询
 *
 * <AUTHOR>
 * @date 2022/12/27 11:28
 */
@Mapper
public interface ProfileVirtualIdentityMapper extends BaseMapper<ProfileVirtualIdentityEntity> {

    /**
     * 根据虚拟身份号和类型获取虚拟身份
     *
     * @param virtualNumber 虚拟身份号
     * @param type          类型
     * @return 虚拟身份
     */
    @Select("select * from t_profile_virtual_identity where virtual_number=#{virtualNumber} and type=#{type}")
    List<ProfileVirtualIdentityEntity> getByVirtualNumberAndType(@Param("virtualNumber")String virtualNumber, @Param("type") Long type);

    /**
     * 根据虚拟身份号和类型获取虚拟身份
     *
     * @param list 虚拟身份号集合
     * @param type          类型
     * @return 虚拟身份
     */
    List<ProfileVirtualIdentityEntity> getByVirtualNumberAndTypes(@Param("list")List<String> list, @Param("type") Long type);
}
