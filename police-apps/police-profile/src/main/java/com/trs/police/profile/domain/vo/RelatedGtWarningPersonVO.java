package com.trs.police.profile.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.trs.police.common.core.vo.profile.Photo;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 群体关联杆体预警人员
 *
 * <AUTHOR>
 * @date 2025/5/27
 */
@Data
public class RelatedGtWarningPersonVO {
    /**
     * 人员id
     */
    private Long id;

    /**
     * 人员姓名
     */
    private String name;

    /**
     * 人员身份证号
     */
    private String idNumber;

    /**
     * 人员性别
     */
    private Long gender;

    /**
     * 人员性别名称
     */
    private String genderName;
    /**
     * 头像
     */
    private List<Photo> photo;

    /**
     * 预警杆体
     */
    private List<String> warningGts;

    /**
     * 预警杆体
     */
    private String warningGtName;

    /**
     * 最新预警时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastWarningTime;

    /**
     * 预警次数
     */
    private Integer warningCount;

    /**
     * 设置性别
     *
     * @param gender 性别
     */
    public void setGender(Long gender) {
        this.gender = gender;
        this.genderName = Long.valueOf(1L).equals(gender) ? "男" : "女";
    }
}
