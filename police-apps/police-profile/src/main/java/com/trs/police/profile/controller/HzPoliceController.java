package com.trs.police.profile.controller;

import com.trs.police.profile.domain.dto.zhzg.HzPoliceDto;
import com.trs.police.profile.domain.dto.zhzg.HzPoliceExportDto;
import com.trs.police.profile.domain.vo.HzPoliceVO;
import com.trs.police.profile.service.HzPoliceService;
import com.trs.web.builder.base.RestfulResultsV2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * 慧政 police_controller
 *
 * <AUTHOR>
 * @date 2025/06/05
 */
@RestController
@RequestMapping("/police")
public class HzPoliceController {


    @Autowired
    private HzPoliceService hzPoliceService;


    /**
     * 列表接口
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping("/list")
    public RestfulResultsV2<HzPoliceVO> list(@RequestBody HzPoliceDto dto){
        return hzPoliceService.list(dto);
    }


    /**
     * 批量导出
     *
     * @param dto dto
     * @param response response
     * @return 结果
     */
    @PostMapping("/export")
    public RestfulResultsV2<Boolean> export(@RequestBody HzPoliceExportDto dto, HttpServletResponse response){
        return hzPoliceService.export(dto,response);
    }


}
