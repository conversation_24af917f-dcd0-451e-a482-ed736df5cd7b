package com.trs.police.profile.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.entity.Label;
import com.trs.police.common.core.entity.ProfileLabelPoliceKindRelationEntity;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.mapper.CommonMapper;
import com.trs.police.common.core.mapper.ProfileLabelPoliceKindRelationMapper;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.vo.Dict2VO;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.core.vo.LabelPoliceKindRelationVO;
import com.trs.police.common.core.vo.profile.LabelVO;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.common.redis.starter.service.RedisService;
import com.trs.police.profile.constant.LabelConstants;
import com.trs.police.profile.domain.vo.LabelParams;
import com.trs.police.profile.mapper.LabelMapper;
import com.trs.police.profile.service.LabelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 标签管理
 *
 * <AUTHOR>
 * @since 2021/12/13
 */
@Service
@Slf4j
public class LabelServiceImpl implements LabelService {

    @Resource
    private LabelMapper labelMapper;

    @Resource
    private RedisService redisService;

    @Resource
    private PermissionService permissionService;

    @Resource
    private CommonMapper commonMapper;

    @Resource
    private DictService dictService;

    @Resource
    private ProfileLabelPoliceKindRelationMapper profileLabelPoliceKindRelationMapper;

    /**
     * 检索线索连接符
     */
    private static final String JOIN_CHARACTER = "-";

    private static final String SCHEMA_PREFIX = "profileSchema";

    private static final String LABEL_LIST_PREFIX = "labelList";

    private static final String LABEL_PREFIX = "label";


    /**
     * 清除label缓存
     */
    @PostConstruct
    public void deleteLabelCache() {
        redisService.delByPrefix(SCHEMA_PREFIX, LABEL_LIST_PREFIX, LABEL_PREFIX);
        log.info("删除label缓存");
    }

    @Override
    public List<LabelVO> getAllLabel(String module) {
        List<Label> labelEntities = labelMapper.findByModuleAndSubjectId(module);
        return labelEntities.stream()
                .map(Label::toVO)
                .collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "labelList", key = "#module+'-'+#rootId")
    public List<LabelVO> getLabelList(String module, Long rootId) {
        List<Label> labelEntities = labelMapper.findByModuleAndSubjectId(module);
        List<LabelVO> voList = new ArrayList<>();
        Map<Long, LabelVO> map = new HashMap<>();
        labelEntities.forEach(entity -> map.put(entity.getId(), generateLabel(entity)));
        for (LabelVO vo : map.values()) {
            Long parentId = vo.getPid();
            if (Objects.isNull(parentId)) {
                voList.add(vo);
            } else if (map.containsKey(parentId)) {
                map.get(parentId).getChildren().add(vo);
            }
        }
        List<LabelVO> result = sortTree(voList);
        if (rootId <= 0) {
            return result;
        } else {
            return result.stream().filter(vo -> vo.getId().equals(rootId)).collect(Collectors.toList());
        }
    }

    @Override
    @Cacheable(value = "label", key = "#id")
    public LabelVO getLabel(Long id) {
        Label label = labelMapper.selectById(id);
        LabelVO labelVO = new LabelVO();
        labelVO.setId(label.getId());
        labelVO.setName(label.getName());
        labelVO.setPath(label.getPath());
        return labelVO;
    }

    private List<LabelVO> sortTree(List<LabelVO> voList) {
        voList = voList.stream().sorted(Comparator.comparing(LabelVO::getShowOrder)).collect(Collectors.toList());
        voList.forEach(vo -> {
            if (Objects.nonNull(vo.getChildren())) {
                vo.setChildren(sortTree(vo.getChildren()));
            }
        });
        return voList;
    }

    /**
     * 遍历
     *
     * @param entity 参数
     * @return vo
     */
    private LabelVO generateLabel(Label entity) {
        LabelVO vo = new LabelVO();
        vo.setPath(entity.getPath());
        vo.setId(entity.getId());
        vo.setPid(entity.getPid());
        vo.setName(entity.getName());
        vo.setIsChecked("1".equals(entity.getStatus()));
        vo.setShowOrder(entity.getShowOrder());
        vo.setIsUsed(true);
        if (Objects.isNull(vo.getChildren())) {
            vo.setChildren(new ArrayList<>());
        }
        //根据标签id获取关联的警种
        List<ProfileLabelPoliceKindRelationEntity> labelList = profileLabelPoliceKindRelationMapper.selectList(
                new QueryWrapper<ProfileLabelPoliceKindRelationEntity>().eq("label_id", entity.getId()));
        List<Long> policeKindCodes = labelList.stream()
                .map(ProfileLabelPoliceKindRelationEntity::getPoliceKindId).collect(Collectors.toList());
        Map<Long, String> policeKind = dictService.commonSearch("police_kind", null, null, null)
                .stream().collect(Collectors.toMap(Dict2VO::getCode, Dict2VO::getName));
        // 将警种代码映射为 LabelPoliceKindRelationVO 列表
        List<LabelPoliceKindRelationVO> policeKinds = policeKindCodes.stream()
                .map(code -> new LabelPoliceKindRelationVO(code, policeKind.get(code)))
                .collect(Collectors.toList());
        vo.setPoliceKinds(policeKinds);
        return vo;
    }


    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void editLabelList(String recordType, List<LabelVO> list) {
        List<Long> ids = new ArrayList<>();
        getAllLabelIds(ids, list);
        List<Label> deleteList;
        if (ids.isEmpty()) {
            deleteList = labelMapper.findByModuleAndSubjectId(recordType);
        } else {
            deleteList = labelMapper.selectList(
                    Wrappers.lambdaQuery(Label.class)
                            .eq(Label::getModule, recordType)
                            .notIn(Label::getId, ids));
        }
        if (!deleteList.isEmpty()) {
            labelMapper.deleteBatchIds(deleteList);
        }
        generateList(null, recordType, list);
        redisService.delByPrefix(SCHEMA_PREFIX, LABEL_LIST_PREFIX);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void addLabelList(String type, LabelParams list) {
        Label label = new Label();
        label.setStatus("1");
        label.setCreateType("0");
        label.setCode(list.getCode());
        label.setModule(type);
        label.setPid(list.getPid()==null?null:Long.valueOf(list.getPid()));
        BeanUtil.copyPropertiesIgnoreNull(list, label);
        //判断要插入的标签是否在mysql数据库中存在,存在则更新状态
        Label existLabel =labelMapper.selectLabelByName(label);
        if (existLabel!=null && existLabel.getStatus().equals(LabelConstants.STOP_USE)){
            existLabel.setStatus(LabelConstants.BEING_USE);
            labelMapper.updateById(existLabel);
        }else {
            LambdaQueryWrapper<Label> wrapper = Wrappers.lambdaQuery(Label.class);
            Integer showOrder = labelMapper.selectList(
                            Objects.nonNull(list.getPid()) ? wrapper.eq(Label::getPid, list.getPid())
                                    : wrapper.isNull(Label::getPid).eq(Label::getModule, type)).stream().map(Label::getShowOrder)
                    .max(Integer::compareTo).orElse(0);
            label.setShowOrder(showOrder + 1);
            if (Objects.nonNull(list.getPid())) {
                label.setPid(Long.parseLong(list.getPid()));
            }
            //设置label的path路径
            if (Objects.nonNull(list.getPid())) {
                Label parentLabel = labelMapper.selectById(label.getPid());
                label.setPath(parentLabel.getPath() + parentLabel.getId() + "-");
            } else {
                label.setPath("-");
            }
            labelMapper.insert(label);
            //建立标签与警种关联关系
            if (StringUtils.isNotEmpty(list.getPoliceKindCode())){
                List<Long> policeKindIdList = Arrays.stream(list.getPoliceKindCode().split(","))
                        .map(Long::valueOf)
                        .collect(Collectors.toList());
                for (Long polyKindId : policeKindIdList) {
                    ProfileLabelPoliceKindRelationEntity relationEntity = new ProfileLabelPoliceKindRelationEntity();
                    relationEntity.setLabelId(label.getId());
                    relationEntity.setPoliceKindId(polyKindId);
                    profileLabelPoliceKindRelationMapper.insert(relationEntity);
                }
            }
        }
        redisService.delByPrefix(SCHEMA_PREFIX, LABEL_LIST_PREFIX);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void editLabel(String type, LabelParams list) {
        if (StringUtils.isNotEmpty(list.getPoliceKindCode())){
            List<Long> policeKindIdList = Arrays.stream(list.getPoliceKindCode().split(","))
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
            //先根据labelId删除所有关联关系
            profileLabelPoliceKindRelationMapper.delete(
                    Wrappers.lambdaQuery(ProfileLabelPoliceKindRelationEntity.class)
                            .eq(ProfileLabelPoliceKindRelationEntity::getLabelId, list.getId()));
            //再重新建立关联关系
            for (Long polyKindId : policeKindIdList) {
                ProfileLabelPoliceKindRelationEntity relationEntity = new ProfileLabelPoliceKindRelationEntity();
                relationEntity.setLabelId(Long.valueOf(list.getId()));
                relationEntity.setPoliceKindId(polyKindId);
                profileLabelPoliceKindRelationMapper.insert(relationEntity);
            }
        }
        Label label = labelMapper.selectById(list.getId());
        label.setName(list.getName());
        labelMapper.updateById(label);
        redisService.delByPrefix(SCHEMA_PREFIX, LABEL_LIST_PREFIX);
    }

    @Override
    public Boolean checkNameRepeat(String type, LabelParams labelParams) {
        if (Objects.nonNull(labelParams.getId())) {
            LambdaQueryWrapper<Label> wrapper = Wrappers.lambdaQuery(Label.class)
                    .eq(Label::getName, labelParams.getName())
                    .eq(Label::getModule, type)
                    .eq(Label::getStatus, LabelConstants.BEING_USE)
                    .notIn(Label::getId, labelParams.getId());

            Long aLong = labelMapper.selectCount(
                    Objects.isNull(labelParams.getPid()) ? wrapper.isNull(Label::getPid)
                            : wrapper.eq(Label::getPid, labelParams.getPid()));
            return aLong > 0;
        } else {
            LambdaQueryWrapper<Label> wrapper = Wrappers.lambdaQuery(Label.class)
                    .eq(Label::getName, labelParams.getName())
                    .eq(Label::getStatus, LabelConstants.BEING_USE)
                    .eq(Label::getModule, type);
            Long aLong = labelMapper.selectCount(
                    Objects.isNull(labelParams.getPid()) ? wrapper.isNull(Label::getPid)
                            : wrapper.eq(Label::getPid, labelParams.getPid()));
            return aLong > 0;
        }
    }

    /**
     * 获取所有id
     *
     * @param ids  id列表
     * @param list 参数
     */
    private void getAllLabelIds(List<Long> ids, List<LabelVO> list) {
        list.forEach(labelVO -> {
            if (Objects.nonNull(labelVO.getId())) {
                ids.add(labelVO.getId());
            }
            if (!labelVO.getChildren().isEmpty()) {
                getAllLabelIds(ids, labelVO.getChildren());
            }
        });
    }

    /**
     * 遍历
     *
     * @param pid        父级id
     * @param recordType 类型
     * @param src        列表
     */
    private void generateList(Long pid, String recordType, List<LabelVO> src) {
        for (int i = 0; i < src.size(); i++) {
            LabelVO label = src.get(i);
            Label entity = new Label();
            if (Objects.nonNull(pid)) {
                Label labelEntity = labelMapper.selectById(pid);
                entity.setPath(labelEntity.getPath() + pid + "-");
            } else {
                entity.setPath("-");
            }
            entity.setId(label.getId());
            entity.setName(label.getName());
            entity.setPid(pid);
            entity.setModule(recordType);
            entity.setStatus(Boolean.TRUE.equals(label.getIsChecked()) ? "1" : "0");
            entity.setCreateType("0");
            entity.setShowOrder(i);
            labelMapper.updateById(entity);
            if (!label.getChildren().isEmpty()) {
                generateList(entity.getId(), recordType, label.getChildren());
            }
        }
    }

    @Override
    public List<LabelVO> getLabelByIds(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return Collections.emptyList();
        }
        return labelMapper.selectBatchIds(ids).stream().map(item -> {
            LabelVO labelVO = new LabelVO();
            labelVO.setId(item.getId());
            labelVO.setName(item.getName());
            return labelVO;
        }).collect(Collectors.toList());
    }

    @Override
    public void updatePath() {
        labelMapper.findAllParent().forEach(parent -> {
            parent.setPath(buildPath());
            labelMapper.updateById(parent);
            updateChildPath(parent);
        });
    }

    @Override
    public List<LabelVO> getListFilterParams(String module) {
        return labelMapper.selectTree(module).stream()
                .map(Label::toVO)
                .collect(Collectors.toList());
    }

    @Override
    public List<LabelVO> getLabelListPermission(String type) {
        //获取用户档案权限
        KeyValueTypeVO permission = null;
        if (Objects.nonNull(AuthHelper.getCurrentUser())) {
            KeyValueTypeVO[] permissions = permissionService.getUserProfileLabels();
            permission = Stream.of(permissions)
                    .filter(label -> label.getKey().equals(type))
                    .findFirst().orElse(null);
        }

        //获取树型结构
        List<LabelVO> personLabels = this.getListFilterParams(type);

        if (Objects.nonNull(permission)) {
            List<Object> treeIds = KeyValueTypeVO.tiledList(permission.getValue());

            if (!treeIds.isEmpty()) {
                List<Object> simplificationList = KeyValueTypeVO.nestingListSimplification(permission.getValue());
                List<Long> allowIds = commonMapper.getProfileLabelChildren(simplificationList, type);

                treeIds.addAll(allowIds);
                LabelVO.removeNodesNotInList(personLabels, treeIds);
            }
        }

        return sortTree(personLabels);
    }

    @Override
    public LabelVO getLabelByTypeAndName(String type, String name) {
        QueryWrapper<Label> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("module", type);
        queryWrapper.eq("name", name);

        Label label = labelMapper.selectOne(queryWrapper);
        if (label != null) {
            LabelVO labelVO = new LabelVO();
            labelVO.setId(label.getId());
            labelVO.setName(label.getName());
            return labelVO;
        }
        return null;
    }

    /**
     * 删除标签
     *
     * @param type    类型
     * @param labelId 参数
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteLabel(String type, Long labelId) {
        Label label = labelMapper.selectById(labelId);
        if (label == null) {
            throw new TRSException("要删除的标签已经不存在");
        }
        //标签已经被使用，不能删除
        if (isInUse(type, label)) {
            throw new TRSException("标签已经被使用，不能删除！");
        }
        List<Label> labels;
        labels = labelMapper.selectByPid(label.getId());
        if (!labels.isEmpty()) {
            throw new TRSException("要删除的标签存在子标签，不允许删除！");
        }
        if (label.getStatus().equals(LabelConstants.BEING_USE)) {
            label.setStatus(LabelConstants.STOP_USE);
        } else {
            throw new TRSException("当前标签已经停用，无法删除");
        }
        labelMapper.updateById(label);
        redisService.delByPrefix(SCHEMA_PREFIX, LABEL_LIST_PREFIX);
    }

    /**
     * 判断标签是否被使用
     *
     * @param type  标签类型
     * @param label 标签
     * @return 是否被使用
     */
    private boolean isInUse(String type, Label label) {
        String labelJson = "[" + label.getId() + "]";
        switch (type) {
            case "person":
                if (labelMapper.getPersonCountByLabel(labelJson) == 0)
                    return false;
                break;
            case "group":
                if (labelMapper.getGroupCountByLabel(labelJson) == 0)
                    return false;
                break;
            case "clue":
                if (labelMapper.getClueCountByLabel(labelJson) == 0)
                    return false;
                break;
            case "event":
                if (labelMapper.getEventCountByLabel(labelJson) == 0)
                    return false;
                break;
            case "regular":
                if (labelMapper.getRegularCountByLabel(labelJson) == 0)
                    return false;
                break;
            default:
                //如果不是指定的类型直接不让删除
                return true;
        }
        return true;
    }

    /**
     * 更新子节点path
     *
     * @param parent 父节点
     */
    private void updateChildPath(Label parent) {
        final List<Label> child = labelMapper.findAllByParentId(parent.getId());
        if (Objects.isNull(child) || child.isEmpty()) {
            return;
        }
        child.forEach(item -> {
            item.setPath(buildPath(parent));
            labelMapper.updateById(item);
            updateChildPath(item);
        });
    }


    /**
     * 构造path
     *
     * @return path
     */
    public static String buildPath() {
        return JOIN_CHARACTER;
    }

    /**
     * 通过父节点的线索构造当前节点的path
     *
     * @param parent 父节点
     * @return 当前节点线索 从根节点id到父节点id的路径
     */
    public static String buildPath(Label parent) {
        if (Objects.isNull(parent)) {
            return buildPath();
        }
        return parent.getPath()
                + parent.getId()
                + JOIN_CHARACTER;
    }
}
