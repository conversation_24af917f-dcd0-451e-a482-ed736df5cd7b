package com.trs.police.profile.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 档案和大屏的关联表
 *
 * <AUTHOR>
 */
@TableName("t_profile_screen_relation")
@Data
public class ScreenRelation implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 关联的档案id
     */
    @TableField
    private Long relationId;

    /**
     * 展示的名称
     */
    @TableField
    private String showName;

    /**
     * 关联的类型
     */
    @TableField
    private Integer relationType;
}
