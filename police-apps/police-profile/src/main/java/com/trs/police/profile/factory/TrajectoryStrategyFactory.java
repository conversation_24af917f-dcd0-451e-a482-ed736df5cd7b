package com.trs.police.profile.factory;



import com.trs.police.profile.factory.strategy.trajectory.TrajectoryQueryStrategy;

import java.util.HashMap;
import java.util.Map;


/**
 * 轨迹策略工厂类
 * 生产不同的轨迹策略实现类
 *
 * <AUTHOR>
 * @since 2025-03-07
 */
public class TrajectoryStrategyFactory {
    /**
     * 轨迹策略实现类集合
     */
    private static final Map<String, TrajectoryQueryStrategy> TRAJECTORY_STRATEGY_MAP = new HashMap<>();

    /**
     * 注册轨迹策略实现类
     *
     * @param key 策略名称
     * @param strategy 策略实现类
     */
    public static void register(String key, TrajectoryQueryStrategy strategy) {
        TRAJECTORY_STRATEGY_MAP.put(key, strategy);
    }
}
