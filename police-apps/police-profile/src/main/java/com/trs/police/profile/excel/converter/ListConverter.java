package com.trs.police.profile.excel.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.metadata.data.WriteCellData;

import java.util.List;

/**
 * easyexcel转化工具
 *
 * <AUTHOR>
 * @date 2024年07月10日
 */
public class ListConverter implements Converter<List> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return List.class;
    }

    @Override
    public WriteCellData<?> convertToExcelData(WriteConverterContext<List> context) {
        List<?> list = context.getValue();
        if (list == null || list.isEmpty()) {
            return new WriteCellData<>("");
        }
        return new WriteCellData<>(list.toString());
    }
}

