package com.trs.police.profile.domain.entity.person;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 人员-打处情况
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_profile_person_dcqk")
public class PersonDcqk extends PersonRelatedType {
    /**
     * 分类，profile_person_dcqk_type_za
     */

    @TableField(exist = false)
    private String dcqk;

    /**
     * 设置打处情况
     *
     * @param dcqk 打处情况
     */
    public void setDcqk(String dcqk) {
        this.dcqk = dcqk;
        this.detail = dcqk;
    }
}
