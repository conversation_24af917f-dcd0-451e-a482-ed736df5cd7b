package com.trs.police.profile.mapper.person;

import com.trs.police.profile.domain.entity.person.PersonGkcs;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 人员员-管控措施mapper
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@Mapper
public interface PersonGkcsMapper extends PersonRelatedBaseMapper<PersonGkcs> {

    /**
     * 根据人员id查询
     *
     * @param personId   人员id
     * @param policeKind  管控警种
     * @return 结果
     */
    @Override
    @Select("select * from t_profile_person_gkcs where person_id = #{personId} and police_kind = #{policeKind}")
    PersonGkcs selectByPersonIdAndPoliceKind(@Param("personId") Long personId, @Param("policeKind") Long policeKind);

    /**
     * 根据人员id查询
     *
     * @param personId   人员id
     * @param policeKind  管控警种
     * @return 结果
     */
    @Select("select * from t_profile_person_gkcs where person_id = #{personId} and police_kind = #{policeKind}")
    List<PersonGkcs> selectListByPersonIdAndPoliceKind(@Param("personId") Long personId, @Param("policeKind") Long policeKind);
}
