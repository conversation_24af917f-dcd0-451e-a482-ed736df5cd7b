package com.trs.police.profile.strategy.zhzg.impl;

import com.trs.police.profile.domain.dto.zhzg.ScoreLgsjDTO;
import com.trs.police.profile.domain.dto.zhzg.ScoreRankDTO;
import com.trs.police.profile.domain.dto.zhzg.ZhzgPersonArchiveDTO;
import com.trs.police.profile.domain.dto.zhzg.ZhzgScoreRuleDTO;
import com.trs.police.profile.domain.vo.zhzg.ZhzgRuleScoreDetailVO;
import com.trs.police.profile.strategy.zhzg.ZhzgScoreStrategy;
import com.trs.police.profile.util.zhzg.ZhzgScoreUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 立功受奖积分计算策略
 */
@Slf4j
@Component
public class AwardsScoreStrategy implements ZhzgScoreStrategy {

    private static final String STRATEGY_NAME = "奖励得分";
    private static final String RULE_TYPE = "AWARDS";

    @Override
    public ZhzgRuleScoreDetailVO calculateScore(ZhzgPersonArchiveDTO personArchive, ZhzgScoreRuleDTO rule) {
        log.debug("开始计算立功受奖积分，人员：{}，规则：{}", personArchive.getName(), rule.getName());
        AwardsType awardsType = AwardsType.valueOf(rule.getRuleSubType());

        ZhzgRuleScoreDetailVO.ZhzgRuleScoreDetailVOBuilder builder = ZhzgRuleScoreDetailVO.builder()
                .ruleId(rule.getId())
                .ruleName(rule.getName())
                .ruleDescription(rule.getDescription())
                .ruleType(rule.getRuleType())
                .isLeaf(rule.getIsLeaf())
                .parentRuleId(rule.getParentId())
                .maxScore(rule.getFullScore())
                .success(true);

        try {
            // 验证数据
            if (!validatePersonArchive(personArchive) || !validateRule(rule)) {
                return builder
                        .score(0.0)
                        .isHit(false)
                        .success(false)
                        .errorMessage(awardsType.getName() + "数据验证失败")
                        .build();
            }

            // 获取立功受奖记录，过滤掉2018年以前的数据
            List<ScoreRankDTO> ranks = personArchive.getRanks();
            if (CollectionUtils.isEmpty(ranks)) {
                return builder
                        .score(0.0)
                        .isHit(false)
                        .calculateDescription(awardsType.getName() + "无职级信息记录")
                        .build();
            }
            ScoreRankDTO firstRank = ranks.get(0);

            //如果firstRank不为空则取startTime和2018年1月1日比较取更晚的日期
            LocalDate filterDate = Objects.nonNull(firstRank) && firstRank.getStartTime().isAfter(LocalDate.of(2018, 1, 1))
                    ? firstRank.getStartTime()
                    : LocalDate.of(2018, 1, 1);

            List<ScoreLgsjDTO> awards = personArchive.getAwards();
            if (CollectionUtils.isEmpty(awards)) {
                return builder
                        .score(0.0)
                        .isHit(false)
                        .calculateDescription(awardsType.getName() + "无立功受奖记录")
                        .build();
            }

            awards = awards.stream()
                    .filter(dto -> dto.getAcquisitionTime().isAfter(filterDate))
                    .collect(Collectors.toList());

            // 计算积分
            long hitCount = awards.stream().filter(dto -> Objects.equals(dto.getLgsj(), awardsType.getType()))
                    .count();

            double calculatedScore = hitCount > 0 ? rule.getScore() : 0.0;

            // 应用规则配置的最大分值限制
            double finalScore = ZhzgScoreUtil.getFinalScore(calculatedScore, rule.getFullScore());

            String hitData = String.format("获得%s记录：%d条", awardsType.getName(), hitCount);
            String calculateDescription = String.format("%d条获得%s记录 × %.1f分/条 = %.1f分",
                    hitCount, awardsType.getName(), rule.getScore(), calculatedScore);
            
            if (finalScore != calculatedScore) {
                calculateDescription += String.format("，受规则上限限制，最终得分：%.3f分", finalScore);
            }

            return builder
                    .score(finalScore)
                    .isHit(finalScore > 0)
                    .hitData(hitData)
                    .calculateDescription(calculateDescription)
                    .build();

        } catch (Exception e) {
            log.error("计算立功受奖积分失败，人员：{}，规则：{}，错误：{}", 
                    personArchive.getName(), rule.getName(), e.getMessage(), e);
            return builder
                    .score(0.0)
                    .isHit(false)
                    .success(false)
                    .errorMessage("计算失败：" + e.getMessage())
                    .build();
        }
    }

    @Getter
    @AllArgsConstructor
    enum AwardsType {
        YRJLM("一、二级英模", 1),
        QGLDMF("全国劳动模范", 2),
        SBJLDMF("省部级劳动模范", 3),
        QGTJYXRMJC("全国特级优秀人民警察", 4),
        QSTJYXRMJC("全省特级优秀人民警察", 5),
        QGYXRMJC("全国优秀人民警察", 6),
        SDJLDMF("市地级劳动模范", 7),
        GRYDG("个人一等功", 8),
        GREDG("个人二等功", 9),
        GRSDG("个人三等功", 10),
        YXGCDY("优秀共产党员", 11),
        YXDWGZZ("优秀党务工作者", 12),
        QSYXRMJC("全省优秀人民警察", 13),
        ;

        private final String name;
        private final Integer type;
    }

    @Getter
    @AllArgsConstructor
    enum RankType {
        A("获得博士研究生学历及学位"),
        B("获得博士学位"),
        C("获得硕士研究生学历及相应学位"),
        D("获得硕士研究生学历或者学位"),
        ;
        private final String name;
    }

    @Override
    public String getStrategyName() {
        return STRATEGY_NAME;
    }

    @Override
    public String getSupportedRuleType() {
        return RULE_TYPE;
    }

    @Override
    public Boolean isEnabled() {
        return true;
    }

    @Override
    public boolean supports(String ruleName, String ruleType) {
        return RULE_TYPE.equals(ruleType) || STRATEGY_NAME.equals(ruleName);
    }

}
