package com.trs.police.profile.schema.field;

import com.trs.police.common.core.entity.Label;
import com.trs.police.common.core.utils.*;
import com.trs.police.common.core.vo.CodeNamePCodeVO;
import com.trs.police.common.core.vo.TreeVO;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.profile.mapper.LabelMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * jsonSchema格式
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FormField extends BaseField {

    private static final long serialVersionUID = -1896009757256949795L;

    private FormFieldSchema formSchema;

    /**
     * formSchema
     *
     * @return {@link FormFieldSchema}
     */
    public FormFieldSchema getFormSchema() {
        if (Objects.isNull(formSchema)) {
            return null;
        }
        //如果是字典类型初始化字典结构
        if (isDictValue()) {
            formSchema.initDictSchema(dict);
        }
        //如果是树形类型初始化树形结构
        if (isTreeValue()) {
            formSchema.initTreeSchema(tree);
        }
        return formSchema;
    }


    /**
     * 回填默认值
     *
     * @param value value
     */
    public void fillDefaultValue(Object value) {
        if (value == null) {
            formSchema.fillDefaultValue(null);
            return;
        }
        //json id 数组
        switch (db.getJdbcType()) {
            case JSON_STRING_ARRAY:
                List<String> strings = JsonUtil.parseArray(value.toString(), String.class);
                if (strings.isEmpty()) {
                    strings = List.of();
                }
                formSchema.fillDefaultValue(strings);
                break;
            case JSON_ID_ARRAY:
                List<Long> ids = JsonUtil.parseArray(value.toString(), Long.class);
                if (this.isTreeValue()) {
                    this.fillDefaultValueWithJsonIdArray(ids, this.getTree());
                } else {
                    formSchema.fillDefaultValue(ids);
                }
                break;
            case JSON_PATH_ID_ARRAY:
                List<Object> deptObjects = JsonUtil.objectToArray(value, Object.class);
                List<Long> deptIds = deptObjects.stream().map(e -> {
                    List<Long> result = JsonUtil.objectToArray(e, Long.class);
                    return com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(result) ? null : result.get(result.size() - 1);
                }).filter(e -> Objects.nonNull(e)).collect(Collectors.toList());
                if (this.isTreeValue()) {
                    this.fillDefaultValueWithJsonIdArray(deptIds, this.getTree());
                } else {
                    formSchema.fillDefaultValue(deptIds);
                }
                break;
            //json图片数组
            case JSON_IMAGE_ARRAY:
                List<FileInfoVO> images = JsonUtil.parseArray(value.toString(), FileInfoVO.class);
                formSchema.fillDefaultValue(images);
                break;
            //处理时间戳
            case TIMESTAMP:
                Timestamp ts = (Timestamp) value;
                formSchema.fillDefaultValue(ts.getTime());
                break;
            //时间格式化
            case TIME_STRING:
                if(value instanceof LocalDateTime){
                    formSchema.fillDefaultValue(((LocalDateTime)value).format(TimeUtil.DEFAULT_TIME_PATTERN));
                }else if(value instanceof Timestamp){
                    formSchema.fillDefaultValue(DateUtil.utcToLocalDateTime(((Timestamp) value).getTime()).format(TimeUtil.DEFAULT_TIME_PATTERN));
                }else {
                    formSchema.fillDefaultValue(value);
                }
                break;
            default:
                formSchema.fillDefaultValue(value);
                break;
        }
    }

    /**
     * 回填树形结构的数据
     *
     * @param ids  数据库中存储的id数组
     * @param tree 树形结构的相关配置
     */
    public void fillDefaultValueWithJsonIdArray(List<Long> ids, TreeProperty tree) {
        //标签
        if (LABEL.equals(tree.getType())) {
            List<TreeVO> all = BeanUtil.getBean(LabelMapper.class).findByModuleAndSubjectId(tree.getRoot())
                .stream().map(Label::toVO).collect(Collectors.toList());
            formSchema.fillDefaultValue(TreeBuilder.buildTree(all, ids));
        }
        //单部门，不能拼接路径
        if (DEPT_ID_WITH_OUT_PATH.equals(tree.getType())) {
            List<TreeVO> all = new ArrayList<>(BeanUtil.getBean(PermissionService.class)
                    .getDeptByCodePrefix(StringUtil.getPrefixCode(tree.getRoot())));
            formSchema.fillDefaultValue(TreeBuilder.buildTree(all, ids));
        }
        //部门
        else if (DEPT.equals(tree.getType())) {
            List<TreeVO> all = new ArrayList<>(BeanUtil.getBean(PermissionService.class)
                .getDeptByCodePrefix(StringUtil.getPrefixCode(tree.getRoot())));
            formSchema.fillDefaultValue(TreeBuilder.buildTree(all, ids));
        }
        // 码表树
        else if (DICT.equals(tree.getType())) {
            String[] strings = tree.getRoot().split("&&");
            List<TreeVO> all = BeanUtil.getBean(DictService.class).getDictListByTypeList(Collections.singletonList(strings[0]))
                    .stream().map(CodeNamePCodeVO::toVo).collect(Collectors.toList());
            formSchema.fillDefaultValue(TreeBuilder.buildTree(all, ids));
        }
    }

}
