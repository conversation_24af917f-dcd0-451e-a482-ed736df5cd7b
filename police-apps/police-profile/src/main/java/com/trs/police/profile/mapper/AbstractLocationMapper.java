package com.trs.police.profile.mapper;

import com.trs.police.profile.domain.entity.JQ;
import com.trs.police.profile.domain.entity.JqFkxx;

import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/2
 */
public interface AbstractLocationMapper {

    /**
     * 查询最新警情
     *
     * @param beginTime 开始时间
     * @param tableName 表名
     * @return 结果集
     */
    List<JQ> selectNew(@Param("beginTime") LocalDateTime beginTime, @Param("tableName") String tableName);

    /**
     * 查询警情
     *
     * @param bh 接警单编号
     * @param tableName 表名
     * @return 结果集
     */
    JQ selectByBh(@Param("bh") String bh, @Param("tableName") String tableName);

    /**
     * 查询警情反馈信息
     *
     * @param bh 接警单编号
     * @param tableName 表名
     * @return 结果集
     */
    List<JqFkxx> selectFkxx(@Param("bh") String bh, @Param("tableName") String tableName);

    /**
     * 查询警情反馈信息
     *
     * @param bh        接警单编号
     * @param czlbbh    操作类别编号
     * @param tableName 表名
     * @return 结果集
     */
    default List<JqFkxx> selectFkxx(@Param("bh") String bh, @Param("czlbbh") String czlbbh, @Param("tableName") String tableName){
        return selectFkxx(bh, tableName);
    }

    /**
     * 查询新警情反馈信息
     *
     * @param beginTime 开始时间
     * @param tableName 表名
     * @return 结果集
     */
    List<JqFkxx> selectFkxxNew(@Param("beginTime") LocalDateTime beginTime, @Param("tableName") String tableName);
}
