package com.trs.police.profile.service;

import com.trs.police.common.core.dto.DictDto;
import com.trs.police.profile.domain.vo.BusinessTypeVO;
import java.util.List;

/**
 * 业务参数
 *
 * <AUTHOR>
 * @since 2022/10/10 9:59
 **/
public interface BusinessService {

    /**
     * 获取业务参数
     *
     * @param moduleId 模块类型
     * @return 数据
     */
    List<BusinessTypeVO> getBusinessType(String moduleId);

    /**
     * 获取列表
     *
     * @param type 类型
     * @return 列表
     */
    List<DictDto> getDictListByType(String type);

    /**
     * 新获取列表
     *
     * @param type 类型
     * @return 列表
     */
    List<DictDto> getDictTreeByType(String type);
}
