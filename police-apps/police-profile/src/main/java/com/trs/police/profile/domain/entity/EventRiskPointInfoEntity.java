package com.trs.police.profile.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import com.trs.police.common.core.handler.typehandler.JsonToListLongListHandler;
import com.trs.police.common.core.handler.typehandler.JsonToLongListHandler;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 自贡事件档案风险点信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_profile_event_risk_point_info",autoResultMap = true)
public class EventRiskPointInfoEntity extends AbstractBaseEntity {
    /**
     * 事件id
     */
    private Long eventId;
    /**
     * 事件类别
     */
    @TableField(value = "event_label",typeHandler = JsonToLongListHandler.class)
    private List<Long> eventLabel;
    /**
     * 事件来源
     */
    private Integer source;
    /**
     * 指向时间
     */
    @TableField(fill = FieldFill.INSERT, value = "related_time")
    private LocalDateTime relatedTime;
    /**
     * 指向结束时间
     */
    @TableField(fill = FieldFill.INSERT, value = "related_end_time")
    private LocalDateTime relatedEndTime;
    /**
     * 发生地址
     */
    private String occurAddress;
    /**
     * 维权地址
     */
    private String relatedAddress;
    /**
     * 所在区域
     */
    private String areaId;
    /**
     * 事件级别
     */
    private Integer level;
    /**
     * 归属地
     */
    private String belongLocation;
    /**
     * 风险等级
     */
    private Integer riskLevel;
    /**
     * 主管单位
     */
    private String controlStation;
    /**
     * 主责警种
     */
    private String controlPolice;
    /**
     * 来源时间
     */
    @TableField(fill = FieldFill.INSERT, value = "source_time")
    private LocalDateTime sourceTime;
    /**
     * 主管单位（配合）
     */
    @TableField(value = "control_station_cooperate",typeHandler = JsonToListLongListHandler.class)
    private List<List<Long>> controlStationCooperate;
    /**
     * 主责警钟（配合）
     */
    @TableField(value = "control_police_cooperate",typeHandler = JsonToListLongListHandler.class)
    private List<List<Long>> controlPoliceCooperate;
    /**
     * 警种类型
     */
    private String policeKind;
    /**
     * 事件指向地点敏感度
     */
    private Integer relatedAddressMgd;
    /**
     * 事件指向地点
     */
    private Integer eventTargetAddress;
    /**
     * 是否删除
     */
    private Integer deleted;

}
