package com.trs.police.profile.domain.dto;

import com.trs.common.base.PreConditionCheck;
import com.trs.common.exception.ServiceException;
import com.trs.police.common.core.dto.BaseListDTO;
import lombok.Data;

/**
 * @ClassName RiskScoreDeductPointsDTO
 * @Description 风险分数扣分dto
 * <AUTHOR>
 * @Date 2024/9/18 17:39
 **/
@Data
public class RiskScoreDeductPointsDTO extends BaseListDTO {

    /**
     * 扣分类型
     */
    private String deductType;

    /**
     * 对象类型
     */
    private String objType;

    /**
     * 对象id
     */
    private String objId;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 是否失效
     * 1：失效
     * 0：未失效
     */
    private Integer invalid;

    /**
     * 扣分日期
     */
    private String deductDate;

    @Override
    protected boolean checkParams() throws ServiceException {
        PreConditionCheck.checkNotEmpty(this.getObjType(), "档案类型不能为空！");
        PreConditionCheck.checkNotEmpty(this.getObjId(), "档案对应ID不能为空！");
        return false;
    }
}
