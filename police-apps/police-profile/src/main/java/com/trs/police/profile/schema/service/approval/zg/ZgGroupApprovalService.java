package com.trs.police.profile.schema.service.approval.zg;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.constant.log.OperateModule;
import com.trs.police.common.core.constant.log.Operation;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.utils.AreaUtils;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.openfeign.starter.service.ApprovalService;
import com.trs.police.common.openfeign.starter.service.OperationLogService;
import com.trs.police.common.openfeign.starter.vo.ApprovalActionVO;
import com.trs.police.common.openfeign.starter.vo.ApprovalRequest;
import com.trs.police.common.openfeign.starter.vo.ProfileApprovalDetail;
import com.trs.police.profile.domain.entity.Group;
import com.trs.police.profile.mapper.GroupMapper;
import com.trs.police.profile.schema.service.approval.ProfileGroupApprovalService;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 自贡群体档案审批服务
 *
 * <AUTHOR>
 */
@Component
public class ZgGroupApprovalService extends ProfileGroupApprovalService {

    @Autowired
    private GroupMapper groupMapper;

    @Autowired
    private ZgProfileApprovalService zgProfileApprovalService;

    @Autowired
    private ApprovalService approvalService;

    @Autowired
    private OperationLogService operationLogService;

    @Override
    public void approval(Long relatedId, @Nullable Consumer<ApprovalRequest> requestParamsSetter) {
        Group entity = groupMapper.selectById(relatedId);

        // 警种现在在的为准
        List<Long> kind = Optional.ofNullable(entity.getPoliceKind())
                .orElse(new ArrayList<>());
        JSONObject saved = StringUtils.isEmpty(entity.getApprovalDetail())
                ? new JSONObject()
                : JSONObject.parseObject(entity.getApprovalDetail());
        List<Long> add = kind.stream()
                .filter(k -> !saved.containsKey(k.toString()))
                .collect(Collectors.toList());

        // 发起审批
        Map<String, ProfileApprovalDetail> detailMap = zgProfileApprovalService.buildApprovalMap(
                add, OperateModule.PROFILE_GROUP_V2, entity.getId(), entity.getName(), getConfig()
        );

        // 新的加旧的
        kind.stream()
                .filter(k -> saved.containsKey(k.toString()))
                .forEach(o -> {
                    detailMap.put(o.toString(), saved.getObject(o.toString(), ProfileApprovalDetail.class));
                });


        // 修改状态
        entity.setApprovalDetail(JSON.toJSONString(detailMap));
        groupMapper.updateById(entity);
    }

    @Override
    public void finishApproval(ApprovalActionVO actionVO) {
        Long id = actionVO.getId();
        Group entity = groupMapper.selectById(id);
        if (Objects.isNull(entity)) {
            return;
        }
        String result = zgProfileApprovalService.editDetail(actionVO, entity.getApprovalDetail());
        if (Objects.equals(result, entity.getApprovalDetail())) {
            return;
        }
        entity.setApprovalDetail(result);
        groupMapper.updateById(entity);

        String title = JSONObject.toJSONString(Map.of("title", actionVO.getTitle()==null?"":actionVO.getTitle()));
        operationLogService.publicCreateOperationLogWithApprovalId(id, null, OperateModule.PROFILE_GROUP_V2, Operation.APPROVAL_RESPONSE, 101L, title, actionVO.getOperateUserId(), actionVO.getOperateUserDeptId(), actionVO.getApprovalId());
    }

    @Override
    public void reApproval(Long id, Long policeKind) {
        Group entity = groupMapper.selectById(id);

        // 警种现在在的为准
        List<Long> kind = Optional.ofNullable(entity.getPoliceKind())
                .orElse(new ArrayList<>());
        JSONObject saved = StringUtils.isEmpty(entity.getApprovalDetail())
                ? new JSONObject()
                : JSONObject.parseObject(entity.getApprovalDetail());

        Map<String, ProfileApprovalDetail> detailMap = zgProfileApprovalService.buildApprovalMap(
                Arrays.asList(policeKind), OperateModule.PROFILE_GROUP_V2, entity.getId(), entity.getName(), getConfig()
        );

        // 新的加旧的
        kind.stream()
                .filter(k -> saved.containsKey(k.toString()) && !Objects.equals(k, policeKind))
                .forEach(o -> {
                    detailMap.put(o.toString(), saved.getObject(o.toString(), ProfileApprovalDetail.class));
                });

        // 修改状态
        entity.setApprovalDetail(JSON.toJSONString(detailMap));
        groupMapper.updateById(entity);
    }

    private String getConfig() {
        CurrentUser user = AuthHelper.getCurrentUser();
        AreaUtils.Level level = AreaUtils.level(user.getDept().getDistrictCode());
        if (AreaUtils.Level.COUNTY.equals(level)) {
            return "ZG_PROFILE_GROUP_COUNTY";
        } else if (AreaUtils.Level.CITY.equals(level)) {
            return "ZG_PROFILE_GROUP_CITY";
        } else {
            throw new TRSException("未知的组织类型");
        }
    }
}