package com.trs.police.profile.domain.dto.promotion;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 职级晋升评估请求DTO
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PromotionEvaluationRequest {

    /**
     * 警员档案ID
     */
    @NotNull(message = "警员档案ID不能为空")
    private Long policeId;

    /**
     * 警员身份证号
     */
    private String idNumber;

    /**
     * 当前职级代码
     */
    private Integer currentRankCode;

    /**
     * 目标晋升职级代码（可选，如果不传则自动根据映射关系确定）
     */
    private Integer targetRankCode;



    /**
     * 是否强制重新计算（默认false，会使用缓存结果）
     */
    @Builder.Default
    private Boolean forceRecalculate = false;

    /**
     * 扩展参数（用于传递额外的业务参数）
     */
    private Object extendParams;
}
