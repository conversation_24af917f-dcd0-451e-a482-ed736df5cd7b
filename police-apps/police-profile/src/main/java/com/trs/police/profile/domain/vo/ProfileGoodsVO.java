package com.trs.police.profile.domain.vo;

import com.alibaba.excel.metadata.data.WriteCellData;
import lombok.Data;

/**
 * Description: 人档导出-人
 *
 * @author: lv.bo
 * @create: 2024-03-04 09:40
 */
@Data
public class ProfileGoodsVO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 物品名称
     */
    private String name;

    /**
     * 审核状态
     */
    private String reviewStatus;

    /**
     * 物品类别
     */
    private String category;

    /**
     * 物品细类
     */
    private String subCategory;

    /**
     * 物品状态 1 消除 0 生效
     */
    private String status;

    /**
     * 关注状态
     */
    private String focusStatus;

    /**
     * 涉及事件数
     */
    private Integer relatedEventNum;

    /**
     * 位置
     */
    private String location;

    /**
     * 物品基本信息描述
     */
    private String goodsDescribe;

    /**
     * 头像
     */
    private WriteCellData<Void> photo;
    /**
     * 创建人id
     */
    private Long createUserId;

    /**
     * 创建人名称
     */
    private String createTrueName;

    /**
     * 创建单位id
     */
    private Long createDeptId;

    /**
     * 创建单位code
     */
    private String createDeptCode;

    /**
     * 创建单位名称
     */
    private String createDeptName;

    /**
     * 录入时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;
    /**
     * 责任人名称
     */
    private String responsibleName;

    /**
     * 责任人电话
     */
    private String responsibleTel;

    /**
     * 责任人职务
     */
    private String responsiblePosition;

    /**
     * 政府领导人名称
     */
    private String govLeaderName;

    /**
     * 政府领导人电话
     */
    private String govLeaderTel;

    /**
     * 政府领导人职务
     */
    private String govLeaderPosition;
}
