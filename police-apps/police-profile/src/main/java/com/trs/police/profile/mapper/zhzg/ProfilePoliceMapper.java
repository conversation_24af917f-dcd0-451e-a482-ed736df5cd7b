package com.trs.police.profile.mapper.zhzg;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.profile.domain.dto.zhzg.HzBigScreenDto;
import com.trs.police.profile.domain.dto.zhzg.HzPoliceDto;
import com.trs.police.profile.domain.entity.zhzg.ProfilePolice;
import com.trs.police.profile.domain.vo.HzPoliceVO;
import com.trs.police.statistic.domain.bean.CountItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 警员档案表
 *
 * <AUTHOR>
 */
@Mapper
public interface ProfilePoliceMapper extends BaseMapper<ProfilePolice> {

    /**
     * 列表查询
     *
     * @param page page
     * @param dto dto
     * @return 结果
     */
    IPage<HzPoliceVO> selectPageList(@Param("page") Page page,@Param("dto") HzPoliceDto dto);

    /**
     * 统计政治面貌数据
     *
     * @param hzBigScreenDto dto
     * @return 数据
     */
    List<CountItem> statisticZzmm(@Param("dto") HzBigScreenDto hzBigScreenDto);

    /**
     * 统计年龄数据数据
     *
     * @param hzBigScreenDto dto
     * @return 结果
     */
    List<CountItem> statisticAge(@Param("dto") HzBigScreenDto hzBigScreenDto);

    /**
     * 职级情况
     *
     * @param dto dto
     * @param codes codes
     * @return 结果
     */
    List<CountItem> zjqkStatistic(@Param("dto") HzBigScreenDto dto,@Param("codes") List<Long> codes);

    /**
     * 统计学历数据
     *
     * @param hzBigScreenDto dto
     * @return 结果
     */
    List<CountItem> statisticXl(@Param("dto") HzBigScreenDto hzBigScreenDto);


    /**
     * 数据总览
     *
     * @param hzBigScreenDto dto
     * @return 结果
     */
    List<CountItem> dataOverview(@Param("dto") HzBigScreenDto hzBigScreenDto);
}
