package com.trs.police.profile.domain.dto;

import com.trs.common.base.PreConditionCheck;
import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.common.pojo.BaseDTO;
import lombok.Data;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/3/7 16:36
 * @since 1.0
 */
@Data
public class RiskLabelDeleteDTO extends BaseDTO {

    private String ids;

    private String type;

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL> 创建时间：2020-09-10 15:49
     */
    @Override
    protected boolean checkParams() throws ServiceException {
        PreConditionCheck.checkNotEmpty(getIds(), new ParamInvalidException("待删除的ID串不能为空"));
        PreConditionCheck.checkNotEmpty(getType(), new ParamInvalidException("类型不能为空"));
        return true;
    }
}
