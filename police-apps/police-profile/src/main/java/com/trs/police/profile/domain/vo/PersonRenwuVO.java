package com.trs.police.profile.domain.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 人员关联任务
 *
 * <AUTHOR>
 * @date 2025/4/27
 */
@Data
public class PersonRenwuVO extends ProfileExportListVO{
    /**
     * 任务标题
     */
    private String dataTitle;

    /**
     * 任务编号
     */
    private String taskNo;

    /**
     * 任务来源
     */
    private String taskSource;

    /**
     * 指向时间
     */
    private String targetTime;

    /**
     * 指向地点
     */
    private String zxdd;

    /**
     * 录入时间
     */
    private LocalDateTime crTime;

    /**
     * 录入时间
     */
    private String crTimeStr;
}
