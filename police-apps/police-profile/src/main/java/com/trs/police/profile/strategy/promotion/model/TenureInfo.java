package com.trs.police.profile.strategy.promotion.model;

import java.time.LocalDate;

/**
 * 任职信息接口
 * <p>
 * 用于统一处理职级和职务的任职信息，提供通用的任职年限评估能力。
 * 通过抽象接口的方式，支持不同类型的任职信息（职级、职务等）的统一处理。
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
public interface TenureInfo {

    /**
     * 获取任职开始时间
     *
     * @return LocalDate 任职开始时间，如果无法获取则返回null
     */
    LocalDate getStartDate();

    /**
     * 获取任职名称
     * <p>
     * 对于职级信息，返回职级名称（如"三级警长"）
     * 对于职务信息，返回职务级别名称（如"正科级"）
     *
     * @return String 任职名称
     */
    String getTenureName();

    /**
     * 获取任职类型描述
     * <p>
     * 用于区分不同类型的任职信息，便于日志记录和错误提示。
     *
     * @return String 任职类型描述，如"职级"、"职务级别"等
     */
    String getTenureType();
}
