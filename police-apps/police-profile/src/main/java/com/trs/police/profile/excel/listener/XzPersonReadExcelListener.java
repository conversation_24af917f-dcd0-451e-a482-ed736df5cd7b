package com.trs.police.profile.excel.listener;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.profile.domain.entity.Person;
import com.trs.police.profile.domain.entity.person.PersonDcqk;
import com.trs.police.profile.domain.entity.person.PersonGkcs;
import com.trs.police.profile.domain.entity.person.PersonGzcs;
import com.trs.police.profile.excel.enums.XzPersonImportFieldEnum;
import com.trs.police.profile.mapper.person.PersonDcqkMapper;
import com.trs.police.profile.mapper.person.PersonGkcsMapper;
import com.trs.police.profile.mapper.person.PersonGzcsMapper;
import com.trs.police.profile.mapper.person.PersonRiskOtherMapper;
import lombok.Builder;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 * @date 2025/1/9
 */
@Builder
public class XzPersonReadExcelListener extends BasePersonReadExcelListener{

    private PersonRiskOtherMapper personRiskOtherMapper;
    private PersonGzcsMapper personGzcsMapper;
    private PersonDcqkMapper personDcqkMapper;
    private PersonGkcsMapper personGkcsMapper;

    @Override
    protected void init(Map<Integer, String> headMap) {
        super.init(headMap);
        headMap.entrySet().stream().sorted(Map.Entry.comparingByKey())
                .forEach(entry -> {
                    XzPersonImportFieldEnum xzPersonImportFieldEnum = XzPersonImportFieldEnum.cnNameOf(entry.getValue());
                    if (Objects.nonNull(xzPersonImportFieldEnum)) {
                        columnMap.put(entry.getKey(), xzPersonImportFieldEnum);
                    }
                });
    }

    @Override
    protected boolean checkHeaderOther(Map<Integer, String> headMap) {
        return super.checkHeaderOther(headMap) && headMap.values().stream().anyMatch(value -> Objects.nonNull(XzPersonImportFieldEnum.cnNameOf(value)));
    }

    @Override
    protected void checkOther(Map<Integer, String> rows, AtomicBoolean validate) {
        checkGkcs(rows, validate);
    }


    @Override
    protected void processOther(Person person, Map<String, String> personMap) {
        super.processOther(person, personMap);
        processGkcs(person, personMap);
        processDetail(person, personMap, personGzcsMapper, PersonGzcs.class);
        processDetail(person, personMap, personDcqkMapper, PersonDcqk.class);
    }

    @Override
    protected Long policeKind() {
        return 5L;
    }

    protected void checkGkcs(Map<Integer, String> rows, AtomicBoolean validate) {
        columnMap.entrySet().stream().filter(entry -> XzPersonImportFieldEnum.GKCS.equals(entry.getValue())).findFirst()
                .ifPresent(entry -> {
                    String values = rows.get(entry.getKey()) == null ? "" : rows.get(entry.getKey());
                    if (StringUtils.isNotBlank(values)) {
                        String[] valueArr = values.split(";");
                        try {
                            for (String value : valueArr) {
                                String[] typeAndDetail = value.split(":");
                                if(typeAndDetail.length != 2 || StringUtils.isEmpty(typeAndDetail[0]) || TimeUtils.stringToDate(typeAndDetail[1], TimeUtils.YYYYMMDD) ==null){
                                    throw new Exception(value);
                                }
                            }
                        }catch (Exception e){
                            addValidFailResult(rows, validate,"非法" + XzPersonImportFieldEnum.GKCS.getCnName() + ":"+ e.getMessage());
                        }
                    }
                });
    }

    protected void processGkcs(Person person, Map<String, String> personMap) {
        PersonGkcs entity = JsonUtil.parseObject(JsonUtil.toJsonString(personMap), PersonGkcs.class);
        entity.fillAuditFields(currentUser);
        entity.setPersonId(person.getId());
        entity.setPoliceKind(policeKind());
        String detail = entity.getDetail();
        if (StringUtils.isNotBlank(detail)) {
            Arrays.stream(detail.split(";")).forEach(oneDetail -> {
                String[] typeAndDetail = oneDetail.split(":");
                if (typeAndDetail.length == 2) {
                    Long count = personGkcsMapper.selectCount(new QueryWrapper<PersonGkcs>()
                            .eq("person_id", person.getId())
                            .eq("police_kind", policeKind())
                            .eq("expiration_date", typeAndDetail[1])
                            .eq("detail", typeAndDetail[0]));
                    if (count == 0) {
                        entity.setId(null);
                        entity.setDetail(typeAndDetail[0]);
                        entity.setExpirationDate(TimeUtils.stringToDate(typeAndDetail[1], TimeUtils.YYYYMMDD));
                        personGkcsMapper.insert(entity);
                    }
                }
            });
        }
    }
}
