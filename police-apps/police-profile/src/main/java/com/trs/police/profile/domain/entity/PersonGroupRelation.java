package com.trs.police.profile.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.Data;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR>
 * @date 创建时间：2024/2/18 14:53
 * @version 1.0
 * @since 1.0
 */
@Data
@TableName("t_profile_person_group_relation")
public class PersonGroupRelation extends AbstractBaseEntity {

    private static final long serialVersionUID = 6694168282783693759L;

    private Long groupId;

    private Long personId;

    private Integer activityLevel;

    private Integer groupWork;

    /**
     * 警种
     */
    private Integer policeKind;
}