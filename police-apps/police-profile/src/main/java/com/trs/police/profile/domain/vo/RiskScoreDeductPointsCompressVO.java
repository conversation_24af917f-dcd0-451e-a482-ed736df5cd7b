package com.trs.police.profile.domain.vo;

import com.trs.police.profile.schema.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * @ClassName RiskScoreDeductPointsCompressVO
 * @Description 压缩处理结果VO
 * <AUTHOR>
 * @Date 2024/10/9 9:48
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class RiskScoreDeductPointsCompressVO extends BaseVO {

    /**
     * 对象类型
     */
    private String objType;

    /**
     * 对象值
     */
    private String objId;

    /**
     * 扣分类型
     */
    private String deductType;

    /**
     * 扣分描述
     */
    private String deductDesc;

    /**
     * 扣分日期
     */
    private String deductDate;

    /**
     * 扣分记录创建时间
     */
    private LocalDateTime deductTime;

    /**
     * 总有效扣分之和
     */
    private Double totalValidDeductScore;

}
