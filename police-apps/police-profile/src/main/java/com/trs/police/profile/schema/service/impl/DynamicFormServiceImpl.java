package com.trs.police.profile.schema.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.trs.common.exception.ServiceException;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.constant.log.Operation;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.openfeign.starter.configure.UserContext;
import com.trs.police.profile.domain.statement.InsertStatement;
import com.trs.police.profile.domain.statement.UpdateStatement;
import com.trs.police.profile.properties.ProfileProperties;
import com.trs.police.profile.schema.db.DatabaseRelation;
import com.trs.police.profile.schema.db.DatabaseRelationType;
import com.trs.police.profile.schema.db.DbRelationSchema;
import com.trs.police.profile.schema.mapper.SchemaMapper;
import com.trs.police.profile.schema.module.FormModuleSchema;
import com.trs.police.profile.schema.module.ModuleData;
import com.trs.police.profile.schema.module.ModuleEntity;
import com.trs.police.profile.schema.module.SchemaType;
import com.trs.police.profile.schema.service.*;
import com.trs.police.profile.schema.service.approval.ApprovalServiceFactory;
import com.trs.police.profile.schema.service.approval.common.EventApprovalService;
import com.trs.police.profile.schema.service.approval.common.PersonApprovalService;
import com.trs.police.profile.schema.vo.FormSchemaVO;
import com.trs.police.profile.util.OperationUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 动态表单
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DynamicFormServiceImpl implements DynamicFormService {

    public static final String OPERATION = "操作记录创建失败";
    @Resource
    private SchemaService schemaService;
    @Resource
    private SchemaMapper schemaMapper;
    @Resource
    private OperationUtil operationUtil;
    @Autowired
    private ProfileProperties controlProperties;
    @Autowired
    private PersonApprovalService personApprovalService;
    @Autowired
    private EventApprovalService goodsApprovalService;

    @Autowired
    private SchemaSubject schemaSubject;
    @Autowired
    private SchemaOperateFactory schemaOperateFactory;

    @Autowired
    private ApprovalServiceFactory approvalServiceFactory;

    private static final String ID = "id";

    @Override
    public FormSchemaVO getFormSchema(Long moduleId, Long keyId) {
        final ModuleEntity moduleEntity = schemaService.getModuleSchema(moduleId);
        //未配置schema时，返回空
        final FormModuleSchema formModule = moduleEntity.getFormSchema();
        if (formModule == null) {
            log.info("解析[{}]表单配置失败！", moduleEntity.getCnName());
            return null;
        }
        Map<String, Object> resultSet = null;
        if (Objects.nonNull(keyId) && Objects.nonNull(moduleEntity.getDatabaseRelation())) {
            resultSet = schemaMapper.doSingleSelect(formModule,
                moduleEntity.getDatabaseRelation(), keyId);
            if (Objects.nonNull(resultSet)) {
                formModule.fillDefaultValue(resultSet);
            }
        }
        FormSchemaVO formSchemaVO = formModule.toVoV2(keyId == null);
        if(resultSet != null){
            formSchemaVO.setId(resultSet.get(ID));
        }
        return formSchemaVO;
    }

    @Override
    public void modifyFormSchema(Long relatedId, ModuleData moduleData) {
        final Long moduleId = moduleData.getModuleId();
        ModuleEntity moduleEntity = schemaService.getModuleSchema(moduleId);
        FormModuleSchema formModuleSchema = moduleEntity.getFormSchema();
        //修改
        if (checkDataExist(formModuleSchema, moduleData)) {
            List<UpdateStatement> updateStatements = moduleData.toUpdateStatements(relatedId);

            Map<String, Object> oldValue = schemaMapper
                .doSingleSelect(moduleEntity.getFormSchema(), moduleEntity.getDatabaseRelation(), relatedId);
            updateStatements.forEach(u -> {
                u.setKeyName(ID);
                u.setKeyValue(oldValue.get(ID).toString());
                schemaMapper.doUpdate(u);
            });
            try {
                operationUtil.createUpdateFormLog(oldValue, moduleEntity, moduleData, relatedId, Operation.UPDATE_DATA);
            } catch (Exception e) {
                log.error(OPERATION + e.getMessage());
            }
            // 更新完后各个 模块回调处理
            schemaSubject.onChange(moduleId, relatedId);
        } else {
            Map<String, DbRelationSchema> relationSchemaMap = Map.of("",
                new DbRelationSchema(moduleEntity.getDatabaseRelation(), List.of(relatedId)));
            List<InsertStatement> insertStatements = moduleData.toInsertStatements(relationSchemaMap);
            insertStatements.forEach(i -> schemaMapper.doInsert(i));
            try {
                operationUtil.createInsertFormLog(relatedId, moduleEntity, moduleData, Operation.ADD);
            } catch (Exception e) {
                log.error(OPERATION + e.getMessage());
            }
        }

        //保存后操作
        schemaOperateFactory.getService(moduleEntity.getType()).ifPresent(s -> s.doAfterSave(relatedId, List.of(moduleData)));
    }

    private boolean checkDataExist(FormModuleSchema formModuleSchema, ModuleData data) {
        if (data.getData() != null && data.getData().get(ID) != null) {
            return schemaMapper.checkRecordExist(formModuleSchema.getTable(), data.getData().get(ID).asLong()) != 0;
        } else {
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, DbRelationSchema> saveAllSchema(List<ModuleData> data) {
        //所有模块数据按关联类型分组，按顺序插入
        List<ModuleEntity> moduleEntityList = data.stream().map(d -> schemaService.getModuleSchema(d.getModuleId())).collect(Collectors.toList());
        Map<DatabaseRelationType, List<ModuleData>> dataMap = data.stream().collect(Collectors.groupingBy(d -> {
            ModuleEntity module = moduleEntityList.stream().filter(m -> m.getId().equals(d.getModuleId())).findFirst().get();
            return module.getDatabaseRelation().getType();
        }));
        // 校验是否重复（如果有需要的话)
        schemaSubject.checkRepeat(dataMap.get(DatabaseRelationType.PRIMARY_KEY));
        //最先插入以json_id_array格式存储关联的数据，并返回插入表名-插入id数组及关联信息
        List<ModuleData> jsonIdArrayList = dataMap.get(DatabaseRelationType.JSON_ID_ARRAY);
        Map<String, DbRelationSchema> jsonIdMap = saveModuleList(jsonIdArrayList, null);
        //插入主表数据，返回主表id
        List<ModuleData> primaryKeyList = dataMap.get(DatabaseRelationType.PRIMARY_KEY);
        Map<String, DbRelationSchema> primaryMap = saveModuleList(primaryKeyList, jsonIdMap);
        //插入外键关联表
        List<ModuleData> foreignKeyList = dataMap.get(DatabaseRelationType.FOREIGN_KEY);
        Map<String, DbRelationSchema> foreignMap = saveModuleList(foreignKeyList, primaryMap);
        //最后插入关联表
        List<ModuleData> relatedTableList = dataMap.get(DatabaseRelationType.RELATION_TABLE);
        Map<String, DbRelationSchema> relatedTableMap = saveRelations(relatedTableList, primaryMap);

        //保存后操作
        String type = moduleEntityList.get(0).getType();
        primaryMap.values()
                .stream().filter(d -> !CollectionUtils.isEmpty(d.getIds()))
                .forEach(d -> d.getIds().stream().forEach(id ->
                        schemaOperateFactory.getService(type).ifPresent(s -> s.doAfterSave(id, data))
                ));

        //审批
        if (CollectionUtils.isNotEmpty(primaryKeyList)) {
            Long moduleId = primaryKeyList.get(0).getModuleId();
            approvalServiceFactory.of(moduleId).ifPresent(approvalService -> {
                primaryMap.forEach((key, value) -> {
                    value.getIds().forEach(id -> approvalService.approval(id, null));
                });
            });
        }

        // 操作日志
        CurrentUser user = Objects.nonNull(AuthHelper.getCurrentUser())
                ? AuthHelper.getCurrentUser()
                : UserContext.getCurrentUser();
        try {
            UserContext.setCurrentUser(user);
            operationUtil.createOperateLog(dataMap, jsonIdMap, primaryMap, foreignMap, relatedTableMap);
        } catch (Exception e) {
            log.error(OPERATION + e.getMessage(), e);
        } finally {
            UserContext.clear();
        }

        return primaryMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyAllSchema(Long relatedId, List<ModuleData> data) {
        Map<SchemaType, List<ModuleData>> collect = data.stream().collect(Collectors.groupingBy(ModuleData::getType));
            List<ModuleData> fromModuleDataList = collect.get(SchemaType.FORM_SCHEMA);
            if(CollectionUtils.isNotEmpty(fromModuleDataList)){
                fromModuleDataList.forEach(moduleData->modifyFormSchema(relatedId, moduleData));
            }
            List<ModuleData> listModuleDataList = collect.get(SchemaType.LIST_SCHEMA);
            if(CollectionUtils.isNotEmpty(listModuleDataList)){
                DynamicListService dynamicListService = BeanUtil.getBean(DynamicListService.class);
                listModuleDataList.forEach(moduleData->dynamicListService.modifyListSchema(relatedId, moduleData));
            }
            List<ModuleData> fileModuleDataList = collect.get(SchemaType.FILE_SCHEMA);
            if(CollectionUtils.isNotEmpty(fileModuleDataList)){
                DynamicFileService dynamicFileService = BeanUtil.getBean(DynamicFileService.class);
                fileModuleDataList.forEach(moduleData->dynamicFileService.modifyDynamicFileList(relatedId, moduleData.getModuleId(),JSONObject.parseArray(JsonUtil.toJsonString(moduleData.getData()), Long.class)));
            }
    }

    private Map<String, DbRelationSchema> saveModuleList(List<ModuleData> data,
        Map<String, DbRelationSchema> insertTableIdMap) {
        Map<String, DbRelationSchema> ids = new HashMap<>(0);
        if (Objects.nonNull(data)) {
            data.forEach(saveVO -> {
                switch (saveVO.getType()) {
                    case FORM_SCHEMA:
                        Map<String, Long> insertMap = saveSingleForm(saveVO, insertTableIdMap);
                        for (Map.Entry<String, Long> entry : insertMap.entrySet()) {
                            DbRelationSchema dbRelationSchema = new DbRelationSchema();
                            ModuleEntity module = schemaService.getModuleSchema(saveVO.getModuleId());
                            DatabaseRelation databaseRelation = module.getDatabaseRelation();
                            dbRelationSchema.setDbRelation(databaseRelation);
                            dbRelationSchema.setIds(List.of(entry.getValue()));
                            ids.put(entry.getKey(), dbRelationSchema);
                        }
                        break;
                    case LIST_SCHEMA:
                        Map<String, List<Long>> insertListMap = saveListSchema(saveVO, insertTableIdMap);
                        for (Map.Entry<String, List<Long>> entry : insertListMap.entrySet()) {
                            DbRelationSchema dbRelationSchema = new DbRelationSchema();
                            ModuleEntity module = schemaService.getModuleSchema(saveVO.getModuleId());
                            DatabaseRelation databaseRelation = module.getDatabaseRelation();
                            dbRelationSchema.setDbRelation(databaseRelation);
                            dbRelationSchema.setIds(entry.getValue());
                            ids.put(entry.getKey(), dbRelationSchema);
                        }
                        break;
                    default:
                }
            });
        }
        return ids;
    }

    private Map<String, DbRelationSchema> saveRelations(List<ModuleData> data,
        Map<String, DbRelationSchema> primaryMap) {
        Map<String, DbRelationSchema> ids = new HashMap<>(0);
        if(CollectionUtils.isEmpty(data)){
            return ids;
        }
        data.forEach(saveVO -> {
            ModuleEntity module = schemaService.getModuleSchema(saveVO.getModuleId());
            Long relatedId = primaryMap.get(module.getDatabaseRelation().getJoinFrom().getTable()).getIds().get(0);
            List<InsertStatement> insertStatements = JsonUtil.jsonNodeToList(saveVO.getData(), Object.class).stream()
                .map(singleData -> ModuleData.objectToInsertRelationStatements(relatedId, module, singleData))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
            insertStatements.forEach(schemaMapper::doInsert);
            DbRelationSchema dbRelationSchema = new DbRelationSchema();
            DatabaseRelation databaseRelation = module.getDatabaseRelation();
            dbRelationSchema.setDbRelation(databaseRelation);
            dbRelationSchema.setIds(insertStatements.stream().map(InsertStatement::getId).collect(Collectors.toList()));
            ids.put(saveVO.getName(), dbRelationSchema);
        });
        return ids;
    }

    private Map<String, Long> saveSingleForm(ModuleData moduleData, Map<String, DbRelationSchema> insertTableIdMap) {
        //新增
        if (Objects.isNull(moduleData.getData().get(ID))) {
            List<InsertStatement> insertStatements = moduleData.toInsertStatements(insertTableIdMap);
            insertStatements.forEach(i -> schemaMapper.doInsert(i));
            return insertStatements.stream()
                .collect(Collectors.toMap(InsertStatement::getTable, InsertStatement::getId));
        }
        return Collections.emptyMap();
    }

    private Map<String, List<Long>> saveListSchema(ModuleData moduleData,
        Map<String, DbRelationSchema> insertTableIdMap) {
        List<InsertStatement> insertStatements = moduleData.toListInsertStatements(insertTableIdMap);
        insertStatements.forEach(i -> schemaMapper.doInsert(i));
        return insertStatements.stream()
                .collect(Collectors.groupingBy(
                        InsertStatement::getTable,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                l -> l.stream().map(InsertStatement::getId).collect(Collectors.toList())
                        )
                ));
    }


    @Override
    public Long saveGroupOrPerson(String groupName, Long userId, Long deptId, String params) {
        CurrentUser user = new CurrentUser();
        user.setId(userId);
        DeptDto deptDto = new DeptDto();
        deptDto.setId(deptId);
        user.setDept(deptDto);
        UserContext.setCurrentUser(user);
        try {
            List<ModuleData> data = buildProfile(groupName, params);
            Map<String, DbRelationSchema> result = saveAllSchema(data);
            if (StringUtils.isNotEmpty(groupName)) {
                return result.get("t_profile_group").getIds().get(0);
            } else {
                //人员档案
                return result.get("t_profile_person").getIds().get(0);
            }
        } catch (Exception e) {
            log.error("新增群体或人员档案失败：{}", e.getMessage(), e);
        }
        return null;
    }

    private List<ModuleData> buildProfile(String groupName, String params) throws ServiceException {
        List<ModuleData> data = new ArrayList<>(0);
        ModuleData dto = new ModuleData();
        ObjectMapper mapper = new ObjectMapper();
        try {
            if (StringUtils.isNotEmpty(params)
                    && StringUtils.isEmpty(groupName)) {
                //人员信息
                dto.setName("人员信息");
                dto.setModuleId(3L);
                dto.setType(SchemaType.FORM_SCHEMA);
                JSONObject object = new JSONObject();
                object.put("idType", 1);
                object.put("photo", new ArrayList<>(0));
                JSONObject obj = JSONObject.parseObject(params);
                object.put("name", obj.getString("name"));
                object.put("idNumber", obj.getString("sfz"));
                object.put("personLabel", new ArrayList<>(0));
                object.put("personType", 0);
                object.put("tel", Collections.singletonList(obj.getString("sjh")));
                object.put("registerArea", obj.getString("hjdDm"));
                dto.setData(mapper.readTree(object.toJSONString()));
                data.add(dto);
                //相关群体
                dto = new ModuleData();
                dto.setName("相关群体");
                dto.setType(SchemaType.LIST_SCHEMA);
                dto.setModuleId(14L);
                JSONArray array = new JSONArray();
                JSONObject o = new JSONObject();
                o.put("id", obj.getLongValue("groupId"));
                array.add(o);
                dto.setData(mapper.readTree(array.toJSONString()));
                data.add(dto);
            } else {
                //群体材料
                dto.setName("群体材料");
                dto.setType(SchemaType.FILE_SCHEMA);
                dto.setModuleId(112L);
                JSONArray array = new JSONArray();
                dto.setData(mapper.readTree(array.toJSONString()));
                data.add(dto);
                //群体信息
                dto = new ModuleData();
                dto.setName("群体信息");
                dto.setModuleId(103L);
                dto.setType(SchemaType.FORM_SCHEMA);
                JSONObject object = new JSONObject();
                object.put("name", groupName);
                object.put("groupLabel", new ArrayList<>(0));
                dto.setData(mapper.readTree(object.toJSONString()));
                data.add(dto);
            }
        } catch (Exception e) {
            throw new ServiceException(e);
        }
        return data;
    }
}
