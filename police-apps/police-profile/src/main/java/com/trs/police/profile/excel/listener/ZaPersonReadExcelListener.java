package com.trs.police.profile.excel.listener;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.profile.domain.entity.Person;
import com.trs.police.profile.domain.entity.person.*;
import com.trs.police.profile.excel.enums.IPersonImportFieldEnum;
import com.trs.police.profile.excel.enums.ZaPersonImportFieldEnum;
import com.trs.police.profile.mapper.person.*;
import lombok.Builder;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 * @date 2025/1/9
 */
@Builder
public class ZaPersonReadExcelListener extends AbsPersonReadExcelListener{

    private PersonRiskZaMapper personRiskZaMapper;
    private PersonGzcsMapper personGzcsMapper;
    private PersonXsbxMapper personXsbxMapper;
    private PersonDcqkMapper personDcqkMapper;
    private PersonDzControlMapper personDzControlMapper;

    /**
     * 管控等级
     */
    private Map<String, Integer> controlLevelMap;

    private Map<String, Integer> sksflryMap;

    private Map<String, Integer> workDutyMap;

    private Map<String, Integer> gzcsMap;

    private Map<String, Integer> xsbxMap;

    private Map<String, Integer> dcqkMap;

    @Override
    protected void init(Map<Integer, String> headMap) {
        controlLevelMap = dictMap("profile_person_control_level_za");
        sksflryMap = dictMap("yes_or_not");
        workDutyMap = dictMap("profile_person_duty_type");
        gzcsMap = dictMap("profile_person_gzcs_type_za");
        xsbxMap = dictMap("profile_person_xsbx_type_za");
        dcqkMap = dictMap("profile_person_dcqk_type_za");
        headMap.entrySet().stream().sorted(Map.Entry.comparingByKey())
                .forEach(entry -> {
                    ZaPersonImportFieldEnum zaPersonImportFieldEnum = ZaPersonImportFieldEnum.cnNameOf(entry.getValue());
                    if (Objects.nonNull(zaPersonImportFieldEnum)) {
                        columnMap.put(entry.getKey(), zaPersonImportFieldEnum);
                    }
                });
    }

    @Override
    protected boolean checkHeaderOther(Map<Integer, String> headMap) {
        return headMap.values().stream().anyMatch(value -> Objects.nonNull(ZaPersonImportFieldEnum.cnNameOf(value)));
    }

    @Override
    protected void checkOther(Map<Integer, String> rows, AtomicBoolean validate) {
        checkDict(rows, validate, ZaPersonImportFieldEnum.CONTROL_LEVEL, controlLevelMap);
        checkDict(rows, validate, ZaPersonImportFieldEnum.SKSFLRY, sksflryMap);
        checkDict(rows, validate, ZaPersonImportFieldEnum.WORK_DUTY_TYPE, workDutyMap);
        myCheckDict(rows, validate, ZaPersonImportFieldEnum.GZCS, gzcsMap);
        myCheckDict(rows, validate, ZaPersonImportFieldEnum.XSBX, xsbxMap);
        myCheckDict(rows, validate, ZaPersonImportFieldEnum.DCQK, dcqkMap);
    }


    @Override
    protected void processOther(Person person, Map<String, String> personMap) {
        process(person, personMap, personRiskZaMapper, PersonRiskZa.class);
        process(person, personMap, personDzControlMapper, PersonDzControl.class);
        myProcessDetail(person, personMap, personGzcsMapper, PersonGzcs.class);
        myProcessDetail(person, personMap, personDcqkMapper, PersonDcqk.class);
        myProcessDetail(person, personMap, personXsbxMapper, PersonXsbx.class);
    }

    @Override
    protected Long policeKind() {
        return 4L;
    }

    protected void myCheckDict(Map<Integer, String> rows, AtomicBoolean validate, IPersonImportFieldEnum fieldEnum, Map<String, Integer> dictMap) {
        // 治安工作措施、打处情况、现实动向格式：电话劝阻：XXXX；见面稳控：XXXXX
        columnMap.entrySet().stream().filter(entry -> fieldEnum.equals(entry.getValue())).findFirst()
                .ifPresent(entry -> {
                    String values = rows.get(entry.getKey()) == null ? "" : rows.get(entry.getKey());
                    if (StringUtils.isNotBlank(values)) {
                        String newValues = "";
                        String[] valueArr = values.split("；");
                        try {
                            for (String value : valueArr) {
                                String[] typeAndDetail = value.split("：");
                                if (typeAndDetail.length == 2){
                                    String type = typeAndDetail[0];
                                    Integer code = dictMap.get(type);
                                    if(code!=null){
                                        newValues += code + ":" + typeAndDetail[1] + ";";
                                    }else {
                                        throw new Exception(value);
                                    }
                                }else {
                                    throw new Exception(value);
                                }
                            }
                            rows.put(entry.getKey(), newValues);
                        }catch (Exception e){
                            addValidFailResult(rows, validate, "非法" + fieldEnum.getCnName() + ":" + e.getMessage());
                        }

                    }
                });
    }

    protected <T extends PersonRelatedType> void myProcessDetail(Person person, Map<String, String> personMap, PersonRelatedBaseMapper<T> personRelatedBaseMapper, Class<T> c) {
        T entity = JsonUtil.parseObject(JsonUtil.toJsonString(personMap), c);
        entity.fillAuditFields(currentUser);
        entity.setPersonId(person.getId());
        entity.setPoliceKind(policeKind());
        String detail = entity.getDetail();
        if (StringUtils.isNotBlank(detail)) {
            Arrays.stream(detail.split(";")).forEach(oneDetail -> {
                String[] typeAndDetail = oneDetail.split(":");
                if (typeAndDetail.length == 2) {
                    Long count = personRelatedBaseMapper.selectCount(new QueryWrapper<T>()
                            .eq("person_id", person.getId())
                            .eq("police_kind", policeKind())
                            .eq("detail", typeAndDetail[1])
                            .eq("type", typeAndDetail[0]));
                    if (count == 0) {
                        entity.setId(null);
                        entity.setDetail(typeAndDetail[1]);
                        entity.setType(Integer.parseInt(typeAndDetail[0]));
                        personRelatedBaseMapper.insert(entity);
                    }
                }
            });
        }
    }
}
