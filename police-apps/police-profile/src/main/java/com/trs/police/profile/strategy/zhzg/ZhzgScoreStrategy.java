package com.trs.police.profile.strategy.zhzg;

import com.trs.police.profile.domain.dto.zhzg.ScoreRankDTO;
import com.trs.police.profile.domain.dto.zhzg.ZhzgPersonArchiveDTO;
import com.trs.police.profile.domain.dto.zhzg.ZhzgScoreRuleDTO;
import com.trs.police.profile.domain.vo.zhzg.ZhzgRuleScoreDetailVO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * 智慧政工积分计算策略接口
 */
public interface ZhzgScoreStrategy {

    /**
     * 计算积分
     *
     * @param personArchive 人员档案信息
     * @param rule          积分规则
     * @return 积分计算详情
     */
    ZhzgRuleScoreDetailVO calculateScore(ZhzgPersonArchiveDTO personArchive, ZhzgScoreRuleDTO rule);

    /**
     * 获取策略名称
     *
     * @return 策略名称
     */
    String getStrategyName();

    /**
     * 获取支持的规则类型
     *
     * @return 支持的规则类型
     */
    String getSupportedRuleType();

    /**
     * 是否启用
     *
     * @return 是否启用
     */
    Boolean isEnabled();

    /**
     * 检查是否支持指定的规则
     *
     * @param ruleName 规则名称
     * @param ruleType 规则类型
     * @return 是否支持
     */
    default boolean supports(String ruleName, String ruleType) {
        return getSupportedRuleType().equals(ruleType) || getStrategyName().equals(ruleName);
    }

    /**
     * 获取策略描述
     *
     * @return 策略描述
     */
    default String getDescription() {
        return "智慧政工积分计算策略：" + getStrategyName();
    }

    /**
     * 验证人员档案数据是否有效
     *
     * @param personArchive 人员档案
     * @return 是否有效
     */
    default boolean validatePersonArchive(ZhzgPersonArchiveDTO personArchive) {
        return personArchive != null && personArchive.getId() != null && validateFront(personArchive);
    }

    /**
     * 验证规则是否有效
     *
     * @param rule 积分规则
     * @return 是否有效
     */
    default boolean validateRule(ZhzgScoreRuleDTO rule) {
        return rule != null 
                && rule.getId() != null 
                && rule.getIsLeaf() != null 
                && rule.getIsLeaf();
    }

    /**
     * 验证前置校验规则，不满足的一律0分
     *
     * @param personArchive 档案信息
     * @return 是否满足
     */
    default boolean validateFront(ZhzgPersonArchiveDTO personArchive) {
        List<ScoreRankDTO> ranks = personArchive.getRanks();
        if (CollectionUtils.isEmpty(ranks)) {
            return false;
        }
        ScoreRankDTO firstRank = ranks.get(0);
        int rankInt = firstRank.getRank();
        Integer promotionStatus = personArchive.getPromotionStatus();
        return (rankInt < 7 || rankInt > 11) && (rankInt < 18 || rankInt > 21) && promotionStatus != null && promotionStatus == 1;
    }

}
