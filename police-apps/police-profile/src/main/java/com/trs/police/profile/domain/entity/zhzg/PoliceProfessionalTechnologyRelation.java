package com.trs.police.profile.domain.entity.zhzg;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 专业技术表
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "t_police_professional_technology_relation", autoResultMap = true)
@AllArgsConstructor
@NoArgsConstructor
public class PoliceProfessionalTechnologyRelation extends AbstractBaseEntity {

    /**
     * 关联警员档案表（t_police_profile）的主键
     */
    private Long profileId;
    /**
     * 获取时间（对应界面“获取时间”）
     */
    private Date acquisitionTime;
    /**
     * 专业技术名称, 码表, type= police_zyjs
     */
    private Integer technology;
    /**
     * 描述（对应界面“描述”输入的具体情况）
     */
    private String description;
    /**
     * 上传材料
     */
    private String materialPath;
    /**
     * 是否删除
     */
    private Boolean deleted;
}

