package com.trs.police.profile.domain.entity.zhzg;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 立功受奖表
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "t_police_professional_lgsj_relation", autoResultMap = true)
@AllArgsConstructor
@NoArgsConstructor
public class PoliceProfessionalLgsjRelation extends AbstractBaseEntity {

    /**
     * 关联警员档案表（t_police_profile）的主键
     */
    private Long profileId;
    /**
     * 立功受奖时间（对应界面“获取时间”）
     */
    private Date acquisitionTime;
    /**
     * 立功受奖, 码表, type= police_lgsj
     */
    private Integer lgsj;
    /**
     * 描述（对应界面“描述”输入的具体情况）
     */
    private String description;
    /**
     * 上传材料
     */
    private String materialPath;
    /**
     * 是否删除
     */
    private Boolean deleted;
}

