package com.trs.police.profile.domain.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 人员档案连表检索条件
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class PersonProfileJoinVO implements Serializable {

    private Boolean controlRegularMonitor;

    private Boolean policeControl;

    private Boolean controlMonitor;

    private String controlBureauPrefix;

    private Boolean policeKind;

    public PersonProfileJoinVO(Boolean controlRegularMonitor, Boolean policeControl, Boolean controlMonitor, Boolean policeKind) {
        this.controlRegularMonitor = controlRegularMonitor;
        this.policeControl = policeControl;
        this.controlMonitor = controlMonitor;
        this.policeKind = policeKind;
    }
}
