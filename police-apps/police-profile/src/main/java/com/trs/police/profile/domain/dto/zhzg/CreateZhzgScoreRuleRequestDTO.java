package com.trs.police.profile.domain.dto.zhzg;

import lombok.Data;

/**
 * 创建积分规则请求参数
 */

@Data
public class CreateZhzgScoreRuleRequestDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 规则名称
     */
    private String name;

    /**
     * 规则描述
     */
    private String description;

    /**
     * 规则父父id
     */
    private Long parentId;

    /**
     * 单项得分
     */
    private Double score;

    /**
     * 满分
     */
    private Double fullScore;

    /**
     * 是否是叶子节点
     */
    private Boolean isLeaf;

    /**
     * 适用职级
     */
    private Integer applicableRank;

}
