package com.trs.police.profile.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.profile.domain.entity.BusinessFilterEntity;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 业务参数配置
 *
 * <AUTHOR>
 * @since 2022/10/10 10:20
 **/
@Mapper
public interface BusinessMapper extends BaseMapper<BusinessFilterEntity> {

    /**
     * 通过模块搜索
     *
     * @param moduleId 模块
     * @return 实体
     */
    @Select("select * from t_business_filter where module=#{moduleId}")
    List<BusinessFilterEntity> selectByModule(@Param("moduleId") String moduleId);
}
