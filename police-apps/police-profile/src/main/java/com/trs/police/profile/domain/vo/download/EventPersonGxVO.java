package com.trs.police.profile.domain.vo.download;

import lombok.Data;

/**
 * 事件人员vo
 *
 * <AUTHOR>
 * @date 2024/9/25
 */
@Data
public class EventPersonGxVO extends EventPersonBaseVO{
    /**
     * 姓名
     */
    private String name;
    /**
     * 性别
     */
    private String gender;
    /**
     * 联系电话
     */
    private String phone;
    /**
     * 人员类型
     */
    private String personType;
    /**
     * 人员标签
     */
    private String personLabel;
    /**
     * 户籍地址
     */
    private String hjPlace;
    /**
     * 常住地址
     */
    private String czPlace;
    /**
     * 管辖单位
     */
    private String unit;
    /**
     * 牵头警种
     */
    private String leadForce;
    /**
     * 稳控状态
     */
    private String maintainState;
    /**
     * 临时人员标识
     */
    private String personel;
    /**
     * 民警姓名
     */
    private String policeName;
    /**
     * 民警电话
     */
    private String policePhone;
    /**
     * 最后核查时间
     */
    private String lastTime;
    /**
     * 超期状态
     */
    private String overdue;
}
