package com.trs.police.profile.domain.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 刑事行政案件VO
 *
 * <AUTHOR>
 * @date 2025/04/30
 */
@Data
public class XsXzAjVO {

    /**
     * 案件名称
     */
    @JSONField(name = "AJMC")
    private String ajmc;

    /**
     *  发现案事件地点_地址名称
     */
    @JSONField(name = "FXDD_DZMC")
    private String fxdddzmc;

    /**
     *  案时间发生地点_地址名称
     */
    @JSONField(name = "AFDD_DZMC")
    private String afdddzmc;

    /**
     *  案事件发送地点_经度
     */
    @JSONField(name = "AFDD_DQJD")
    private String afdddqjd;

    /**
     *  案事件发生地点_纬度
     */
    @JSONField(name = "AFDD_DQWD")
    private String afdddqwd;

    /**
     *  简要案情
     */
    @JSONField(name = "JYAQ")
    private String jyaq;

    /**
     *  受理时间
     */
    @JSONField(name = "SLSJ")
    private String slsj;

    /**
     *  办案单位_公安机构名称
     */
    @JSONField(name = "BADW_GAJGMC")
    private String badwgajgmc;

    /**
     *  办案单位_公安机构代码
     */
    @JSONField(name = "BADW_GAJGJGDM")
    private String badwgajgjgdm;

    /**
     *  录入时间
     */
    @JSONField(name = "LRSJ")
    private String lrsj;

    /**
     *  案事件编号
     */
    @JSONField(name = "AJBH")
    private String ajbh;

    /**
     *  发现案事件地点_行政区划代码
     */
    @JSONField(name = "FXDD_XZQHDM")
    private String fxddxzqhdm;

    /**
     *  案事件发生地点_行政区划代码
     */
    @JSONField(name = "AFDD_XZQHDM")
    private String afddxzqhdm;
}
