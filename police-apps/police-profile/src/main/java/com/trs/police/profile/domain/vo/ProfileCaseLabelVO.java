package com.trs.police.profile.domain.vo;

import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.profile.domain.entity.ProfileCaseEntity;
import com.trs.police.profile.domain.entity.ProfileCaseLabelEntity;
import com.trs.police.profile.service.CaseService;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/1/31 14:50
 */
@Data
public class ProfileCaseLabelVO {

    private Long id;
    private List<List<String>> description;
    private List<List<String>> infringementTarget;
    private List<List<String>> suspectedPerson;
    private List<List<String>> suspectedVehicle;

    /**
     * 转化未详情vo
     *
     * @param entity 实体类
     * @return {@link ProfileCaseLabelVO}
     */
    public static ProfileCaseLabelVO caseVoToCaseLabelBasicVo(CaseListVO entity) {
        final ProfileCaseLabelVO vo = new ProfileCaseLabelVO();
        if(Objects.nonNull(entity.getDescription())){
            vo.setDescription(entity.getDescription());
        }
        if(Objects.nonNull(entity.getInfringementTarget())){
            vo.setInfringementTarget(entity.getInfringementTarget());
        }
        if(Objects.nonNull(entity.getSuspectedPerson())){
            vo.setSuspectedPerson(entity.getSuspectedPerson());
        }
        if(Objects.nonNull(entity.getSuspectedVehicle())){
            vo.setSuspectedVehicle(entity.getSuspectedVehicle());
        }

        return vo;
    }

    /**
     * 转化未详情vo
     *
     * @param entity 实体类
     * @return {@link ProfileCaseLabelVO}
     */
    public static ProfileCaseLabelVO entityToCaseLabelBasicVo(ProfileCaseEntity entity) {
        final ProfileCaseLabelVO vo = new ProfileCaseLabelVO();
        if(Objects.nonNull(entity.getDescription())){
            vo.setDescription(codeResolvedIntoPathName(entity.getDescription()));
        }
        if(Objects.nonNull(entity.getInfringementTarget())){
            vo.setInfringementTarget(codeResolvedIntoPathName(entity.getInfringementTarget()));
        }
        if(Objects.nonNull(entity.getSuspectedPerson())){
            vo.setSuspectedPerson(codeResolvedIntoPathName(entity.getSuspectedPerson()));
        }
        if(Objects.nonNull(entity.getSuspectedVehicle())){
            vo.setSuspectedVehicle(codeResolvedIntoPathName(entity.getSuspectedVehicle()));
        }

        return vo;
    }

    /**
     * 转化为编辑回显vo
     *
     * @param entity 实体类
     * @return {@link ProfileCaseLabelVO}
     */
    public static ProfileCaseLabelVO entityToCaseLabelEditVo(ProfileCaseEntity entity) {
        final ProfileCaseLabelVO vo = new ProfileCaseLabelVO();
        vo.setDescription(codeResolvedIntoPathCode(entity.getDescription()));
        vo.setInfringementTarget(codeResolvedIntoPathCode(entity.getInfringementTarget()));
        vo.setSuspectedPerson(codeResolvedIntoPathCode(entity.getSuspectedPerson()));
        vo.setSuspectedVehicle(codeResolvedIntoPathCode(entity.getSuspectedVehicle()));
        return vo;
    }

    /**
     * @param code 案件标签code
     * @return 案件code级联数据
     */
    public static List<ProfileCaseLabelEntity> analysisPath(String code) {
        final CaseService caseService = BeanUtil.getBean(CaseService.class);
        final ProfileCaseLabelEntity profileCaseLabelEntity = caseService.findByCode(code);
        List<ProfileCaseLabelEntity> result = new ArrayList<>();
        Arrays.stream(profileCaseLabelEntity.getPath().split("-")).forEach(item -> {
            if (StringUtils.isNotBlank(item)) {
                ProfileCaseLabelEntity caseLabel = caseService.findByCode(item);
                if (StringUtils.isNotBlank(caseLabel.getType())) {
                    result.add(caseLabel);
                }
            }
        });
        result.add(profileCaseLabelEntity);
        return result;
    }

    /**
     * 案件标签解析成 级联code
     *
     * @param codes code
     * @return 级联code
     */
    public static List<List<String>> codeResolvedIntoPathCode(List<String> codes) {
        return codes.stream()
            .map(code -> analysisPath(code).stream().map(ProfileCaseLabelEntity::getCode).collect(Collectors.toList()))
            .collect(Collectors.toList());
    }

    /**
     * 案件标签解析成 级联name
     *
     * @param codes code
     * @return 级联name
     */
    public static List<List<String>> codeResolvedIntoPathName(List<String> codes) {
        return codes.stream()
            .map(code -> analysisPath(code).stream().map(ProfileCaseLabelEntity::getName).collect(Collectors.toList()))
            .collect(Collectors.toList());
    }
}
