<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.profile.mapper.ZtryMapper">

    <insert id="insert" parameterType="com.trs.police.profile.domain.entity.Ztry">
        <!--@mbg.generated-->
        insert ignore into t_profile_ztry (code, photo,
                                    `name`, nick_name, gender,
                                    nation, id_number, birthday,
                                    height, body, skin,
                                    accent, hjd_code, hjd_detail,
                                    xzd_code, xzd_detail, jg_code,
                                    jg_detail, ajbh, deleted,
                                    ajlb_code, ajlb_mc, ladw_code,
                                    ladw_mc, larq, tprq, tpfx,
                                    ztlx, tjlbh, tjljb,
                                    jj, zbdw_code, zbdw_mc,
                                    zbr, zbr_lxfs, lrsj,
                                    update_time, jyaq)
        values ( #{ztry.code,jdbcType=VARCHAR}, #{ztry.photo,jdbcType=VARCHAR},
                #{ztry.name,jdbcType=VARCHAR}, #{ztry.nickName,jdbcType=VARCHAR}, #{ztry.gender,jdbcType=INTEGER},
                #{ztry.nation,jdbcType=INTEGER}, #{ztry.idNumber,jdbcType=VARCHAR}, #{ztry.birthday,jdbcType=DATE},
                #{ztry.height,jdbcType=VARCHAR}, #{ztry.body,jdbcType=VARCHAR}, #{ztry.skin,jdbcType=VARCHAR},
                #{ztry.accent,jdbcType=VARCHAR}, #{ztry.hjdCode,jdbcType=VARCHAR}, #{ztry.hjdDetail,jdbcType=VARCHAR},
                #{ztry.xzdCode,jdbcType=VARCHAR}, #{ztry.xzdDetail,jdbcType=VARCHAR}, #{ztry.jgCode,jdbcType=VARCHAR},
                #{ztry.jgDetail,jdbcType=VARCHAR}, #{ztry.ajbh,jdbcType=VARCHAR}, #{ztry.deleted,jdbcType=INTEGER},
                #{ztry.ajlbCode,jdbcType=VARCHAR}, #{ztry.ajlbMc,jdbcType=VARCHAR}, #{ztry.ladwCode,jdbcType=VARCHAR},
                #{ztry.ladwMc,jdbcType=VARCHAR}, #{ztry.larq,jdbcType=DATE}, #{ztry.tprq,jdbcType=DATE}, #{ztry.tpfx,jdbcType=VARCHAR},
                #{ztry.ztlx,jdbcType=VARCHAR}, #{ztry.tjlbh,jdbcType=VARCHAR}, #{ztry.tjljb,jdbcType=VARCHAR},
                #{ztry.jj,jdbcType=VARCHAR}, #{ztry.zbdwCode,jdbcType=VARCHAR}, #{ztry.zbdwMc,jdbcType=VARCHAR},
                #{ztry.zbr,jdbcType=VARCHAR}, #{ztry.zbrLxfs,jdbcType=VARCHAR}, #{ztry.lrsj,jdbcType=TIMESTAMP},
                #{ztry.updateTime,jdbcType=TIMESTAMP}, #{ztry.jyaq,jdbcType=LONGVARCHAR})
        ON DUPLICATE KEY UPDATE
        code = #{ztry.code,jdbcType=VARCHAR},
        photo = #{ztry.photo,jdbcType=VARCHAR},
        `name` = #{ztry.name,jdbcType=VARCHAR},
        nick_name = #{ztry.nickName,jdbcType=VARCHAR},
        gender = #{ztry.gender,jdbcType=INTEGER},
        nation = #{ztry.nation,jdbcType=INTEGER},
        id_number = #{ztry.idNumber,jdbcType=VARCHAR},
        birthday = #{ztry.birthday,jdbcType=DATE},
        height = #{ztry.height,jdbcType=VARCHAR},
        body = #{ztry.body,jdbcType=VARCHAR},
        skin = #{ztry.skin,jdbcType=VARCHAR},
        accent = #{ztry.accent,jdbcType=VARCHAR},
        hjd_code = #{ztry.hjdCode,jdbcType=VARCHAR},
        hjd_detail = #{ztry.hjdDetail,jdbcType=VARCHAR},
        xzd_code = #{ztry.xzdCode,jdbcType=VARCHAR},
        xzd_detail = #{ztry.xzdDetail,jdbcType=VARCHAR},
        jg_code = #{ztry.jgCode,jdbcType=VARCHAR},
        jg_detail = #{ztry.jgDetail,jdbcType=VARCHAR},
        ajbh = #{ztry.ajbh,jdbcType=VARCHAR},
        deleted = #{ztry.deleted,jdbcType=INTEGER},
        ajlb_code = #{ztry.ajlbCode,jdbcType=VARCHAR},
        ajlb_mc = #{ztry.ajlbMc,jdbcType=VARCHAR},
        ladw_code = #{ztry.ladwCode,jdbcType=VARCHAR},
        ladw_mc = #{ztry.ladwMc,jdbcType=VARCHAR},
        larq = #{ztry.larq,jdbcType=DATE},
        tprq = #{ztry.tprq,jdbcType=DATE},
        tpfx = #{ztry.tpfx,jdbcType=VARCHAR},
        ztlx = #{ztry.ztlx,jdbcType=VARCHAR},
        tjlbh = #{ztry.tjlbh,jdbcType=VARCHAR},
        tjljb = #{ztry.tjljb,jdbcType=VARCHAR},
        jj = #{ztry.jj,jdbcType=VARCHAR},
        zbdw_code = #{ztry.zbdwCode,jdbcType=VARCHAR},
        zbdw_mc = #{ztry.zbdwMc,jdbcType=VARCHAR},
        zbr = #{ztry.zbr,jdbcType=VARCHAR},
        zbr_lxfs = #{ztry.zbrLxfs,jdbcType=VARCHAR},
        lrsj = #{ztry.lrsj,jdbcType=TIMESTAMP},
        update_time = #{ztry.updateTime,jdbcType=TIMESTAMP},
        jyaq = #{ztry.jyaq,jdbcType=LONGVARCHAR}
    </insert>
</mapper>