spring:
    application:
        name: profile
    cloud:
        nacos:
            server-addr: @spring.cloud.nacos.server-addr@
            username: @spring.cloud.nacos.username@
            password: @spring.cloud.nacos.password@
            config:
                file-extension: yaml
                namespace: @spring.cloud.nacos.namespace@
                extension-configs:
                    - data-id: common-third-api.properties
                      group: DEFAULT_GROUP
                      refresh: true
                    - data-id: commons-oauth2-client.yaml
                      group: DEFAULT_GROUP
                      refresh: true
                    - data-id: commons-datasource.yaml
                      group: DEFAULT_GROUP
                      refresh: true
                    - data-id: commons-flyway.yaml
                      group: DEFAULT_GROUP
                      refresh: true
                    - data-id: commons-redis.yaml
                      group: DEFAULT_GROUP
                      refresh: true
                    - data-id: commons-moye.yaml
                      group: DEFAULT_GROUP
                      refresh: true
                    - data-id: commons-rabbit.yaml
                      group: DEFAULT_GROUP
                      refresh: true
                    - data-id: commons-es.yaml
                      group: DEFAULT_GROUP
                      refresh: true
                    - data-id: profile-new.properties
                      group: DEFAULT_GROUP
                      refresh: true
                    # 临时注释掉，使用本地配置文件进行测试
                    - data-id: police-promotion-config.yaml
                      group: DEFAULT_GROUP
                      refresh: true
            discovery:
                namespace: ${spring.cloud.nacos.config.namespace}
trs:
  auto:
    nacos:
      config:
        turnOn: true



