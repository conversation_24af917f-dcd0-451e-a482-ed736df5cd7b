package com.trs.police.intelligence.dto;

import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.common.utils.JsonUtils;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.intelligence.constant.ActionEnum;
import com.trs.police.intelligence.constant.Constants;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

import static com.trs.common.base.PreConditionCheck.checkArgument;
import static com.trs.common.base.PreConditionCheck.checkNotEmpty;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * YaoQingReportDataDTO
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/15 09:45
 * @since 1.0
 */
@Data
@ToString(callSuper = true)
public class YaoQingReportDataDTO extends BaseActionDTO {

    private String reportType;

    private String reportTarget;

    private String smsContent;

    private String smsTargetUser;

    public YaoQingReportDataDTO() {
        setActionEnum(ActionEnum.YAO_QING_REPORT_DATA);
    }

    /**
     * makeTargetIds<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 18:22
     */
    public List<ObjDTO> makeTargetIds() {
        if (JsonUtils.isValidArray(getReportTarget())) {
            return JsonUtil.parseArray(getReportTarget(), ObjDTO.class);
        }
        final var target = StringUtils.getLongList(getReportTarget(), StringUtils.SEPARATOR_COMMA, true);
        final var permissionService = BeanUtil.getBean(PermissionService.class);
        switch (StringUtils.showEmpty(getReportType())) {
            case Constants.REPORT_TYPE_LEADER_USER_ID:
                return target.stream()
                        .map(permissionService::getUserById)
                        .flatMap(it -> it.getDeptList().stream().map(dept -> ObjDTO.ofUser(it.getId(), dept.getId())))
                        .collect(Collectors.toList());
            case Constants.REPORT_TYPE_LEADER_DEPT_ID:
            default:
                return target.stream().map(ObjDTO::ofDept).collect(Collectors.toList());
        }
    }

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL> 创建时间：2020-09-10 15:49
     */
    @Override
    protected boolean checkParams() throws ServiceException {
        checkNotEmpty(getReportType(), new ParamInvalidException("上报类型不能为空"));
        checkNotEmpty(getReportTarget(), new ParamInvalidException("报送目标不能为空"));
        var target = makeTargetIds();
        checkArgument(CollectionUtils.isNotEmpty(target), new ParamInvalidException("报送目标ID不能为空"));
        for (ObjDTO dto : target) {
            dto.isValid();
        }
        if (StringUtils.isNotEmpty(getSmsContent()) || StringUtils.isNotEmpty(getSmsTargetUser())) {
            checkNotEmpty(getSmsContent(), new ParamInvalidException("发送目标不为空时，短信内容不能为空"));
            checkNotEmpty(getSmsTargetUser(), new ParamInvalidException("短信内容不为空时，发送目标不能为空"));
        }
        return super.checkParams();
    }
}
