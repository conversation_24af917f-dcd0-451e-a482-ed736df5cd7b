package com.trs.police.intelligence.utils;

import io.vavr.Tuple2;

import java.util.HashMap;
import java.util.Map;

/**
 * 工作状态转换工具类
 *
 * <AUTHOR>
 * @version v0
 * @since 1.0.0
 * @since 2024-09-27 19:23:16
 */
public class MyWorkStatusUtil {

    private MyWorkStatusUtil() {
    }

    private static final Map<String, Tuple2<String, String>> MAP = new HashMap<>();

    static {
        MAP.put("工作中", new Tuple2<>("1", "工作中"));
        MAP.put("已稳控", new Tuple2<>("2", "已稳控"));
        MAP.put("外省人员", new Tuple2<>("3", "外省人员"));
        MAP.put("无法核查", new Tuple2<>("4", "无法核查"));
        MAP.put("查否", new Tuple2<>("5", "查否"));
        MAP.put("查无此人", new Tuple2<>("6", "查无此人"));
        MAP.put("重复人员", new Tuple2<>("11", "重复人员"));
        MAP.put("非本区县", new Tuple2<>("21", "非本区县"));
        MAP.put("非本辖区", new Tuple2<>("22", "非本辖区"));

        MAP.put("见面稳控", new Tuple2<>("2", "已稳控"));
        MAP.put("敲打教育", new Tuple2<>("2", "已稳控"));
        MAP.put("技术管控", new Tuple2<>("2", "已稳控"));
    }

    /**
     * 工作状态转换
     *
     * @param key key
     * @return {@link Tuple2 }<{@link String }, {@link String }>
     * <AUTHOR>
     * @since 1.0.0
     * @since 2024-09-27 19:23:41
     */
    public static Tuple2<String, String> workStatus2Hczt(String key) {
        return MAP.get(key);
    }

}
