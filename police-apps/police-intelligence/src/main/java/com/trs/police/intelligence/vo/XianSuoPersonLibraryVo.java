package com.trs.police.intelligence.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.trs.police.common.core.vo.permission.SimpleDeptVO;
import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/5/10 14:17
 * @since 1.0
 */
@Data
@ToString(callSuper = true)
public class XianSuoPersonLibraryVo extends BaseEntityVo {

    private Long dataId;

    private String zjhm;

    private String xm;

    private String xb;

    private String sjhwm;

    private String hjd;

    private String gsdy;

    private String gsdymc;

    private Integer age;

    private String remarksLabel;

    private String remarksLabelName;

    private SimpleDeptVO checkDept;

    private List<Long> relatedClueIds;

    private String updateUser;

    private String updateUserTrueName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    private Long lastClueId;

    private Long archivesId;
}
