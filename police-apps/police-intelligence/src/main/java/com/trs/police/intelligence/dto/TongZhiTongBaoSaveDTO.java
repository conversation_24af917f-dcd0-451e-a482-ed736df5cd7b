package com.trs.police.intelligence.dto;

import com.trs.common.base.PreConditionCheck;
import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;

import static com.trs.common.base.PreConditionCheck.checkArgument;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * YaoQingSaveDTO
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/12 01:46
 * @since 1.0
 */
@ToString(callSuper = true)
@Data
public class TongZhiTongBaoSaveDTO extends BaseSaveDTO {

    /**
     * 创建类型
     * 0：原发
     * 1：追加
     * 2：转发
     */
    private Integer createType;

    public TongZhiTongBaoSaveDTO() {
        this.createType = 0;
    }

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL> 创建时间：2020-09-10 15:49
     */
    @Override
    protected boolean checkParams() throws ServiceException {
        PreConditionCheck.checkNotEmpty(getDataType(), new ParamInvalidException("类型不能为空"));
        PreConditionCheck.checkNotEmpty(getDataTitle(), new ParamInvalidException("标题不能为空"));
        if (getVersionFlag() != 0) {
            checkArgument(
                    getDataId() != null && getDataId() > 0L,
                    new ParamInvalidException("修订时数据ID不能为为空")
            );
            checkArgument(getDraftsFlag() != 1, new ParamInvalidException("修订时不能为草稿"));
        } else {
            checkArgument(getDraftsFlag() == 1, new ParamInvalidException("草稿才能进行新增/编辑"));
        }
        checkArgument(CollectionUtils.isNotEmpty(makeAcceptTarget()), new ParamInvalidException("接收单位/人员不能为空"));
        return true;
    }
}
