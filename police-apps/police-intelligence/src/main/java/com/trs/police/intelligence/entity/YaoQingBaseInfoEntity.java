package com.trs.police.intelligence.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.common.utils.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * YaoQingBaseInfoEntity
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/12 00:23
 * @since 1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("tb_intelligence_yaoqing_base_info")
public class YaoQingBaseInfoEntity extends BaseIntelligenceEntity {

    @TableField
    private Date happenedTime;

    @TableField
    private String labels;

    @TableField
    private String acceptLeaders;

    @TableField
    private Date nextReportTimeLimit;

    @TableField
    private Date nextReportTimeLast;

    @TableField
    private Date finalReportTime;

    @TableField
    private Date signTime;

    /**
     * 首报时间
     */
    @TableField
    private Date firstReportTime;

    @TableField
    private String fieldsEditInfo;

    /**
     * 字段是否填报完了
     * 1:填报完了
     * 0:没有填报完
     * -1:没有填报完，但是已经发送了消息
     */
    private Integer fieldsIsFinished = 0;

    @TableField
    private String dataSubTitle;

    @TableField(exist = false)
    private String versionDataSubTitle;

    /**
     * <p>续报是否超时</p>
     * <ul>
     *     <li>0:否</li>
     *     <li>1:是</li>
     * </ul>
     */
    @TableField
    private Integer nextReportTimeout = 0;

    @TableField
    private String ccDeptIds;

    @TableField
    private Integer watchFlag;

    @TableField
    private Long targetZhiLingId;

    @TableField
    private Integer zhiLingDataNo;


    /**
     * makeCcDeptIds<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/18 21:38
     */
    public List<Long> makeCcDeptIds() {
        return Arrays.stream(StringUtils.showEmpty(getCcDeptIds()).split(StringUtils.SEPARATOR_COMMA))
                .filter(StringUtils::isNotEmpty)
                .map(Long::valueOf)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * makeShowName<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/19 00:42
     */
    @Override
    public String makeShowName() {
        return "要情";
    }
}
