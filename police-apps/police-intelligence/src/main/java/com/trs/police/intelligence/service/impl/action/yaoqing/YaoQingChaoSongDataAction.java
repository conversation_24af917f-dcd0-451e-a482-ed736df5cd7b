package com.trs.police.intelligence.service.impl.action.yaoqing;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.trs.common.base.Report;
import com.trs.common.base.Report.RESULT;
import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.intelligence.constant.ActionEnum;
import com.trs.police.intelligence.constant.Constants;
import com.trs.police.intelligence.constant.Status;
import com.trs.police.intelligence.dto.CcDataDTO;
import com.trs.police.intelligence.entity.YaoQingBaseInfoEntity;
import com.trs.police.intelligence.service.BaseYaoQingAction;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.trs.common.base.PreConditionCheck.checkArgument;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/5/8 21:38
 * @since 1.0
 */
@Component
public class YaoQingChaoSongDataAction extends BaseYaoQingAction<CcDataDTO, Report<String>, Report<String>> {

    /**
     * actionEnum<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:09
     */
    @Override
    public ActionEnum actionEnum() {
        return ActionEnum.YAO_QING_CC_DATA;
    }

    /**
     * mergeOneR<BR>
     *
     * @param login   参数
     * @param reports 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:36
     */
    @Override
    protected Report<String> mergeOneR(Optional<CurrentUser> login, List<Report<String>> reports) {
        return reports
                .stream()
                .filter(it -> it.getResult() == RESULT.FAIL)
                .findAny()
                .orElse(new Report<>(desc(), "成功处理"));
    }

    /**
     * check<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:43
     */
    @Override
    protected void check(Optional<CurrentUser> login, YaoQingBaseInfoEntity entity, CcDataDTO dto)
            throws ServiceException {
        super.check(login, entity, dto);
        checkArgument(
                !entity.getStatusCode().equals(Status.CAOGAOXIANG.getCode()),
                new ParamInvalidException("草稿箱的数据不能通报")
        );
        final CurrentUser user = login.orElseThrow(() -> new ServiceException("未登录"));
        checkArgument(
                entity.userCreateEntity(login)
                        || entity.makeAcceptDeptIds().contains(user.getDeptId())
                        || StringUtils.showEmpty(entity.getAcceptLeaders()).contains(user.getUsername()),
                new ServiceException("只有数据创建者跟接收者可以发起通报")
        );
        List<Long> deptIds = entity.makeCcDeptIds();
        List<Long> ccIds = dto.makeCcDeptIds();
        checkArgument(
                ccIds.stream().anyMatch(it -> !deptIds.contains(it)),
                new ParamInvalidException("已经通报给目标单位了，不能重复通报")
        );
    }

    /**
     * doOneAction<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:17
     */
    @Override
    public Report<String> doOneAction(Optional<CurrentUser> login, YaoQingBaseInfoEntity entity, CcDataDTO dto)
            throws ServiceException {
        List<Long> deptIds = entity.makeCcDeptIds();
        deptIds.addAll(dto.makeCcDeptIds());
        // 构建通报关系
        getDataRelationMappingMgr().saveRelation(
                login.orElseThrow(() -> new ServiceException("登录异常")),
                module(),
                entity.getDataId(),
                Constants.DEPT,
                deptIds,
                Constants.DATA_RELATION_STATUS_TYPE_CC,
                0
        );
        // 合并通报的单位ID串
        var ccDeptIds = deptIds.stream()
                .distinct()
                .map(it -> Long.toString(it))
                .collect(Collectors.joining(StringUtils.SEPARATOR_COMMA));
        entity.setCcDeptIds(ccDeptIds);
        new LambdaUpdateChainWrapper<>(getMapper())
                .set(YaoQingBaseInfoEntity::getCcDeptIds, ccDeptIds)
                .eq(YaoQingBaseInfoEntity::getDataId, entity.getDataId())
                .update();
        return new Report<>(desc(), "通报要情成功");
    }

    /**
     * makeLogContent<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @param one    参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/16 17:40
     */
    @Override
    protected String makeLogContent(Optional<CurrentUser> login, YaoQingBaseInfoEntity entity, CcDataDTO dto,
                                    Report<String> one) {
        final String deptNames = Optional.ofNullable(getPermissionService().getDeptByIds(dto.makeCcDeptIds()))
                .orElse(Collections.emptyList())
                .stream()
                .map(it -> StringUtils.showEmpty(it.getShortName(), it.getName()))
                .collect(Collectors.joining(StringUtils.SEPARATOR_COMMA));
        return String.format("通报要情 %s 至: %s", entity.getDataTitle(), deptNames);
    }

    /**
     * makeMessageContent<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @param one    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:38
     */
    @Override
    protected String makeMessageContent(Optional<CurrentUser> login, YaoQingBaseInfoEntity entity, CcDataDTO dto, Report<String> one) throws ServiceException {
        JSONObject value = new JSONObject();
        value.put("id", entity.getDataId());
        value.put("versionId", entity.getVersionId());
        value.put("status", "通报要情");
        value.put("content", makeLogContent(login, entity, dto, one));
        return String.format("intelligence_type(%s)", value);
    }

    /**
     * makeMessageSimpleContent<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @param one    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 17:07
     */
    @Override
    protected String makeMessageSimpleContent(Optional<CurrentUser> login, YaoQingBaseInfoEntity entity, CcDataDTO dto, Report<String> one) throws ServiceException {
        return makeLogContent(login, entity, dto, one);
    }

}
