package com.trs.police.intelligence.utils;

import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.trs.common.base.PreConditionCheck;
import com.trs.common.utils.StringUtils;
import io.vavr.control.Try;
import org.apache.poi.xwpf.usermodel.*;
import org.ddr.poi.html.HtmlRenderPolicy;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * WordUtils
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/22 12:52
 * @since 1.0
 */
public class WordUtils {

    /**
     * buildParamText<BR>
     *
     * @param document 参数
     * @param textMap  参数
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/6/5 18:27
     */
    public static void buildParamText(XWPFDocument document, Map<String, String> textMap) {
        //获取段落集合
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        for (XWPFParagraph paragraph : paragraphs) {
            //判断此段落时候需要进行替换
            String text = paragraph.getText();
            if (checkText(text)) {
                List<XWPFRun> runs = paragraph.getRuns();
                for (XWPFRun run : runs) {
                    //替换模板原来位置
                    run.setText(changeValue(run.toString(), textMap), 0);
                }
            }
        }
    }

    /**
     * buildWordTableText<BR>
     *
     * @param document    参数
     * @param tableList   参数
     * @param headerIndex 参数
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/6/5 18:27
     */
    public static void buildWordTableText(XWPFDocument document, List<String[]> tableList, int headerIndex) {
        if (null == tableList) {
            return;
        }
        //获取表格对象集合
        List<XWPFTable> tables = document.getTables();
        for (XWPFTable table : tables) {
            XWPFTableRow copyRow = table.getRow(headerIndex);
            List<XWPFTableCell> cellList = copyRow.getTableCells();
            if (null == cellList) {
                break;
            }
            //遍历要添加的数据的list
            for (int i = 0; i < tableList.size(); i++) {
                //插入一行
                XWPFTableRow targetRow = table.insertNewTableRow(headerIndex + 1 + i);
                //复制行属性
                targetRow.getCtRow().setTrPr(copyRow.getCtRow().getTrPr());

                String[] strings = tableList.get(i);
                for (int j = 0; j < strings.length; j++) {
                    XWPFTableCell sourceCell = cellList.get(j);
                    //插入一个单元格
                    XWPFTableCell targetCell = targetRow.addNewTableCell();
                    //复制列属性
                    targetCell.getCTTc().setTcPr(sourceCell.getCTTc().getTcPr());
                    targetCell.setText(strings[j]);
                }
            }
        }
    }

    /**
     * changeValue<BR>
     *
     * @param value   参数
     * @param textMap 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/6/5 18:27
     */
    public static String changeValue(String value, Map<String, String> textMap) {
        Set<Map.Entry<String, String>> textSets = textMap.entrySet();
        for (Map.Entry<String, String> textSet : textSets) {
            String key = "{{" + textSet.getKey() + "}}";
            if (value.contains(key)) {
                value = textSet.getValue();
            }
        }
        //模板未匹配到区域替换为空
        if (checkText(value)) {
            value = "";
        }
        return value;
    }

    /**
     * checkText<BR>
     *
     * @param text 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/6/5 18:27
     */
    public static boolean checkText(String text) {
        return text.contains("{{");
    }

    /**
     * 获取模板文件流
     *
     * @param fileName 文件名称
     * @return 结果
     */
    public static InputStream getTemplateInputStream(String fileName) {
        // 先根据相对路径获取resource路径下的文件模板
        String basePath = "templates/" + fileName;
        Try<InputStream> resourceInputStream = Try.of(
                () -> WordUtils.class.getClassLoader().getResourceAsStream(basePath));
        if (resourceInputStream.isFailure() || Objects.isNull(resourceInputStream.get())) {
            throw new RuntimeException("获取文件模板失败!");
        }
        return resourceInputStream.get();
    }

    /**
     * 处理内容撑满一页A4纸<BR>
     *
     * @param content      参数
     * @param line         内容允许的总行数
     * @param lineFontSize 每一行的字数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/30 16:11
     */
    public static String parseContentForExport(String content, Integer line, Integer lineFontSize) {
        PreConditionCheck.checkArgument(line > 0, "一页A4纸的行数必须大于0");
        PreConditionCheck.checkArgument(lineFontSize > 0, "每一行数据的字数必须大于0");
        StringBuilder tmp = new StringBuilder(StringUtils.showEmpty(content));
        int addLineSize = line;
        for (String s : tmp.toString().split("\n")) {
            Integer fontSize = StringUtils.showEmpty(s).length();
            addLineSize = addLineSize - (fontSize / lineFontSize) - (fontSize % lineFontSize == 0 ? 0 : 1);
        }
        addLineSize = Math.max(0, addLineSize);
        while (addLineSize > 0) {
            tmp.append("\n");
            addLineSize--;
        }
        return tmp.toString();
    }

    /**
     * exportFile<BR>
     *
     * @param fileName   参数
     * @param tempName   参数
     * @param model      参数
     * @param htmlFields 需要按照HTML解析的字段
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/22 11:54
     */
    public static String exportFile(String fileName, String tempName, Object model, String... htmlFields) {
        HtmlRenderPolicy htmlRenderPolicy = new HtmlRenderPolicy();
        var configure = Configure.builder();
        if (htmlFields != null) {
            for (String field : htmlFields) {
                configure.bind(field, htmlRenderPolicy);
            }
        }
        XWPFTemplate template = XWPFTemplate.compile(getTemplateInputStream(tempName), configure.build())
                .render(model);
        try {
            FileOutputStream fos = new FileOutputStream(fileName);
            template.writeAndClose(fos);
        } catch (IOException ignored) {
        }
        return fileName;
    }
}
