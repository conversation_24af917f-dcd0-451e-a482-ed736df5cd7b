package com.trs.police.intelligence.dto;

import com.trs.common.exception.ServiceException;
import com.trs.common.pojo.BaseDTO;
import lombok.Data;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * AttributeTemplatesDTO
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/11 18:00
 * @since 1.0
 */
@Data
public class AttributeTemplatesDTO extends BaseDTO {

    private String dataType;

    private String dataClass;

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2020-09-10 15:49
     */
    @Override
    protected boolean checkParams() throws ServiceException {
        return true;
    }
}
