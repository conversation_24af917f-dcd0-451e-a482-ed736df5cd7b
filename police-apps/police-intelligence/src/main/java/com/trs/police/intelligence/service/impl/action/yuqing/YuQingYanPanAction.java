package com.trs.police.intelligence.service.impl.action.yuqing;

import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.trs.common.base.PreConditionCheck;
import com.trs.common.base.Report;
import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.intelligence.constant.ActionEnum;
import com.trs.police.intelligence.constant.Constants;
import com.trs.police.intelligence.constant.Status;
import com.trs.police.intelligence.dto.CcDataDTO;
import com.trs.police.intelligence.dto.YuQingYanPanDTO;
import com.trs.police.intelligence.entity.YuQingBaseInfoEntity;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.trs.common.base.PreConditionCheck.checkArgument;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/15 09:47
 * @since 1.0
 */
@Component
public class YuQingYanPanAction
        extends BaseYuQingWithReportAction<YuQingYanPanDTO> {

    @Autowired
    private YuQingChaoSongAction cc;

    /**
     * actionEnum<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:09
     */
    @Override
    public ActionEnum actionEnum() {
        return ActionEnum.YUQING_YANPAN_DATA;
    }

    /**
     * check<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:43
     */
    @Override
    protected void check(
            Optional<CurrentUser> login,
            YuQingBaseInfoEntity entity,
            YuQingYanPanDTO dto
    ) throws ServiceException {
        super.check(login, entity, dto);
        checkArgument(
                !entity.getStatusCode().equals(Status.CAOGAOXIANG.getCode()),
                new ParamInvalidException("草稿箱的数据不能反馈")
        );
        checkArgument(
                !entity.getStatusCode().equals(Status.YIWANJIE.getCode()),
                new ParamInvalidException("已完结的数据不能反馈")
        );
        List<Long> deptIds = entity.makeCcDeptIds();
        List<Long> ccIds = StringUtils.getLongList(dto.getCcDeptIds(), StringUtils.SEPARATOR_COMMA, true);
        PreConditionCheck.checkArgument(
                CollectionUtils.isEmpty(ccIds) || ccIds.stream().anyMatch(it -> !deptIds.contains(it)),
                new ParamInvalidException("已经抄送给目标单位了，不能重复抄送")
        );
    }

    /**
     * doOneAction<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:17
     */
    @Override
    public Report<String> doOneAction(
            Optional<CurrentUser> login,
            YuQingBaseInfoEntity entity,
            YuQingYanPanDTO dto
    ) throws ServiceException {
        entity.setGzyq(dto.getGzyq());
        entity.setAcceptDeptIds(dto.getAcceptDepts());
        new LambdaUpdateChainWrapper<>(getMapper())
                .set(YuQingBaseInfoEntity::getGzyq, dto.getGzyq())
                .set(YuQingBaseInfoEntity::getAcceptDeptIds, dto.getAcceptDepts())
                .eq(YuQingBaseInfoEntity::getDataId, entity.getDataId())
                .update();
        if (StringUtils.isNotEmpty(dto.getCcDeptIds())){
            CcDataDTO ccDataDTO = new CcDataDTO();
            ccDataDTO.setDataId(entity.getDataId());
            ccDataDTO.setCcDeptIds(dto.getCcDeptIds());
            ccDataDTO.setActionEnum(ActionEnum.YUQING_CC_DATA);
            var report = cc.doAction(ccDataDTO);
            if (Objects.equals(report.getResult(), Report.RESULT.FAIL)) {
                throw new ServiceException(report.getDetail());
            }
        }
        Set<Long> target = new HashSet<>(2);
        getDataRelationMappingMgr().getRelationList(
                module(),
                entity.getDataId(),
                Constants.DEPT,
                Constants.DATA_RELATION_STATUS_TYPE_DEFAULT
        ).forEach(it -> target.add(it.getObjId()));
        Optional.ofNullable(entity.makeAcceptDeptIds())
                .filter(CollectionUtils::isNotEmpty)
                .ifPresent(target::addAll);
        // 构建关联关系
        getDataRelationMappingMgr().saveRelation(
                login.orElseThrow(() -> new ServiceException("未登录")),
                module(),
                entity.getDataId(),
                Constants.DEPT,
                new ArrayList<>(target),
                Constants.DATA_RELATION_STATUS_TYPE_DEFAULT,
                0,
                false
        );
        return new Report<>(desc(), "研判舆情");
    }

    /**
     * makeLogContent<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @param one    参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/16 17:40
     */
    @Override
    protected String makeLogContent(
            Optional<CurrentUser> login,
            YuQingBaseInfoEntity entity,
            YuQingYanPanDTO dto,
            Report<String> one
    ) {
        StringBuilder sb = new StringBuilder();
        sb.append("工作要求：").append(dto.getGzyq()).append("。");
        if (StringUtils.isNotEmpty(dto.getContent())) {
            sb.append("研判意见：").append(dto.getContent()).append("。");
        }
        return sb.toString();
    }

}
