package com.trs.police.intelligence.vo.third;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName ProvinceCluePersonVO
 * @Description 省厅线索涉及人员
 * <AUTHOR>
 * @Date 2024/9/4 16:33
 **/
@Data
public class ProvinceCluePersonVO implements Serializable {

    /**
     * 线索编号
     */
    private String xsbh;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 身份证，下发线索人员身份证为空时，该
     * 信息为系统默认生成唯一标识
     */
    private String rySfz;

    /**
     * 户籍地代码
     */
    private String hjdDm;

    /**
     * 户籍地名称
     */
    private String hjdMc;

    /**
     * 手机号
     */
    private String sjh;

    /**
     * 人员核查状态（字典），参考附录：人员核查状态字典
     * 为空代表未开始
     */
    private String hcztDm;

    /**
     * 人员核查状态名称
     */
    private String hcztMc;

}
