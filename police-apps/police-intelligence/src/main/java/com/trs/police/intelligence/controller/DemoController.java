package com.trs.police.intelligence.controller;

import com.trs.common.base.PreConditionCheck;
import com.trs.common.concurrent.FutureCallback;
import com.trs.common.concurrent.ListenableExecutorService;
import com.trs.common.concurrent.ListeningCallbacks;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.ThreadUtil;
import com.trs.common.utils.TimeUtils;
import com.trs.police.intelligence.service.BaseThirdDataSync;
import com.trs.web.builder.base.RestfulResultsV2;
import com.trs.web.builder.util.KeyMgrFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * DemoController
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/8 11:51
 * @since 1.0
 */
@RestController
@RequestMapping("/public/demo")
@Slf4j
public class DemoController {

    private static final ListenableExecutorService SERVICE = ThreadUtil.makeService(
            "intelligence-demo-",
            new ThreadPoolExecutor.AbortPolicy()
    );

    /**
     * syncClueTask<BR>
     *
     * @param day       参数
     * @param startTime 参数
     * @param endTime   参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/4/27 14:29
     */
    @GetMapping("syncClueTask")
    public RestfulResultsV2<String> syncClueTask(Integer day, String startTime, String endTime) {
        return RestfulResultsV2.checkedBuild(() -> {
            PreConditionCheck.checkArgument(
                    Objects.nonNull(day) || StringUtils.isNotEmpty(startTime),
                    "day跟startTime不能同时为空！"
            );
            final List<BaseThirdDataSync> mgrs = KeyMgrFactory.getMgrs(BaseThirdDataSync.class);
            String taskId = TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD_HHMMSS_SSS2);
            final String kssj = StringUtils.showEmpty(startTime, TimeUtils.dateBefOrAft(day, TimeUtils.YYYYMMDD_HHMMSS));
            for (BaseThirdDataSync mgr : mgrs) {
                ListeningCallbacks.addCallback(
                        SERVICE.submit(() -> {
                            mgr.syncData(taskId, kssj, endTime);
                            return true;
                        }),
                        new FutureCallback<Boolean>() {
                            @Override
                            public void onSuccess(Boolean result) {
                                log.info("[{}][{}]同步[{}]数据完成,result=[{}]！", taskId, mgr.desc(), day, result);
                            }

                            @Override
                            public void onFail(Throwable error) {
                                log.error("[{}][{}]同步[{}]数据出错了！", taskId, mgr.desc(), day, error);
                            }
                        }
                );
            }
            return "异步同步中,taskId=" + taskId;
        });
    }

}
