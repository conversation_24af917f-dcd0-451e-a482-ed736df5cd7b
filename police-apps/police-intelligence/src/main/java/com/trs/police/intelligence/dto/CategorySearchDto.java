package com.trs.police.intelligence.dto;

import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.common.pojo.BaseDTO;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.request.ListParamsRequest;
import lombok.Data;

import static com.trs.common.base.PreConditionCheck.checkArgument;
import static com.trs.common.base.PreConditionCheck.checkNotEmpty;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2025</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2025/4/16 19:36
 * @since 1.0
 */
@Data
public class CategorySearchDto extends BaseDTO {

    private String path;
    private String from;
    private String groupField;
    private String dateFormat;
    private ListParamsRequest request;

    @Override
    protected boolean checkParams() throws ServiceException {
        checkNotEmpty(getPath(), new ParamInvalidException("路径不能为空"));
        checkNotEmpty(getFrom(), new ParamInvalidException("from不能为空"));
        checkArgument(
                getGroupField().matches(StringUtils.ORDER_FIELD_REX),
                new ParamInvalidException("非法统计字段[" + getGroupField() + "]")
        );
        return true;
    }
}
