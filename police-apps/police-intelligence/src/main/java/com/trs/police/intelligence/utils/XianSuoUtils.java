package com.trs.police.intelligence.utils;

import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.intelligence.constant.Constants;
import com.trs.police.intelligence.entity.XianSuoBaseInfoEntity;
import io.vavr.control.Try;
import org.apache.poi.xwpf.usermodel.*;

import java.util.*;
import java.util.stream.Collectors;

import static com.trs.common.utils.AreaCodeUtil.isStartWithCode;
import static com.trs.common.utils.AreaCodeUtil.spreadingAreaCode;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> zhang.yang  E-mail: <EMAIL>
 * @version 1.0
 * @since ：2025/4/2 11:40
 * @since 1.0
 */
public class XianSuoUtils {

    /**
     * 表格样式配置
     */
    private static final String FONT_SONG = "仿宋";
    private static final int FONT_SIZE = 12;
    private static final int FOOT_FONT_SIZE = 16;
    private static final String[] FOOTER_KEYS = {"ccUnits", "signer"};

    /**
     * makeWorkInfo<BR>
     *
     * @param from   参数
     * @param user   参数
     * @param entity 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/6/9 16:40
     */
    public static String makeWorkInfo(String from, CurrentUser user, XianSuoBaseInfoEntity entity) {
        var list = Optional.ofNullable(entity.getRelatedPersons())
                .map(it -> it.stream()
                        .filter(i -> Try.of(
                                () -> {
                                    switch (from) {
                                        case Constants.UP:
                                            // 线索的创建人(按照XMKFB-1596要求屏蔽创建人)以及指派给当前部门的人员都是可见的
                                            if (Objects.equals(i.getCheckDeptId(), user.getDeptId())) {
                                                return true;
                                            }
                                            // 我接收的中，如果是上报过程中应该是都可见
                                            if (Constants.PUSHUP.equals(entity.getUpStatusType())) {
                                                return true;
                                            }
                                            // 如果用户可以看子的数据，就判断指派单位是否在其的子地域中
                                            if (DeptUtils.canSeeChildrenData(user)) {
                                                return isStartWithCode(i.getCheckDeptCode(), spreadingAreaCode(user.getDept().getDistrictCode(), false));
                                            }
                                            return false;
                                        default:
                                            // 其他地方看到的应该都是全部
                                            return true;
                                    }
                                }
                        ).getOrElse(false)).collect(Collectors.toList())
                ).orElse(Collections.emptyList());
        Long all = Integer.valueOf(list.size()).longValue();
        Long assign = list.stream().filter(it -> {
            // 没有指派单位的(草稿箱的数据按照XMKFB-1727要求都是未指派)
            if (Objects.isNull(it.getCheckDeptId())
                    || Objects.isNull(it.getAssignTime())
                    || Objects.equals(1, entity.getDraftsFlag())
            ) {
                return false;
            }
            // 指派单位在自己这儿的
            if (Objects.equals(it.getCheckDeptId(), user.getDeptId())) {
                return false;
            }
            // 如果不能看子数据，那么就都算已指派
            if (!DeptUtils.canSeeChildrenData(user)) {
                return true;
            }
            // 可以看子的需要看地域编码是否在其子地域中
            return Try.of(() -> isStartWithCode(it.getCheckDeptCode(), spreadingAreaCode(user.getDept().getDistrictCode(), false)))
                    .getOrElse(false);
        }).count();
        Long worked = list.stream().filter(it -> Constants.CN_YIWENKONG.equals(it.getWorkStatus())).count();
        return String.format("%s/%s/%s", assign, worked, all);
    }

    /**
     * 模板导出doc
     *
     * @param doc          参数
     * @param placeholders 参数
     * @param personList   参数
     */
    public static void processTemplate(XWPFDocument doc, Map<String, String> placeholders, List<Map<String, String>> personList) {
        // 处理普通占位符
        replaceGlobalPlaceholders(doc, placeholders);
        // 处理动态表格
        if (!personList.isEmpty()) {
            processPersonTable(doc, personList);
        }
        List<XWPFParagraph> paragraphs = doc.getParagraphs();
        for (XWPFParagraph p : paragraphs) {
            String text = p.getText();
            if (text != null) {
                if ((text.startsWith("抄送单位") || text.startsWith("签发"))) {
                    // 仅修改字体
                    setFooterFont(p);
                }
            }
        }
    }

    private static void setFooterFont(XWPFParagraph p) {
        // 保留原有段落格式（缩进、对齐等），仅修改字体
        for (XWPFRun run : p.getRuns()) {
            applyFontStyle(run, true);
        }
    }

    private static void replaceGlobalPlaceholders(XWPFDocument doc, Map<String, String> data) {
        final String key = "dataContent";
        // 处理段落中的占位符
        doc.getParagraphs().forEach(p -> {
            String text = p.getText();
            if (text != null) {
                for (Map.Entry<String, String> entry : data.entrySet()) {
                    if (key.equals(entry.getKey())) {
                        replaceParagraphTextContent(p, text, key, entry.getValue());
                    } else {
                        text = text.replace("{{" + entry.getKey() + "}}", entry.getValue());
                        replaceParagraphText(p, text, key);
                    }
                }
            }
        });
    }

    private static void processPersonTable(XWPFDocument doc, List<Map<String, String>> personList) {
        // 通过表格特征定位目标表格
        for (XWPFTable table : doc.getTables()) {
            if (isTargetTable(table)) {
                // 清空模板行（保留表头）
                while (table.getRows().size() > 1) {
                    table.removeRow(1);
                }
                // 动态添加数据行
                for (int i = 0; i < personList.size(); i++) {
                    Map<String, String> person = personList.get(i);
                    XWPFTableRow newRow = table.createRow();
                    // 序号列自动生成
                    setCellValue(newRow, 0, String.valueOf(i + 1));
                    setCellValue(newRow, 1, person.get("zjhm"));
                    setCellValue(newRow, 2, person.get("xm"));
                    setCellValue(newRow, 3, person.get("sjhwm"));
                    setCellValue(newRow, 4, person.get("hjd"));
                    setCellValue(newRow, 5, person.get("gsdymc"));
                    setCellValue(newRow, 6, "已匹配");
                }
                break;
            }
        }
    }

    private static boolean isTargetTable(XWPFTable table) {
        // 通过表头特征识别目标表格
        if (table.getRows().isEmpty()) {
            return false;
        }
        XWPFTableRow headerRow = table.getRow(0);
        return headerRow.getCell(0).getText().contains("序号")
                && headerRow.getCell(1).getText().contains("身份证");
    }


    private static void replaceParagraphTextContent(XWPFParagraph p, String newText, String key, String val) {
        if (!newText.startsWith("{{" + key + "}}")) {
            return;
        }
        // 清空原有Run
        for (XWPFRun run : p.getRuns()) {
            run.setText("", 0);
        }
        // 添加新内容到第一个Run
        XWPFRun run;
        // 处理换行
        newText = newText.replace("{{" + key + "}}", val);
        String[] lines = newText.split("\n");
        if (!p.getRuns().isEmpty()) {
            run = p.getRuns().get(0);
            int i = 0;
            for (String line : lines) {
                run.setText(line, i);
                run.addBreak();
                i++;
            }
        } else {
            run = p.createRun();
            for (String line : lines) {
                run.setText(line);
                run.addBreak();
            }
        }
        // 移除最后一个多余换行
        run.removeBreak();
    }

    private static void replaceParagraphText(XWPFParagraph p, String newText, String key) {
        if (newText.startsWith("{{" + key + "}}")) {
            return;
        }
        // 清空原有Run
        for (XWPFRun run : p.getRuns()) {
            run.setText("", 0);
        }
        // 添加新内容到第一个Run
        if (!p.getRuns().isEmpty()) {
            p.getRuns().get(0).setText(newText, 0);
        } else {
            p.createRun().setText(newText);
        }
    }

    private static void setCellValue(XWPFTableRow row, int col, String value) {
        XWPFTableCell cell = row.getCell(col);
        if (cell == null) {
            cell = row.createCell();
        }
        // 清除旧样式
        cell.removeParagraph(0);
        // 垂直居中
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        // 水平居中
        cell.getParagraphs().forEach(p ->
                p.setAlignment(ParagraphAlignment.CENTER)
        );
        XWPFParagraph p = cell.addParagraph();
        XWPFRun run = p.createRun();
        run.setText(value);
        // 设置大小和字体
        applyFontStyle(run, false);
    }

    private static void applyFontStyle(XWPFRun run, boolean isFoot) {
        run.setFontFamily(FONT_SONG);
        if (isFoot) {
            run.setFontSize(FOOT_FONT_SIZE);
        } else {

            run.setFontSize(FONT_SIZE);
        }
        // 其他样式（可选）
        run.setColor("000000");
    }

}
