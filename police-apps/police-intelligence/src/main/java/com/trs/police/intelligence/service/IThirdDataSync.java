package com.trs.police.intelligence.service;

import com.trs.common.base.Report;
import com.trs.common.exception.ServiceException;
import com.trs.police.intelligence.dto.third.FeedbackDataDTO;
import com.trs.police.intelligence.dto.third.PullDataDTO;
import com.trs.police.intelligence.dto.third.PushDataDTO;
import com.trs.police.intelligence.dto.third.SignDataDTO;
import com.trs.police.intelligence.vo.third.ProvinceClueReportVO;
import com.trs.police.intelligence.vo.third.PullDataVo;
import com.trs.web.builder.base.IKey;

import java.util.List;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/9/25 09:27
 * @since 1.0
 */
public interface IThirdDataSync extends IKey {

    /**
     * syncData<BR>
     *
     * @param taskId 参数
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/25 09:27
     */
    void syncData(String taskId) throws ServiceException;

    /**
     * syncData<BR>
     *
     * @param taskId    参数
     * @param startTime 参数
     * @param endTime   参数
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/4/27 19:04
     */
    void syncData(String taskId, String startTime, String endTime) throws ServiceException;

    /**
     * pullData<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/25 09:30
     */
    List<PullDataVo> pullData(PullDataDTO dto) throws ServiceException;

    /**
     * pushData<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/25 09:30
     */
    ProvinceClueReportVO pushData(PushDataDTO dto) throws ServiceException;

    /**
     * signData<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/25 09:59
     */
    Report<String> signData(SignDataDTO dto) throws ServiceException;

    /**
     * feedbackData<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/25 10:00
     */
    Report<String> feedbackData(FeedbackDataDTO dto) throws ServiceException;
}
