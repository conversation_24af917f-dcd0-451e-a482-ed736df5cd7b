package com.trs.police.intelligence.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.trs.common.pojo.BaseVO;
import com.trs.police.common.core.vo.permission.SimpleDeptVO;
import lombok.Data;

import java.util.Date;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * BaseVersionVo
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/11 23:36
 * @since 1.0
 */
@Data
public abstract class BaseVersionVo extends BaseVO {

    private Long yqDataId;

    private Long dataId;

    private Long versionId;

    private Integer versionFlag;

    private String versionTitle;

    private String versionContent;

    private String versionCrUser;

    private String versionCrUserTrueName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date versionCrTime;

    private String versionUpdateUser;

    private String versionUpdateUserTrueName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastVersionTime;

    private SimpleDeptVO crDept;

    private SimpleDeptVO updateDept;
}
