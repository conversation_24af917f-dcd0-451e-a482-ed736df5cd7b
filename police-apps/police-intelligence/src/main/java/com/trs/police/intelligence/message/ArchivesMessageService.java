package com.trs.police.intelligence.message;

import com.trs.police.common.core.vo.search.ArchivesVO;
import com.trs.police.common.openfeign.starter.service.SearchService;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/10/10 11:29
 * @since 1.0
 */
@Service
@Getter
@Slf4j
public class ArchivesMessageService {

    private final SearchService searchService;

    public ArchivesMessageService(SearchService searchService) {
        this.searchService = searchService;
    }

    /**
     * pushArchivesMessage<BR>
     *
     * @param message 参数
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/10 11:32
     */
    public void pushArchivesMessage(List<ArchivesVO> message) {
        var flag = BeanFactoryHolder.getEnv()
                .getProperty("intelligence.archivesMessage.canPush", Boolean.class, true);
        if (flag) {
            getSearchService().pushArchivesMessage(message);
        } else {
            log.info("未开启档案消息发送");
        }
    }
}
