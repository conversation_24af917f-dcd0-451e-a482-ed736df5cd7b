package com.trs.police.intelligence.dto.third;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName ProvinceCluePersonDTO
 * @Description 省厅线索核查人员DTO
 * <AUTHOR>
 * @Date 2024/9/4 17:59
 **/
@Data
public class ProvinceClueReportPersonDTO implements Serializable {

    /**
     * 人员身份证
     */
    private String rySfz;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 手机号
     */
    private String sjh;

    /**
     * 人员类型代码（字典）,参考附录:人员类型字典
     */
    private String rylxDm;

    /**
     * 人员类型名称
     */
    private String rylxMc;

    /**
     * 户籍地代码
     */
    private String hjdDm;

    /**
     * 户籍地名称
     */
    private String hjdMc;

    /**
     * 备注
     */
    private String bz;

}
