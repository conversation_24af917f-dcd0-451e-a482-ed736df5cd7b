package com.trs.police.intelligence.constant;

import com.trs.common.base.PreConditionCheck;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * Status
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/11 23:04
 * @since 1.0
 */
public enum Status {

    /**
     * 无状态的数据
     */
    WUZHUANGTAI("WUZHUANGTAI", "", -1),
    CAOGAOXIANG("CAOGAOXIANG", "草稿箱", 0),

    DAIQIANSHOU("DAIQIANSHOU", "待签收", 100),
    DAICHULI("DAICHULI", "待处理", 101),
    DAIFANKUI("DAIFANKUI", "待反馈", 102),
    JINXINGZHONG("JINXINGZHONG", "进行中", 103),
    CHUZHIZHONG("CHUZHIZHONG", "处置中", 104),

    YIQIANSHOU("YIQIANSHOU", "已签收", 110),

    YICAIYONG("YICAIYONG", "已采用", 111),

    BUCAIYONG("BUCAIYONG", "不采用", 112),

    YIZHUANBAO("YIZHUANBAO", "已转报", 113),

    YIXIAFA("YIXIAFA", "已下发", 114),

    YIWANCHENG("YIWANCHENG", "已完成", 115),
    YIFANKUI("YIFANKUI", "已反馈", 116),

    YITUIHUI("YITUIHUI", "已退回", 120),

    YITONGBAO("YITONGBAO", "已通报", 130),

    YIZHONGBAO("YIZHONGBAO", "已终报", 140),
    YIWANJIE("YIWANJIE", "已完结", 141),

    WEIJIEAN("WEIJIEAN", "未结案", 150),

    YIJIEAN("YIJIEAN", "已结案", 160),

    YIZUOFEI("YIZUOFEI", "已作废", 170),

    YIFABU("YIFABU", "已发布", 180);

    private final String enName;
    private final String zhName;
    private final Integer code;

    /**
     * getEnName<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 23:08
     */
    public String getEnName() {
        return enName;
    }

    /**
     * getZhName<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 23:08
     */
    public String getZhName() {
        return zhName;
    }

    /**
     * getCode<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 23:09
     */
    public Integer getCode() {
        return code;
    }

    Status(String enName, String zhName, Integer code) {
        this.enName = enName;
        this.zhName = zhName;
        this.code = code;
    }

    /**
     * findByCode<BR>
     *
     * @param code 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 23:09
     */
    public static Status findByCode(Integer code) {
        PreConditionCheck.checkNotNull(code, "code不能为空");
        for (Status value : Status.values()) {
            if (code.equals(value.getCode())) {
                return value;
            }
        }
        throw new IllegalArgumentException("code[" + code + "]不存在对应状态");
    }
}
