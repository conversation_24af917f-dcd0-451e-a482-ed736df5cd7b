package com.trs.police.intelligence.service;

import com.trs.common.exception.ServiceException;
import com.trs.police.common.core.constant.log.OperateModule;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.mapper.DeptMapper;
import com.trs.police.intelligence.constant.Constants;
import com.trs.police.intelligence.dto.BaseActionDTO;
import com.trs.police.intelligence.entity.ActionLogEntity;
import com.trs.police.intelligence.entity.DataRelationMappingEntity;
import com.trs.police.intelligence.entity.RenWuBaseInfoEntity;
import com.trs.police.intelligence.entity.RenWuRelatedPersonEntity;
import com.trs.police.intelligence.mapper.RenWuBaseInfoMapper;
import com.trs.police.intelligence.mapper.RenWuRelatedPersonMapper;
import com.trs.police.intelligence.mapper.RenWuRelatedPersonMappingMapper;
import com.trs.police.intelligence.service.impl.RenWuEntityServiceImpl;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * @param <T>    参数
 * @param <R>    参数
 * @param <OneR> 参数
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/17 20:52
 * @since 1.0
 */
@Slf4j
public abstract class BaseRenWuAction<T extends BaseActionDTO, R, OneR>
        extends BaseAction<T, R, OneR, RenWuBaseInfoEntity> {

    @Resource
    @Getter
    private RenWuBaseInfoMapper mapper;

    @Getter
    @Resource
    private RenWuEntityServiceImpl entityService;

    @Resource
    @Getter
    private RenWuRelatedPersonMapper relatedPersonMapper;

    @Resource
    @Getter
    private RenWuRelatedPersonMappingMapper relatedPersonMappingMapper;

    @Getter
    @Resource
    private ICommonService commonService;

    @Getter
    @Resource
    private DeptMapper deptMapper;

    /**
     * needRecordPersonLog<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/9 23:04
     */
    public boolean needRecordPersonLog() {
        return false;
    }


    /**
     * makeLogContent<BR>
     *
     * @param login   参数
     * @param entity  参数
     * @param person  参数
     * @param logType 参数
     * @param dto     参数
     * @param one     参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/9 23:08
     */
    protected String makePersonLogContent(
            Optional<CurrentUser> login,
            RenWuBaseInfoEntity entity,
            RenWuRelatedPersonEntity person,
            String logType,
            T dto,
            OneR one
    ) throws ServiceException {
        if (needRecordPersonLog()) {
            throw new ServiceException(desc() + "需要重写该方法");
        }
        return "";
    }

    /**
     * recordPersonLog<BR>
     *
     * @param login   参数
     * @param entity  参数
     * @param person  参数
     * @param logType 参数
     * @param dto     参数
     * @param one     参数
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/9 23:12
     */
    public void recordPersonLog(
            Optional<CurrentUser> login,
            RenWuBaseInfoEntity entity,
            RenWuRelatedPersonEntity person,
            String logType,
            T dto,
            OneR one
    ) throws ServiceException {
        ActionLogEntity log = ActionLogEntity.addInfoOnCreate(
                login.orElseThrow(() -> new ServiceException("未登录")),
                new ActionLogEntity()
        );
        log.setContent(makePersonLogContent(login, entity, person, logType, dto, one));
        log.setLogType(logType);
        log.setObjType(Constants.RENWU_RELATED_PERSON);
        log.setObjId(person.getDataId());
        log.setObjRootId(entity.getDataId());
        log.setAttachment(dto.getAttachment());
        getActionLogMapper().insert(addOtherDataForPersonLog(log, login, entity, person, logType, dto, one));
        // 发送消息
        getSearchService().pushArchivesMessage(person.convertToArchivesVO());
    }

    /**
     * addOtherDataForPersonLog<BR>
     *
     * @param log     参数
     * @param login   参数
     * @param entity  参数
     * @param person  参数
     * @param logType 参数
     * @param dto     参数
     * @param one     参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/14 15:52
     */
    protected ActionLogEntity addOtherDataForPersonLog(
            ActionLogEntity log,
            Optional<CurrentUser> login,
            RenWuBaseInfoEntity entity,
            RenWuRelatedPersonEntity person,
            String logType,
            T dto,
            OneR one
    ) {
        return log;
    }

    /**
     * findByIds<BR>
     *
     * @param ids 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/9 13:13
     */
    @Override
    protected List<RenWuBaseInfoEntity> findByIds(List<Long> ids) {
        return getMapper().findByIds(ids);
    }

    /**
     * operateModule<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/7/2 14:57
     */
    @Override
    public OperateModule operateModule() {
        return OperateModule.INTELLIGENCE_RENWU;
    }

    /**
     * needSendMessage<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:22
     */
    @Override
    protected Boolean needSendChatMessage() {
        return false;
    }

    /**
     * makeMessageSimpleContent<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @param one    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 17:07
     */
    @Override
    protected String makeMessageSimpleContent(Optional<CurrentUser> login, RenWuBaseInfoEntity entity, T dto,
                                              OneR one) throws ServiceException {
        return "";
    }

    /**
     * makeMessageContent<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @param one    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:38
     */
    @Override
    protected String makeMessageContent(Optional<CurrentUser> login, RenWuBaseInfoEntity entity, T dto, OneR one)
            throws ServiceException {
        return "";
    }

    /**
     * clearPushDownMapping<BR>
     *
     * @param taskId 参数
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/20 16:13
     */
    public void clearPushDownMapping(Long taskId) throws ServiceException {
        final List<DataRelationMappingEntity> mappings = getDataRelationMappingMgr().getRelationList(
                module(),
                taskId,
                Constants.DEPT,
                Constants.PUSHDOWN
        );
        final var personDeptIds = relatedPersonMapper.relatedPersonList(taskId)
                .stream()
                .filter(it -> Objects.nonNull(it.getAssignTime()))
                .flatMap(it -> {
                    List<Long> deptIds = new ArrayList<>(3);
                    deptIds.add(it.getMainDeptId());
                    deptIds.add(it.getLeaderDeptId());
                    deptIds.addAll(it.makeOtherDeptIds());
                    return deptIds.stream();
                }).filter(Objects::nonNull).collect(Collectors.toSet());
        for (DataRelationMappingEntity mapping : mappings) {
            if (!personDeptIds.contains(mapping.getObjId())) {
                log.info("删除数据[{}]", mapping);
                getDataRelationMappingMgr().deleteEntity(mapping.getDataId());
            }
        }
    }

    /**
     * doOneSomeThingAfter<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @param oneR   参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:30
     */
    @Override
    public OneR doOneSomeThingAfter(Optional<CurrentUser> login, RenWuBaseInfoEntity entity, T dto, OneR oneR) throws ServiceException {
        login.ifPresent(user -> addProcessed(user, entity));
        return oneR;
    }

    /**
     * addProcessed<BR>
     *
     * @param login  参数
     * @param entity 参数
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/1/6 14:30
     */
    public void addProcessed(CurrentUser login, RenWuBaseInfoEntity entity) {
        final var target = getDataRelationMappingMgr().getRelationList(
                        module(),
                        entity.getDataId(),
                        Constants.DEPT,
                        Constants.DATA_RELATION_STATUS_TYPE_PROCESSED
                ).stream()
                .map(DataRelationMappingEntity::getObjId)
                .distinct()
                .collect(Collectors.toList());
        // 塞入已处理的
        if (!target.contains(login.getDeptId())) {
            target.add(login.getDeptId());
            getDataRelationMappingMgr().saveRelation(
                    login,
                    module(),
                    entity.getDataId(),
                    Constants.DEPT,
                    target,
                    Constants.DATA_RELATION_STATUS_TYPE_PROCESSED,
                    0,
                    false
            );
        }
    }
}
