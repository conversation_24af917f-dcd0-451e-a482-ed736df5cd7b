package com.trs.police.intelligence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.db.sdk.pojo.BaseRecordDO;
import com.trs.police.intelligence.vo.GroupDetailVo;
import com.trs.police.intelligence.vo.UserDetailVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/6/13 14:04
 * @since 1.0
 */
@Mapper
public interface IntelligenceCommonMapper extends BaseMapper<BaseRecordDO> {

    /**
     * findGroupDetail<BR>
     *
     * @param id 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/13 14:11
     */
    List<GroupDetailVo> findGroupDetail(@Param("id") Long id);

    /**
     * findUserDetail<BR>
     *
     * @param id 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/13 16:35
     */
    UserDetailVo findUserDetail(@Param("id") Long id);
}
