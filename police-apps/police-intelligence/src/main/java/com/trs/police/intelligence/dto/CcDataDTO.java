package com.trs.police.intelligence.dto;

import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.common.utils.StringUtils;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.trs.common.base.PreConditionCheck.checkArgument;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/5/9 11:19
 * @since 1.0
 */
@Data
@ToString(callSuper = true)
public class CcDataDTO extends BaseActionDTO {

    private String ccDeptIds;

    /**
     * makeCcDeptIds<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/9 16:15
     */
    public List<Long> makeCcDeptIds() {
        return Arrays.stream(StringUtils.showEmpty(getCcDeptIds()).split(StringUtils.SEPARATOR_COMMA_OR_SEMICOLON))
                .filter(StringUtils::isNotEmpty)
                .map(Long::valueOf)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL> 创建时间：2020-09-10 15:49
     */
    @Override
    protected boolean checkParams() throws ServiceException {
        checkArgument(CollectionUtils.isNotEmpty(makeCcDeptIds()), new ParamInvalidException("抄送目标单位不能为空"));
        return super.checkParams();
    }
}
