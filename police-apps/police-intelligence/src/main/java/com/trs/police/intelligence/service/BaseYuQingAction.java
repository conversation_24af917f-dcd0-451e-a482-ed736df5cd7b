package com.trs.police.intelligence.service;

import com.trs.common.exception.ServiceException;
import com.trs.police.common.core.constant.log.OperateModule;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.intelligence.dto.BaseActionDTO;
import com.trs.police.intelligence.entity.YuQingBaseInfoEntity;
import com.trs.police.intelligence.mapper.YuQingBaseInfoMapper;
import com.trs.police.intelligence.mapper.YuQingVersionInfoMapper;
import com.trs.police.intelligence.service.impl.YuQingEntityServiceImpl;
import lombok.Getter;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * BaseYuQingAction
 *
 * @param <T>    参数
 * @param <R>    参数
 * @param <OneR> 参数
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/17 20:52
 * @since 1.0
 */
public abstract class BaseYuQingAction<T extends BaseActionDTO, R, OneR>
        extends BaseAction<T, R, OneR, YuQingBaseInfoEntity> {

    @Resource
    @Getter
    private YuQingBaseInfoMapper mapper;

    @Getter
    @Resource
    private YuQingEntityServiceImpl entityService;

    @Resource
    @Getter
    private YuQingVersionInfoMapper versionMapper;

    /**
     * operateModule<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/7/2 14:57
     */
    @Override
    public OperateModule operateModule() {
        return OperateModule.INTELLIGENCE_YUQING;
    }

    /**
     * needSendMessage<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:22
     */
    @Override
    protected Boolean needSendChatMessage() {
        return false;
    }

    @Override
    protected Boolean needSendNoticeMessage() {
        return false;
    }

    /**
     * makeMessageSimpleContent<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @param one    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 17:07
     */
    @Override
    protected String makeMessageSimpleContent(Optional<CurrentUser> login, YuQingBaseInfoEntity entity, T dto,
                                              OneR one) throws ServiceException {
        return "";
    }

    /**
     * makeMessageContent<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @param one    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:38
     */
    @Override
    protected String makeMessageContent(Optional<CurrentUser> login, YuQingBaseInfoEntity entity, T dto, OneR one)
            throws ServiceException {
        return "";
    }
}
