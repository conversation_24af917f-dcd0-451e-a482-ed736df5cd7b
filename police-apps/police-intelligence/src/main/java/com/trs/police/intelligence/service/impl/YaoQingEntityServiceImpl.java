package com.trs.police.intelligence.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepoove.poi.data.*;
import com.deepoove.poi.data.style.BorderStyle;
import com.trs.common.base.PreConditionCheck;
import com.trs.common.base.Report;
import com.trs.common.base.Report.RESULT;
import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.constant.enums.MessageTypeEnum;
import com.trs.police.common.core.constant.message.WebsocketMessageTypeConstant;
import com.trs.police.common.core.dto.UserDto;
import com.trs.police.common.core.entity.BigScreenPinDataEntity;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.core.vo.permission.SimpleDeptVO;
import com.trs.police.intelligence.constant.Constants;
import com.trs.police.intelligence.constant.Status;
import com.trs.police.intelligence.dto.DataAttributesDTO;
import com.trs.police.intelligence.dto.EntityDetailDTO;
import com.trs.police.intelligence.dto.ExportDTO;
import com.trs.police.intelligence.dto.YaoQingSaveDTO;
import com.trs.police.intelligence.entity.CollectionEntity;
import com.trs.police.intelligence.entity.YaoQingBaseInfoEntity;
import com.trs.police.intelligence.entity.YaoQingVersionInfoEntity;
import com.trs.police.intelligence.mapper.YaoQingBaseInfoMapper;
import com.trs.police.intelligence.mapper.YaoQingVersionInfoMapper;
import com.trs.police.intelligence.mgr.BaseFieldValidator;
import com.trs.police.intelligence.mgr.EntityConvertToVo;
import com.trs.police.intelligence.service.BaseIntelligenceEntityService;
import com.trs.police.intelligence.utils.FieldUtils;
import com.trs.police.intelligence.utils.WordUtils;
import com.trs.police.intelligence.vo.AttributeItemExportVo;
import com.trs.police.intelligence.vo.AttributeTemplatesVo;
import com.trs.police.intelligence.vo.TotalAndNumberVo;
import com.trs.police.intelligence.vo.YaoQingEntityVo;
import com.trs.web.builder.base.RestfulResultsV2;
import com.trs.web.builder.util.BeanFactoryHolder;
import io.vavr.Tuple2;
import io.vavr.control.Try;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.trs.common.base.PreConditionCheck.checkArgument;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * YaoQingEntityServiceImpl
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/12 01:45
 * @since 1.0
 */
@Service
@Slf4j
public class YaoQingEntityServiceImpl extends BaseIntelligenceEntityService<
        YaoQingBaseInfoEntity,
        YaoQingVersionInfoEntity,
        YaoQingSaveDTO,
        YaoQingEntityVo,
        YaoQingEntityVo
        > {

    @Resource
    @Getter
    private YaoQingBaseInfoMapper mapper;

    @Resource
    @Getter
    private YaoQingVersionInfoMapper versionMapper;

    /**
     * mergeOnSave<BR>
     *
     * @param user        参数
     * @param isAdd       参数
     * @param templatesVo 参数
     * @param saveDTO     参数
     * @param attributes  参数
     * @param entity      参数
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/12 02:14
     */
    private void mergeOnSave(CurrentUser user, Boolean isAdd, AttributeTemplatesVo templatesVo, YaoQingSaveDTO saveDTO, List<DataAttributesDTO> attributes, YaoQingBaseInfoEntity entity) throws ServiceException {
        if (isAdd) {
            Date date = new Date();
            entity.setCrDeptId(user.getDeptId());
            entity.setCrUser(user.getUsername());
            entity.setCrUserTrueName(StringUtils.showEmpty(user.getRealName(), user.getUsername()));
            entity.setCrTime(date);
            entity.setIsDel(0);
            entity.setRootId(Optional.ofNullable(saveDTO.getRootId()).orElse(0L));
            entity.setDataType(saveDTO.getDataType());
            entity.setDataClass(saveDTO.getDataClass());
            entity.setDataNo(getMaxNo(saveDTO.getDataClass()) + 1);
            entity.setDataYear(TimeUtils.getFieldOfDate(date, Calendar.YEAR));
            entity.setDataTitle(saveDTO.getDataTitle());
            entity.setDraftsFlag(Optional.ofNullable(saveDTO.getDraftsFlag()).orElse(1));
            entity.setStatusCode(Status.CAOGAOXIANG.getCode());
        }
        Optional.ofNullable(saveDTO.getCrDeptId()).ifPresent(entity::setCrDeptId);
        // 草稿箱的数据还是可以更新标题
        if (Objects.equals(entity.getStatusCode(), Status.CAOGAOXIANG.getCode())) {
            entity.setDataTitle(saveDTO.getDataTitle());
        }
        // 副标题可以一直更新
        entity.setDataSubTitle(saveDTO.getDataSubTitle());
        if (TimeUtils.isValid(saveDTO.getNextReportTimeLimit())) {
            entity.setNextReportTimeLimit(TimeUtils.stringToDate(saveDTO.getNextReportTimeLimit()));
        }
        entity.setClassicalFlag(Optional.ofNullable(saveDTO.getClassicalFlag()).orElse(0));
        // 终报之后修改状态为已终报
        if (Objects.equals(saveDTO.getVersionFlag(), Constants.VERSION_FLAG_ZHONGBAO)) {
            entity.setStatusCode(Status.YIZHONGBAO.getCode());
            entity.setFinalReportTime(new Date());
        } else if (Objects.equals(saveDTO.getVersionFlag(), Constants.VERSION_FLAG_XUBAO_OR_XIUDING)) {
            entity.setNextReportTimeLast(new Date());
        }
        // 先清空，避免用户取消了事发时间后不生效
        entity.setHappenedTime(null);
        if (TimeUtils.isValid(saveDTO.getHappenedTime())) {
            entity.setHappenedTime(TimeUtils.stringToDate(saveDTO.getHappenedTime()));
        }
        // 先清空，避免用户取消了所有标签后不生效
        entity.setLabels(null);
        attributes.stream()
                .filter(it -> Constants.LABELS.equals(it.getKey()))
                .map(DataAttributesDTO::getValue)
                .findFirst()
                .ifPresent(entity::setLabels);
        Long all = makeAllCount(templatesVo, attributes);
        Long edit = makeEditCount(templatesVo, attributes);
        entity.setFieldsEditInfo(edit + StringUtils.SEPARATOR_SEMICOLON + Math.max(edit, all));
    }

    /**
     * mergeData<BR>
     *
     * @param dto     参数
     * @param entity  参数
     * @param version 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/12 00:13
     */
    @Override
    public YaoQingEntityVo mergeDataOnDetail(EntityDetailDTO dto, YaoQingBaseInfoEntity entity, YaoQingVersionInfoEntity version)
            throws ServiceException {
        List<Long> deptIds = new ArrayList<>(0);
        Optional.ofNullable(findAcceptDeptIds(entity, version))
                .filter(CollectionUtils::isNotEmpty)
                .ifPresent(deptIds::addAll);
        Optional.ofNullable(entity.makeCcDeptIds())
                .filter(CollectionUtils::isNotEmpty)
                .ifPresent(deptIds::addAll);
        Optional.ofNullable(entity.getCrDeptId())
                .filter(it -> !deptIds.contains(it))
                .ifPresent(deptIds::add);
        Map<Long, SimpleDeptVO> deptMapping = Optional.of(deptIds)
                .filter(CollectionUtils::isNotEmpty)
                .map(this::findDeptByIds)
                .map(it -> it.stream().collect(Collectors.toMap(
                        SimpleDeptVO::getDeptId,
                        r -> r,
                        (a, b) -> a
                ))).orElse(new HashMap<>(0));
        Map<String, UserDto> userMapping = new HashMap<>(1);
        findUserByName(entity.getCrUser()).ifPresent(it -> userMapping.put(it.getUsername(), it));
        final YaoQingEntityVo vo = makeVo(entity, deptMapping, userMapping);
        vo.setVersionDataTitle(version.getVersionDataTitle());
        vo.setVersionDataSubTitle(version.getVersionDataSubTitle());
        vo.setDataContent(version.getVersionDataContent());
        vo.setVersionDataContent(version.getVersionDataContent());
        vo.setVersionHappenedTime(version.getVersionHappenedTime());
        AttributeTemplatesVo template = findAttributeTemplates(entity.getDataType(), entity.getDataClass());
        List<Map<String, Object>> list = getAttributeMapper().findAttribute(
                template.getRelationTableName(),
                entity.getDataId(),
                version.getVersionId()
        );
        Map<String, Object> dbData;
        if (!list.isEmpty()) {
            dbData = list.get(0);
        } else {
            dbData = Collections.emptyMap();
        }
        Map<String, String> attributes = FieldUtils.getAttributes(template, dbData);
        vo.setAttributes(attributes);
        return vo;
    }

    /**
     * 获取最大的编号<BR>
     *
     * @param dataClass 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/12 09:22
     */
    @Override
    public Integer getMaxNo(String dataClass, CurrentUser user) throws ServiceException {
        if (Objects.isNull(user)) {
            user = AuthHelper.getNotNullUser();
        }
        return new LambdaQueryChainWrapper<>(getMapper())
                .select(YaoQingBaseInfoEntity::getDataNo)
                .eq(YaoQingBaseInfoEntity::getIsDel, 0)
                .eq(YaoQingBaseInfoEntity::getCrDeptId, user.getDeptId())
                .eq(YaoQingBaseInfoEntity::getDataYear, TimeUtils.getFieldOfDate(new Date(), Calendar.YEAR))
                .orderByDesc(YaoQingBaseInfoEntity::getDataNo)
                .page(new Page<>(1, 1))
                .getRecords()
                .stream()
                .filter(Objects::nonNull)
                .map(YaoQingBaseInfoEntity::getDataNo)
                .findFirst()
                .orElse(0);
    }

    /**
     * getOrderNo<BR>
     *
     * @param dataClass 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/19 11:52
     */
    @Override
    public String getOrderNo(String dataClass, CurrentUser user) throws ServiceException {
        return Integer.toString(getMaxNo(dataClass));
    }

    /**
     * saveOrUpdate<BR>
     *
     * @param saveDTO 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 22:39
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Report<String> saveOrUpdate(YaoQingSaveDTO saveDTO, CurrentUser user) throws ServiceException {
        saveDTO.isValid();
        YaoQingBaseInfoEntity entity;
        boolean isAdd = false;
        AttributeTemplatesVo templatesVo = findAttributeTemplates(saveDTO.getDataType(), saveDTO.getDataClass());
        if (saveDTO.getDataId() != null && saveDTO.getDataId() > 0L) {
            entity = findById(saveDTO.getDataId());
            checkArgument(
                    entity.userCreateEntity(Optional.of(user)),
                    new ServiceException("暂不支持处理他人上报的" + desc())
            );
        } else {
            entity = new YaoQingBaseInfoEntity();
            isAdd = true;
        }
        List<DataAttributesDTO> attributes = saveDTO.makeAttributes();
        check(isAdd, templatesVo, saveDTO, attributes, entity);
        mergeOnSave(user, isAdd, templatesVo, saveDTO, attributes, entity);
        if (entity.getDataId() != null && entity.getDataId() > 0L) {
            getMapper().updateById(entity);
        } else {
            getMapper().insert(entity);
        }
        // 回填rootId
        if (Optional.ofNullable(entity.getRootId()).filter(it -> it > 0L).isEmpty()) {
            entity.setRootId(entity.getDataId());
            getMapper().updateById(entity);
        }
        YaoQingVersionInfoEntity versionInfo = YaoQingVersionInfoEntity.of(
                user,
                entity.getDataId(),
                saveDTO.getVersionFlag(),
                saveDTO.getDataTitle(),
                saveDTO.getDataSubTitle(),
                saveDTO.getDataContent(),
                TimeUtils.isValid(saveDTO.getHappenedTime()) ? TimeUtils.stringToDate(saveDTO.getHappenedTime()) : null
        );
        // 回填版本,草稿阶段的数据才能重复覆盖
        if (!isAdd
                && saveDTO.getVersionFlag() == 0
                && Optional.ofNullable(entity.getVersionId()).filter(it -> it > 0L).isPresent()) {
            versionInfo.setDataId(entity.getVersionId());
            getVersionMapper().updateById(versionInfo);
        } else {
            getVersionMapper().insert(versionInfo);
        }
        entity.setVersionId(versionInfo.getVersionId());
        getMapper().updateById(entity);
        getAttributeMapper().deleteAttributeDataId(
                templatesVo.getRelationTableName(),
                entity.getDataId(),
                entity.getVersionId()
        );
        final var map = saveDTO.makeDbMap(templatesVo, attributes);
        map.put("yq_data_id", entity.getDataId());
        map.put("version_id", entity.getVersionId());
        map.put("is_del", 0);
        map.put("cr_user", user.getUsername());
        map.put("cr_time", TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD_HHMMSS));
        map.put("cr_dept_id", user.getDeptId());
        List<String> keys = new ArrayList<>(map.size());
        List<Object> values = new ArrayList<>(map.size());
        map.forEach((key, value) -> {
            keys.add(key);
            values.add(value);
        });
        getAttributeMapper().addAttributeData(
                templatesVo.getRelationTableName(),
                keys,
                values
        );
        return new Report<>("保存要情", "成功进行保存", RESULT.SUCCESS, entity.getDataId());
    }


    /**
     * check<BR>
     *
     * @param isAdd       参数
     * @param templatesVo 参数
     * @param saveDTO     参数
     * @param attributes  参数
     * @param entity      参数
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/12 02:01
     */
    @Override
    public void check(Boolean isAdd, AttributeTemplatesVo templatesVo, YaoQingSaveDTO saveDTO, List<DataAttributesDTO> attributes, YaoQingBaseInfoEntity entity) throws ServiceException {
        super.check(isAdd, templatesVo, saveDTO, attributes, entity);
        if (!isAdd) {
            checkArgument(
                    !Objects.equals(entity.getStatusCode(), Status.YIZHONGBAO.getCode()),
                    new ServiceException("已终报的数据不能再编辑")
            );
            if (Objects.equals(saveDTO.getVersionFlag(), Constants.VERSION_FLAG_SHOUBAO)) {
                checkArgument(
                        Optional.ofNullable(entity.getDraftsFlag()).filter(it -> it == 1).isPresent(),
                        new ParamInvalidException("非草稿的数据不能进行编辑")
                );
            } else {
                checkArgument(
                        Optional.ofNullable(entity.getDraftsFlag()).filter(it -> it == 0).isPresent(),
                        new ParamInvalidException("草稿的数据不能进行续报或终报")
                );
            }
        }
    }

    /**
     * queryList<BR>
     *
     * @param from    参数
     * @param request 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 23:02
     */
    @Override
    public RestfulResultsV2<YaoQingEntityVo> queryList(String from, ListParamsRequest request)
            throws ServiceException {
        final CurrentUser user = AuthHelper.getNotNullUser();
        List<KeyValueTypeVO> baseFilter = request.getFilterParams()
                .stream()
                .filter(it -> !it.getKey().startsWith(Constants.ATTRIBUTES_DOT))
                .map(FieldUtils::reBuildFilter)
                .collect(Collectors.toList());
        var map = request.getFilterParams()
                .stream()
                .collect(Collectors.groupingBy(KeyValueTypeVO::getKey));
        String dataType = Optional.ofNullable(map.get(Constants.DATA_TYPE))
                .map(FieldUtils::parseValueFromFilter)
                .filter(StringUtils::isNotEmpty)
                .orElse("");
        String dataClass = Optional.ofNullable(map.get(Constants.DATA_CLASS))
                .map(FieldUtils::parseValueFromFilter)
                .filter(StringUtils::isNotEmpty)
                .orElse("");
        Optional<Tuple2<String, List<KeyValueTypeVO>>> opt = makeAttributesFilter(dataType, dataClass, request);
        String relationTableName = StringUtils.showEmpty(opt.map(it -> it._1)
                .filter(StringUtils::isNotEmpty)
                .orElse(null));
        List<Long> realAllDeptIds = new ArrayList<>(1);
        if (Objects.equals(Constants.REAL_ALL, from)) {
            realAllDeptIds.add(user.getDeptId());
            Optional.ofNullable(getPermissionService().getCurrentUserDataPermissionInfo().getDeptIds())
                    .ifPresent(realAllDeptIds::addAll);
        }
        Page<YaoQingBaseInfoEntity> page = getMapper()
                .doPageSelect(
                        from,
                        user,
                        realAllDeptIds,
                        baseFilter,
                        relationTableName,
                        opt.map(it -> it._2).orElse(Collections.emptyList()),
                        request.getSearchParams(),
                        request.getSortParams(),
                        request.getPageParams().toPage()
                );
        List<Long> deptIds = new ArrayList<>();
        List<String> userNames = new ArrayList<>();
        List<Long> dataIds = new ArrayList<>();
        page.getRecords()
                .forEach(it -> {
                    deptIds.addAll(it.makeAcceptDeptIds());
                    Optional.ofNullable(it.makeCcDeptIds())
                            .filter(CollectionUtils::isNotEmpty)
                            .ifPresent(deptIds::addAll);
                    if (!deptIds.contains(it.getCrDeptId())) {
                        deptIds.add(it.getCrDeptId());
                    }
                    dataIds.add(it.getDataId());
                    userNames.add(it.getCrUser());
                });
        Map<Long, SimpleDeptVO> deptMapping = CollectionUtils.isEmpty(deptIds) ? Collections.EMPTY_MAP
                : findDeptByIds(deptIds)
                .stream()
                .collect(Collectors.toMap(SimpleDeptVO::getDeptId, it -> it));
        Map<String, UserDto> userMapping = CollectionUtils.isEmpty(userNames) ? Collections.EMPTY_MAP
                : findUserByNames(userNames)
                .stream()
                .collect(Collectors.toMap(UserDto::getUsername, it -> it));

        Map<Long, CollectionEntity> collectIds =
                CollectionUtils.isEmpty(dataIds) ? Collections.EMPTY_MAP : getCollectionMapper()
                        .findCollectionEntityObjId(
                                Constants.USER,
                                user.getId(),
                                key(),
                                dataIds
                        ).stream().collect(Collectors.toMap(CollectionEntity::getObjId, it -> it, (a, b) -> a));
        final Map<Long, BigScreenPinDataEntity> pinDataMapping;
        if (Objects.equals(Constants.BIG_SCREEN, from)) {
            pinDataMapping = findPinData(dataIds, user);
        } else {
            pinDataMapping = Collections.EMPTY_MAP;
        }
        return RestfulResultsV2.ok(
                        page.getRecords()
                                .stream()
                                .map(it -> {
                                    var vo = makeVo(it, deptMapping, userMapping);
                                    vo.setCollectionFlag(0);
                                    Optional.ofNullable(collectIds.get(it.getDataId()))
                                            .ifPresent(col -> {
                                                vo.setCollectionFlag(1);
                                                vo.setCollectionTitle(col.getCollectionTitle());
                                            });
                                    if (Objects.equals(Constants.BIG_SCREEN, from)) {
                                        BigScreenPinDataEntity pinData = pinDataMapping.get(it.getDataId());
                                        if (Objects.nonNull(pinData)) {
                                            vo.setShowName(StringUtils.showEmpty(pinData.getShowTitle(), it.getDataTitle()));
                                            vo.setIsAddScreen(Optional.ofNullable(pinData.getPinFlag()).orElse(0));
                                        } else {
                                            vo.setShowName(it.getDataTitle());
                                            vo.setIsAddScreen(0);
                                        }
                                    }
                                    return vo;
                                })
                                .collect(Collectors.toList())
                ).addPageNum(request.getPageParams().getPageNumber())
                .addPageSize(request.getPageParams().getPageSize())
                .addTotalCount(page.getTotal());
    }

    /**
     * export<BR>
     *
     * @param export 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/22 10:58
     */
    @Override
    public Tuple2<String, String> export(ExportDTO export) throws Exception {
        PreConditionCheck.checkNotEmpty(export.getTemplateType(), new ServiceException("要情导出的模板类型不能为空"));
        EntityDetailDTO dto = new EntityDetailDTO();
        dto.setDataId(export.getDataId());
        dto.setVersionId(Optional.ofNullable(export.getVersionId()).orElse(0L));
        dto.setOnExport(true);
        YaoQingEntityVo detail = detail(dto);
        PreConditionCheck.checkNotNull(detail, new ServiceException("不存在可导出的数据"));
        Tuple2<String, Map<String, Object>> d;
        switch (export.getTemplateType()) {
            case Constants.XXKB:
                d = parseForXinXiKuaiBao(detail);
                break;
            case Constants.DZZB:
            default:
                d = parseForZhongYaoXinXiZhuanBao(detail);
                break;
        }
        final String fileName = findExportFilePath() + "/" + UUID.randomUUID() + ".docx";
        return new Tuple2<>(
                detail.getDataTitle() + ".docx",
                WordUtils.exportFile(fileName, d._1, d._2)
        );
    }

    private Tuple2<Integer, RenderData> parsePersonForExport(YaoQingEntityVo detail) throws ServiceException {
        if (Objects.isNull(detail)) {
            return null;
        }
        Map<String, String> map = detail.getAttributes();
        if (map == null || map.isEmpty()) {
            return null;
        }
        final AttributeTemplatesVo templates = findAttributeTemplates(detail.getDataType(), detail.getDataClass());
        if (templates == null) {
            return null;
        }
        if (CollectionUtils.isEmpty(templates.getFields())) {
            return null;
        }
        JSONArray persons = new JSONArray();
        templates.getFields().forEach(field -> {
            if (!map.containsKey(field.getKey())) {
                return;
            }
            if (!Objects.equals("person", field.getSchema().getType())) {
                return;
            }
            var value = map.get(field.getKey());
            final BaseFieldValidator validator = BaseFieldValidator.findByType(
                    StringUtils.showEmpty(field.getSchema().getValidateType(), field.getSchema().getType())
            );
            if (validator.isNotEmpty(value)) {
                persons.addAll(JSONArray.parseArray(value));
            }
        });
        if (persons.isEmpty()) {
            return null;
        }
        List<RowRenderData> row = new ArrayList<>(0);
        row.add(Rows.create("序号", "姓名", "证件号码", "人员状态", "人员类型", "性别", "民族", "年龄", "户籍地", "特殊身份"));
        for (int i = 0; i < persons.size(); i++) {
            JSONObject person = persons.getJSONObject(i);
            row.add(Rows.create(
                    Integer.toString(i + 1),
                    StringUtils.showEmpty(person.getString("xm")),
                    StringUtils.showEmpty(person.getString("zjhm")),
                    StringUtils.showEmpty(person.getString("ryzt")),
                    StringUtils.showEmpty(person.getString("rylx")),
                    StringUtils.showEmpty(person.getString("xb")),
                    StringUtils.showEmpty(person.getString("mz")),
                    StringUtils.showEmpty(person.getString("age")),
                    StringUtils.showEmpty(person.getString("gsdymc")),
                    ""
            ));
        }
        return new Tuple2<>(persons.size() + 1, Tables.create(row.toArray(new RowRenderData[0])));
    }

    /**
     * parseForZhongYaoXinXiZhuanBao<BR>
     *
     * @param detail 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/4 14:54
     */
    private Tuple2<String, Map<String, Object>> parseForZhongYaoXinXiZhuanBao(YaoQingEntityVo detail) throws ServiceException {
        final Map<String, Object> map = new HashMap<>(12);
        map.put("deptName", detail.getCrDept().getShortName());
        map.put("dataYear", detail.getDataYear());
        map.put("dataNo", detail.getDataNo());
        map.put("signer", StringUtils.showEmpty(detail.getAttributes().get("signer")));
        map.put("reportTime", TimeUtils.dateToString(detail.getCrTime(), TimeUtils.YYYYMMDD2));
        map.put("dataTitle", detail.getDataTitle());
        map.put("haveDataSubTitle", StringUtils.isNotEmpty(detail.getDataSubTitle()));
        map.put("dataSubTitle", detail.getDataSubTitle());
        map.put("crUserTrueName", detail.getCrUserTrueName());
        map.put("phone", StringUtils.showEmpty(detail.getAttributes().get("phone")));
        if (CollectionUtils.isNotEmpty(detail.getCcDepts())) {
            String ccDepts = detail.getCcDepts()
                    .stream()
                    .map(it -> StringUtils.showEmpty(it.getShortName(), it.getDeptName()))
                    .distinct()
                    .collect(Collectors.joining("，"));
            if (StringUtils.isNotEmpty(ccDepts)) {
                map.put("ccDepts", String.format("发：%s。", ccDepts));
            }
        }
        Integer rowSize = map.containsKey("ccDepts") ? 1 : 0;
        List<AttributeItemExportVo> attributeItems = detail.getAttributeItems();
        if (CollectionUtils.isNotEmpty(attributeItems)) {
            List<RowRenderData> row = new ArrayList<>(0);
            MergeCellRule.MergeCellRuleBuilder rule = MergeCellRule.builder();
            for (int i = 0, len = attributeItems.size(); i < len; ) {
                // 记录目前有几行数据了，方便合并规则的设置
                final int size = row.size();
                final AttributeItemExportVo one = attributeItems.get(i);
                // 说明偏移到了最后一个数据或单独占一行的数据
                if (i + 1 == len || one.getOnlyOneLine()) {
                    row.add(Rows.of(one.getText(), null).textColor("3C509D").bgColor("F5F7FE").create());
                    // 合并两个单元格
                    rule.map(MergeCellRule.Grid.of(size, 0), MergeCellRule.Grid.of(size, 1));
                    // 因为上面取用了1个对象，所以角标加1
                    i += 1;
                } else {
                    // 获取第二个数据
                    AttributeItemExportVo two = attributeItems.get(i + 1);
                    // 如果第二个是需要占一行
                    if (two.getOnlyOneLine()) {
                        // 先展示第一个
                        row.add(Rows.of(one.getText(), null).textColor("3C509D").bgColor("F5F7FE").create());
                        // 虽然其要求拆分展示，但是因为下一个要求独占一行，所以也只能合并两个单元格
                        rule.map(MergeCellRule.Grid.of(size, 0), MergeCellRule.Grid.of(size, 1));
                        // 接着展示独占一行得数据
                        row.add(Rows.of(two.getText(), null).textColor("3C509D").bgColor("F5F7FE").create());
                        // 合并两个单元格
                        rule.map(MergeCellRule.Grid.of(size + 1, 0), MergeCellRule.Grid.of(size + 1, 1));
                    } else {
                        // 两个都是拆分展示，那就正常展示两列
                        row.add(Rows.of(one.getText(), two.getText()).textColor("3C509D").bgColor("F5F7FE").create());
                    }
                    // 因为上面取用了两个对象，所以角标加2
                    i += 2;
                }
            }
            map.put("haveAttributeTable", true);
            map.put(
                    "attributeTable",
                    Tables.of(row.toArray(new RowRenderData[]{})).mergeRule(rule.build())
                            .border(
                                    BorderStyle.builder()
                                            .withSize(24)
                                            .withColor("FFFFFF")
                                            .withType(XWPFTable.XWPFBorderType.SINGLE)
                                            .build()
                            ).create()
            );
            rowSize += row.size();
        } else {
            map.put("haveAttributeTable", false);
        }
        var personTable = parsePersonForExport(detail);
        if (Objects.nonNull(personTable) && personTable._1 > 0) {
            map.put("havePersonTable", true);
            map.put("personTable", personTable._2);
            rowSize += personTable._1;
        } else {
            map.put("havePersonTable", false);
        }
        map.put("dataContent", WordUtils.parseContentForExport(detail.getDataContent(), Math.max(12 - rowSize, 1), 25));
        return new Tuple2<>("qzx.yaoqing.bdzcl.docx", map);
    }

    /**
     * parseForXinXiKuaiBao<BR>
     *
     * @param detail 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/4 14:54
     */
    private Tuple2<String, Map<String, Object>> parseForXinXiKuaiBao(YaoQingEntityVo detail) throws ServiceException {
        final Map<String, Object> map = new HashMap<>(12);
        map.put("districtMainName", detail.getCrDept().getDistrictMainName());
        map.put("deptName", detail.getCrDept().getShortName());
        map.put("dataNo", detail.getDataNo());
        map.put("signer", StringUtils.showEmpty(detail.getAttributes().get("signer")));
        map.put(
                "signTime",
                Optional.ofNullable(detail.getSignTime())
                        .map(it -> TimeUtils.dateToString(it, TimeUtils.YYYYMMDD_HHMMSS) + "签收")
                        .orElse("")
        );
        map.put("drafter", StringUtils.showEmpty(detail.getAttributes().get("drafter")));
        map.put("reportTime", TimeUtils.dateToString(detail.getCrTime(), TimeUtils.YYYYMMDD_HHMMSS));
        map.put("dataTitle", detail.getDataTitle());
        map.put("haveDataSubTitle", StringUtils.isNotEmpty(detail.getDataSubTitle()));
        map.put("dataSubTitle", detail.getDataSubTitle());
        map.put("phone", StringUtils.showEmpty(detail.getAttributes().get("phone")));
        if (CollectionUtils.isNotEmpty(detail.getCcDepts())) {
            String ccDepts = detail.getCcDepts()
                    .stream()
                    .map(it -> StringUtils.showEmpty(it.getShortName(), it.getDeptName()))
                    .distinct()
                    .collect(Collectors.joining("，"));
            if (StringUtils.isNotEmpty(ccDepts)) {
                map.put("ccDepts", String.format("发：%s。", ccDepts));
            }
        }
        Integer rowSize = map.containsKey("ccDepts") ? 1 : 0;
        List<AttributeItemExportVo> attributeItems = detail.getAttributeItems();
        if (CollectionUtils.isNotEmpty(attributeItems)) {
            List<RowRenderData> row = new ArrayList<>(0);
            MergeCellRule.MergeCellRuleBuilder rule = MergeCellRule.builder();
            for (int i = 0, len = attributeItems.size(); i < len; ) {
                // 记录目前有几行数据了，方便合并规则的设置
                final int size = row.size();
                final AttributeItemExportVo one = attributeItems.get(i);
                // 说明偏移到了最后一个数据或单独占一行的数据
                if (i + 1 == len || one.getOnlyOneLine()) {
                    row.add(Rows.of(one.getText(), null).textColor("3C509D").bgColor("F5F7FE").create());
                    // 合并两个单元格
                    rule.map(MergeCellRule.Grid.of(size, 0), MergeCellRule.Grid.of(size, 1));
                    // 因为上面取用了1个对象，所以角标加1
                    i += 1;
                } else {
                    // 获取第二个数据
                    AttributeItemExportVo two = attributeItems.get(i + 1);
                    // 如果第二个是需要占一行
                    if (two.getOnlyOneLine()) {
                        // 先展示第一个
                        row.add(Rows.of(one.getText(), null).textColor("3C509D").bgColor("F5F7FE").create());
                        // 虽然其要求拆分展示，但是因为下一个要求独占一行，所以也只能合并两个单元格
                        rule.map(MergeCellRule.Grid.of(size, 0), MergeCellRule.Grid.of(size, 1));
                        // 接着展示独占一行得数据
                        row.add(Rows.of(two.getText(), null).textColor("3C509D").bgColor("F5F7FE").create());
                        // 合并两个单元格
                        rule.map(MergeCellRule.Grid.of(size + 1, 0), MergeCellRule.Grid.of(size + 1, 1));
                    } else {
                        // 两个都是拆分展示，那就正常展示两列
                        row.add(Rows.of(one.getText(), two.getText()).textColor("3C509D").bgColor("F5F7FE").create());
                    }
                    // 因为上面取用了两个对象，所以角标加2
                    i += 2;
                }
            }
            map.put("haveAttributeTable", true);
            map.put(
                    "attributeTable",
                    Tables.of(row.toArray(new RowRenderData[]{})).mergeRule(rule.build())
                            .border(
                                    BorderStyle.builder()
                                            .withSize(24)
                                            .withColor("FFFFFF")
                                            .withType(XWPFTable.XWPFBorderType.SINGLE)
                                            .build()
                            ).create()
            );
            rowSize += row.size();
        } else {
            map.put("haveAttributeTable", false);
        }
        var personTable = parsePersonForExport(detail);
        if (Objects.nonNull(personTable) && personTable._1 > 0) {
            map.put("havePersonTable", true);
            map.put("personTable", personTable._2);
            rowSize += personTable._1;
        } else {
            map.put("havePersonTable", false);
        }
        map.put("dataContent", WordUtils.parseContentForExport(detail.getDataContent(), Math.max(12 - rowSize, 1), 25));
        return new Tuple2<>("qzx.yaoqing.xxkb.docx", map);
    }

    /**
     * markFieldsFinished<BR>
     *
     * @param user 参数
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/14 16:07
     */
    public void markFieldsFinished(CurrentUser user) {
        var hour = BeanFactoryHolder.getEnv()
                .getProperty("intelligence.task.markFieldsFinished.hour", Integer.class, -12);
        new LambdaQueryChainWrapper<>(getMapper())
                .eq(YaoQingBaseInfoEntity::getIsDel, 0)
                .isNotNull(YaoQingBaseInfoEntity::getFirstReportTime)
                .lt(YaoQingBaseInfoEntity::getFirstReportTime, TimeUtils.hourDefOrAft(hour, TimeUtils.YYYYMMDD_HHMMSS))
                .gt(YaoQingBaseInfoEntity::getChatId, 0L)
                .in(YaoQingBaseInfoEntity::getStatusCode, List.of(
                        Status.DAIQIANSHOU.getCode(),
                        Status.YIQIANSHOU.getCode(),
                        Status.YITUIHUI.getCode())
                ).eq(YaoQingBaseInfoEntity::getFieldsIsFinished, 0)
                .list()
                .forEach(it -> {
                    TotalAndNumberVo vo = TotalAndNumberVo.of(it.getFieldsEditInfo());
                    // 标记数据为已完成
                    if (Objects.equals(vo.getTotal(), vo.getNumber())) {
                        new LambdaUpdateChainWrapper<>(getMapper())
                                .set(YaoQingBaseInfoEntity::getFieldsIsFinished, 1)
                                .eq(YaoQingBaseInfoEntity::getDataId, it.getDataId())
                                .update();
                    } else {
                        Try.run(() -> doSendMessage(
                                Optional.ofNullable(user),
                                it,
                                WebsocketMessageTypeConstant.MESSAGE,
                                MessageTypeEnum.TEXT.getCode(),
                                String.format("intelligence_type(%s)", makeFieldsFinishedLog(it, hour, vo)),
                                "字段填报提醒"
                        )).onFailure(e -> log.error("[{}]发送字段填报消息失败", desc(), e));
                        new LambdaUpdateChainWrapper<>(getMapper())
                                .set(YaoQingBaseInfoEntity::getFieldsIsFinished, -1)
                                .eq(YaoQingBaseInfoEntity::getDataId, it.getDataId())
                                .update();
                    }
                });
        new LambdaQueryChainWrapper<>(getMapper())
                .eq(YaoQingBaseInfoEntity::getIsDel, 0)
                .eq(YaoQingBaseInfoEntity::getFieldsIsFinished, -1)
                .isNotNull(YaoQingBaseInfoEntity::getFirstReportTime)
                .orderByDesc(YaoQingBaseInfoEntity::getFirstReportTime)
                .page(new Page<>(1, 1000))
                .getRecords()
                .forEach(it -> {
                    TotalAndNumberVo vo = TotalAndNumberVo.of(it.getFieldsEditInfo());
                    // 标记数据为已完成
                    if (Objects.equals(vo.getTotal(), vo.getNumber())) {
                        new LambdaUpdateChainWrapper<>(getMapper())
                                .set(YaoQingBaseInfoEntity::getFieldsIsFinished, 1)
                                .eq(YaoQingBaseInfoEntity::getDataId, it.getDataId())
                                .update();
                    }
                });
    }

    /**
     * makeFieldsFinishedLog<BR>
     *
     * @param it   参数
     * @param hour 参数
     * @param vo   参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/14 16:46
     */
    private JSONObject makeFieldsFinishedLog(YaoQingBaseInfoEntity it, Integer hour, TotalAndNumberVo vo) {
        JSONObject value = new JSONObject();
        value.put("id", it.getDataId());
        value.put("versionId", it.getVersionId());
        value.put("status", "要情提醒");
        value.put("content", String.format(
                "接报时间已达%s小时，你的要情报告中尚有%s项内容未填写，请尽快补全",
                Math.abs(hour),
                Math.abs(vo.getTotal() - vo.getNumber())
        ));
        return value;
    }

    /**
     * makeVo<BR>
     *
     * @param it          参数
     * @param deptMapping 参数
     * @param userMapping 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/14 21:42
     */
    public YaoQingEntityVo makeVo(
            YaoQingBaseInfoEntity it,
            Map<Long, SimpleDeptVO> deptMapping,
            Map<String, UserDto> userMapping
    ) {
        YaoQingEntityVo vo = EntityConvertToVo.INSTANCE.entityToVo(it);
        Try.of(() -> findAttributeTemplates(it.getDataType(), it.getDataClass()))
                .onSuccess(tmp -> {
                    vo.setDataTypeShowName(tmp.getDataTypeShowName());
                    if (!Constants.DEFAULT.equals(it.getDataClass())) {
                        vo.setDataClassShowName(tmp.getDataClassShowName());
                    }
                });
        vo.setCrDept(deptMapping.get(vo.getCrDeptId()));
        vo.setAcceptDepts(
                it.makeAcceptDeptIds()
                        .stream()
                        .map(deptMapping::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList())
        );
        vo.setCcDepts(
                it.makeCcDeptIds()
                        .stream()
                        .map(deptMapping::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList())
        );
        Optional.ofNullable(userMapping.get(it.getCrUser()))
                .map(i -> StringUtils.showEmpty(i.getRealName(), i.getUsername()))
                .ifPresent(vo::setCrUserTrueName);
        vo.setFieldsInfo(TotalAndNumberVo.of(it.getFieldsEditInfo()));
        return addCommonInfo(it, vo);
    }

    @Override
    public String key() {
        return Constants.YAOQING;
    }

    @Override
    public String desc() {
        return "要情";
    }
}
