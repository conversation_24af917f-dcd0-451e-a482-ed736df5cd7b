package com.trs.police.intelligence.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.trs.common.base.PreConditionCheck;
import com.trs.common.base.Report;
import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.constant.message.WebsocketMessageTypeConstant;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.DistrictListDto;
import com.trs.police.common.core.dto.UserDto;
import com.trs.police.common.core.entity.BigScreenPinDataEntity;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.mapper.BigScreenPinDataMapper;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.TodoTaskVO;
import com.trs.police.common.core.vo.message.Channel;
import com.trs.police.common.core.vo.message.WebsocketMessageVO;
import com.trs.police.common.core.vo.permission.SimpleDeptVO;
import com.trs.police.common.core.vo.search.ArchivesVO;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.intelligence.constant.ActionEnum;
import com.trs.police.intelligence.constant.Constants;
import com.trs.police.intelligence.constant.Status;
import com.trs.police.intelligence.dto.*;
import com.trs.police.intelligence.dto.third.PullDataDTO;
import com.trs.police.intelligence.entity.*;
import com.trs.police.intelligence.mapper.*;
import com.trs.police.intelligence.message.ArchivesMessageService;
import com.trs.police.intelligence.mgr.AttributeTemplatesMgr;
import com.trs.police.intelligence.mgr.BaseFieldConvert;
import com.trs.police.intelligence.mgr.BaseFieldValidator;
import com.trs.police.intelligence.mgr.DataRelationMappingMgr;
import com.trs.police.intelligence.utils.FieldUtils;
import com.trs.police.intelligence.vo.*;
import com.trs.police.intelligence.vo.export.ChuZhiItemVo;
import com.trs.police.intelligence.vo.export.ChuZhiSummaryVo;
import com.trs.police.intelligence.vo.third.PullDataVo;
import com.trs.police.statistic.domain.bean.CountItem;
import com.trs.web.builder.base.RestfulResultsV2;
import com.trs.web.builder.util.BeanFactoryHolder;
import io.vavr.Tuple2;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.io.File;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.trs.common.base.PreConditionCheck.checkArgument;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * BaseIntelligenceEntityService
 *
 * @param <Entity>    参数
 * @param <DetailVo>  参数
 * @param <ListVo>    参数
 * @param <Save>      参数
 * @param <VersionVo> 参数
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/11 23:20
 * @since 1.0
 */
@Slf4j
@Getter
@Setter
public abstract class BaseIntelligenceEntityService<Entity extends BaseIntelligenceEntity,
        VersionVo extends BaseVersionEntity, Save extends BaseSaveDTO, ListVo extends BaseIntelligenceEntityVo,
        DetailVo extends BaseIntelligenceEntityVo> implements IIntelligenceEntityService<Save, ListVo, DetailVo> {

    @Resource
    private AttributeMapper attributeMapper;

    @Resource
    private ActionLogMapper actionLogMapper;

    @Resource
    private PermissionService permissionService;

    @Resource
    private AttributeTemplatesMgr attributeTemplatesMgr;

    @Resource
    private DataRelationMappingMgr dataRelationMappingMgr;

    @Resource
    private CollectionMapper collectionMapper;

    @Resource
    private ICommonService commonService;

    @Resource
    private IMessageService messageService;

    @Resource
    private BigScreenPinDataMapper bigScreenPinDataMapper;

    @Resource
    private CommonLabelMapper commonLabelMapper;

    @Resource
    private AreaEntityMapper areaEntityMapper;

    @Resource
    private ArchivesMessageService searchService;

    @Resource
    private DictService dictService;

    @Override
    public Report<String> saveOrUpdate(Save saveDTO) throws ServiceException {
        return saveOrUpdate(
                saveDTO,
                Optional.ofNullable(saveDTO.getUser()).orElseGet(AuthHelper::getNotNullUser)
        );
    }

    /**
     * saveOrUpdate<BR>
     *
     * @param saveDTO 参数
     * @param user    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/5/28 15:29
     */
    public abstract Report<String> saveOrUpdate(Save saveDTO, CurrentUser user) throws ServiceException;

    /**
     * getDistrictByHjdDm<BR>
     *
     * @param hjdDm 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/5/28 15:40
     */
    public DistrictListDto getDistrictByHjdDm(String hjdDm) {
        if (StringUtils.isEmpty(hjdDm)) {
            return null;
        }
        return getDictService().getDistrictByCode(hjdDm);
    }

    /**
     * findDeptIdByCode<BR>
     *
     * @param code 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/4/23 15:45
     */
    public Optional<Long> findDeptIdByCode(String code) {
        return Optional.ofNullable(code)
                .filter(StringUtils::isNotEmpty)
                .map(it -> getPermissionService().getDeptByCode(it))
                .map(DeptDto::getId);
    }

    /**
     * findExportFilePath<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/25 18:10
     */
    protected String findExportFilePath() {
        var tmp = BeanFactoryHolder.getEnv()
                .getProperty("intelligence.common.exportFilePath", "/tmp");
        if (tmp.endsWith("/")) {
            tmp = tmp.substring(0, tmp.length() - 1);
        }
        final var path = tmp + "/" + TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD5) + "/" + key();
        final var filePath = new File(path);
        if (!filePath.exists()) {
            log.info("文件路径[{}]不存在，开始创建", path);
            if (filePath.mkdirs()) {
                log.info("成功创建[{}]", path);
            } else {
                log.warn("创建[{}]失败", path);
            }
        }
        return path;
    }


    /**
     * 标识超时<BR>
     *
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/13 18:00
     */
    public void markTimeOut() {

    }

    /**
     * 发送临近反馈超时的消息<BR>
     *
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/13 18:00
     */
    public void sendFeedbackTimeOutMessage() {

    }

    /**
     * doSendMessage<BR>
     *
     * @param login                参数
     * @param entity               参数
     * @param websocketMessageType 参数
     * @param messageType          参数
     * @param content              参数
     * @param simpleContent        参数
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/18 18:13
     */
    public void doSendMessage(
            Optional<CurrentUser> login,
            Entity entity,
            String websocketMessageType,
            String messageType,
            String content,
            String simpleContent
    ) throws ServiceException {
        // 这个时候没有绑定会话，就不需要发送消息
        if (entity.getChatId() == null || entity.getChatId() <= 0L) {
            return;
        }
        Channel channel = new Channel(
                Constants.SYSTEM_FLAG,
                module(),
                WebsocketMessageTypeConstant.MESSAGE,
                null
        );
        final WebsocketMessageVO.Message message = new WebsocketMessageVO.Message();
        message.setTypes(Collections.singletonList(messageType));
        message.setContent(content);
        message.setSimpleContent(simpleContent);
        WebsocketMessageVO wsMessage = WebsocketMessageVO.builder()
                .systemInfo(channel)
                .conversationId(entity.getChatId())
                .type(websocketMessageType)
                .sendTime(LocalDateTime.now())
                .sender(login.orElseThrow(() -> new ServiceException("未登录")))
                .read(false)
                .withdraw(false)
                .message(message)
                .important(false)
                .build();
        getMessageService().receiveUserMessage(wsMessage);
    }

    /**
     * module<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/11 10:00
     */
    public String module() {
        return key();
    }

    /**
     * getMapper<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/17 20:55
     */
    public abstract BaseEntityMapper<Entity> getMapper();

    /**
     * getVersionMapper<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/17 21:01
     */
    public abstract BaseMapper<VersionVo> getVersionMapper();

    /**
     * supportVersion<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/7 20:51
     */
    public Boolean supportVersion() {
        return true;
    }

    /**
     * findAttributeTemplatesVo<BR>
     *
     * @param dataType  参数
     * @param dataClass 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/12 02:44
     */
    public AttributeTemplatesVo findAttributeTemplates(String dataType, String dataClass) throws ServiceException {
        AttributeTemplatesDTO dto = new AttributeTemplatesDTO();
        dto.setDataType(dataType);
        dto.setDataClass(StringUtils.showEmpty(dataClass, Constants.DEFAULT));
        return Optional.ofNullable(
                        attributeTemplatesMgr.attributeTemplates(key(), dto)
                ).filter(CollectionUtils::isNotEmpty)
                .map(it -> it.get(0))
                .orElseThrow(() -> new ServiceException("属性模板不存在"));
    }

    /**
     * addCommonInfo<BR>
     *
     * @param entity   参数
     * @param detailVo 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/20 11:25
     */
    public DetailVo addCommonInfo(Entity entity, DetailVo detailVo) {
        detailVo.setAreaName(BeanFactoryHolder.getEnv()
                .getProperty("intelligence.export.excel.areaName", "自贡市"));
        // 注入标识
        detailVo.setUserCreateEntity(
                entity.userCreateEntity(Optional.ofNullable(AuthHelper.getCurrentUser()))
        );
        // 回填信息
        detailVo.setAcceptTarget(JsonUtil.toJsonString(entity.makeAcceptTarget()));
        detailVo.setAcceptUserList(
                entity.makeAcceptUsers()
                        .stream()
                        .filter(it -> Objects.equals(Constants.USER, it.getObjType()))
                        .map(it -> getPermissionService().findSimpleUser(it.getObjId(), it.getObjDeptId()))
                        .collect(Collectors.toList())
        );
        return detailVo;
    }

    /**
     * detail<BR>
     *
     * @param dataId    参数
     * @param versionId 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/22 12:16
     */
    public DetailVo detail(Long dataId, Long versionId) throws ServiceException {
        EntityDetailDTO dto = new EntityDetailDTO();
        dto.setDataId(dataId);
        dto.setVersionId(versionId);
        return detail(dto);
    }

    /**
     * detail<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 22:53
     */
    @Override
    public DetailVo detail(EntityDetailDTO dto) throws ServiceException {
        dto.isValid();
        Long dataId = dto.getDataId();
        Long versionId = dto.getVersionId();
        Entity entity = findById(dataId);
        VersionVo version = findVersion(
                dto,
                Optional.ofNullable(versionId).filter(it -> it > 0L).orElse(entity.getVersionId())
        );
        if (supportVersion()) {
            checkArgument(
                    Objects.nonNull(version),
                    new ServiceException(String.format("ID=[%s]的[%s]对象不存在对应的版本", dataId, desc()))
            );
        }
        DetailVo detailVo = mergeDataOnDetail(dto, entity, version);
        if (Objects.nonNull(version)) {
            detailVo.setVersionId(version.getVersionId());
            detailVo.setVersionDataTitle(version.getVersionDataTitle());
            detailVo.setVersionDataContent(version.getVersionDataContent());
        }
        // 回填状态码
        Optional.ofNullable(version)
                .map(BaseVersionEntity::getStatusCode)
                .filter(it -> it > 0)
                .ifPresent(detailVo::setStatusCode);
        if (supportVersion()) {
            detailVo.setVersionNum(Math.max(
                    1L,
                    getVersionMapper().selectCount(
                            new QueryWrapper<VersionVo>()
                                    .eq("is_del", 0)
                                    .eq("yq_data_id", entity.getDataId())
                    )
            ));
        }
        final var acceptDetail = getDataRelationMappingMgr()
                .findMixDetail(key(), dataId, Constants.DATA_RELATION_STATUS_TYPE_RECEIVE);
        final var signDetail = getDataRelationMappingMgr()
                .findMixDetail(key(), dataId, Constants.DATA_RELATION_STATUS_TYPE_SIGN);
        final var feedbackDetail = getDataRelationMappingMgr()
                .findMixDetail(key(), dataId, Constants.DATA_RELATION_STATUS_TYPE_FEEDBACK);
        final var forwardDetail = getDataRelationMappingMgr()
                .findMixDetail(key(), entity.getRootId(), Constants.DATA_RELATION_STATUS_TYPE_FORWARD);
        var map = getDataRelationMappingMgr()
                .findMixDetail(key(), dataId, Constants.DATA_RELATION_STATUS_TYPE_DEFAULT)
                .stream()
                .collect(Collectors.toMap(DealDetailVo::getObjDeptId, it -> it, (a, b) -> a));
        acceptDetail.forEach(it -> {
            if (it.getStatusCode() > 0) {
                Optional.ofNullable(map.get(it.getObjDeptId()))
                        .ifPresent(r -> r.setStatusCode(Status.DAIQIANSHOU.getCode()));
            }
        });
        signDetail.forEach(it -> {
            if (it.getStatusCode() > 0) {
                Optional.ofNullable(map.get(it.getObjDeptId()))
                        .ifPresent(r -> r.setStatusCode(Status.DAIFANKUI.getCode()));
            }
        });
        feedbackDetail.forEach(it -> {
            if (it.getStatusCode() > 0) {
                Optional.ofNullable(map.get(it.getObjDeptId()))
                        .ifPresent(r -> r.setStatusCode(Status.YIFANKUI.getCode()));
            }
        });
        detailVo.setFeedbackDetail(feedbackDetail);
        detailVo.setSignDetail(signDetail);
        detailVo.setAcceptDetail(acceptDetail);
        detailVo.setForwardDetail(forwardDetail);
        detailVo.setDealDetail(new ArrayList<>(map.values()));
        if (dto.getOnExport()) {
            detailVo.setAttributeItems(parseExportAttributeItem(detailVo));
        }
        return addCommonInfo(entity, detailVo);
    }

    /**
     * makeVo<BR>
     *
     * @param vo          参数
     * @param deptMapping 参数
     * @param userMapping 参数
     * @param <T>         参数
     * @param it          参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/4/23 14:51
     */
    public <T extends BaseIntelligenceEntityVo> T makeVo(Entity it, T vo, Map<Long, SimpleDeptVO> deptMapping, Map<String, UserDto> userMapping) {
        // 对于我接收的使用关联数据的状态
        if (it.getUpStatusCode() != null) {
            vo.setStatusCode(it.getUpStatusCode());
        }
        vo.setCrDept(deptMapping.get(it.getCrDeptId()));
        vo.setAcceptDepts(
                it.makeAcceptDeptIds()
                        .stream()
                        .map(deptMapping::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList())
        );
        Optional.ofNullable(userMapping.get(it.getCrUser()))
                .map(i -> StringUtils.showEmpty(i.getRealName(), i.getUsername()))
                .ifPresent(vo::setCrUserTrueName);
        return vo;
    }

    @Override
    public List<CountItem> categoryList(CategorySearchDto dto) throws ServiceException {
        return List.of();
    }

    /**
     * findAcceptDeptIds<BR>
     *
     * @param entity  参数
     * @param version 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/14 21:36
     */
    protected List<Long> findAcceptDeptIds(Entity entity, VersionVo version) {
        return entity.makeAcceptDeptIds();
    }

    /**
     * mergeDataOnDetail<BR>
     *
     * @param dto     参数
     * @param entity  参数
     * @param version 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/12 00:13
     */
    public abstract DetailVo mergeDataOnDetail(
            EntityDetailDTO dto,
            Entity entity,
            VersionVo version
    ) throws ServiceException;

    /**
     * findLabelByIds<BR>
     *
     * @param ids 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/8/14 18:29
     */
    public List<CommonLabelEntity> findLabelByIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return new LambdaQueryChainWrapper<>(getCommonLabelMapper())
                .eq(CommonLabelEntity::getIsDel, 0)
                .in(CommonLabelEntity::getDataId, ids)
                .list();
    }

    /**
     * findLabelById<BR>
     *
     * @param id 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/8/14 18:29
     */
    public Optional<CommonLabelEntity> findLabelById(Long id) {
        if (Objects.isNull(id)) {
            return Optional.empty();
        }
        return findLabelByIds(List.of(id))
                .stream()
                .findFirst();
    }

    /**
     * findAreaByIds<BR>
     *
     * @param codes 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/8/14 18:29
     */
    public List<AreaEntity> findAreaByCodes(Collection<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Collections.emptyList();
        }
        return new LambdaQueryChainWrapper<>(getAreaEntityMapper())
                .in(AreaEntity::getAreaCode, codes)
                .list();
    }

    /**
     * findAreaById<BR>
     *
     * @param code 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/8/14 18:29
     */
    public Optional<AreaEntity> findAreaByCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return Optional.empty();
        }
        return findAreaByCodes(List.of(code))
                .stream()
                .findFirst();
    }


    /**
     * findDeptById<BR>
     *
     * @param deptId 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/12 00:08
     */
    public Optional<SimpleDeptVO> findDeptById(Long deptId) {
        return findDeptByIds(List.of(deptId))
                .stream()
                .findFirst();
    }

    /**
     * findDeptByIds<BR>
     *
     * @param deptIds 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/12 00:08
     */
    public List<SimpleDeptVO> findDeptByIds(List<Long> deptIds) {
        if (CollectionUtils.isEmpty(deptIds)) {
            return Collections.emptyList();
        }
        return getPermissionService().getDeptByIds(deptIds)
                .stream()
                .map(DeptDto::toSimpleVO)
                .collect(Collectors.toList());
    }

    /**
     * findCurrentUser<BR>
     *
     * @param userName 参数
     * @param deptId   参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/8/9 11:37
     */
    public Optional<CurrentUser> findCurrentUser(String userName, Long deptId) {
        return findUserByName(userName)
                .map(it -> getPermissionService().findCurrentUser(it.getId(), deptId));
    }

    /**
     * findUserByName<BR>
     *
     * @param userName 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/12 14:59
     */
    public Optional<UserDto> findUserByName(String userName) {
        if (StringUtils.isEmpty(userName)) {
            return Optional.empty();
        }
        return findUserByNames(Collections.singletonList(userName))
                .stream()
                .findFirst();
    }

    /**
     * findUserByNames<BR>
     *
     * @param userNames 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/12 14:59
     */
    public List<UserDto> findUserByNames(List<String> userNames) {
        if (CollectionUtils.isEmpty(userNames)) {
            return Collections.emptyList();
        }
        return getPermissionService().getUserByUserNames(userNames);
    }

    /**
     * findById<BR>
     *
     * @param dataId 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 23:23
     */
    public Entity findById(Long dataId) throws ServiceException {
        Entity entity = getMapper().selectById(dataId);
        checkArgument(
                Objects.nonNull(entity) && entity.getIsDel() == 0,
                new ServiceException(String.format("不存在ID=[%s]的[%s]对象", dataId, desc()))
        );
        return entity;
    }

    /**
     * findVersion<BR>
     *
     * @param dto       参数
     * @param versionId 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 23:26
     */
    public VersionVo findVersion(EntityDetailDTO dto, Long versionId) throws ServiceException {
        if (supportVersion()) {
            Long dataId = dto.getDataId();
            return getVersionMapper().selectOne(
                    new QueryWrapper<VersionVo>()
                            .eq("yq_data_id", dataId)
                            .eq("data_id", versionId)
            );
        }
        return null;
    }

    /**
     * makeAttributesFilter<BR>
     *
     * @param dataType  参数
     * @param dataClass 参数
     * @param request   参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/12 13:35
     */
    public Optional<Tuple2<String, List<KeyValueTypeVO>>> makeAttributesFilter(
            String dataType,
            String dataClass,
            ListParamsRequest request
    ) throws ServiceException {
        var map = request.getFilterParams()
                .stream()
                .collect(Collectors.groupingBy(KeyValueTypeVO::getKey));
        if (StringUtils.isEmpty(dataType) || StringUtils.isEmpty(dataClass)) {
            return Optional.empty();
        }
        AttributeTemplatesVo templatesVo = findAttributeTemplates(dataType, dataClass);
        if (templatesVo == null || StringUtils.isEmpty(templatesVo.getTemplateName())) {
            return Optional.empty();
        }
        Map<String, String> mapping = templatesVo.getFields()
                .stream()
                .collect(Collectors.toMap(FieldVo::getKey, FieldVo::getDbKey, (a, b) -> a));
        List<KeyValueTypeVO> result = new ArrayList<>(map.size());
        map.keySet()
                .stream()
                .filter(it -> StringUtils.isNotEmpty(it) && it.startsWith(Constants.ATTRIBUTES_DOT))
                .forEach(key -> {
                    String mappingKey = key.substring(Constants.ATTRIBUTES_DOT.length());
                    if (mapping.containsKey(mappingKey)) {
                        map.get(key)
                                .forEach(vo -> result.add(FieldUtils.reBuildFilter(
                                        new KeyValueTypeVO(mapping.get(mappingKey), vo.getValue(), vo.getType())
                                )));
                    } else {
                        switch (mappingKey) {
                            case "carHpzl":
                                map.get(key)
                                        .forEach(vo -> result.add(FieldUtils.reBuildFilter(
                                                new KeyValueTypeVO(mappingKey, vo.getValue(), vo.getType())
                                        )));
                                break;
                            default:
                                log.warn("暂不支持{},{}", key, mappingKey);
                        }
                    }
                });
        return Optional.of(new Tuple2<>(templatesVo.getRelationTableName(), result));
    }

    /**
     * getUnReadNum<BR>
     *
     * @param from 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/18 23:37
     */
    @Override
    public List<GroupVo> getUnReadNum(String from) throws ServiceException {
        return getMapper().getUnReadNum(from, AuthHelper.getNotNullUser());
    }

    /**
     * check<BR>
     *
     * @param isAdd       参数
     * @param templatesVo 参数
     * @param saveDTO     参数
     * @param attributes  参数
     * @param entity      参数
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/12 02:01
     */
    public void check(Boolean isAdd, AttributeTemplatesVo templatesVo, Save saveDTO, List<DataAttributesDTO> attributes, Entity entity) throws ServiceException {
        PreConditionCheck.checkNotNull(entity, new ServiceException("对象不能为空"));
        for (FieldVo field : templatesVo.getFields()) {
            final BaseFieldValidator validator = BaseFieldValidator.findByType(
                    StringUtils.showEmpty(field.getSchema().getValidateType(), field.getSchema().getType())
            );
            if (field.getProperties().getRequired()) {
                // 校验必填项
                if (attributes.stream()
                        .noneMatch(it -> it.getKey().equals(field.getKey()) && validator.isNotEmpty(it.getValue()))) {
                    throw new ParamInvalidException(
                            String.format("文档属性中[%s]属于必填项", field.getSchema().getTitle())
                    );
                }
            }
            // 校验有效性
            for (DataAttributesDTO attribute : attributes) {
                if (field.getKey().equals(attribute.getKey()) && validator.isNotEmpty(attribute.getValue())) {
                    if (validator.isNotValid(attribute.getValue())) {
                        throw new ParamInvalidException(
                                String.format(
                                        "文档属性中[%s]的值[%s]不合法",
                                        field.getSchema().getTitle(),
                                        attribute.getValue()
                                )
                        );
                    } else {
                        final BaseFieldConvert fieldConvert = BaseFieldConvert.findByType(
                                StringUtils.showEmpty(field.getSchema().getValidateType(), field.getSchema().getType())
                        );
                        // 发送消息
                        List<ArchivesVO> message = fieldConvert.convertToArchivesVO(attribute.getValue());
                        if (CollectionUtils.isNotEmpty(message)) {
                            getSearchService().pushArchivesMessage(message);
                        }
                    }
                }
            }
        }
    }

    /**
     * makeAllCount<BR>
     *
     * @param templatesVo 参数
     * @param attributes  参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/26 20:07
     */
    public Long makeAllCount(AttributeTemplatesVo templatesVo, List<DataAttributesDTO> attributes) {
        return templatesVo.getFields()
                .stream()
                .filter(it -> !it.getProperties().getHide())
                .count();
    }

    /**
     * makeEditCount<BR>
     *
     * @param templatesVo 参数
     * @param attributes  参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/26 20:07
     */
    public Long makeEditCount(AttributeTemplatesVo templatesVo, List<DataAttributesDTO> attributes) {
        Map<String, FieldVo> map = templatesVo.getFields()
                .stream()
                .collect(Collectors.toMap(FieldVo::getKey, it -> it));
        return attributes.stream().filter(dto -> Optional.ofNullable(map.get(dto.getKey()))
                .filter(field -> {
                    if (field.getProperties().getHide()) {
                        return false;
                    }
                    final BaseFieldValidator validator = BaseFieldValidator.findByType(
                            StringUtils.showEmpty(field.getSchema().getValidateType(), field.getSchema().getType())
                    );
                    return validator.isNotEmpty(dto.getValue());
                }).isPresent()
        ).count();
    }

    /**
     * findPinData<BR>
     *
     * @param dataIds 参数
     * @param user    参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/11 11:40
     */
    public Map<Long, BigScreenPinDataEntity> findPinData(Collection<Long> dataIds, CurrentUser user) {
        if (CollectionUtils.isEmpty(dataIds)
                || Optional.ofNullable(user)
                .map(CurrentUser::getDept)
                .map(DeptDto::getDistrictCode)
                .filter(StringUtils::isNotEmpty)
                .isEmpty()) {

            return Collections.emptyMap();
        }
        return new LambdaQueryChainWrapper<>(getBigScreenPinDataMapper())
                .eq(BigScreenPinDataEntity::getObjType, module())
                .in(BigScreenPinDataEntity::getObjId, dataIds)
                .eq(BigScreenPinDataEntity::getDistrictCode, user.getDept().getDistrictCode())
                .list()
                .stream()
                .collect(Collectors.toMap(it -> Long.valueOf(it.getObjId()), it -> it, (a, b) -> a));
    }

    /**
     * todoList<BR>
     *
     * @param pageParams 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/22 19:57
     */
    @Override
    public PageResult<TodoTaskVO> todoList(PageParams pageParams) throws ServiceException {
        RestfulResultsV2<ListVo> list = queryList("todo", new ListParamsRequest(pageParams));
        if (!Objects.equals(list.getCode(), 200)
                || CollectionUtils.isEmpty(list.getDatas())) {
            return PageResult.empty(pageParams);
        }
        return PageResult.of(
                list.getDatas().stream().map(BaseIntelligenceEntityVo::todo).collect(Collectors.toList()),
                pageParams.getPageNumber(),
                list.getSummary().getTotal(),
                pageParams.getPageSize()
        );
    }

    /**
     * parseExportAttributeItem<BR>
     *
     * @param detail 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/24 22:43
     */
    public List<AttributeItemExportVo> parseExportAttributeItem(DetailVo detail) throws ServiceException {
        return parseExportAttributeItem(detail.getDataType(), detail.getDataClass(), detail.getAttributes());
    }

    /**
     * parseExportAttributeItem<BR>
     *
     * @param dataType  参数
     * @param dataClass 参数
     * @param map       参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/26 12:15
     */
    public List<AttributeItemExportVo> parseExportAttributeItem(
            String dataType,
            String dataClass,
            Map<String, String> map
    ) throws ServiceException {
        if (map == null || map.isEmpty()) {
            return Collections.emptyList();
        }
        final AttributeTemplatesVo templates = findAttributeTemplates(dataType, dataClass);
        if (templates == null) {
            return Collections.emptyList();
        }
        var exportFieldKeys = StringUtils.getList(templates.getExportFieldKeys(), StringUtils.SEPARATOR_COMMA);
        if (exportFieldKeys.isEmpty()) {
            return Collections.emptyList();
        }
        final var fieldVoMap = templates.getFields()
                .stream()
                .collect(Collectors.toMap(FieldVo::getKey, it -> it, (a, b) -> a));
        if (fieldVoMap.isEmpty()) {
            return Collections.emptyList();
        }
        final var exportFieldKeysOneLine = StringUtils.getList(templates.getExportFieldKeysOneLine(), StringUtils.SEPARATOR_COMMA);
        return exportFieldKeys
                .stream()
                .filter(fieldVoMap::containsKey)
                .map(fieldVoMap::get)
                .filter(field -> {
                    final BaseFieldValidator validator = BaseFieldValidator.findByType(
                            StringUtils.showEmpty(field.getSchema().getValidateType(), field.getSchema().getType())
                    );
                    return validator.isNotEmpty(map.get(field.getKey()));
                })
                .map(it -> {
                    var vo = BaseFieldConvert.findByType(
                            StringUtils.showEmpty(it.getSchema().getValidateType(), it.getSchema().getType())
                    ).convertForExport(it, map.get(it.getKey()), map.get(it.getKey() + Constants.ATTRIBUTES_VALUE), map);
                    vo.setOnlyOneLine(exportFieldKeysOneLine.contains(it.getKey()));
                    return vo;
                }).collect(Collectors.toList());
    }

    /**
     * recordLog<BR>
     *
     * @param login      参数
     * @param entity     参数
     * @param logContent 参数
     * @param logType    参数
     * @param attachment 参数
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/8/14 14:25
     */
    public void recordLog(
            Optional<CurrentUser> login,
            Entity entity,
            String logContent,
            String logType,
            String attachment
    ) throws ServiceException {
        recordLog(
                login,
                entity,
                logContent,
                logType,
                attachment,
                Function.identity()
        );
    }

    /**
     * recordLog<BR>
     *
     * @param login      参数
     * @param entity     参数
     * @param logContent 参数
     * @param logType    参数
     * @param attachment 参数
     * @param convert    参数
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/8/14 14:25
     */
    public void recordLog(Optional<CurrentUser> login, Entity entity, String logContent, String logType, String attachment, Function<ActionLogEntity, ActionLogEntity> convert) throws ServiceException {
        ActionLogEntity log = ActionLogEntity.addInfoOnCreate(
                login.orElseThrow(() -> new ServiceException("未登录")),
                new ActionLogEntity()
        );
        log.setContent(logContent);
        log.setLogType(logType);
        log.setObjType(module());
        log.setObjId(entity.getDataId());
        log.setObjRootId(entity.getRootId());
        log.setAttachment(attachment);
        getActionLogMapper().insert(convert.apply(log));
    }


    /**
     * summaryLogs<BR>
     *
     * @param entity 参数
     * @param logs   参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/3 19:14
     */
    public List<ChuZhiSummaryVo> summaryLogs(Entity entity, List<ActionLogVo> logs) throws ServiceException {
        List<ChuZhiItemVo> items = new ArrayList<>(0);
        for (ActionLogVo log : logs) {
            Optional<ActionEnum> action = ActionEnum.logTypeAndModule(log.getLogType(), log.getObjType());
            if (action.isPresent()) {
                items.addAll(BaseAction.findAction(action.get()).parseLog(entity, log));
            }
        }
        var map = items.stream()
                .collect(Collectors.groupingBy(ChuZhiItemVo::getDate));
        return map.entrySet()
                .stream()
                .map(item -> ChuZhiSummaryVo.of(
                        item.getKey(),
                        item.getValue()
                                .stream()
                                .sorted(Comparator.comparing(a -> TimeUtils.stringToDate(a.getTime())))
                                .collect(Collectors.toList())
                )).sorted(Comparator.comparing(a -> TimeUtils.stringToDate(a.getDate())))
                .collect(Collectors.toList());
    }

    /**
     * consumerThirdData<BR>
     *
     * @param pullDataVo   线索对象
     * @param cityUser     本地接收的用户（市局用户）
     * @param provinceUser 省厅用户
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/25 10:09
     */
    @Override
    public Optional<Long> consumerThirdData(PullDataVo pullDataVo, CurrentUser cityUser, CurrentUser provinceUser) throws ServiceException {
        // 大部分平台不需要消费处理
        return Optional.empty();
    }

    @Override
    public void finishedConsumerThirdData(PullDataDTO dto, CurrentUser cityUser, CurrentUser provinceUser) throws ServiceException {
        // 大部分平台不需要消费处理
    }

    @Override
    public List<? extends BaseEntityVo> relatedPersonList(RelatedPersonQueryDTO dto) throws ServiceException {
        return List.of();
    }

    @Override
    public List<PersonLastReportTimeVo> findPersonLastReportTime(FindPersonLastReportDto dto) throws ServiceException {
        return List.of();
    }
}
