package com.trs.police.intelligence.vo;

import com.trs.police.common.core.vo.permission.SimpleDeptVO;
import com.trs.police.intelligence.constant.Constants;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * YaoQingEntityVo
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/12 14:41
 * @since 1.0
 */
@Data
@ToString(callSuper = true)
public class YuQingEntityVo extends BaseIntelligenceEntityVo {

    private List<SimpleDeptVO> ccDepts;

    private String attachment;

    private String yqdj;

    private String yqlx;

    private Long yqlxId;

    private String sjdy;

    private String sjdyCode;
    private String sjdyName;

    private String yqly;

    private String url;

    private String gzyq;

    private Integer readStatus;

    public YuQingEntityVo() {
        setDataTypeShowName(Constants.YUQING);
        setDataClassShowName(Constants.DEFAULT);
        this.readStatus = 0;
    }

    /**
     * objType<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/11 11:28
     */
    @Override
    public String getObjType() {
        return Constants.YUQING;
    }
}
