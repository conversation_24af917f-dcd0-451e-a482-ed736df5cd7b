package com.trs.police.intelligence.service.impl.action.tongzhitongbao;

import com.alibaba.fastjson.JSONObject;
import com.trs.common.base.Report;
import com.trs.common.base.Report.RESULT;
import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.intelligence.constant.ActionEnum;
import com.trs.police.intelligence.constant.Constants;
import com.trs.police.intelligence.constant.Status;
import com.trs.police.intelligence.dto.CuiBanActionDTO;
import com.trs.police.intelligence.entity.TongZhiTongBaoBaseInfoEntity;
import com.trs.police.intelligence.service.BaseTongZhiTongBaoAction;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.trs.common.base.PreConditionCheck.checkArgument;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * ZhiLingAddExtInfoAction
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/18 15:01
 * @since 1.0
 */
@Component
public class TongZhiTongBaoCuiBanDataAction
        extends BaseTongZhiTongBaoAction<CuiBanActionDTO, Report<String>, Report<String>> {

    /**
     * actionEnum<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:09
     */
    @Override
    public ActionEnum actionEnum() {
        return ActionEnum.TONGZHITONGBAO_CUI_BAN;
    }

    /**
     * check<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:43
     */
    @Override
    protected void check(Optional<CurrentUser> login, TongZhiTongBaoBaseInfoEntity entity, CuiBanActionDTO dto)
            throws ServiceException {
        super.check(login, entity, dto);
        checkArgument(
                !entity.getStatusCode().equals(Status.CAOGAOXIANG.getCode()),
                new ParamInvalidException("草稿箱的数据不能催办")
        );
        final var acceptTarget = entity.makeAcceptTarget();
        checkArgument(
                entity.userCreateEntity(login),
                new ServiceException("创建人/单位才能进行催办")
        );
        checkArgument(CollectionUtils.isNotEmpty(acceptTarget), new ParamInvalidException("用户不在接收范围内，无法催办"));
        if (!acceptTarget.contains(dto.toObjDTO())) {
            checkArgument(
                    Objects.equals(Constants.DEPT, dto.getObjType()) && acceptTarget.stream()
                            .anyMatch(it -> Objects.equals(it.getObjDeptId(), dto.getObjId())),
                    new ParamInvalidException("不在接收范围内，无法催办")
            );
        }
    }

    /**
     * mergeOneR<BR>
     *
     * @param login   参数
     * @param reports 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:36
     */
    @Override
    protected Report<String> mergeOneR(Optional<CurrentUser> login, List<Report<String>> reports) {
        return reports.stream()
                .filter(it -> it.getResult() == RESULT.FAIL)
                .findAny()
                .orElse(new Report<>(desc(), "成功处理"));
    }

    /**
     * doOneAction<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:17
     */
    @Override
    public Report<String> doOneAction(Optional<CurrentUser> login, TongZhiTongBaoBaseInfoEntity entity,
                                      CuiBanActionDTO dto)
            throws ServiceException {
        return new Report<>(desc(), "操作成功");
    }

    /**
     * makeLogContent<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @param one    参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/16 17:40
     */
    @Override
    protected String makeLogContent(Optional<CurrentUser> login, TongZhiTongBaoBaseInfoEntity entity,
                                    CuiBanActionDTO dto,
                                    Report<String> one) {
        String name = getPermissionService().getDeptById(dto.getObjId()).getShortName();
        return String.format("催办 %s 处理%s %s", name, entity.makeShowName(), entity.getDataTitle());
    }

    /**
     * makeMessageContent<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @param one    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:38
     */
    @Override
    protected String makeMessageContent(Optional<CurrentUser> login, TongZhiTongBaoBaseInfoEntity entity,
                                        CuiBanActionDTO dto,
                                        Report<String> one) throws ServiceException {
        JSONObject value = JSONObject.parseObject(JsonUtil.toJsonString(entity));
        value.put("dataId", entity.getDataId());
        value.put("id", entity.getDataId());
        value.put("versionId", entity.getVersionId());
        value.put("status", entity.makeShowName() + "催办");
        String name = getPermissionService().getDeptById(dto.getObjId()).getShortName();
        value.put("content", String.format("催办 %s 处理%s %s", name, entity.makeShowName(), entity.getDataTitle()));
        value.put("dataTitle", entity.getDataTitle());
        return String.format("notifycation_type(%s)", value);
    }

    /**
     * makeMessageSimpleContent<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @param one    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 17:07
     */
    @Override
    protected String makeMessageSimpleContent(Optional<CurrentUser> login, TongZhiTongBaoBaseInfoEntity entity,
                                              CuiBanActionDTO dto, Report<String> one) throws ServiceException {
        String name = getPermissionService().getDeptById(dto.getObjId()).getShortName();
        return String.format("催办 %s 处理%s %s", name, entity.makeShowName(), entity.getDataTitle());
    }

}
