<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.intelligence.mapper.YuQingBaseInfoMapper">

    <select id="getMaxNo" resultType="java.lang.Integer">
        SELECT
        MAX(data_no)
        FROM
        tb_intelligence_yuqing_base_info tb
        <where>
            tb.is_del = 0
            AND tb.data_year = #{dataYear};
        </where>
    </select>

    <sql id="from_all">
        mp.status_type = '0' AND tb.drafts_flag=0
    </sql>

    <!-- 抄送我的 -->
    <sql id="from_cc">
        mp.status_type = 'cc' AND tb.drafts_flag=0
    </sql>
    <sql id="from_collection">
        tb.data_id IN (
        select
        obj_id
        from tb_intelligence_collection_data
        where
        relation_type='user'
        AND relation_id = #{user.id}
        AND obj_Type='yuqing'
        AND is_del=0
        )
        AND
        tb.drafts_flag=0
    </sql>

    <sql id="base_from">
        tb.is_del = 0
        <choose>
            <when test='from == "cc"'>
                and
                <include refid="from_cc"></include>
            </when>
            <when test='from == "all"'>
                and
                <include refid="from_all"></include>
            </when>
            <when test='from == "collection"'>
                and
                <include refid="from_collection"></include>
            </when>
            <otherwise>
                <!-- 一个永远为假的条件 -->
                and tb.is_del != 0
            </otherwise>
        </choose>
    </sql>

    <select id="doPageSelect" resultType="com.trs.police.intelligence.entity.YuQingBaseInfoEntity">
        SELECT
        tb.*,
        <if test='from == "cc" or from == "all"'>
            mp.status_code AS read_status,
        </if>
        <if test='from == "collection"'>
            col.collection_title AS collection_title,
        </if>
        tv.version_data_content as data_content,
        tv.version_data_title as version_data_title,
        tv.version_data_content as version_data_content
        FROM
        tb_intelligence_yuqing_base_info tb
        left join tb_intelligence_yuqing_version_info tv
        on tb.version_id = tv.data_id AND tb.data_id = tv.yq_data_id AND tv.is_del = 0
        <if test='from == "collection"'>
            left join tb_intelligence_collection_data col
            on col.obj_id = tb.data_id
            AND col.relation_type='user'
            AND col.relation_id = #{user.id}
            AND col.obj_Type='yuqing'
            AND col.is_del=0
        </if>
        <if test='from == "cc"'>
            left join tb_intelligence_data_relation_mapping mp
            on mp.relation_id = tb.data_id
            AND mp.relation_type='yuqing'
            AND mp.obj_type='dept'
            AND mp.obj_id = #{user.deptId}
            AND mp.status_type = 'cc'
            AND mp.is_del=0
        </if>
        <if test='from == "all"'>
            left join tb_intelligence_data_relation_mapping mp
            on mp.relation_id = tb.data_id
            AND mp.relation_type='yuqing'
            AND mp.obj_type='dept'
            AND mp.obj_id = #{user.deptId}
            AND mp.status_type='0'
            AND mp.is_del=0
        </if>
        <where>
            <include refid="base_from"></include>
            <if test="searchParams != null and @org.apache.commons.lang3.StringUtils@isNotBlank(searchParams.getSearchValue())">
                <bind name="pattern" value="'%' + searchParams.getSearchValue() + '%'"/>
                <choose>
                    <when test='searchParams.searchField == "dataTitle"'>
                        <choose>
                            <when test='from == "collection"'>
                                AND col.collection_title like #{pattern}
                            </when>
                            <otherwise>
                                AND tb.data_title like #{pattern}
                            </otherwise>
                        </choose>
                    </when>
                    <when test='searchParams.searchField == "crUser"'>
                        AND (tb.cr_user like #{pattern} OR tb.cr_user_true_name like #{pattern})
                    </when>
                </choose>
            </if>
            <foreach collection="baseFilter" item="param">
                <choose>
                    <!-- 时间范围,查全部时不加条件 -->
                    <when test="param.type.equals('timeParams')">
                        <if test="param.getProcessedValue().isAll() == false">
                            AND tb.${param.key} between #{param.value.beginTime} and #{param.value.endTime}
                        </if>
                    </when>
                    <!-- array -->
                    <when test="param.type.equals('array') or param.type.equals('in') ">
                        <bind name="inValues" value="param.getProcessedValue()"/>
                        <if test="inValues != null and inValues.size()>0">
                            AND tb.${param.key} IN
                            <foreach collection="inValues" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                        </if>
                    </when>
                    <!-- array_find_in_set -->
                    <when test="param.type.equals('array_find_in_set') or param.type.equals('find_in_set')">
                        <bind name="inValues" value="param.getProcessedValue()"/>
                        <if test="inValues != null and inValues.size()>0">
                            AND
                            <foreach collection="inValues" item="item" open="(" separator=" OR " close=")">
                                FIND_IN_SET(#{item}, tb.${param.key})
                            </foreach>
                        </if>
                    </when>
                    <!-- 直接本字段比较 -->
                    <otherwise>
                        AND tb.${param.key} = #{param.value}
                    </otherwise>
                </choose>
            </foreach>
        </where>
        <choose>
            <when test="sortParams != null and sortParams.sortField != null">
                ORDER BY tb.${sortParams.sortField} ${sortParams.getProcessedValue()}, tb.data_id desc
            </when>
            <otherwise>
                order by tb.cr_time desc,tb.data_id desc
            </otherwise>
        </choose>
    </select>

    <select id="getUnReadNum" resultType="com.trs.police.intelligence.vo.GroupVo">
        select
        t.relation_id AS groupName,
        count(1) AS groupValue
        from
        tb_intelligence_data_relation_mapping t
        <where>
            t.is_del = 0
            AND t.relation_type='yuqing'
            AND t.status_code = 0
            AND t.obj_type='dept'
            AND t.obj_id = #{user.deptId}
            <if test='from == "cc"'>
                AND t.status_type = 'cc'
            </if>
            <if test='from == "all"'>
                AND t.status_type = '0'
            </if>
        </where>
        group by t.relation_id;
    </select>
</mapper>
