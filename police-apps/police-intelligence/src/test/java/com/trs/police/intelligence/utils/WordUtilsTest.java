package com.trs.police.intelligence.utils;

import com.trs.common.utils.TimeUtils;
import com.trs.police.intelligence.vo.export.ChuZhiItemVo;
import com.trs.police.intelligence.vo.export.ChuZhiSummaryVo;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

class WordUtilsTest {

    private String content =
            "区块对开始和结束标签中间可以包含多个图片、表格、段落、列表、图表等，开始和结束标签可以跨多个段落，也可以在同一个段落，但是如果在表格中使用区块对，开始和结束标签必须在同一个单元格内，因为跨多个单元格的渲染行为是未知的。\n"
                    + "    区块对在处理一系列文档元素的时候非常有用，位于区块对中的文档元素可以被渲染零次，一次或N次，这取决于区块对的取值。";

    @Test
    void exportFile() {
        final Map<String, Object> map = new HashMap<>();
        map.put("deptName", "测试单位");
        map.put("dataYear", 2024);
        map.put("dataNo", 1);
        map.put("signer", "TRS");
        map.put("reportTime", TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD2));
        map.put("dataTitle", "测试标题");
        map.put("haveDataSubTitle", true);
        map.put("dataSubTitle", "测试副标题");
        map.put("dataContent", WordUtils.parseContentForExport(content, 16, 25));
        map.put("crUserTrueName", "系统管理员");
        map.put("phone", "13412341234");
        final String fileName = "target/要情文件测试.docx";
        WordUtils.exportFile(fileName, "qzx.yaoqing.bdzcl.docx", map);
        System.out.println(fileName);
    }

    @Test
    void testZhiLingExportFile() {
        final Map<String, Object> map = new HashMap<>();
        map.put("orderNo", "公厅联指〔2024〕27号");
        map.put("signer", "TRS");
        map.put("orderTime", TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD_HHMMSS));
        map.put("orderLevel", "一级");
        map.put("dataTitle", "测试指令");
        map.put("dataContent", WordUtils.parseContentForExport(content, 11, 26));
        final String fileName = "target/指令文件测试.docx";
        WordUtils.exportFile(fileName, "qzx.zhiling.template.docx", map);
        System.out.println(fileName);
    }

    @Test
    void testExportZhiLingEvent() {
        final Map<String, Object> map = new HashMap<>();
        map.put("loginUserDeptName", "四川省测试单位");
        map.put("loginUserDeptShortName", "测试单位");
        map.put("dataTitle", "指令的回复标题");
        map.put("pushTime", TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD_HHMMSS));
        map.put("crUserTrueName", "管理员");
        map.put("crDeptName", "泸州市龙马潭区分局");
        map.put("crDeptShortName", "龙马潭区分局");
        List<ChuZhiSummaryVo> czqk = List.of(
                ChuZhiSummaryVo.of(
                        TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD2),
                        List.of(
                                ChuZhiItemVo.of(
                                        TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD),
                                        TimeUtils.getCurrentDate(TimeUtils.HHMMSS),
                                        "测试单位",
                                        "测试单位",
                                        "管理员",
                                        "指令",
                                        "你局提请关于请求协调****工作的报告已获**回复，现将**情况流转至你局掌握。 **通报：******以此形成闭环，真正化解双方长达十余年的婚恋纠。"
                                ),
                                ChuZhiItemVo.of(
                                        TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD),
                                        TimeUtils.getCurrentDate(TimeUtils.HHMMSS),
                                        "测试单位",
                                        "测试单位",
                                        "管理员",
                                        "反馈",
                                        "按要求落实。"
                                ),
                                ChuZhiItemVo.of(
                                        TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD),
                                        TimeUtils.getCurrentDate(TimeUtils.HHMMSS),
                                        "测试单位",
                                        "测试单位",
                                        "管理员",
                                        "反馈",
                                        "已报指挥长及相关部门立即开展工作。"
                                )
                        )
                )
        );
        map.put("czqk", czqk);
        List<ChuZhiSummaryVo> jaxx = List.of(
                ChuZhiSummaryVo.of(
                        TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD2),
                        List.of(
                                ChuZhiItemVo.of(
                                        TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD),
                                        TimeUtils.getCurrentDate(TimeUtils.HHMMSS),
                                        "测试单位",
                                        "测试单位",
                                        "管理员",
                                        "结案",
                                        "。"
                                )
                        )
                )
        );
        map.put("jaxx", jaxx);
        final String fileName = "target/指令处置事件文件测试.docx";
        WordUtils.exportFile(fileName, "qzx.zhiling.sjczlc.template.docx", map);
        System.out.println(fileName);
    }
}