package com.trs.police.intelligence.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

class FieldUtilsTest {

    @Test
    void reBuildFilter() throws JsonProcessingException {
        var v = FieldUtils.reBuildFilter(
                new KeyValueTypeVO("a", List.of("降雨"), "select")
        );
        v.getValue();
        String json = "{\"pageParams\":{\"pageNumber\":1,\"pageSize\":10},\"searchParams\":{\"searchField\":\"dataTitle\",\"searchValue\":\"\"},\"sortParams\":{\"sortField\":\"cr_time\"},\"filterParams\":[{\"key\":\"data_type\",\"type\":\"string\",\"value\":\"zhiling\"},{\"key\":\"data_class\",\"type\":\"string\",\"value\":\"default\"},{\"key\":\"attributes.orderLevel\",\"type\":\"checkbok\",\"value\":[\"二级\"]}]}";
        ObjectMapper objectMapper = new ObjectMapper();
        ListParamsRequest request = objectMapper.readValue(json, ListParamsRequest.class);
        FieldUtils.reBuildFilter(
                request.getFilterParams().get(0)
        );
    }

    @Test
    void testSome() {
        List<Integer> list = new ArrayList<>(1);
        list.add(null);
        System.out.println(list
                .stream()
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(0));
    }
}