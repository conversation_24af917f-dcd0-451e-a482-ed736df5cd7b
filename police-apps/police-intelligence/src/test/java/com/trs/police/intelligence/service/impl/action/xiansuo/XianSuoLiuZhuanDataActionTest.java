package com.trs.police.intelligence.service.impl.action.xiansuo;

import com.trs.common.exception.ServiceException;
import com.trs.police.intelligence.PoliceIntelligenceApplication;
import com.trs.police.intelligence.dto.XianSuoLiuZhuanDataDTO;
import com.trs.police.test.BaseTestCase;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = PoliceIntelligenceApplication.class)
class XianSuoLiuZhuanDataActionTest extends BaseTestCase {

    @Autowired
    private XianSuoLiuZhuanDataAction action;

    @Test
    void doAction() throws ServiceException {
        XianSuoLiuZhuanDataDTO dto = new XianSuoLiuZhuanDataDTO();
        dto.setDataId(367L);
        print(action.doAction(dto));
    }
}