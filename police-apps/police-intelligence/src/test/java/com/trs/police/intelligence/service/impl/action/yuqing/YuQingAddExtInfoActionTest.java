package com.trs.police.intelligence.service.impl.action.yuqing;

import com.trs.police.intelligence.PoliceIntelligenceApplication;
import com.trs.police.intelligence.dto.YuQingModifyContentDTO;
import com.trs.police.intelligence.service.impl.YuQingEntityServiceImpl;
import com.trs.police.test.BaseTestCase;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = PoliceIntelligenceApplication.class)
class YuQingAddExtInfoActionTest extends BaseTestCase {

    @Autowired
    private YuQingAddExtInfoAction action;

    @Autowired
    private YuQingEntityServiceImpl service;

    @Test
    void testAction() throws Exception {
        service.setPermissionService(permissionService);
        YuQingModifyContentDTO dto = new YuQingModifyContentDTO();
        dto.setDataId(1L);
        dto.setVersionId(1L);
        dto.setYqdj("重大");
        dto.setDataContent("<ul><li>测试保存的内容重大</li></ul>");
        action.doAction(dto);
        print(service.detail(1L));

        dto.setDataId(1L);
        dto.setVersionId(0L);
        dto.setYqdj("一般");
        dto.setDataContent("<ul><li>续保测试保存的内容一般</li></ul>");
        action.doAction(dto);
        print(service.detail(1L));
    }

}