package com.trs.police.intelligence.service.impl;

import com.trs.common.exception.ServiceException;
import com.trs.common.utils.TimeUtils;
import com.trs.police.intelligence.PoliceIntelligenceApplication;
import com.trs.police.intelligence.constant.Constants;
import com.trs.police.intelligence.dto.ExportDTO;
import com.trs.police.intelligence.dto.ZhiLingSaveDTO;
import com.trs.police.intelligence.utils.FieldUtils;
import com.trs.police.intelligence.vo.AttributeTemplatesVo;
import com.trs.police.test.BaseTestCase;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.*;

@SpringBootTest(classes = PoliceIntelligenceApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class ZhiLingEntityServiceImplTest extends BaseTestCase {

    @Autowired
    @InjectMocks
    private ZhiLingEntityServiceImpl service;

    @Test
    void getOrderNo() throws ServiceException {
        System.out.println(service.getMaxNo(Constants.ZHILING));
    }

    @Test
    void saveOrUpdate() throws Exception {
        ZhiLingSaveDTO dto = new ZhiLingSaveDTO();
        dto.setDataId(0L);
        dto.setDataTitle("测试保存编号");
        dto.setDataContent("测试保存编号");
        dto.setDataNo(113);
        dto.setDataType("zhiling");
        dto.setDataClass("default");
        dto.setAttributes("{\"dataId\":0,\"dataNo\":113,\"dataTitle\":\"测试保存编号\",\"dataContent\":\"测试保存编号\",\"orderNo\":\"公厅联指〔2024〕113号\",\"orderType\":\"领导批示\",\"orderLevel\":\"其他\",\"feedbackLimitTime\":null,\"signer\":\"cc\",\"leaderUnit\":\"四川省遂宁市\",\"contactsUser\":\"cc\",\"phone\":\"13412341234\",\"relatedYaoQing\":\"\",\"relatedClue\":\"\",\"acceptDeptIds\":\"1\",\"attachment\":[],\"noticeType\":\"一般性通报\",\"noticeNo\":\"\",\"needFeedback\":0,\"insertExcels\":[],\"leaderUnitId\":430}");
        dto.setDraftsFlag(1);
        service.saveOrUpdate(dto);
    }

    @Test
    void detail() throws ServiceException {
        var entity = service.findById(202L);
        AttributeTemplatesVo template = service.findAttributeTemplates(entity.getDataType(), entity.getDataClass());
        List<Map<String, Object>> list = service.getAttributeMapper().findAttribute(
                template.getRelationTableName(),
                entity.getDataId(),
                entity.getVersionId()
        );
        Map<String, Object> dbData;
        if (!list.isEmpty()) {
            dbData = list.get(0);
        } else {
            dbData = Collections.emptyMap();
        }
        Map<String, String> attributes = FieldUtils.getAttributes(template, dbData);
    }

    @Test
    void testTime() {
        final Date now = new Date();
        String nowTime = TimeUtils.dateToString(now, TimeUtils.YYYYMMDD_HHMMSS);
        String timeFor30Min = TimeUtils.dateToString(TimeUtils.befOrAft(
                now,
                30,
                Calendar.MINUTE
        ), TimeUtils.YYYYMMDD_HHMMSS);
        String timeFor2H = TimeUtils.hourDefOrAft(now, 2, TimeUtils.YYYYMMDD_HHMMSS);
        System.out.println(nowTime);
        System.out.println(timeFor30Min);
        System.out.println(timeFor2H);
    }

    @Test
    void testExport() throws Exception {
        ExportDTO dto = new ExportDTO();
        dto.setDataId(202L);
        dto.setTemplateType(Constants.SJCZLC);
        print(service.export(dto));
    }
}