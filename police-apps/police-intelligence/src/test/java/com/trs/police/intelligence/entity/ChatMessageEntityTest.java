package com.trs.police.intelligence.entity;

import org.junit.jupiter.api.Test;

class ChatMessageEntityTest {

    @Test
    void getMessage() {
        ChatMessageEntity entity = new ChatMessageEntity();
        entity.setContent("{\n" +
                "    \"message\":{\n" +
                "    \"content\":\"directive_type({\\\"orderType\\\":\\\"警情舆情处置\\\",\\\"crTime\\\":1731292048000,\\\"dataContent\\\":\\\"下发指令带有附件\\\",\\\"signInfo\\\":\\\"1;1\\\",\\\"feedbackTimeout\\\":1,\\\"crUser\\\":\\\"admin\\\",\\\"chatId\\\":612,\\\"rootId\\\":470,\\\"createType\\\":0,\\\"versionDataContent\\\":\\\"下发指令带有附件\\\",\\\"acceptUsers\\\":\\\"[]\\\",\\\"feedbackInfo\\\":\\\"1;1\\\",\\\"acceptInfo\\\":\\\"1;1\\\",\\\"content\\\":\\\"zhengchang反馈\\\",\\\"dataId\\\":470,\\\"orderLevel\\\":\\\"一级\\\",\\\"topMarking\\\":0,\\\"acceptDeptIds\\\":\\\"502\\\",\\\"id\\\":470,\\\"pushTime\\\":1731292048000,\\\"fieldsEditInfo\\\":\\\"8;13\\\",\\\"draftsFlag\\\":0,\\\"dataYear\\\":2024,\\\"orderNo\\\":\\\"公厅联指〔2024〕289号\\\",\\\"watchFlag\\\":0,\\\"crDeptId\\\":2,\\\"dataType\\\":\\\"zhiling\\\",\\\"dataClass\\\":\\\"default\\\",\\\"signTimeout\\\":0,\\\"versionId\\\":554,\\\"feedbackTimeLimit\\\":1731295540000,\\\"feedbackMessageFlag\\\":2,\\\"classicalFlag\\\":0,\\\"notGenerateNo\\\":false,\\\"dataTitle\\\":\\\"下发指令带有附件\\\",\\\"files\\\":\\\"[{\\\\\\\"name\\\\\\\":\\\\\\\"新建文本文档 (2).txt\\\\\\\",\\\\\\\"url\\\\\\\":\\\\\\\"/oss/file/e385fde6ec4d718d7e0a72f9b63f2948.txt\\\\\\\",\\\\\\\"size\\\\\\\":\\\\\\\"313 bytes\\\\\\\",\\\\\\\"id\\\\\\\":143389,\\\\\\\"previewImage\\\\\\\":null,\\\\\\\"ext\\\\\\\":\\\\\\\"txt\\\\\\\",\\\\\\\"type\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"duration\\\\\\\":null,\\\\\\\"pdfUrl\\\\\\\":null}]\\\",\\\"dataNo\\\":289,\\\"isDel\\\":0,\\\"versionDataTitle\\\":\\\"下发指令带有附件\\\",\\\"crUserTrueName\\\":\\\"管理员\\\",\\\"statusCode\\\":150,\\\"status\\\":\\\"指令反馈\\\"})\"\n" +
                "    }\n" +
                "}");
        System.out.println(entity.getMessage());
    }
}