package com.trs.police.permission.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.constant.enums.DataPermissionEnum;
import com.trs.police.common.core.constant.enums.PermissionParamsEnum;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.mapper.CommonMapper;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.vo.IdNameVO;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.permission.DataPermissionInfo.ProfilePermission;
import com.trs.police.common.core.vo.permission.SimpleUserVO;
import com.trs.police.common.redis.starter.service.RedisService;
import com.trs.police.permission.constant.PermissionConstant;
import com.trs.police.permission.domain.entity.DataPermission;
import com.trs.police.permission.domain.entity.Dept;
import com.trs.police.permission.domain.entity.User;
import com.trs.police.permission.domain.entity.UserPermissionRelation;
import com.trs.police.permission.domain.vo.PermissionListVO;
import com.trs.police.permission.domain.vo.UserPermListVO;
import com.trs.police.permission.domain.vo.UserPermRelationVO;
import com.trs.police.permission.enums.DataPermissionTypeEnum;
import com.trs.police.permission.mapper.DataPermissionMapper;
import com.trs.police.permission.mapper.UserDataPermRelationMapper;
import com.trs.police.permission.mapper.UserMapper;
import com.trs.police.permission.service.DataPermissionService;
import com.trs.police.permission.service.DeptService;
import com.trs.police.permission.service.UserService;
import com.trs.police.permission.util.PermissionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 数据权限
 *
 * <AUTHOR> yanghy
 * @date : 2022/7/20 16:50
 */
@Service
@Slf4j
public class DataPermissionServiceImpl implements DataPermissionService {

    @Resource
    private DataPermissionMapper dataPermissionMapper;
    @Resource
    private DeptService deptService;
    @Resource
    private UserDataPermRelationMapper userDataPermRelationMapper;
    @Resource
    private UserMapper userMapper;
    @Lazy
    @Resource
    private UserService userService;
    @Resource
    private CommonMapper commonMapper;
    @Resource
    private RedisService redisService;

    @PostConstruct
    void deleteCache() {
        log.info("删除数据权限相关缓存...");

        redisService.delByPrefix("permission");
    }

    @Override
    public List<IdNameVO> getDataPermissions() {
        return dataPermissionMapper.findAllByType(DataPermissionTypeEnum.CUSTOM.getCode()).stream()
            .map(DataPermission::toIdNameVo)
            .collect(Collectors.toList());
    }

    @Override
    public List<PermissionListVO> getPermsVO(DataPermissionTypeEnum type) {
        return dataPermissionMapper.findAllByType(type.getCode()).stream().map(e -> {
            String userDisplayName = null;
            LocalDateTime createTime = null;
            if (DataPermissionTypeEnum.SYSTEM != type) {
                User user = userMapper.findById(e.getCreateUserId());
                Dept dept = deptService.getDeptById(e.getCreateDeptId());
                userDisplayName = PermissionUtil.generateUserDept(user, dept);
                createTime = e.getCreateTime();
            }
            return new PermissionListVO(e.getId(), e.getName(), e.getType().getName(), e.getDescription(),
                userDisplayName, createTime);
        }).collect(Collectors.toList());
    }

    @Override
    public List<PermissionListVO> getPermsVoByLikeName(DataPermissionTypeEnum type, String name) {
        return dataPermissionMapper.findAllByTypeAndLikeName(type.getCode(), name).stream().map(e -> {
            String userDisplayName = null;
            LocalDateTime createTime = null;
            if (DataPermissionTypeEnum.SYSTEM != type) {
                User user = userMapper.findById(e.getCreateUserId());
                Dept dept = deptService.getDeptById(e.getCreateDeptId());
                userDisplayName = PermissionUtil.generateUserDept(user, dept);
                createTime = e.getCreateTime();
            }
            return new PermissionListVO(e.getId(), e.getName(), e.getType().getName(), e.getDescription(),
                    userDisplayName, createTime);
        }).collect(Collectors.toList());
    }

    @Override
    public boolean checkName(String name) {
        LambdaQueryWrapper<DataPermission> wrapper = Wrappers.lambdaQuery(DataPermission.class)
            .eq(DataPermission::getName, name);
        return !dataPermissionMapper.exists(wrapper);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void create(DataPermission role) {
        String name = role.getName();
        if (!this.checkName(name)) {
            throw new TRSException("新建数据权限失败！权限名：" + name + ",已存在");
        }
        role.setType(DataPermissionTypeEnum.CUSTOM);
        if (role.getPermission() == null) {
            role.setPermission(DataPermissionEnum.SELF);
        }
        role.fillAuditFields(AuthHelper.getNotNullUser());
        dataPermissionMapper.insert(role);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    @CacheEvict(value = "permission:profile", key = "#dataPermission.id")
    public void update(DataPermission dataPermission) {
        String name = dataPermission.getName();
        DataPermission oldDataPermission = dataPermissionMapper.selectById(dataPermission.getId());
        if (!this.checkName(name) && !oldDataPermission.getName().equals(name)) {
            throw new TRSException("新建数据权限失败！权限名：" + name + ",已存在");
        }
        dataPermission.setCreateUserId(oldDataPermission.getCreateUserId());
        dataPermission.setCreateDeptId(oldDataPermission.getCreateDeptId());
        dataPermission.setCreateTime(oldDataPermission.getCreateTime());
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        if (currentUser != null) {
            dataPermission.setUpdateDeptId(currentUser.getId());
            dataPermission.setUpdateDeptId(currentUser.getDeptId());
        }
        dataPermission.setUpdateTime(LocalDateTime.now());
        dataPermissionMapper.updateById(dataPermission);

        //删除档案缓存
        redisService.delByPrefix("profileSchema");
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    @CacheEvict(value = "profileSchema", allEntries = true)
    public void addBatchUser(Long dataId, List<UserPermRelationVO> userPermissionRelation) {
        for (UserPermRelationVO e : userPermissionRelation) {
            e.setPermissionId(dataId);
            UserPermissionRelation relation = e.toUserPermissionRelation();
            //查重
            LambdaQueryWrapper<UserPermissionRelation> wrapper = Wrappers.lambdaQuery(UserPermissionRelation.class)
                .eq(UserPermissionRelation::getPermissionId, e.getPermissionId())
                .eq(UserPermissionRelation::getUserId, e.getUserId())
                .eq(UserPermissionRelation::getDeptId, e.getDeptId());
            if (userDataPermRelationMapper.exists(wrapper)) {
                continue;
            }
            userDataPermRelationMapper.insert(relation);
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    @CacheEvict(value = "profileSchema", allEntries = true)
    public void deleteBatchUser(Long dataId, List<UserPermRelationVO> userPermissionRelation) {
        userDataPermRelationMapper.deleteBatchUser(dataId, userPermissionRelation);
    }

    @Override
    public PageResult<UserPermListVO> getUserList(Long permissionId, ListParamsRequest params) {
        List<KeyValueTypeVO> filterParams = this.buildParams();
        PageParams pageParams = params.getPageParams();
        Page<UserPermListVO> page = userDataPermRelationMapper.findUserPage(
                permissionId,
                filterParams,
                params.getSearchParams(),
                pageParams.toPage()
        );
        return PageResult.of(page.getRecords(), pageParams.getPageNumber(), page.getTotal(), pageParams.getPageSize());
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public boolean delete(Long dataId) {
        return userDataPermRelationMapper.countByPermissionId(dataId) == 0
            && dataPermissionMapper.deleteById(dataId) == 1;
    }


    @Override
    public DataPermissionEnum getMaxDataPermission(Long userId, Long deptId) {
        DataPermission max = dataPermissionMapper.findMaxByUserAndDept(userId, deptId);
        return Objects.isNull(max) ? DataPermissionEnum.SELF : max.getPermission();
    }

    @Override
    public DataPermission getMaxDataPermissionDetail(Long userId, Long deptId) {
        return dataPermissionMapper.findMaxByUserAndDept(userId, deptId);
    }

    @Override
    public List<SimpleUserVO> getUsers(String deptCode, boolean isContainChild, Long dataId) {
        List<KeyValueTypeVO> filterParams = buildParams();
        return userMapper.getUserHasNoDataPermission(deptCode, isContainChild, dataId, filterParams);
    }

    @Override
    public DataPermission getDetail(Long id) {
        return dataPermissionMapper.selectById(id);
    }

    @Override
    public List<String> getProfileLabelsByIds(String ids) {
        List<String> idList = Arrays.asList(ids.split(","));
        return dataPermissionMapper.getProfileLabelsByIds(idList);
    }

    @Override
    public List<KeyValueTypeVO> buildParams() {
        return buildParams(PermissionParamsEnum.DEFAULT);
    }

    @Override
    public List<KeyValueTypeVO> buildParams(PermissionParamsEnum permissionParamsEnum) {
        List<KeyValueTypeVO> filterParams = new ArrayList<>();
        CurrentUser currentUser = AuthHelper.getNotNullUser();
        DataPermission dataPermission = dataPermissionMapper.findMaxByUserAndDept(currentUser.getId(),
            currentUser.getDeptId());
        DataPermissionEnum permission =
            Objects.isNull(dataPermission) ? DataPermissionEnum.SELF : dataPermission.getPermission();
        switch (permission) {
            case ALL:
                break;
            case SELF:
                if (permissionParamsEnum == PermissionParamsEnum.SELF_EQUAL_DEPT) {
                    //权限为本人时，按照本部门部门处理
                    filterParams.add(
                        new KeyValueTypeVO(PermissionConstant.PERMISSION_DEPT, List.of(currentUser.getDeptId()),
                            "long"));
                    filterParams.add(
                        new KeyValueTypeVO(PermissionConstant.PERMISSION_DEPT_CODE,
                            List.of(currentUser.getDept().getCode()), "string"));
                } else {
                    filterParams.add(
                        new KeyValueTypeVO(PermissionConstant.CURRENT_USER, currentUser.getId(), "string"));
                }
                break;
            default:
                List<DeptDto> deptsByDataPermission = deptService.getDeptByDataPermission();
                List<Long> deptIds = deptsByDataPermission.stream().map(DeptDto::getId).collect(Collectors.toList());
                List<String> deptCodes = deptsByDataPermission.stream().map(DeptDto::getCode)
                    .collect(Collectors.toList());
                filterParams.add(new KeyValueTypeVO(PermissionConstant.PERMISSION_DEPT, deptIds, null));
                filterParams.add(new KeyValueTypeVO(PermissionConstant.PERMISSION_DEPT_CODE, deptCodes, null));
                break;
        }
        //档案标签权限
        filterParams.addAll(buildParamsProfile(dataPermission));
        return filterParams;
    }

    @Override
    public List<KeyValueTypeVO> buildParamsProfile() {
        CurrentUser currentUser = AuthHelper.getNotNullUser();
        DataPermission dataPermission = dataPermissionMapper.findMaxByUserAndDept(currentUser.getId(),
            currentUser.getDeptId());
        return buildParamsProfile(dataPermission);
    }


    private List<KeyValueTypeVO> buildParamsProfile(DataPermission dataPermission) {
        if (dataPermission == null || dataPermission.getProfileLabels() == null) {
            return Collections.emptyList();
        } else {
            return Arrays.stream(dataPermission.getProfileLabels()).map(e -> {
                if (e.getKey() == null){
                    return null;
                }
                List<Object> labels = KeyValueTypeVO.nestingListSimplification(e.getValue());
                if (labels.isEmpty()) {
                    return null;
                }
                List<Long> personLabel = commonMapper.getProfileLabelChildren(labels, e.getKey());
                return new KeyValueTypeVO(e.getKey() + "LabelPermission", personLabel, "array");
            }).filter(Objects::nonNull).collect(Collectors.toList());
        }
    }

    @Cacheable(value = "permission:profile", key = "#dataPermission.id")
    @Override
    public ProfilePermission getProfilePermission(DataPermission dataPermission) {
        ProfilePermission profilePermission = new ProfilePermission();

        for (KeyValueTypeVO profileLabel : dataPermission.getProfileLabels()) {
            List<Object> labels = KeyValueTypeVO.nestingListSimplification(profileLabel.getValue());
            if (!labels.isEmpty()) {
                List<Long> labelIds = commonMapper.getProfileLabelChildren(labels, profileLabel.getKey());
                String fieldName = profileLabel.getKey() + "LabelIds";
                BeanUtil.setProperty(profilePermission, fieldName, labelIds);
            }
            // 没有设置标签
            if (Objects.isNull(profileLabel.getValue())) {
                String fieldName = profileLabel.getKey() + "IsNull";
                BeanUtil.setProperty(profilePermission, fieldName, Boolean.TRUE);
            }
        }
        return BeanUtil.isAllPropertyNull(profilePermission) ? null : profilePermission;
    }

    @Override
    public List<Long> getDeptIds() {
        return getDeptIds(PermissionParamsEnum.DEFAULT);
    }

    @Override
    public List<Long> getDeptIds(PermissionParamsEnum permissionParamsEnum) {
        DataPermissionEnum currentUserMaxDataPermission = userService.getCurrentUserMaxDataPermission();
        List<Long> deptIds = List.of();
        switch (currentUserMaxDataPermission) {
            case ALL:
                break;
            case SELF:
                CurrentUser currentUser = AuthHelper.getNotNullUser();
                if (permissionParamsEnum == PermissionParamsEnum.SELF_EQUAL_DEPT) {
                    //权限为本人时，按照本部门部门处理
                    deptIds = List.of(currentUser.getDeptId());
                } else {
                    deptIds = List.of(-1L);
                }
                break;
            default:
                List<DeptDto> deptByDataPermission = deptService.getDeptByDataPermission();
                deptIds = deptByDataPermission.stream().map(DeptDto::getId).collect(Collectors.toList());
        }
        return deptIds;
    }

    @Override
    public KeyValueTypeVO[] getUserProfileLabels() {
        CurrentUser currentUser = AuthHelper.getNotNullUser();
        DataPermission maxByUserAndDept = dataPermissionMapper.findMaxByUserAndDept(currentUser.getId(),
            currentUser.getDeptId());
        if (Objects.isNull(maxByUserAndDept)) {
            return new KeyValueTypeVO[0];
        }
        return maxByUserAndDept.getProfileLabels();
    }

    @Override
    public KeyValueTypeVO[] getUserSearch(Long userId, Long deptId) {
        DataPermission maxByUserAndDept = dataPermissionMapper.findMaxByUserAndDept(userId, deptId);
        if (Objects.isNull(maxByUserAndDept)) {
            return new KeyValueTypeVO[0];
        }
        return maxByUserAndDept.getSearchSchema();
    }
}
