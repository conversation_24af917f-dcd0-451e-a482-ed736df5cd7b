package com.trs.police.permission.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.trs.police.permission.domain.entity.Role;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 角色操作权限
 *
 * <AUTHOR> yanghy
 * @date : 2022/7/27 16:25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RoleOperationVO {

    /**
     * 角色Id
     */
    @JsonIgnore
    private Long id;
    /**
     * 操作权限
     */
    @JsonProperty("operations")
    private List<String> operation;
    /**
     * 功能模块权限
     */
    @JsonProperty("modules")
    private List<String> module;

    /**
     * 转role实体
     *
     * @return com.trs.police.permission.domain.entity.Role  角色实体
     */
    public Role toRole() {
        Role role = new Role();
        role.setId(this.getId());
        role.setOperation(this.getOperation());
        role.setModule(this.getModule());
        return role;
    }
}
