package com.trs.police.permission.controller;

import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.SearchUserDTO;
import com.trs.police.common.core.dto.UserDto;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.entity.DeptDistrict;
import com.trs.police.common.core.utils.IpUtil;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.core.vo.permission.*;
import com.trs.police.common.openfeign.starter.DTO.ApprovalUserV2DTO;
import com.trs.police.permission.domain.entity.Dept;
import com.trs.police.permission.mapper.DeptDistrictMapper;
import com.trs.police.permission.service.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 不需要权限
 *
 * <AUTHOR>
 * @since 2022/4/26 11:22
 **/
@RestController
@RequestMapping("/public")
public class PublicController {

    @Resource
    private UserService userService;
    @Resource
    private RoleService roleService;
    @Resource
    private DeptDistrictMapper deptDistrictMapper;
    @Resource
    private DataPermissionService dataPermissionService;

    @Autowired
    private DeptService deptService;

    @Autowired
    private GovWxUserInfoService govWxUserInfoService;


    /**
     * 根据用户username或mobile查询所有部门
     *
     * @param username 用户username
     * @param mobile 手机号码
     * @return {@link Dept}
     */
    @GetMapping("/user/dept")
    public List<Dept> findLoginUserDept(String username, String mobile) {
        return userService.findLoginUserDept(username, mobile);
    }

    /**
     * 根据用户username查询所有部门-不包含父级
     *
     * @param username 用户username
     * @return {@link Dept}
     */
    @GetMapping("/user/deptNoParent")
    public List<Dept> findLoginUserDeptfindLoginUserDeptNoParent(String username) {
        return userService.findLoginUserDeptNoParent(username);
    }

    /**
     * 根据部门编号前缀查询所有用户列表
     *
     * @param deptCodePrefix 部门编号前缀
     * @return {@link CurrentUser}  用户列表
     */
    @GetMapping("/user/dept-code-prefix")
    public List<CurrentUser> getUserListByDeptCodePrefix(@RequestParam("deptCodePrefix") String deptCodePrefix) {
        return userService.getUserListByDeptCodePrefix(deptCodePrefix);
    }

    /**
     * 根据用户id查询用户详情
     *
     * @param userId 用户id
     * @return 用户详情
     */
    @GetMapping("/user/info/{userId}")
    public UserDto getUserById(@PathVariable("userId") Long userId) {
        return userService.getUserById(userId).toDto();
    }

    /**
     * 根据用户名字查询用户详情
     *
     * @param userNames 用户名字
     * @return 用户详情
     */
    @PostMapping("/user/infos")
    public List<UserDto> getUserByRealNames(@RequestBody List<String> userNames) {
        return userService.getUserByByFieldNameUsernames("real_name", userNames);
    }

    /**
     * 根据用户名字查询用户详情<BR>
     *
     * @param fieldName 参数
     * @param userNames 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/12 16:03
     */
    @PostMapping("/user/{fieldName}/infos")
    public List<UserDto> getUserByFieldNameNames(
        @PathVariable("fieldName") String fieldName,
        @RequestBody List<String> userNames
    ) {
        return userService.getUserByByFieldNameUsernames(fieldName, userNames);
    }

    /**
     * 根据用户id查询用户详情
     *
     * @param userIds 用户id
     * @return 用户详情
     */
    @PostMapping("/user/list")
    public List<UserDto> getUserListById(@RequestBody List<Long> userIds) {
        return userService.getUserListById(userIds);
    }

    /**
     * 根据用户id和部门id拼装currentUser
     *
     * @param userId 用户id
     * @param deptId 部门id
     * @return {@link CurrentUser}
     */
    @GetMapping("/user/{userId}/{deptId}")
    public CurrentUser findCurrentUser(@PathVariable("userId") Long userId, @PathVariable("deptId") Long deptId) {
        return userService.findCurrentUser(userId, deptId);
    }

    /**
     * 根据用户id和部门id拼装SimpleUserVO
     *
     * @param userId 用户id
     * @param deptId 部门id
     * @return {@link SimpleUserVO}
     */
    @GetMapping("/simple-user/{userId}/{deptId}")
    public SimpleUserVO findSimpleUser(@PathVariable("userId") Long userId, @PathVariable("deptId") Long deptId) {
        return userService.findSimpleUser(userId, deptId);
    }

    /**
     * 查询拥有该角色的上级部门用户列表
     *
     * @param deptId 部门id
     * @param level  上级部门类型
     * @param roleId 角色id
     * @return {@link UserDeptVO} 用户列表
     */
    @GetMapping("/role/superior/users")
    public List<UserDeptVO> getUsersByRoleAndDept(
        @RequestParam("deptId") @Nullable Long deptId,
        @RequestParam("level") @Nullable Integer level,
        @RequestParam("roleId") Long roleId) {
        return roleService.getUsersByCurrentDeptAndLevelAndRole(deptId, level, roleId);
    }

    /**
     * 获取部门下的用户
     *
     * @param code 地域码
     * @param roleId 角色
     * @param deptType 部门类型
     * @return 部门下的用户
     */
    @GetMapping("/role/code/type/users")
    public List<UserDeptVO> getUsersByAreaAndRoleIdAndType(
            @RequestParam("deptId") @Nullable String code,
            @RequestParam("roleId") Long roleId,
            @RequestParam("level") @Nullable Integer deptType) {
        return roleService.getUsersByCodeAndRoleIdAndType(code, roleId, deptType);
    }

    /**
     * 获取审批的用户
     *
     * @param dto 参数
     * @return 审批的用户
     */
    @PostMapping("/getApprovalUser")
    public List<UserDeptVO> getApprovalUser(@RequestBody ApprovalUserV2DTO dto) {
        return roleService.getApprovalUser(dto);
    }

    /**
     * 获取审批的用户
     *
     * @param dto 参数
     * @return 审批的用户
     */
    @PostMapping("/getApprovalUserTestResult")
    public Map getApprovalUserTestResult(@RequestBody ApprovalUserV2DTO dto) {
        return roleService.getApprovalUserTestResult(dto);
    }

    /**
     * 查找部门
     *
     * @param parenId parenId
     * @param type type
     * @param childType childType
     * @return Dept
     */
    @GetMapping("/getChildDept")
    public UserDeptVO getChildDept(
            @RequestParam("parenId") Long parenId,
            @RequestParam("type") @Nullable Long type,
            @RequestParam("childType") @Nullable Long childType) {
        Dept deptByParenIdAndType = roleService.findDeptByParenIdAndType(parenId, type, childType, null);
        if (Objects.isNull(deptByParenIdAndType)) {
            return new UserDeptVO();
        } else {
            UserDeptVO userDeptVO = new UserDeptVO();
            userDeptVO.setDeptId(deptByParenIdAndType.getId());
            return userDeptVO;
        }
    }

    /**
     * 通过地域加警种查找部门
     *
     * @param areaCode 地域编码
     * @param policeKind 警种
     * @return 地区下的警种
     */
    @GetMapping("/findByAreaAndPoliceKind")
    public List<DeptVO> findByAreaAndPoliceKind(@RequestParam("areaCode") String areaCode, @RequestParam("policeKind") Long policeKind) {
        return deptService.findByAreaAndPoliceKind(areaCode, policeKind);
    }

    /**
     * parenDeptInKeyPath
     *
     * @param currentDeptId parenDeptInKeyPath
     * @return return
     */
    @GetMapping("/parenDeptInKeyPath")
    public DeptDto parenDeptInKeyPath(@RequestParam("currentDeptId") Long currentDeptId) {
        return deptService.parenDeptInKeyPath(currentDeptId);
    }

    /**
     * 查询本部门拥有该角色的用户列表
     *
     * @param deptId 部门id
     * @param roleId 角色id
     * @return {@link UserDeptVO} 用户列表
     */
    @GetMapping("/role/dept/users")
    public List<UserDeptVO> getDeptUsersByRole(
        @RequestParam("deptId") @Nullable Long deptId,
        @RequestParam("roleId") Long roleId) {
        return roleService.getUsersByDeptAndRole(deptId, roleId);
    }

    /**
     * 查询本部门拥有该角色的用户列表
     *
     * @param deptId  部门id
     * @param roleIds 角色id
     * @return {@link UserDeptVO} 用户列表
     */
    @GetMapping("/roles/dept/users")
    public List<SimpleUserVO> getDeptUsersByRoles(
        @RequestParam("deptId") @Nullable Long deptId,
        @RequestParam("roleIds") String roleIds) {
        return userService.getDeptUsersByRoles(deptId, roleIds);
    }

    /**
     * 获取用户武器库
     *
     * @param userId 用户id
     * @param deptId 部门id
     * @return {@link UserDeptRelationVO}
     */
    @GetMapping("/user/armory/{userId}/{deptId}")
    public UserDeptRelationVO getUserArmory(@PathVariable("userId") Long userId,
        @PathVariable("deptId") Long deptId) {
        return userService.getUserArmory(userId, deptId);
    }

    /**
     * @param userDeptRelation 武器
     * @see <a href="http://192.168.200.192:3001/project/4974/interface/api/142162">武器库-编辑当前用户的武器库</a>
     */
    @PutMapping("/user/armory")
    void updateCurrentUserArmory(@RequestBody UserDeptRelationVO userDeptRelation) {
        userService.updateArmory(userDeptRelation);
    }

    /**
     * 通过身份证获取
     *
     * @param idNumber 身份证
     * @return 用户信息
     */
    @GetMapping("/user/{idNumber}")
    public UserDto getUserByIdCard(@PathVariable("idNumber") String idNumber) {
        return userService.getUserByIdNumber(idNumber);
    }

    /**
     * 通过身份证获取
     *
     * @param idNumbers 身份证
     * @return 用户信息
     */
    @PostMapping("/user/listByIdCards")
    public List<UserDto> getUserByIdCards(@RequestBody List<String> idNumbers) {
        return userService.getUserByIdNumbers(idNumbers);
    }

    /**
     * 根据部门和角色获取用户信息
     *
     * @param roleId 角色id
     * @param deptId 部门id
     * @return 用户信息
     */
    @GetMapping("/role-dept/{roleId}/{deptId}")
    public List<SimpleUserVO> getByRoleAndDept(@PathVariable("roleId") Long roleId,
        @PathVariable("deptId") Long deptId) {
        return userService.getByRoleAndDept(roleId, deptId);
    }

    /**
     * 根据经纬度获取所属派出所id
     *
     * @param lng  经度
     * @param lat  纬度
     * @param type 类型
     * @return 部门id
     */
    @GetMapping("/dept-district")
    public Long getDeptIdByLatAndLng(@RequestParam("lng") String lng,
        @RequestParam("lat") String lat,
        @RequestParam("type") Integer type) {
        return deptDistrictMapper.getDeptIdByLatAndLng(lng, lat, type);
    }

    /**
     * 根据经纬度获取所属派出所code
     *
     * @param lng  经度
     * @param lat  纬度
     * @param type 类型
     * @return 部门id
     */
    @GetMapping("/deptCode-district")
    public String getDeptCodeByLatAndLng(@RequestParam("lng") String lng,
                                     @RequestParam("lat") String lat,
                                     @RequestParam("type") Integer type) {
        return deptDistrictMapper.getDeptCodeByLatAndLng(lng, lat, type);
    }

    /**
     * 创建用户 http://192.168.200.192:3001/project/4974/interface/api/139031
     *
     * @param user 用户信息
     */
    @PostMapping("/user")
    public void createUser(@Validated(UserVO.Create.class) @RequestBody UserVO user) {
        userService.createUser(user);
    }

    /**
     * 获取当前用户检索权限
     *
     * @param userId 用户id
     * @param deptId 部门id
     * @return 检索权限
     */
    @GetMapping("/data/search/{userId}/{deptId}")
    KeyValueTypeVO[] getUserSearch(@PathVariable("userId") Long userId, @PathVariable("deptId") Long deptId) {
        return dataPermissionService.getUserSearch(userId, deptId);
    }

    /**
     * 查询用户详情
     *
     * @return 用户详情
     */
    @GetMapping("/user/all")
    public List<UserDto> getUserList() {
        return userService.getUserList();
    }

    /**
     * 查询拥有某个模块对应操做的人
     *
     * @param moduleName 模块名
     * @param operation  操作名
     * @return 权限列表
     */
    @GetMapping("/permission/roles")
    public List<Long> rolesWithPermission(@RequestParam("moduleName") String moduleName,
        @RequestParam("operation") @Nullable String operation) {
        return roleService.rolesWithPermission(moduleName, operation);
    }

    /**
     * 获取角色下所有人员信息
     *
     * @param roleId 角色Id
     * @return {@link UserDeptVO} 人员
     */
    @GetMapping("/{roleId}/user/all-list")
    public List<SimpleUserVO> getUserListByRoleId(@PathVariable("roleId") Long roleId) {
        return roleService.getUserDeptListByRoleId(roleId);
    }

    /**
     * 获取访问ip
     *
     * @param request request
     * @return ip
     */
    @GetMapping("getIpInfo")
    @ApiOperation(value = "根据身份证号获取人员详情", notes = "根据身份证号获取人员详情")
    public String getIpInfo(HttpServletRequest request) {
        return IpUtil.getRemoteAddress(request);
    }

    /**
     * 根据部门编码列表获取当级部门所有的用户列表
     *
     * @param searchUserDTO dto 参数
     * @return {@link List}<{@link SimpleUserVO}> userList
     */
    @PostMapping("/user/getByDeptIdList")
    public List<SimpleUserVO> getUserListByDeptIdList(@RequestBody SearchUserDTO searchUserDTO) {
        return userService.getUserListByDeptIdList(searchUserDTO);
    }

    /**
     * 加密历史数据
     *
     */
    @GetMapping("/user/encryptHistory")
    public void encryptHistory(){
        userService.encryptHistory();
    }

    /**
     * 完整性加密历史数据
     *
     */
    @GetMapping("/user/encryptHistoryMac")
    public void encryptHistoryMac(){
        userService.encryptHistoryMac();
    }

    /**
     * 完整性加密历史用户角色数据
     *
     */
    @GetMapping("/user/encryptHistoryUserRoleMac")
    public void encryptHistoryUserRoleMac(){
        userService.encryptHistoryUserRoleMac();
    }

    /**
     * 查询该id下的所有部门和用户
     *
     * @param deptId 部门id
     * @param username 用户名
     * @param containChild 是否包含子部门
     * @param postCode 岗位代码
     * @param districtCode 地域代码
     * @return {@link List }<{@link DeptVO }>
     * <AUTHOR>
     * @since 1.0.0
     * @since 2024-11-21 11:34:34
     */
    @GetMapping("/user/findDeptAndUserTree/{deptId}")
    public DeptVO findDeptAndUserTree(@PathVariable("deptId") Long deptId, String username, Boolean containChild, Integer postCode, String districtCode){
        return userService.findDeptAndUserTree(deptId, username, containChild, postCode, districtCode);
    }

    /**
     * 根据区域编码获取区域下的派出所信息
     *
     * @param code code
     * @return {@link List}<{@link DeptDistrict}>
     */
    @GetMapping("/getDeptDistrictByCode")
    public List<DeptDistrict> getDeptDistrictByCode(@RequestParam("code") String code) {
        return deptDistrictMapper.getDeptDistrictByCode(code);
    }

    /**
     * 查询该id下的所有部门和用户
     *
     * @param deptId 部门id
     * @param username 用户名
     * @param containChild 是否包含子部门
     * @param postCode 岗位代码
     * @param districtCode 地域代码
     * @return {@link List }<{@link DeptVO }>
     * <AUTHOR>
     * @since 1.0.0
     * @since 2024-11-21 11:34:34
     */
    @GetMapping("/user/findDeptAndUserTreeV2/{deptId}")
    public DeptVO findDeptAndUserTreeV2(@PathVariable("deptId") Long deptId, String username, Boolean containChild, Integer postCode, String districtCode){
        return userService.findDeptAndUserTreeV2(deptId, username, containChild, postCode, districtCode);
    }

    /**
     * 查询该id下的所有部门和用户
     *
     * @param username     用户名
     * @param containChild 是否包含子部门
     * @param postCode     岗位代码
     * @param districtCode 地域代码
     * @return {@link List }<{@link DeptVO }>
     * <AUTHOR>
     * @since 1.0.0
     * @since 2024-11-21 11:34:34
     */
    @GetMapping("/user/findDeptAndUserTreeV3")
    public DeptVO findDeptAndUserTreeV3(String username, Boolean containChild, Integer postCode, String districtCode) {
        return userService.findDeptAndUserTree(null, username, containChild, postCode, districtCode);
    }

    /**
     * 同步政务微信用户到云哨
     */
    @GetMapping("/syncGovWxUserToYs")
    public void syncGovWxUserToYs() {
        govWxUserInfoService.syncGovWxUserToYs();
    }
}