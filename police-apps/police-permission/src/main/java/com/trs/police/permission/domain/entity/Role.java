package com.trs.police.permission.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import com.trs.police.common.core.vo.IdNameVO;
import com.trs.police.permission.domain.vo.DashboardConfigVO;
import com.trs.police.permission.enums.RoleTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 角色表
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "t_role",autoResultMap = true)
@AllArgsConstructor
@NoArgsConstructor
public class Role extends AbstractBaseEntity {

    private static final long serialVersionUID = 7086253304131771864L;

    /**
     * 角色名称
     */
    @TableField("name")
    private String name;

    /**
     * 角类型 permission_role_type  1: 系统角色 2: 自定义角色 3: 权限中心泸州
     */
    @TableField("type")
    private RoleTypeEnum type;

    /**
     * 操作权限
     */
    @TableField(value = "operation", typeHandler = JacksonTypeHandler.class)
    private List<String> operation;

    /**
     * 功能模块权限
     */
    @TableField(value = "module", typeHandler = JacksonTypeHandler.class)
    private List<String> module;

    /**
     * 角色描述
     */
    @TableField("description")
    private String description;

    /**
     * 工作台配置
     */
    @TableField(value = "dashboard_config", typeHandler = JacksonTypeHandler.class)
    private DashboardConfigVO dashboardConfig;

    /**
     * 排序值
     */
    @TableField("sort_value")
    private Integer sortValue;

    /**
     * 转IdNameVo
     *
     * @return IdNameVO
     */
    public IdNameVO toIdNameVo(){
        return new IdNameVO(this.getId(),this.getName());
    }
}

