package com.trs.police.permission.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 数据权限类别
 *
 * <AUTHOR>
 */

@AllArgsConstructor
public enum DataPermissionTypeEnum {
    /**
     * enum
     */
    SYSTEM(1, "系统数据"),
    CUSTOM(2, "自定义数据");

    @Getter
    @EnumValue
    @JsonValue
    private final Integer code;

    @Getter
    private final String name;

    /**
     * code转换枚举
     *
     * @param code 编码
     * @return 枚举
     */
    @JsonCreator
    public static DataPermissionTypeEnum codeOf(Integer code) {
        if (Objects.nonNull(code)) {
            for (DataPermissionTypeEnum typeEnum : DataPermissionTypeEnum.values()) {
                if (code.equals(typeEnum.getCode())) {
                    return typeEnum;
                }
            }
        }
        return null;
    }
}
