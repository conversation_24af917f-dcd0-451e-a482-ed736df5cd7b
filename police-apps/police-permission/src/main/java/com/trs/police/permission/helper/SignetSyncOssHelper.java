package com.trs.police.permission.helper;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.S3Object;
import com.trs.police.common.core.excpetion.ServiceException;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import com.trs.police.common.openfeign.starter.service.OssService;
import com.trs.police.permission.properties.SignetSyncProperties;
import com.trs.sdk.client.IOssClient;
import com.trs.sdk.factory.OssFactory;
import constant.Aws3ConfigFieldConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * @author: dingkeyu
 * @date: 2024/09/26
 * @description: 签章图片同步OSS helper
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnBean(value = SignetSyncProperties.class)
public class SignetSyncOssHelper {

    private final OssService ossService;

    private final SignetSyncProperties properties;

    private IOssClient ossClient;

    private AmazonS3 s3Client;

    private AmazonS3 getS3Client() {
        if (Objects.isNull(s3Client)) {
            synchronized (this) {
                if (Objects.isNull(s3Client)) {
                    BasicAWSCredentials credentialsProvider = new BasicAWSCredentials(properties.getAccessKeyId(), properties.getSecretAccessKey());
                    return AmazonS3ClientBuilder.standard()
                            .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(properties.getHost(), null))
                            .withPathStyleAccessEnabled(true)
                            .withClientConfiguration(new ClientConfiguration().withSignerOverride("AWSS3V4SignerType"))
                            .withCredentials(new AWSStaticCredentialsProvider(credentialsProvider))
                            .build();
                }
            }
        }
        return s3Client;
    }

    private IOssClient getOssClient() {
        if (Objects.isNull(ossClient)) {
            synchronized (this) {
                if (Objects.isNull(ossClient)) {
                    Map<String, String> config = new HashMap<>();
                    config.put("trs.oss.sdk.name", "Aws3ClientFactory");
                    config.put(Aws3ConfigFieldConstant.OSS_HOST, properties.getHost());
                    config.put(Aws3ConfigFieldConstant.OSS_PROTOCOL, "http");
                    config.put(Aws3ConfigFieldConstant.OSS_ACCESSKEY, properties.getAccessKeyId());
                    config.put(Aws3ConfigFieldConstant.OSS_SECRETKEY, properties.getSecretAccessKey());
                    this.ossClient = OssFactory.getOssClient(config);
                }
            }
        }
        return this.ossClient;
    }

    /**
     * 从yq获取签章图片并上传到本地oss
     *
     * @param fileNames 文件名列表
     * @return 结果
     * @throws ServiceException 相关异常
     */
    public List<FileInfoVO> getFilesFromThird(List<String> fileNames) throws ServiceException {
        try {
            if (CollectionUtils.isEmpty(fileNames)) {
                return new ArrayList<>(0);
            }
            List<MultipartFile> files = new ArrayList<>();
            for (String fileName : fileNames) {
                final MultipartFile multipartFile = downloadFile(fileName);
                files.add(multipartFile);
            }
            return ossService.uploadAttachments(files);
        } catch (IOException e) {
            log.error("签章图片同步-从对象存储获取文件信息失败！", e);
            throw new ServiceException("从对象存储获取文件信息失败！");
        }
    }

    /**
     * 从yq获取文件并上传到本地oss
     *
     * @param fileName 文件名
     * @return 结果
     * @throws IOException 相关异常
     */
    public FileInfoVO getFileFromThird(String fileName) throws IOException {
        final MultipartFile multipartFile = downloadFile(fileName);
        return ossService.upload(multipartFile, false);
    }

    private MultipartFile downloadFile(String fileName) throws IOException {
        log.info("签章图片同步-待下载文件名称为【{}】", fileName);
        String finalUrl;
        if (fileName.startsWith("/")) {
            finalUrl = fileName.replaceFirst("/battle/", "");
        } else {
            finalUrl = fileName.replaceFirst("battle/", "");
        }
        log.info("签章图片同步-从yq获取文件名称为【{}】", finalUrl);
        final S3Object s3Object = getS3Client().getObject("battle", finalUrl);
        return downloadFileAsMultipartFile(finalUrl, s3Object.getObjectContent());
    }

    private static MultipartFile downloadFileAsMultipartFile(String fileURL, InputStream upload) throws IOException {
        try (InputStream inputStream = new BufferedInputStream(upload);
             ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer, 0, 1024)) != -1) {
                byteArrayOutputStream.write(buffer, 0, bytesRead);
            }
            byte[] fileBytes = byteArrayOutputStream.toByteArray();
            String fileName = fileURL.substring(fileURL.lastIndexOf('/') + 1);
            return new MockMultipartFile(fileName, fileName, "application/octet-stream", fileBytes);
        }
    }

}
