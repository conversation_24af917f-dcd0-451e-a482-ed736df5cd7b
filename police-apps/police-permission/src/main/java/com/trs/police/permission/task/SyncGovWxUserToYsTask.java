package com.trs.police.permission.task;


import com.trs.police.permission.service.GovWxUserInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 同步政务微信用户到云哨
 */

@Slf4j
@Component
@ConditionalOnProperty(name = "com.trs.schedule.govWxUserSync.task.key", havingValue = "true")
public class SyncGovWxUserToYsTask {

    @Autowired
    private GovWxUserInfoService govWxUserInfoService;

    /**
     * 同步政务微信用户到云哨
     */
    @Scheduled(cron = "${com.trs.schedule.govWxUserSync.task.cron:0 0 0 1 */3 ?}")
    public void syncGovWxUserToYs() {
        try {
            log.info("开始同步政务微信用户到云哨");
            govWxUserInfoService.syncGovWxUserToYs();
            log.info("成功同步政务微信用户到云哨");
        }catch (Exception e) {
            log.error("同步政务微信用户到云哨失败：{}", e.getMessage());
        }
    }
}
