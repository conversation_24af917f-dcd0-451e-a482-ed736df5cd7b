TID: N/A [33m2025-05-16 15:27:11[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.a.cloud.nacos.client.NacosPropertySourceBuilder[0;39m - [35mIgnore the empty nacos configuration and get it based on dataId[comparison-api] & group[DEFAULT_GROUP][0;39m 
TID: N/A [33m2025-05-16 15:27:16[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.SensingDevice".[0;39m 
TID: N/A [33m2025-05-16 15:27:16[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.injector.AbstractMethod[0;39m - [35m[com.trs.police.comparison.api.dao.SubscribeInfoMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert][0;39m 
TID: N/A [33m2025-05-16 15:27:22[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.ReportTimeYWZJIDRelation".[0;39m 
TID: N/A [33m2025-05-16 15:28:25[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Start destroying Publisher[0;39m 
TID: N/A [33m2025-05-16 15:28:25[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Destruction of the end[0;39m 
TID: N/A [33m2025-05-16 15:28:25[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Start destroying common HttpClient[0;39m 
TID: N/A [33m2025-05-16 15:28:25[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Destruction of the end[0;39m 
TID: N/A [33m2025-05-16 15:28:31[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Start destroying NacosRestTemplate[0;39m 
TID: N/A [33m2025-05-16 15:28:31[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Destruction of the end[0;39m 
TID: N/A [33m2025-05-16 15:30:07[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.a.cloud.nacos.client.NacosPropertySourceBuilder[0;39m - [35mIgnore the empty nacos configuration and get it based on dataId[comparison-api] & group[DEFAULT_GROUP][0;39m 
TID: N/A [33m2025-05-16 15:30:10[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.SensingDevice".[0;39m 
TID: N/A [33m2025-05-16 15:30:10[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.injector.AbstractMethod[0;39m - [35m[com.trs.police.comparison.api.dao.SubscribeInfoMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert][0;39m 
TID: N/A [33m2025-05-16 15:30:16[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.ReportTimeYWZJIDRelation".[0;39m 
TID: N/A [33m2025-05-16 15:31:19[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Start destroying Publisher[0;39m 
TID: N/A [33m2025-05-16 15:31:19[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Start destroying common HttpClient[0;39m 
TID: N/A [33m2025-05-16 15:31:19[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Destruction of the end[0;39m 
TID: N/A [33m2025-05-16 15:31:19[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Destruction of the end[0;39m 
TID: N/A [33m2025-05-16 15:31:25[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Start destroying NacosRestTemplate[0;39m 
TID: N/A [33m2025-05-16 15:31:25[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Destruction of the end[0;39m 
TID: N/A [33m2025-05-16 15:31:38[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.a.cloud.nacos.client.NacosPropertySourceBuilder[0;39m - [35mIgnore the empty nacos configuration and get it based on dataId[comparison-api] & group[DEFAULT_GROUP][0;39m 
TID: N/A [33m2025-05-16 15:31:43[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.SensingDevice".[0;39m 
TID: N/A [33m2025-05-16 15:31:43[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.injector.AbstractMethod[0;39m - [35m[com.trs.police.comparison.api.dao.SubscribeInfoMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert][0;39m 
TID: N/A [33m2025-05-16 15:31:49[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.ReportTimeYWZJIDRelation".[0;39m 
TID: N/A [33m2025-05-16 15:33:22[0;39m [31m[HikariPool-1 housekeeper][0;39m [31mWARN [0;39m [36mcom.zaxxer.hikari.pool.HikariPool[0;39m - [35mHikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m40s769ms606µs800ns).[0;39m 
TID: N/A [33m2025-05-16 15:34:23[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Start destroying Publisher[0;39m 
TID: N/A [33m2025-05-16 15:34:23[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Destruction of the end[0;39m 
TID: N/A [33m2025-05-16 15:34:23[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Start destroying common HttpClient[0;39m 
TID: N/A [33m2025-05-16 15:34:23[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Destruction of the end[0;39m 
TID: N/A [33m2025-05-16 15:34:32[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Start destroying NacosRestTemplate[0;39m 
TID: N/A [33m2025-05-16 15:34:32[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Destruction of the end[0;39m 
TID: N/A [33m2025-05-16 15:34:49[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.a.cloud.nacos.client.NacosPropertySourceBuilder[0;39m - [35mIgnore the empty nacos configuration and get it based on dataId[comparison-api] & group[DEFAULT_GROUP][0;39m 
TID: N/A [33m2025-05-16 15:34:53[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.SensingDevice".[0;39m 
TID: N/A [33m2025-05-16 15:34:53[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.injector.AbstractMethod[0;39m - [35m[com.trs.police.comparison.api.dao.SubscribeInfoMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert][0;39m 
TID: N/A [33m2025-05-16 15:34:58[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.ReportTimeYWZJIDRelation".[0;39m 
TID: N/A [33m2025-05-16 15:36:02[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Start destroying Publisher[0;39m 
TID: N/A [33m2025-05-16 15:36:02[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Destruction of the end[0;39m 
TID: N/A [33m2025-05-16 15:36:02[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Start destroying common HttpClient[0;39m 
TID: N/A [33m2025-05-16 15:36:02[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Destruction of the end[0;39m 
TID: N/A [33m2025-05-16 15:36:08[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Start destroying NacosRestTemplate[0;39m 
TID: N/A [33m2025-05-16 15:36:08[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Destruction of the end[0;39m 
TID: N/A [33m2025-05-16 15:36:27[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.a.cloud.nacos.client.NacosPropertySourceBuilder[0;39m - [35mIgnore the empty nacos configuration and get it based on dataId[comparison-api] & group[DEFAULT_GROUP][0;39m 
TID: N/A [33m2025-05-16 15:36:32[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.SensingDevice".[0;39m 
TID: N/A [33m2025-05-16 15:36:32[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.injector.AbstractMethod[0;39m - [35m[com.trs.police.comparison.api.dao.SubscribeInfoMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert][0;39m 
TID: N/A [33m2025-05-16 15:36:39[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.ReportTimeYWZJIDRelation".[0;39m 
TID: N/A [33m2025-05-16 15:37:42[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Start destroying Publisher[0;39m 
TID: N/A [33m2025-05-16 15:37:42[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Start destroying common HttpClient[0;39m 
TID: N/A [33m2025-05-16 15:37:42[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Destruction of the end[0;39m 
TID: N/A [33m2025-05-16 15:37:42[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Destruction of the end[0;39m 
TID: N/A [33m2025-05-16 15:37:48[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Start destroying NacosRestTemplate[0;39m 
TID: N/A [33m2025-05-16 15:37:48[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Destruction of the end[0;39m 
