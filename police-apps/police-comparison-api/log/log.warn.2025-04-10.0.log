TID: N/A [33m2025-04-10 10:06:17[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.a.cloud.nacos.client.NacosPropertySourceBuilder[0;39m - [35mIgnore the empty nacos configuration and get it based on dataId[comparison-api] & group[DEFAULT_GROUP][0;39m 
TID: N/A [33m2025-04-10 10:06:22[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.SensingDevice".[0;39m 
TID: N/A [33m2025-04-10 10:06:22[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.injector.AbstractMethod[0;39m - [35m[com.trs.police.comparison.api.dao.SubscribeInfoMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert][0;39m 
TID: N/A [33m2025-04-10 10:06:28[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.ReportTimeYWZJIDRelation".[0;39m 
TID: N/A [33m2025-04-10 10:06:29[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Start destroying Publisher[0;39m 
TID: N/A [33m2025-04-10 10:06:29[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Start destroying common HttpClient[0;39m 
TID: N/A [33m2025-04-10 10:06:29[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:06:29[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:06:36[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Start destroying NacosRestTemplate[0;39m 
TID: N/A [33m2025-04-10 10:06:36[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:07:39[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.a.cloud.nacos.client.NacosPropertySourceBuilder[0;39m - [35mIgnore the empty nacos configuration and get it based on dataId[comparison-api] & group[DEFAULT_GROUP][0;39m 
TID: N/A [33m2025-04-10 10:07:46[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.SensingDevice".[0;39m 
TID: N/A [33m2025-04-10 10:07:46[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.injector.AbstractMethod[0;39m - [35m[com.trs.police.comparison.api.dao.SubscribeInfoMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert][0;39m 
TID: N/A [33m2025-04-10 10:07:55[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.ReportTimeYWZJIDRelation".[0;39m 
TID: N/A [33m2025-04-10 10:08:30[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Start destroying common HttpClient[0;39m 
TID: N/A [33m2025-04-10 10:08:30[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Start destroying Publisher[0;39m 
TID: N/A [33m2025-04-10 10:08:30[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:08:30[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:08:36[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Start destroying NacosRestTemplate[0;39m 
TID: N/A [33m2025-04-10 10:08:36[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:18:49[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.a.cloud.nacos.client.NacosPropertySourceBuilder[0;39m - [35mIgnore the empty nacos configuration and get it based on dataId[comparison-api] & group[DEFAULT_GROUP][0;39m 
TID: N/A [33m2025-04-10 10:18:54[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.SensingDevice".[0;39m 
TID: N/A [33m2025-04-10 10:18:54[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.injector.AbstractMethod[0;39m - [35m[com.trs.police.comparison.api.dao.SubscribeInfoMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert][0;39m 
TID: N/A [33m2025-04-10 10:19:00[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.ReportTimeYWZJIDRelation".[0;39m 
TID: N/A [33m2025-04-10 10:19:35[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Start destroying common HttpClient[0;39m 
TID: N/A [33m2025-04-10 10:19:35[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Start destroying Publisher[0;39m 
TID: N/A [33m2025-04-10 10:19:35[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:19:35[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:19:41[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Start destroying NacosRestTemplate[0;39m 
TID: N/A [33m2025-04-10 10:19:41[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:21:22[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.a.cloud.nacos.client.NacosPropertySourceBuilder[0;39m - [35mIgnore the empty nacos configuration and get it based on dataId[comparison-api] & group[DEFAULT_GROUP][0;39m 
TID: N/A [33m2025-04-10 10:21:26[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.SensingDevice".[0;39m 
TID: N/A [33m2025-04-10 10:21:27[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.injector.AbstractMethod[0;39m - [35m[com.trs.police.comparison.api.dao.SubscribeInfoMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert][0;39m 
TID: N/A [33m2025-04-10 10:21:32[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.ReportTimeYWZJIDRelation".[0;39m 
TID: N/A [33m2025-04-10 10:22:07[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Start destroying Publisher[0;39m 
TID: N/A [33m2025-04-10 10:22:07[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:22:07[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Start destroying common HttpClient[0;39m 
TID: N/A [33m2025-04-10 10:22:07[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:22:13[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Start destroying NacosRestTemplate[0;39m 
TID: N/A [33m2025-04-10 10:22:13[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:24:50[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.a.cloud.nacos.client.NacosPropertySourceBuilder[0;39m - [35mIgnore the empty nacos configuration and get it based on dataId[comparison-api] & group[DEFAULT_GROUP][0;39m 
TID: N/A [33m2025-04-10 10:24:54[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.SensingDevice".[0;39m 
TID: N/A [33m2025-04-10 10:24:54[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.injector.AbstractMethod[0;39m - [35m[com.trs.police.comparison.api.dao.SubscribeInfoMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert][0;39m 
TID: N/A [33m2025-04-10 10:24:59[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.ReportTimeYWZJIDRelation".[0;39m 
TID: N/A [33m2025-04-10 10:25:50[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Start destroying common HttpClient[0;39m 
TID: N/A [33m2025-04-10 10:25:50[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Start destroying Publisher[0;39m 
TID: N/A [33m2025-04-10 10:25:50[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:25:50[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:25:53[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Start destroying NacosRestTemplate[0;39m 
TID: N/A [33m2025-04-10 10:25:53[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:28:12[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.a.cloud.nacos.client.NacosPropertySourceBuilder[0;39m - [35mIgnore the empty nacos configuration and get it based on dataId[comparison-api] & group[DEFAULT_GROUP][0;39m 
TID: N/A [33m2025-04-10 10:28:16[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.SensingDevice".[0;39m 
TID: N/A [33m2025-04-10 10:28:16[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.injector.AbstractMethod[0;39m - [35m[com.trs.police.comparison.api.dao.SubscribeInfoMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert][0;39m 
TID: N/A [33m2025-04-10 10:28:22[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.ReportTimeYWZJIDRelation".[0;39m 
TID: N/A [33m2025-04-10 10:30:06[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Start destroying Publisher[0;39m 
TID: N/A [33m2025-04-10 10:30:06[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Start destroying common HttpClient[0;39m 
TID: N/A [33m2025-04-10 10:30:06[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:30:06[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:30:11[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Start destroying NacosRestTemplate[0;39m 
TID: N/A [33m2025-04-10 10:30:11[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:31:10[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.a.cloud.nacos.client.NacosPropertySourceBuilder[0;39m - [35mIgnore the empty nacos configuration and get it based on dataId[comparison-api] & group[DEFAULT_GROUP][0;39m 
TID: N/A [33m2025-04-10 10:31:14[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.SensingDevice".[0;39m 
TID: N/A [33m2025-04-10 10:31:14[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.injector.AbstractMethod[0;39m - [35m[com.trs.police.comparison.api.dao.SubscribeInfoMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert][0;39m 
TID: N/A [33m2025-04-10 10:31:20[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.ReportTimeYWZJIDRelation".[0;39m 
TID: N/A [33m2025-04-10 10:32:25[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Start destroying Publisher[0;39m 
TID: N/A [33m2025-04-10 10:32:25[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:32:25[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Start destroying common HttpClient[0;39m 
TID: N/A [33m2025-04-10 10:32:28[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:33:03[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Start destroying NacosRestTemplate[0;39m 
TID: N/A [33m2025-04-10 10:33:03[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:36:10[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.a.cloud.nacos.client.NacosPropertySourceBuilder[0;39m - [35mIgnore the empty nacos configuration and get it based on dataId[comparison-api] & group[DEFAULT_GROUP][0;39m 
TID: N/A [33m2025-04-10 10:36:14[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.SensingDevice".[0;39m 
TID: N/A [33m2025-04-10 10:36:14[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.injector.AbstractMethod[0;39m - [35m[com.trs.police.comparison.api.dao.SubscribeInfoMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert][0;39m 
TID: N/A [33m2025-04-10 10:36:19[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.ReportTimeYWZJIDRelation".[0;39m 
TID: N/A [33m2025-04-10 10:36:57[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Start destroying common HttpClient[0;39m 
TID: N/A [33m2025-04-10 10:36:57[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Start destroying Publisher[0;39m 
TID: N/A [33m2025-04-10 10:36:57[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:36:57[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:37:03[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Start destroying NacosRestTemplate[0;39m 
TID: N/A [33m2025-04-10 10:37:03[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:39:36[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.a.cloud.nacos.client.NacosPropertySourceBuilder[0;39m - [35mIgnore the empty nacos configuration and get it based on dataId[comparison-api] & group[DEFAULT_GROUP][0;39m 
TID: N/A [33m2025-04-10 10:39:40[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.SensingDevice".[0;39m 
TID: N/A [33m2025-04-10 10:39:40[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.injector.AbstractMethod[0;39m - [35m[com.trs.police.comparison.api.dao.SubscribeInfoMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert][0;39m 
TID: N/A [33m2025-04-10 10:39:46[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.ReportTimeYWZJIDRelation".[0;39m 
TID: N/A [33m2025-04-10 10:40:53[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Start destroying Publisher[0;39m 
TID: N/A [33m2025-04-10 10:40:53[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Start destroying common HttpClient[0;39m 
TID: N/A [33m2025-04-10 10:40:53[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:40:53[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:40:56[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Start destroying NacosRestTemplate[0;39m 
TID: N/A [33m2025-04-10 10:40:56[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:42:06[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.a.cloud.nacos.client.NacosPropertySourceBuilder[0;39m - [35mIgnore the empty nacos configuration and get it based on dataId[comparison-api] & group[DEFAULT_GROUP][0;39m 
TID: N/A [33m2025-04-10 10:42:10[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.SensingDevice".[0;39m 
TID: N/A [33m2025-04-10 10:42:10[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.injector.AbstractMethod[0;39m - [35m[com.trs.police.comparison.api.dao.SubscribeInfoMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert][0;39m 
TID: N/A [33m2025-04-10 10:42:16[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.ReportTimeYWZJIDRelation".[0;39m 
TID: N/A [33m2025-04-10 10:43:21[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Start destroying Publisher[0;39m 
TID: N/A [33m2025-04-10 10:43:21[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Start destroying common HttpClient[0;39m 
TID: N/A [33m2025-04-10 10:43:21[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:43:21[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:43:24[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Start destroying NacosRestTemplate[0;39m 
TID: N/A [33m2025-04-10 10:43:24[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:45:41[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.a.cloud.nacos.client.NacosPropertySourceBuilder[0;39m - [35mIgnore the empty nacos configuration and get it based on dataId[comparison-api] & group[DEFAULT_GROUP][0;39m 
TID: N/A [33m2025-04-10 10:45:45[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.SensingDevice".[0;39m 
TID: N/A [33m2025-04-10 10:45:45[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.injector.AbstractMethod[0;39m - [35m[com.trs.police.comparison.api.dao.SubscribeInfoMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert][0;39m 
TID: N/A [33m2025-04-10 10:45:51[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.ReportTimeYWZJIDRelation".[0;39m 
TID: N/A [33m2025-04-10 10:46:52[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Start destroying Publisher[0;39m 
TID: N/A [33m2025-04-10 10:46:52[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Start destroying common HttpClient[0;39m 
TID: N/A [33m2025-04-10 10:46:52[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:46:52[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:46:58[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Start destroying NacosRestTemplate[0;39m 
TID: N/A [33m2025-04-10 10:46:58[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:47:12[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.a.cloud.nacos.client.NacosPropertySourceBuilder[0;39m - [35mIgnore the empty nacos configuration and get it based on dataId[comparison-api] & group[DEFAULT_GROUP][0;39m 
TID: N/A [33m2025-04-10 10:47:18[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.SensingDevice".[0;39m 
TID: N/A [33m2025-04-10 10:47:18[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.injector.AbstractMethod[0;39m - [35m[com.trs.police.comparison.api.dao.SubscribeInfoMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert][0;39m 
TID: N/A [33m2025-04-10 10:47:24[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.ReportTimeYWZJIDRelation".[0;39m 
TID: N/A [33m2025-04-10 10:48:32[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Start destroying Publisher[0;39m 
TID: N/A [33m2025-04-10 10:48:32[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Start destroying common HttpClient[0;39m 
TID: N/A [33m2025-04-10 10:48:32[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:48:32[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:48:38[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Start destroying NacosRestTemplate[0;39m 
TID: N/A [33m2025-04-10 10:48:38[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:49:19[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.a.cloud.nacos.client.NacosPropertySourceBuilder[0;39m - [35mIgnore the empty nacos configuration and get it based on dataId[comparison-api] & group[DEFAULT_GROUP][0;39m 
TID: N/A [33m2025-04-10 10:49:23[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.SensingDevice".[0;39m 
TID: N/A [33m2025-04-10 10:49:23[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.injector.AbstractMethod[0;39m - [35m[com.trs.police.comparison.api.dao.SubscribeInfoMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert][0;39m 
TID: N/A [33m2025-04-10 10:49:28[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.ReportTimeYWZJIDRelation".[0;39m 
TID: N/A [33m2025-04-10 10:50:29[0;39m [31m[push-third-part-subscribe-scheduled-task-3][0;39m [31mWARN [0;39m [36mc.t.p.c.api.schedule.Push2ThirdPartSchedule[0;39m - [35m未找到当前 com.trs.police.comparison.api.spi.third.ZgDaHuaSubscribeFaceServiceImpl 详情[0;39m 
TID: N/A [33m2025-04-10 10:50:32[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Start destroying common HttpClient[0;39m 
TID: N/A [33m2025-04-10 10:50:32[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Start destroying Publisher[0;39m 
TID: N/A [33m2025-04-10 10:50:32[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:50:32[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 10:50:35[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Start destroying NacosRestTemplate[0;39m 
TID: N/A [33m2025-04-10 10:50:35[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 15:30:48[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.a.cloud.nacos.client.NacosPropertySourceBuilder[0;39m - [35mIgnore the empty nacos configuration and get it based on dataId[comparison-api] & group[DEFAULT_GROUP][0;39m 
TID: N/A [33m2025-04-10 15:30:52[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.SensingDevice".[0;39m 
TID: N/A [33m2025-04-10 15:30:52[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.injector.AbstractMethod[0;39m - [35m[com.trs.police.comparison.api.dao.SubscribeInfoMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert][0;39m 
TID: N/A [33m2025-04-10 15:30:57[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.ReportTimeYWZJIDRelation".[0;39m 
TID: N/A [33m2025-04-10 15:31:39[0;39m [31m[HikariPool-1 housekeeper][0;39m [31mWARN [0;39m [36mcom.zaxxer.hikari.pool.HikariPool[0;39m - [35mHikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=48s441ms664µs400ns).[0;39m 
TID: N/A [33m2025-04-10 15:31:39[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Start destroying common HttpClient[0;39m 
TID: N/A [33m2025-04-10 15:31:39[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Start destroying Publisher[0;39m 
TID: N/A [33m2025-04-10 15:31:39[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 15:31:39[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 15:31:45[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Start destroying NacosRestTemplate[0;39m 
TID: N/A [33m2025-04-10 15:31:45[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 15:33:02[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.a.cloud.nacos.client.NacosPropertySourceBuilder[0;39m - [35mIgnore the empty nacos configuration and get it based on dataId[comparison-api] & group[DEFAULT_GROUP][0;39m 
TID: N/A [33m2025-04-10 15:33:06[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.SensingDevice".[0;39m 
TID: N/A [33m2025-04-10 15:33:06[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.injector.AbstractMethod[0;39m - [35m[com.trs.police.comparison.api.dao.SubscribeInfoMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert][0;39m 
TID: N/A [33m2025-04-10 15:33:11[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.ReportTimeYWZJIDRelation".[0;39m 
TID: N/A [33m2025-04-10 15:33:12[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Start destroying Publisher[0;39m 
TID: N/A [33m2025-04-10 15:33:12[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 15:33:12[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Start destroying common HttpClient[0;39m 
TID: N/A [33m2025-04-10 15:33:12[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 15:33:18[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Start destroying NacosRestTemplate[0;39m 
TID: N/A [33m2025-04-10 15:33:18[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 15:42:37[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.a.cloud.nacos.client.NacosPropertySourceBuilder[0;39m - [35mIgnore the empty nacos configuration and get it based on dataId[comparison-api] & group[DEFAULT_GROUP][0;39m 
TID: N/A [33m2025-04-10 15:42:40[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.SensingDevice".[0;39m 
TID: N/A [33m2025-04-10 15:42:40[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.injector.AbstractMethod[0;39m - [35m[com.trs.police.comparison.api.dao.SubscribeInfoMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert][0;39m 
TID: N/A [33m2025-04-10 15:42:45[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.ReportTimeYWZJIDRelation".[0;39m 
TID: N/A [33m2025-04-10 15:43:34[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.a.cloud.nacos.client.NacosPropertySourceBuilder[0;39m - [35mIgnore the empty nacos configuration and get it based on dataId[comparison-api] & group[DEFAULT_GROUP][0;39m 
TID: N/A [33m2025-04-10 15:43:38[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.SensingDevice".[0;39m 
TID: N/A [33m2025-04-10 15:43:38[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.injector.AbstractMethod[0;39m - [35m[com.trs.police.comparison.api.dao.SubscribeInfoMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert][0;39m 
TID: N/A [33m2025-04-10 15:43:43[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.ReportTimeYWZJIDRelation".[0;39m 
TID: N/A [33m2025-04-10 15:43:45[0;39m [31m[push-third-part-subscribe-scheduled-task-1][0;39m [31mWARN [0;39m [36mc.t.p.c.api.schedule.Push2ThirdPartSchedule[0;39m - [35m未找到当前 com.trs.police.comparison.api.spi.third.ZgDaHuaSubscribeFaceServiceImpl 详情[0;39m 
TID: N/A [33m2025-04-10 15:43:47[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Start destroying common HttpClient[0;39m 
TID: N/A [33m2025-04-10 15:43:47[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 15:43:47[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Start destroying Publisher[0;39m 
TID: N/A [33m2025-04-10 15:43:47[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 15:43:53[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Start destroying NacosRestTemplate[0;39m 
TID: N/A [33m2025-04-10 15:43:53[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 15:44:45[0;39m [31m[push-third-part-subscribe-scheduled-task-1][0;39m [31mWARN [0;39m [36mc.t.p.c.api.schedule.Push2ThirdPartSchedule[0;39m - [35m未找到当前 com.trs.police.comparison.api.spi.third.ZgDaHuaSubscribeFaceServiceImpl 详情[0;39m 
TID: N/A [33m2025-04-10 15:44:45[0;39m [31m[push-third-part-subscribe-scheduled-task-1][0;39m [31mWARN [0;39m [36mc.t.p.c.api.schedule.Push2ThirdPartSchedule[0;39m - [35m未找到当前 com.trs.police.comparison.api.spi.third.zgdahua.ZgDaHuaSubscribeVehicleServiceImpl 详情[0;39m 
TID: N/A [33m2025-04-10 15:44:45[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Start destroying common HttpClient[0;39m 
TID: N/A [33m2025-04-10 15:44:45[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Start destroying Publisher[0;39m 
TID: N/A [33m2025-04-10 15:44:45[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 15:44:45[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 15:44:51[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Start destroying NacosRestTemplate[0;39m 
TID: N/A [33m2025-04-10 15:44:51[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 16:10:51[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.a.cloud.nacos.client.NacosPropertySourceBuilder[0;39m - [35mIgnore the empty nacos configuration and get it based on dataId[comparison-api] & group[DEFAULT_GROUP][0;39m 
TID: N/A [33m2025-04-10 16:10:54[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.SensingDevice".[0;39m 
TID: N/A [33m2025-04-10 16:10:54[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.injector.AbstractMethod[0;39m - [35m[com.trs.police.comparison.api.dao.SubscribeInfoMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert][0;39m 
TID: N/A [33m2025-04-10 16:11:00[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.ReportTimeYWZJIDRelation".[0;39m 
TID: N/A [33m2025-04-10 16:12:00[0;39m [31m[push-third-part-subscribe-scheduled-task-3][0;39m [31mWARN [0;39m [36mc.t.p.c.api.schedule.Push2ThirdPartSchedule[0;39m - [35m未找到当前 com.trs.police.comparison.api.spi.third.ZgDaHuaSubscribeFaceServiceImpl 详情[0;39m 
TID: N/A [33m2025-04-10 16:12:02[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Start destroying Publisher[0;39m 
TID: N/A [33m2025-04-10 16:12:02[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 16:12:02[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Start destroying common HttpClient[0;39m 
TID: N/A [33m2025-04-10 16:12:02[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 16:12:05[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Start destroying NacosRestTemplate[0;39m 
TID: N/A [33m2025-04-10 16:12:05[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 16:13:49[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.a.cloud.nacos.client.NacosPropertySourceBuilder[0;39m - [35mIgnore the empty nacos configuration and get it based on dataId[comparison-api] & group[DEFAULT_GROUP][0;39m 
TID: N/A [33m2025-04-10 16:13:52[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.SensingDevice".[0;39m 
TID: N/A [33m2025-04-10 16:13:52[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.injector.AbstractMethod[0;39m - [35m[com.trs.police.comparison.api.dao.SubscribeInfoMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert][0;39m 
TID: N/A [33m2025-04-10 16:13:56[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.ReportTimeYWZJIDRelation".[0;39m 
TID: N/A [33m2025-04-10 16:13:58[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Start destroying common HttpClient[0;39m 
TID: N/A [33m2025-04-10 16:13:58[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Start destroying Publisher[0;39m 
TID: N/A [33m2025-04-10 16:13:58[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 16:13:58[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 16:14:07[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Start destroying NacosRestTemplate[0;39m 
TID: N/A [33m2025-04-10 16:14:07[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 16:18:22[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.a.cloud.nacos.client.NacosPropertySourceBuilder[0;39m - [35mIgnore the empty nacos configuration and get it based on dataId[comparison-api] & group[DEFAULT_GROUP][0;39m 
TID: N/A [33m2025-04-10 16:18:25[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.SensingDevice".[0;39m 
TID: N/A [33m2025-04-10 16:18:25[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.injector.AbstractMethod[0;39m - [35m[com.trs.police.comparison.api.dao.SubscribeInfoMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert][0;39m 
TID: N/A [33m2025-04-10 16:18:30[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.ReportTimeYWZJIDRelation".[0;39m 
TID: N/A [33m2025-04-10 16:19:30[0;39m [31m[push-third-part-subscribe-scheduled-task-2][0;39m [31mWARN [0;39m [36mc.t.p.c.api.schedule.Push2ThirdPartSchedule[0;39m - [35m未找到当前 com.trs.police.comparison.api.spi.third.ZgDaHuaSubscribeFaceServiceImpl 详情[0;39m 
TID: N/A [33m2025-04-10 16:19:32[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Start destroying Publisher[0;39m 
TID: N/A [33m2025-04-10 16:19:32[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Start destroying common HttpClient[0;39m 
TID: N/A [33m2025-04-10 16:19:32[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 16:19:32[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 16:19:38[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Start destroying NacosRestTemplate[0;39m 
TID: N/A [33m2025-04-10 16:19:38[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 16:21:20[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.a.cloud.nacos.client.NacosPropertySourceBuilder[0;39m - [35mIgnore the empty nacos configuration and get it based on dataId[comparison-api] & group[DEFAULT_GROUP][0;39m 
TID: N/A [33m2025-04-10 16:21:24[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.SensingDevice".[0;39m 
TID: N/A [33m2025-04-10 16:21:24[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.injector.AbstractMethod[0;39m - [35m[com.trs.police.comparison.api.dao.SubscribeInfoMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert][0;39m 
TID: N/A [33m2025-04-10 16:21:29[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.ReportTimeYWZJIDRelation".[0;39m 
TID: N/A [33m2025-04-10 16:21:45[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Start destroying Publisher[0;39m 
TID: N/A [33m2025-04-10 16:21:45[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Start destroying common HttpClient[0;39m 
TID: N/A [33m2025-04-10 16:21:45[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 16:21:45[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 16:21:51[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Start destroying NacosRestTemplate[0;39m 
TID: N/A [33m2025-04-10 16:21:51[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 16:28:00[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.a.cloud.nacos.client.NacosPropertySourceBuilder[0;39m - [35mIgnore the empty nacos configuration and get it based on dataId[comparison-api] & group[DEFAULT_GROUP][0;39m 
TID: N/A [33m2025-04-10 16:28:03[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.SensingDevice".[0;39m 
TID: N/A [33m2025-04-10 16:28:03[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.injector.AbstractMethod[0;39m - [35m[com.trs.police.comparison.api.dao.SubscribeInfoMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert][0;39m 
TID: N/A [33m2025-04-10 16:28:08[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.ReportTimeYWZJIDRelation".[0;39m 
TID: N/A [33m2025-04-10 16:28:51[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.a.cloud.nacos.client.NacosPropertySourceBuilder[0;39m - [35mIgnore the empty nacos configuration and get it based on dataId[comparison-api] & group[DEFAULT_GROUP][0;39m 
TID: N/A [33m2025-04-10 16:28:54[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.SensingDevice".[0;39m 
TID: N/A [33m2025-04-10 16:28:54[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.injector.AbstractMethod[0;39m - [35m[com.trs.police.comparison.api.dao.SubscribeInfoMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert][0;39m 
TID: N/A [33m2025-04-10 16:28:59[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.ReportTimeYWZJIDRelation".[0;39m 
TID: N/A [33m2025-04-10 16:29:03[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Start destroying Publisher[0;39m 
TID: N/A [33m2025-04-10 16:29:03[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 16:29:03[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Start destroying common HttpClient[0;39m 
TID: N/A [33m2025-04-10 16:29:03[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 16:29:09[0;39m [31m[push-third-part-subscribe-scheduled-task-2][0;39m [31mWARN [0;39m [36mc.t.p.c.api.schedule.Push2ThirdPartSchedule[0;39m - [35m未找到当前 com.trs.police.comparison.api.spi.third.ZgDaHuaSubscribeFaceServiceImpl 详情[0;39m 
TID: N/A [33m2025-04-10 16:29:09[0;39m [31m[push-third-part-subscribe-scheduled-task-2][0;39m [31mWARN [0;39m [36mc.t.p.c.api.schedule.Push2ThirdPartSchedule[0;39m - [35m未找到当前 com.trs.police.comparison.api.spi.third.zgdahua.ZgDaHuaSubscribeVehicleServiceImpl 详情[0;39m 
TID: N/A [33m2025-04-10 16:29:10[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Start destroying NacosRestTemplate[0;39m 
TID: N/A [33m2025-04-10 16:29:10[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 16:29:11[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Start destroying Publisher[0;39m 
TID: N/A [33m2025-04-10 16:29:11[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 16:29:11[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Start destroying common HttpClient[0;39m 
TID: N/A [33m2025-04-10 16:29:11[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 16:29:17[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Start destroying NacosRestTemplate[0;39m 
TID: N/A [33m2025-04-10 16:29:17[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 16:32:55[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.a.cloud.nacos.client.NacosPropertySourceBuilder[0;39m - [35mIgnore the empty nacos configuration and get it based on dataId[comparison-api] & group[DEFAULT_GROUP][0;39m 
TID: N/A [33m2025-04-10 16:32:59[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.SensingDevice".[0;39m 
TID: N/A [33m2025-04-10 16:32:59[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.injector.AbstractMethod[0;39m - [35m[com.trs.police.comparison.api.dao.SubscribeInfoMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert][0;39m 
TID: N/A [33m2025-04-10 16:33:07[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.ReportTimeYWZJIDRelation".[0;39m 
TID: N/A [33m2025-04-10 16:34:07[0;39m [31m[push-third-part-subscribe-scheduled-task-2][0;39m [31mWARN [0;39m [36mc.t.p.c.api.schedule.Push2ThirdPartSchedule[0;39m - [35m未找到当前 com.trs.police.comparison.api.spi.third.ZgDaHuaSubscribeFaceServiceImpl 详情[0;39m 
TID: N/A [33m2025-04-10 16:34:10[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Start destroying Publisher[0;39m 
TID: N/A [33m2025-04-10 16:34:10[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 16:34:10[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Start destroying common HttpClient[0;39m 
TID: N/A [33m2025-04-10 16:34:10[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 16:34:16[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Start destroying NacosRestTemplate[0;39m 
TID: N/A [33m2025-04-10 16:34:16[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 16:35:05[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.a.cloud.nacos.client.NacosPropertySourceBuilder[0;39m - [35mIgnore the empty nacos configuration and get it based on dataId[comparison-api] & group[DEFAULT_GROUP][0;39m 
TID: N/A [33m2025-04-10 16:35:09[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.SensingDevice".[0;39m 
TID: N/A [33m2025-04-10 16:35:09[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.injector.AbstractMethod[0;39m - [35m[com.trs.police.comparison.api.dao.SubscribeInfoMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert][0;39m 
TID: N/A [33m2025-04-10 16:35:14[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.ReportTimeYWZJIDRelation".[0;39m 
TID: N/A [33m2025-04-10 16:35:15[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Start destroying Publisher[0;39m 
TID: N/A [33m2025-04-10 16:35:15[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 16:35:15[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Start destroying common HttpClient[0;39m 
TID: N/A [33m2025-04-10 16:35:15[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 16:35:24[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Start destroying NacosRestTemplate[0;39m 
TID: N/A [33m2025-04-10 16:35:24[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 20:00:19[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.a.cloud.nacos.client.NacosPropertySourceBuilder[0;39m - [35mIgnore the empty nacos configuration and get it based on dataId[comparison-api] & group[DEFAULT_GROUP][0;39m 
TID: N/A [33m2025-04-10 20:00:24[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.SensingDevice".[0;39m 
TID: N/A [33m2025-04-10 20:00:24[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.injector.AbstractMethod[0;39m - [35m[com.trs.police.comparison.api.dao.SubscribeInfoMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert][0;39m 
TID: N/A [33m2025-04-10 20:00:31[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.ReportTimeYWZJIDRelation".[0;39m 
TID: N/A [33m2025-04-10 20:00:31[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Start destroying common HttpClient[0;39m 
TID: N/A [33m2025-04-10 20:00:31[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Start destroying Publisher[0;39m 
TID: N/A [33m2025-04-10 20:00:31[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 20:00:31[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 20:00:38[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Start destroying NacosRestTemplate[0;39m 
TID: N/A [33m2025-04-10 20:00:38[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 20:01:19[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.a.cloud.nacos.client.NacosPropertySourceBuilder[0;39m - [35mIgnore the empty nacos configuration and get it based on dataId[comparison-api] & group[DEFAULT_GROUP][0;39m 
TID: N/A [33m2025-04-10 20:01:23[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.SensingDevice".[0;39m 
TID: N/A [33m2025-04-10 20:01:23[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.injector.AbstractMethod[0;39m - [35m[com.trs.police.comparison.api.dao.SubscribeInfoMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert][0;39m 
TID: N/A [33m2025-04-10 20:01:29[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.ReportTimeYWZJIDRelation".[0;39m 
TID: N/A [33m2025-04-10 20:03:37[0;39m [31m[HikariPool-1 housekeeper][0;39m [31mWARN [0;39m [36mcom.zaxxer.hikari.pool.HikariPool[0;39m - [35mHikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=2m14s744ms876µs800ns).[0;39m 
TID: N/A [33m2025-04-10 20:03:37[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Start destroying Publisher[0;39m 
TID: N/A [33m2025-04-10 20:03:37[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 20:03:37[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Start destroying common HttpClient[0;39m 
TID: N/A [33m2025-04-10 20:03:37[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Destruction of the end[0;39m 
TID: N/A [33m2025-04-10 20:03:47[0;39m [31m[push-third-part-subscribe-scheduled-task-2][0;39m [31mWARN [0;39m [36mc.t.p.c.api.schedule.Push2ThirdPartSchedule[0;39m - [35m未找到当前 com.trs.police.comparison.api.spi.third.ZgDaHuaSubscribeFaceServiceImpl 详情[0;39m 
TID: N/A [33m2025-04-10 20:03:47[0;39m [31m[push-third-part-subscribe-scheduled-task-2][0;39m [31mWARN [0;39m [36mc.t.p.c.api.schedule.Push2ThirdPartSchedule[0;39m - [35m未找到当前 com.trs.police.comparison.api.spi.third.zgdahua.ZgDaHuaSubscribeVehicleServiceImpl 详情[0;39m 
TID: N/A [33m2025-04-10 20:03:48[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Start destroying NacosRestTemplate[0;39m 
TID: N/A [33m2025-04-10 20:03:48[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Destruction of the end[0;39m 
