TID: N/A [33m2025-01-07 11:06:20[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.b.t.context.SpringBootTestContextBootstrapper[0;39m - [35mNeither @ContextConfiguration nor @ContextHierarchy found for test class [com.trs.police.comparison.api.spi.third.ControlTest], using SpringBootContextLoader[0;39m 
TID: N/A [33m2025-01-07 11:06:20[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.test.context.support.AbstractContextLoader[0;39m - [35mCould not detect default resource locations for test class [com.trs.police.comparison.api.spi.third.ControlTest]: no resource found for suffixes {-context.xml, Context.groovy}.[0;39m 
TID: N/A [33m2025-01-07 11:06:20[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.b.t.context.SpringBootTestContextBootstrapper[0;39m - [35mLoaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener][0;39m 
TID: N/A [33m2025-01-07 11:06:20[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.b.t.context.SpringBootTestContextBootstrapper[0;39m - [35mUsing TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@65045a87, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@47f4e407, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@2d1dee39, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@48d5f34e, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@fc258b1, org.springframework.test.context.support.DirtiesContextTestExecutionListener@6ff65192, org.springframework.test.context.transaction.TransactionalTestExecutionListener@2dd80673, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@4af0df05, org.springframework.test.context.event.EventPublishingTestExecutionListener@57ea113a, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@acdb094][0;39m 
TID: N/A [33m2025-01-07 11:06:25[0;39m [31m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.config.impl.LocalConfigInfoProcessor[0;39m - [35mLOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config[0;39m 
TID: N/A [33m2025-01-07 11:06:25[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.a.cloud.nacos.client.NacosPropertySourceBuilder[0;39m - [35mIgnore the empty nacos configuration and get it based on dataId[comparison-api] & group[DEFAULT_GROUP][0;39m 
TID: N/A [33m2025-01-07 11:06:25[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.config.utils.JvmUtil[0;39m - [35misMultiInstance:false[0;39m 
TID: N/A [33m2025-01-07 11:06:25[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.c.b.c.PropertySourceBootstrapConfiguration[0;39m - [35mLocated property source: [BootstrapPropertySource {name='bootstrapProperties-comparison-api.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-comparison-api,DEFAULT_GROUP'}][0;39m 
TID: N/A [33m2025-01-07 11:06:25[0;39m [31m[main][0;39m [34mINFO [0;39m [36mc.trs.police.comparison.api.spi.third.ControlTest[0;39m - [35mNo active profile set, falling back to default profiles: default[0;39m 
TID: N/A [33m2025-01-07 11:06:27[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.config.RepositoryConfigurationDelegate[0;39m - [35mMultiple Spring Data modules found, entering strict repository configuration mode![0;39m 
TID: N/A [33m2025-01-07 11:06:27[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.config.RepositoryConfigurationDelegate[0;39m - [35mBootstrapping Spring Data JPA repositories in DEFAULT mode.[0;39m 
TID: N/A [33m2025-01-07 11:06:27[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.config.RepositoryConfigurationDelegate[0;39m - [35mFinished Spring Data repository scanning in 58 ms. Found 0 JPA repository interfaces.[0;39m 
TID: N/A [33m2025-01-07 11:06:27[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.config.RepositoryConfigurationDelegate[0;39m - [35mMultiple Spring Data modules found, entering strict repository configuration mode![0;39m 
TID: N/A [33m2025-01-07 11:06:27[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.config.RepositoryConfigurationDelegate[0;39m - [35mBootstrapping Spring Data Redis repositories in DEFAULT mode.[0;39m 
TID: N/A [33m2025-01-07 11:06:27[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.config.RepositoryConfigurationDelegate[0;39m - [35mFinished Spring Data repository scanning in 28 ms. Found 0 Redis repository interfaces.[0;39m 
TID: N/A [33m2025-01-07 11:06:28[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.springframework.cloud.context.scope.GenericScope[0;39m - [35mBeanFactory id=1b50f227-1ffb-3068-83e0-113e46951f8e[0;39m 
TID: N/A [33m2025-01-07 11:06:28[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.flywaydb.core.internal.license.VersionPrinter[0;39m - [35mFlyway Community Edition 7.7.3 by Redgate[0;39m 
TID: N/A [33m2025-01-07 11:06:28[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.zaxxer.hikari.HikariDataSource[0;39m - [35mHikariPool-1 - Starting...[0;39m 
TID: N/A [33m2025-01-07 11:06:29[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.zaxxer.hikari.HikariDataSource[0;39m - [35mHikariPool-1 - Start completed.[0;39m 
TID: N/A [33m2025-01-07 11:06:29[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.f.core.internal.database.base.DatabaseType[0;39m - [35mDatabase: ****************************************** (MySQL 8.0)[0;39m 
TID: N/A [33m2025-01-07 11:06:31[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.flywaydb.core.internal.command.DbValidate[0;39m - [35mSuccessfully validated 4 migrations (execution time 00:00.626s)[0;39m 
TID: N/A [33m2025-01-07 11:06:32[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.flywaydb.core.internal.command.DbMigrate[0;39m - [35mCurrent version of schema `comparison-api`: 1.0.2[0;39m 
TID: N/A [33m2025-01-07 11:06:33[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.flywaydb.core.internal.command.DbMigrate[0;39m - [35mMigrating schema `comparison-api` to version "1.0.3 - init ddl"[0;39m 
TID: N/A [33m2025-01-07 11:06:33[0;39m [31m[main][0;39m [31mWARN [0;39m [36mo.f.c.internal.sqlscript.DefaultSqlScriptExecutor[0;39m - [35mDB: Integer display width is deprecated and will be removed in a future release. (SQL State: HY000 - Error Code: 1681)[0;39m 
TID: N/A [33m2025-01-07 11:06:33[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.f.c.internal.sqlscript.DefaultSqlScriptExecutor[0;39m - [35m0 rows affected[0;39m 
TID: N/A [33m2025-01-07 11:06:35[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.flywaydb.core.internal.command.DbMigrate[0;39m - [35mSuccessfully applied 1 migration to schema `comparison-api`, now at version v1.0.3 (execution time 00:03.838s)[0;39m 
TID: N/A [33m2025-01-07 11:06:36[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.hibernate.jpa.internal.util.LogHelper[0;39m - [35mHHH000204: Processing PersistenceUnitInfo [name: default][0;39m 
TID: N/A [33m2025-01-07 11:06:36[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.hibernate.Version[0;39m - [35mHHH000412: Hibernate ORM core version 5.4.33[0;39m 
TID: N/A [33m2025-01-07 11:06:37[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.hibernate.annotations.common.Version[0;39m - [35mHCANN000001: Hibernate Commons Annotations {5.1.2.Final}[0;39m 
TID: N/A [33m2025-01-07 11:06:37[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.hibernate.dialect.Dialect[0;39m - [35mHHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect[0;39m 
TID: N/A [33m2025-01-07 11:06:38[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.h.e.t.jta.platform.internal.JtaPlatformInitiator[0;39m - [35mHHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform][0;39m 
TID: N/A [33m2025-01-07 11:06:38[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.orm.jpa.LocalContainerEntityManagerFactoryBean[0;39m - [35mInitialized JPA EntityManagerFactory for persistence unit 'default'[0;39m 
TID: N/A [33m2025-01-07 11:06:40[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.injector.AbstractMethod[0;39m - [35m[com.trs.police.comparison.api.dao.SubscribeInfoMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert][0;39m 
TID: N/A [33m2025-01-07 11:06:41[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.hibernate.validator.internal.util.Version[0;39m - [35mHV000001: Hibernate Validator 7.0.4.Final[0;39m 
TID: N/A [33m2025-01-07 11:06:46[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.springframework.cloud.commons.util.InetUtils[0;39m - [35mCannot determine local hostname[0;39m 
TID: N/A [33m2025-01-07 11:06:48[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.springframework.cloud.commons.util.InetUtils[0;39m - [35mCannot determine local hostname[0;39m 
TID: N/A [33m2025-01-07 11:06:48[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.SensingDevice".[0;39m 
TID: N/A [33m2025-01-07 11:06:48[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35minitializer namespace from System Property :null[0;39m 
TID: N/A [33m2025-01-07 11:06:48[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35minitializer namespace from System Environment :null[0;39m 
TID: N/A [33m2025-01-07 11:06:48[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35minitializer namespace from System Property :null[0;39m 
TID: N/A [33m2025-01-07 11:06:49[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mnew ips(1) service: DEFAULT_GROUP@@comparison-api@@DEFAULT -> [{"instanceId":"*********#10011#DEFAULT#DEFAULT_GROUP@@comparison-api","ip":"*********","port":10011,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@comparison-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}][0;39m 
TID: N/A [33m2025-01-07 11:06:49[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcurrent ips:(1) service: DEFAULT_GROUP@@comparison-api@@DEFAULT -> [{"instanceId":"*********#10011#DEFAULT#DEFAULT_GROUP@@comparison-api","ip":"*********","port":10011,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@comparison-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}][0;39m 
TID: N/A [33m2025-01-07 11:06:49[0;39m [31m[main][0;39m [34mINFO [0;39m [36mc.trs.police.comparison.api.spi.third.ControlTest[0;39m - [35mStarted ControlTest in 28.458 seconds (JVM running for 29.935)[0;39m 
TID: N/A [33m2025-01-07 11:06:49[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.config.impl.ClientWorker[0;39m - [35m[fixed-nacos-svc_8848-ys-dev] [subscribe] comparison-api.properties+DEFAULT_GROUP+ys-dev[0;39m 
TID: N/A [33m2025-01-07 11:06:49[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.config.impl.CacheData[0;39m - [35m[fixed-nacos-svc_8848-ys-dev] [add-listener] ok, tenant=ys-dev, dataId=comparison-api.properties, group=DEFAULT_GROUP, cnt=1[0;39m 
TID: N/A [33m2025-01-07 11:06:49[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.config.impl.ClientWorker[0;39m - [35m[fixed-nacos-svc_8848-ys-dev] [subscribe] comparison-api+DEFAULT_GROUP+ys-dev[0;39m 
TID: N/A [33m2025-01-07 11:06:49[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.config.impl.CacheData[0;39m - [35m[fixed-nacos-svc_8848-ys-dev] [add-listener] ok, tenant=ys-dev, dataId=comparison-api, group=DEFAULT_GROUP, cnt=1[0;39m 
TID: N/A [33m2025-01-07 11:06:49[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Start destroying Publisher[0;39m 
TID: N/A [33m2025-01-07 11:06:49[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Start destroying common HttpClient[0;39m 
TID: N/A [33m2025-01-07 11:06:49[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Destruction of the end[0;39m 
TID: N/A [33m2025-01-07 11:06:49[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Destruction of the end[0;39m 
TID: N/A [33m2025-01-07 11:06:49[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin[0;39m 
TID: N/A [33m2025-01-07 11:06:49[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop[0;39m 
TID: N/A [33m2025-01-07 11:06:49[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.core.HostReactor do shutdown begin[0;39m 
TID: N/A [33m2025-01-07 11:06:50[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin[0;39m 
TID: N/A [33m2025-01-07 11:06:53[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop[0;39m 
TID: N/A [33m2025-01-07 11:06:53[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin[0;39m 
TID: N/A [33m2025-01-07 11:06:56[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop[0;39m 
TID: N/A [33m2025-01-07 11:06:56[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.core.HostReactor do shutdown stop[0;39m 
TID: N/A [33m2025-01-07 11:06:56[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin[0;39m 
TID: N/A [33m2025-01-07 11:06:56[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Start destroying NacosRestTemplate[0;39m 
TID: N/A [33m2025-01-07 11:06:56[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Destruction of the end[0;39m 
TID: N/A [33m2025-01-07 11:06:56[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.alibaba.nacos.client.identify.CredentialWatcher[0;39m - [35m[null] CredentialWatcher is stopped[0;39m 
TID: N/A [33m2025-01-07 11:06:56[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.alibaba.nacos.client.identify.CredentialService[0;39m - [35m[null] CredentialService is freed[0;39m 
TID: N/A [33m2025-01-07 11:06:56[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop[0;39m 
TID: N/A [33m2025-01-07 11:06:56[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mo.s.orm.jpa.LocalContainerEntityManagerFactoryBean[0;39m - [35mClosing JPA EntityManagerFactory for persistence unit 'default'[0;39m 
TID: N/A [33m2025-01-07 11:06:56[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.zaxxer.hikari.HikariDataSource[0;39m - [35mHikariPool-1 - Shutdown initiated...[0;39m 
TID: N/A [33m2025-01-07 11:06:57[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.zaxxer.hikari.HikariDataSource[0;39m - [35mHikariPool-1 - Shutdown completed.[0;39m 
TID: N/A [33m2025-01-07 11:07:26[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.b.t.context.SpringBootTestContextBootstrapper[0;39m - [35mNeither @ContextConfiguration nor @ContextHierarchy found for test class [com.trs.police.comparison.api.spi.third.ControlTest], using SpringBootContextLoader[0;39m 
TID: N/A [33m2025-01-07 11:07:26[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.test.context.support.AbstractContextLoader[0;39m - [35mCould not detect default resource locations for test class [com.trs.police.comparison.api.spi.third.ControlTest]: no resource found for suffixes {-context.xml, Context.groovy}.[0;39m 
TID: N/A [33m2025-01-07 11:07:26[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.b.t.context.SpringBootTestContextBootstrapper[0;39m - [35mLoaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener][0;39m 
TID: N/A [33m2025-01-07 11:07:26[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.b.t.context.SpringBootTestContextBootstrapper[0;39m - [35mUsing TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@65045a87, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@47f4e407, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@2d1dee39, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@48d5f34e, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@fc258b1, org.springframework.test.context.support.DirtiesContextTestExecutionListener@6ff65192, org.springframework.test.context.transaction.TransactionalTestExecutionListener@2dd80673, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@4af0df05, org.springframework.test.context.event.EventPublishingTestExecutionListener@57ea113a, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@acdb094][0;39m 
TID: N/A [33m2025-01-07 11:07:30[0;39m [31m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.config.impl.LocalConfigInfoProcessor[0;39m - [35mLOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config[0;39m 
TID: N/A [33m2025-01-07 11:07:31[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.a.cloud.nacos.client.NacosPropertySourceBuilder[0;39m - [35mIgnore the empty nacos configuration and get it based on dataId[comparison-api] & group[DEFAULT_GROUP][0;39m 
TID: N/A [33m2025-01-07 11:07:31[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.config.utils.JvmUtil[0;39m - [35misMultiInstance:false[0;39m 
TID: N/A [33m2025-01-07 11:07:31[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.c.b.c.PropertySourceBootstrapConfiguration[0;39m - [35mLocated property source: [BootstrapPropertySource {name='bootstrapProperties-comparison-api.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-comparison-api,DEFAULT_GROUP'}][0;39m 
TID: N/A [33m2025-01-07 11:07:31[0;39m [31m[main][0;39m [34mINFO [0;39m [36mc.trs.police.comparison.api.spi.third.ControlTest[0;39m - [35mNo active profile set, falling back to default profiles: default[0;39m 
TID: N/A [33m2025-01-07 11:07:32[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.config.RepositoryConfigurationDelegate[0;39m - [35mMultiple Spring Data modules found, entering strict repository configuration mode![0;39m 
TID: N/A [33m2025-01-07 11:07:32[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.config.RepositoryConfigurationDelegate[0;39m - [35mBootstrapping Spring Data JPA repositories in DEFAULT mode.[0;39m 
TID: N/A [33m2025-01-07 11:07:32[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.config.RepositoryConfigurationDelegate[0;39m - [35mFinished Spring Data repository scanning in 81 ms. Found 0 JPA repository interfaces.[0;39m 
TID: N/A [33m2025-01-07 11:07:32[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.config.RepositoryConfigurationDelegate[0;39m - [35mMultiple Spring Data modules found, entering strict repository configuration mode![0;39m 
TID: N/A [33m2025-01-07 11:07:32[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.config.RepositoryConfigurationDelegate[0;39m - [35mBootstrapping Spring Data Redis repositories in DEFAULT mode.[0;39m 
TID: N/A [33m2025-01-07 11:07:32[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.config.RepositoryConfigurationDelegate[0;39m - [35mFinished Spring Data repository scanning in 36 ms. Found 0 Redis repository interfaces.[0;39m 
TID: N/A [33m2025-01-07 11:07:32[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.springframework.cloud.context.scope.GenericScope[0;39m - [35mBeanFactory id=1b50f227-1ffb-3068-83e0-113e46951f8e[0;39m 
TID: N/A [33m2025-01-07 11:07:33[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.flywaydb.core.internal.license.VersionPrinter[0;39m - [35mFlyway Community Edition 7.7.3 by Redgate[0;39m 
TID: N/A [33m2025-01-07 11:07:33[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.zaxxer.hikari.HikariDataSource[0;39m - [35mHikariPool-1 - Starting...[0;39m 
TID: N/A [33m2025-01-07 11:07:34[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.zaxxer.hikari.HikariDataSource[0;39m - [35mHikariPool-1 - Start completed.[0;39m 
TID: N/A [33m2025-01-07 11:07:34[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.f.core.internal.database.base.DatabaseType[0;39m - [35mDatabase: ****************************************** (MySQL 8.0)[0;39m 
TID: N/A [33m2025-01-07 11:07:37[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.flywaydb.core.internal.command.DbValidate[0;39m - [35mSuccessfully validated 4 migrations (execution time 00:00.837s)[0;39m 
TID: N/A [33m2025-01-07 11:07:38[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.flywaydb.core.internal.command.DbMigrate[0;39m - [35mCurrent version of schema `comparison-api`: 1.0.3[0;39m 
TID: N/A [33m2025-01-07 11:07:38[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.flywaydb.core.internal.command.DbMigrate[0;39m - [35mSchema `comparison-api` is up to date. No migration necessary.[0;39m 
TID: N/A [33m2025-01-07 11:07:40[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.hibernate.jpa.internal.util.LogHelper[0;39m - [35mHHH000204: Processing PersistenceUnitInfo [name: default][0;39m 
TID: N/A [33m2025-01-07 11:07:40[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.hibernate.Version[0;39m - [35mHHH000412: Hibernate ORM core version 5.4.33[0;39m 
TID: N/A [33m2025-01-07 11:07:40[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.hibernate.annotations.common.Version[0;39m - [35mHCANN000001: Hibernate Commons Annotations {5.1.2.Final}[0;39m 
TID: N/A [33m2025-01-07 11:07:40[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.hibernate.dialect.Dialect[0;39m - [35mHHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect[0;39m 
TID: N/A [33m2025-01-07 11:07:41[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.h.e.t.jta.platform.internal.JtaPlatformInitiator[0;39m - [35mHHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform][0;39m 
TID: N/A [33m2025-01-07 11:07:41[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.orm.jpa.LocalContainerEntityManagerFactoryBean[0;39m - [35mInitialized JPA EntityManagerFactory for persistence unit 'default'[0;39m 
TID: N/A [33m2025-01-07 11:07:42[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.injector.AbstractMethod[0;39m - [35m[com.trs.police.comparison.api.dao.SubscribeInfoMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert][0;39m 
TID: N/A [33m2025-01-07 11:07:42[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.hibernate.validator.internal.util.Version[0;39m - [35mHV000001: Hibernate Validator 7.0.4.Final[0;39m 
TID: N/A [33m2025-01-07 11:07:46[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.springframework.cloud.commons.util.InetUtils[0;39m - [35mCannot determine local hostname[0;39m 
TID: N/A [33m2025-01-07 11:07:47[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.springframework.cloud.commons.util.InetUtils[0;39m - [35mCannot determine local hostname[0;39m 
TID: N/A [33m2025-01-07 11:07:48[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.SensingDevice".[0;39m 
TID: N/A [33m2025-01-07 11:07:48[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35minitializer namespace from System Property :null[0;39m 
TID: N/A [33m2025-01-07 11:07:48[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35minitializer namespace from System Environment :null[0;39m 
TID: N/A [33m2025-01-07 11:07:48[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35minitializer namespace from System Property :null[0;39m 
TID: N/A [33m2025-01-07 11:07:48[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mnew ips(1) service: DEFAULT_GROUP@@comparison-api@@DEFAULT -> [{"instanceId":"*********#10011#DEFAULT#DEFAULT_GROUP@@comparison-api","ip":"*********","port":10011,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@comparison-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}][0;39m 
TID: N/A [33m2025-01-07 11:07:48[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcurrent ips:(1) service: DEFAULT_GROUP@@comparison-api@@DEFAULT -> [{"instanceId":"*********#10011#DEFAULT#DEFAULT_GROUP@@comparison-api","ip":"*********","port":10011,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@comparison-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}][0;39m 
TID: N/A [33m2025-01-07 11:07:48[0;39m [31m[main][0;39m [34mINFO [0;39m [36mc.trs.police.comparison.api.spi.third.ControlTest[0;39m - [35mStarted ControlTest in 22.042 seconds (JVM running for 22.979)[0;39m 
TID: N/A [33m2025-01-07 11:07:48[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.config.impl.ClientWorker[0;39m - [35m[fixed-nacos-svc_8848-ys-dev] [subscribe] comparison-api.properties+DEFAULT_GROUP+ys-dev[0;39m 
TID: N/A [33m2025-01-07 11:07:48[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.config.impl.CacheData[0;39m - [35m[fixed-nacos-svc_8848-ys-dev] [add-listener] ok, tenant=ys-dev, dataId=comparison-api.properties, group=DEFAULT_GROUP, cnt=1[0;39m 
TID: N/A [33m2025-01-07 11:07:48[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.config.impl.ClientWorker[0;39m - [35m[fixed-nacos-svc_8848-ys-dev] [subscribe] comparison-api+DEFAULT_GROUP+ys-dev[0;39m 
TID: N/A [33m2025-01-07 11:07:48[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.config.impl.CacheData[0;39m - [35m[fixed-nacos-svc_8848-ys-dev] [add-listener] ok, tenant=ys-dev, dataId=comparison-api, group=DEFAULT_GROUP, cnt=1[0;39m 
TID: N/A [33m2025-01-07 11:07:48[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Start destroying common HttpClient[0;39m 
TID: N/A [33m2025-01-07 11:07:48[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Start destroying Publisher[0;39m 
TID: N/A [33m2025-01-07 11:07:48[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Destruction of the end[0;39m 
TID: N/A [33m2025-01-07 11:07:48[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Destruction of the end[0;39m 
TID: N/A [33m2025-01-07 11:07:48[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin[0;39m 
TID: N/A [33m2025-01-07 11:07:48[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop[0;39m 
TID: N/A [33m2025-01-07 11:07:48[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.core.HostReactor do shutdown begin[0;39m 
TID: N/A [33m2025-01-07 11:07:49[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin[0;39m 
TID: N/A [33m2025-01-07 11:07:52[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop[0;39m 
TID: N/A [33m2025-01-07 11:07:52[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin[0;39m 
TID: N/A [33m2025-01-07 11:07:55[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop[0;39m 
TID: N/A [33m2025-01-07 11:07:55[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.core.HostReactor do shutdown stop[0;39m 
TID: N/A [33m2025-01-07 11:07:55[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin[0;39m 
TID: N/A [33m2025-01-07 11:07:55[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Start destroying NacosRestTemplate[0;39m 
TID: N/A [33m2025-01-07 11:07:55[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Destruction of the end[0;39m 
TID: N/A [33m2025-01-07 11:07:55[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.alibaba.nacos.client.identify.CredentialWatcher[0;39m - [35m[null] CredentialWatcher is stopped[0;39m 
TID: N/A [33m2025-01-07 11:07:55[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.alibaba.nacos.client.identify.CredentialService[0;39m - [35m[null] CredentialService is freed[0;39m 
TID: N/A [33m2025-01-07 11:07:55[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop[0;39m 
TID: N/A [33m2025-01-07 11:07:55[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mo.s.orm.jpa.LocalContainerEntityManagerFactoryBean[0;39m - [35mClosing JPA EntityManagerFactory for persistence unit 'default'[0;39m 
TID: N/A [33m2025-01-07 11:07:55[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.zaxxer.hikari.HikariDataSource[0;39m - [35mHikariPool-1 - Shutdown initiated...[0;39m 
TID: N/A [33m2025-01-07 11:07:57[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.zaxxer.hikari.HikariDataSource[0;39m - [35mHikariPool-1 - Shutdown completed.[0;39m 
TID: N/A [33m2025-01-07 11:08:52[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.b.t.context.SpringBootTestContextBootstrapper[0;39m - [35mNeither @ContextConfiguration nor @ContextHierarchy found for test class [com.trs.police.comparison.api.spi.third.ControlTest], using SpringBootContextLoader[0;39m 
TID: N/A [33m2025-01-07 11:08:52[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.test.context.support.AbstractContextLoader[0;39m - [35mCould not detect default resource locations for test class [com.trs.police.comparison.api.spi.third.ControlTest]: no resource found for suffixes {-context.xml, Context.groovy}.[0;39m 
TID: N/A [33m2025-01-07 11:08:53[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.b.t.context.SpringBootTestContextBootstrapper[0;39m - [35mLoaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener][0;39m 
TID: N/A [33m2025-01-07 11:08:53[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.b.t.context.SpringBootTestContextBootstrapper[0;39m - [35mUsing TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@65045a87, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@47f4e407, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@2d1dee39, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@48d5f34e, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@fc258b1, org.springframework.test.context.support.DirtiesContextTestExecutionListener@6ff65192, org.springframework.test.context.transaction.TransactionalTestExecutionListener@2dd80673, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@4af0df05, org.springframework.test.context.event.EventPublishingTestExecutionListener@57ea113a, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@acdb094][0;39m 
TID: N/A [33m2025-01-07 11:08:57[0;39m [31m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.config.impl.LocalConfigInfoProcessor[0;39m - [35mLOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config[0;39m 
TID: N/A [33m2025-01-07 11:08:57[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.a.cloud.nacos.client.NacosPropertySourceBuilder[0;39m - [35mIgnore the empty nacos configuration and get it based on dataId[comparison-api] & group[DEFAULT_GROUP][0;39m 
TID: N/A [33m2025-01-07 11:08:57[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.config.utils.JvmUtil[0;39m - [35misMultiInstance:false[0;39m 
TID: N/A [33m2025-01-07 11:08:57[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.c.b.c.PropertySourceBootstrapConfiguration[0;39m - [35mLocated property source: [BootstrapPropertySource {name='bootstrapProperties-comparison-api.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-comparison-api,DEFAULT_GROUP'}][0;39m 
TID: N/A [33m2025-01-07 11:08:57[0;39m [31m[main][0;39m [34mINFO [0;39m [36mc.trs.police.comparison.api.spi.third.ControlTest[0;39m - [35mNo active profile set, falling back to default profiles: default[0;39m 
TID: N/A [33m2025-01-07 11:08:58[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.config.RepositoryConfigurationDelegate[0;39m - [35mMultiple Spring Data modules found, entering strict repository configuration mode![0;39m 
TID: N/A [33m2025-01-07 11:08:58[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.config.RepositoryConfigurationDelegate[0;39m - [35mBootstrapping Spring Data JPA repositories in DEFAULT mode.[0;39m 
TID: N/A [33m2025-01-07 11:08:58[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.config.RepositoryConfigurationDelegate[0;39m - [35mFinished Spring Data repository scanning in 49 ms. Found 0 JPA repository interfaces.[0;39m 
TID: N/A [33m2025-01-07 11:08:58[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.config.RepositoryConfigurationDelegate[0;39m - [35mMultiple Spring Data modules found, entering strict repository configuration mode![0;39m 
TID: N/A [33m2025-01-07 11:08:58[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.config.RepositoryConfigurationDelegate[0;39m - [35mBootstrapping Spring Data Redis repositories in DEFAULT mode.[0;39m 
TID: N/A [33m2025-01-07 11:08:58[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.config.RepositoryConfigurationDelegate[0;39m - [35mFinished Spring Data repository scanning in 22 ms. Found 0 Redis repository interfaces.[0;39m 
TID: N/A [33m2025-01-07 11:08:58[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.springframework.cloud.context.scope.GenericScope[0;39m - [35mBeanFactory id=1b50f227-1ffb-3068-83e0-113e46951f8e[0;39m 
TID: N/A [33m2025-01-07 11:08:59[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.flywaydb.core.internal.license.VersionPrinter[0;39m - [35mFlyway Community Edition 7.7.3 by Redgate[0;39m 
TID: N/A [33m2025-01-07 11:08:59[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.zaxxer.hikari.HikariDataSource[0;39m - [35mHikariPool-1 - Starting...[0;39m 
TID: N/A [33m2025-01-07 11:08:59[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.zaxxer.hikari.HikariDataSource[0;39m - [35mHikariPool-1 - Start completed.[0;39m 
TID: N/A [33m2025-01-07 11:08:59[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.f.core.internal.database.base.DatabaseType[0;39m - [35mDatabase: ****************************************** (MySQL 8.0)[0;39m 
TID: N/A [33m2025-01-07 11:09:01[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.flywaydb.core.internal.command.DbValidate[0;39m - [35mSuccessfully validated 4 migrations (execution time 00:00.462s)[0;39m 
TID: N/A [33m2025-01-07 11:09:02[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.flywaydb.core.internal.command.DbMigrate[0;39m - [35mCurrent version of schema `comparison-api`: 1.0.3[0;39m 
TID: N/A [33m2025-01-07 11:09:02[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.flywaydb.core.internal.command.DbMigrate[0;39m - [35mSchema `comparison-api` is up to date. No migration necessary.[0;39m 
TID: N/A [33m2025-01-07 11:09:02[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.hibernate.jpa.internal.util.LogHelper[0;39m - [35mHHH000204: Processing PersistenceUnitInfo [name: default][0;39m 
TID: N/A [33m2025-01-07 11:09:03[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.hibernate.Version[0;39m - [35mHHH000412: Hibernate ORM core version 5.4.33[0;39m 
TID: N/A [33m2025-01-07 11:09:03[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.hibernate.annotations.common.Version[0;39m - [35mHCANN000001: Hibernate Commons Annotations {5.1.2.Final}[0;39m 
TID: N/A [33m2025-01-07 11:09:03[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.hibernate.dialect.Dialect[0;39m - [35mHHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect[0;39m 
TID: N/A [33m2025-01-07 11:09:03[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.h.e.t.jta.platform.internal.JtaPlatformInitiator[0;39m - [35mHHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform][0;39m 
TID: N/A [33m2025-01-07 11:09:03[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.orm.jpa.LocalContainerEntityManagerFactoryBean[0;39m - [35mInitialized JPA EntityManagerFactory for persistence unit 'default'[0;39m 
TID: N/A [33m2025-01-07 11:09:04[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.injector.AbstractMethod[0;39m - [35m[com.trs.police.comparison.api.dao.SubscribeInfoMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert][0;39m 
TID: N/A [33m2025-01-07 11:09:05[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.hibernate.validator.internal.util.Version[0;39m - [35mHV000001: Hibernate Validator 7.0.4.Final[0;39m 
TID: N/A [33m2025-01-07 11:09:06[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.ReportTimeYWZJIDRelation".[0;39m 
TID: N/A [33m2025-01-07 11:09:09[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.springframework.cloud.commons.util.InetUtils[0;39m - [35mCannot determine local hostname[0;39m 
TID: N/A [33m2025-01-07 11:09:10[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.springframework.cloud.commons.util.InetUtils[0;39m - [35mCannot determine local hostname[0;39m 
TID: N/A [33m2025-01-07 11:09:10[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.SensingDevice".[0;39m 
TID: N/A [33m2025-01-07 11:09:10[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35minitializer namespace from System Property :null[0;39m 
TID: N/A [33m2025-01-07 11:09:10[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35minitializer namespace from System Environment :null[0;39m 
TID: N/A [33m2025-01-07 11:09:10[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35minitializer namespace from System Property :null[0;39m 
TID: N/A [33m2025-01-07 11:09:11[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mnew ips(1) service: DEFAULT_GROUP@@comparison-api@@DEFAULT -> [{"instanceId":"*********#10011#DEFAULT#DEFAULT_GROUP@@comparison-api","ip":"*********","port":10011,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@comparison-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}][0;39m 
TID: N/A [33m2025-01-07 11:09:11[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcurrent ips:(1) service: DEFAULT_GROUP@@comparison-api@@DEFAULT -> [{"instanceId":"*********#10011#DEFAULT#DEFAULT_GROUP@@comparison-api","ip":"*********","port":10011,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@comparison-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}][0;39m 
TID: N/A [33m2025-01-07 11:09:11[0;39m [31m[main][0;39m [34mINFO [0;39m [36mc.trs.police.comparison.api.spi.third.ControlTest[0;39m - [35mStarted ControlTest in 18.259 seconds (JVM running for 19.142)[0;39m 
TID: N/A [33m2025-01-07 11:09:11[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.config.impl.ClientWorker[0;39m - [35m[fixed-nacos-svc_8848-ys-dev] [subscribe] comparison-api.properties+DEFAULT_GROUP+ys-dev[0;39m 
TID: N/A [33m2025-01-07 11:09:11[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.config.impl.CacheData[0;39m - [35m[fixed-nacos-svc_8848-ys-dev] [add-listener] ok, tenant=ys-dev, dataId=comparison-api.properties, group=DEFAULT_GROUP, cnt=1[0;39m 
TID: N/A [33m2025-01-07 11:09:11[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.config.impl.ClientWorker[0;39m - [35m[fixed-nacos-svc_8848-ys-dev] [subscribe] comparison-api+DEFAULT_GROUP+ys-dev[0;39m 
TID: N/A [33m2025-01-07 11:09:11[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.config.impl.CacheData[0;39m - [35m[fixed-nacos-svc_8848-ys-dev] [add-listener] ok, tenant=ys-dev, dataId=comparison-api, group=DEFAULT_GROUP, cnt=1[0;39m 
TID: N/A [33m2025-01-07 11:09:11[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Start destroying Publisher[0;39m 
TID: N/A [33m2025-01-07 11:09:11[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Start destroying common HttpClient[0;39m 
TID: N/A [33m2025-01-07 11:09:11[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Destruction of the end[0;39m 
TID: N/A [33m2025-01-07 11:09:11[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Destruction of the end[0;39m 
TID: N/A [33m2025-01-07 11:09:11[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin[0;39m 
TID: N/A [33m2025-01-07 11:09:11[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop[0;39m 
TID: N/A [33m2025-01-07 11:09:11[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.core.HostReactor do shutdown begin[0;39m 
TID: N/A [33m2025-01-07 11:09:12[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin[0;39m 
TID: N/A [33m2025-01-07 11:09:15[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop[0;39m 
TID: N/A [33m2025-01-07 11:09:15[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin[0;39m 
TID: N/A [33m2025-01-07 11:09:18[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop[0;39m 
TID: N/A [33m2025-01-07 11:09:18[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.core.HostReactor do shutdown stop[0;39m 
TID: N/A [33m2025-01-07 11:09:18[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin[0;39m 
TID: N/A [33m2025-01-07 11:09:18[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Start destroying NacosRestTemplate[0;39m 
TID: N/A [33m2025-01-07 11:09:18[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Destruction of the end[0;39m 
TID: N/A [33m2025-01-07 11:09:18[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.alibaba.nacos.client.identify.CredentialWatcher[0;39m - [35m[null] CredentialWatcher is stopped[0;39m 
TID: N/A [33m2025-01-07 11:09:18[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.alibaba.nacos.client.identify.CredentialService[0;39m - [35m[null] CredentialService is freed[0;39m 
TID: N/A [33m2025-01-07 11:09:18[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop[0;39m 
TID: N/A [33m2025-01-07 11:09:18[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mo.s.orm.jpa.LocalContainerEntityManagerFactoryBean[0;39m - [35mClosing JPA EntityManagerFactory for persistence unit 'default'[0;39m 
TID: N/A [33m2025-01-07 11:09:18[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.zaxxer.hikari.HikariDataSource[0;39m - [35mHikariPool-1 - Shutdown initiated...[0;39m 
TID: N/A [33m2025-01-07 11:09:19[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.zaxxer.hikari.HikariDataSource[0;39m - [35mHikariPool-1 - Shutdown completed.[0;39m 
TID: N/A [33m2025-01-07 11:09:38[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.b.t.context.SpringBootTestContextBootstrapper[0;39m - [35mNeither @ContextConfiguration nor @ContextHierarchy found for test class [com.trs.police.comparison.api.spi.third.ControlTest], using SpringBootContextLoader[0;39m 
TID: N/A [33m2025-01-07 11:09:38[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.test.context.support.AbstractContextLoader[0;39m - [35mCould not detect default resource locations for test class [com.trs.police.comparison.api.spi.third.ControlTest]: no resource found for suffixes {-context.xml, Context.groovy}.[0;39m 
TID: N/A [33m2025-01-07 11:09:39[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.b.t.context.SpringBootTestContextBootstrapper[0;39m - [35mLoaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener][0;39m 
TID: N/A [33m2025-01-07 11:09:39[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.b.t.context.SpringBootTestContextBootstrapper[0;39m - [35mUsing TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@65045a87, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@47f4e407, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@2d1dee39, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@48d5f34e, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@fc258b1, org.springframework.test.context.support.DirtiesContextTestExecutionListener@6ff65192, org.springframework.test.context.transaction.TransactionalTestExecutionListener@2dd80673, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@4af0df05, org.springframework.test.context.event.EventPublishingTestExecutionListener@57ea113a, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@acdb094][0;39m 
TID: N/A [33m2025-01-07 11:09:43[0;39m [31m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.config.impl.LocalConfigInfoProcessor[0;39m - [35mLOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config[0;39m 
TID: N/A [33m2025-01-07 11:09:43[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.a.cloud.nacos.client.NacosPropertySourceBuilder[0;39m - [35mIgnore the empty nacos configuration and get it based on dataId[comparison-api] & group[DEFAULT_GROUP][0;39m 
TID: N/A [33m2025-01-07 11:09:43[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.config.utils.JvmUtil[0;39m - [35misMultiInstance:false[0;39m 
TID: N/A [33m2025-01-07 11:09:43[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.c.b.c.PropertySourceBootstrapConfiguration[0;39m - [35mLocated property source: [BootstrapPropertySource {name='bootstrapProperties-comparison-api.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-comparison-api,DEFAULT_GROUP'}][0;39m 
TID: N/A [33m2025-01-07 11:09:43[0;39m [31m[main][0;39m [34mINFO [0;39m [36mc.trs.police.comparison.api.spi.third.ControlTest[0;39m - [35mNo active profile set, falling back to default profiles: default[0;39m 
TID: N/A [33m2025-01-07 11:09:44[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.config.RepositoryConfigurationDelegate[0;39m - [35mMultiple Spring Data modules found, entering strict repository configuration mode![0;39m 
TID: N/A [33m2025-01-07 11:09:44[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.config.RepositoryConfigurationDelegate[0;39m - [35mBootstrapping Spring Data JPA repositories in DEFAULT mode.[0;39m 
TID: N/A [33m2025-01-07 11:09:44[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.config.RepositoryConfigurationDelegate[0;39m - [35mFinished Spring Data repository scanning in 43 ms. Found 0 JPA repository interfaces.[0;39m 
TID: N/A [33m2025-01-07 11:09:44[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.config.RepositoryConfigurationDelegate[0;39m - [35mMultiple Spring Data modules found, entering strict repository configuration mode![0;39m 
TID: N/A [33m2025-01-07 11:09:44[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.config.RepositoryConfigurationDelegate[0;39m - [35mBootstrapping Spring Data Redis repositories in DEFAULT mode.[0;39m 
TID: N/A [33m2025-01-07 11:09:44[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.config.RepositoryConfigurationDelegate[0;39m - [35mFinished Spring Data repository scanning in 22 ms. Found 0 Redis repository interfaces.[0;39m 
TID: N/A [33m2025-01-07 11:09:44[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.springframework.cloud.context.scope.GenericScope[0;39m - [35mBeanFactory id=1b50f227-1ffb-3068-83e0-113e46951f8e[0;39m 
TID: N/A [33m2025-01-07 11:09:45[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.flywaydb.core.internal.license.VersionPrinter[0;39m - [35mFlyway Community Edition 7.7.3 by Redgate[0;39m 
TID: N/A [33m2025-01-07 11:09:45[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.zaxxer.hikari.HikariDataSource[0;39m - [35mHikariPool-1 - Starting...[0;39m 
TID: N/A [33m2025-01-07 11:09:45[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.zaxxer.hikari.HikariDataSource[0;39m - [35mHikariPool-1 - Start completed.[0;39m 
TID: N/A [33m2025-01-07 11:09:46[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.f.core.internal.database.base.DatabaseType[0;39m - [35mDatabase: ****************************************** (MySQL 8.0)[0;39m 
TID: N/A [33m2025-01-07 11:09:47[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.flywaydb.core.internal.command.DbValidate[0;39m - [35mSuccessfully validated 4 migrations (execution time 00:00.580s)[0;39m 
TID: N/A [33m2025-01-07 11:09:48[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.flywaydb.core.internal.command.DbMigrate[0;39m - [35mCurrent version of schema `comparison-api`: 1.0.3[0;39m 
TID: N/A [33m2025-01-07 11:09:48[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.flywaydb.core.internal.command.DbMigrate[0;39m - [35mSchema `comparison-api` is up to date. No migration necessary.[0;39m 
TID: N/A [33m2025-01-07 11:09:49[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.hibernate.jpa.internal.util.LogHelper[0;39m - [35mHHH000204: Processing PersistenceUnitInfo [name: default][0;39m 
TID: N/A [33m2025-01-07 11:09:49[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.hibernate.Version[0;39m - [35mHHH000412: Hibernate ORM core version 5.4.33[0;39m 
TID: N/A [33m2025-01-07 11:09:49[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.hibernate.annotations.common.Version[0;39m - [35mHCANN000001: Hibernate Commons Annotations {5.1.2.Final}[0;39m 
TID: N/A [33m2025-01-07 11:09:49[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.hibernate.dialect.Dialect[0;39m - [35mHHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect[0;39m 
TID: N/A [33m2025-01-07 11:09:50[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.h.e.t.jta.platform.internal.JtaPlatformInitiator[0;39m - [35mHHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform][0;39m 
TID: N/A [33m2025-01-07 11:09:50[0;39m [31m[main][0;39m [34mINFO [0;39m [36mo.s.orm.jpa.LocalContainerEntityManagerFactoryBean[0;39m - [35mInitialized JPA EntityManagerFactory for persistence unit 'default'[0;39m 
TID: N/A [33m2025-01-07 11:09:51[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.injector.AbstractMethod[0;39m - [35m[com.trs.police.comparison.api.dao.SubscribeInfoMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert][0;39m 
TID: N/A [33m2025-01-07 11:09:51[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.hibernate.validator.internal.util.Version[0;39m - [35mHV000001: Hibernate Validator 7.0.4.Final[0;39m 
TID: N/A [33m2025-01-07 11:09:52[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.ReportTimeYWZJIDRelation".[0;39m 
TID: N/A [33m2025-01-07 11:09:55[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.springframework.cloud.commons.util.InetUtils[0;39m - [35mCannot determine local hostname[0;39m 
TID: N/A [33m2025-01-07 11:09:57[0;39m [31m[main][0;39m [34mINFO [0;39m [36morg.springframework.cloud.commons.util.InetUtils[0;39m - [35mCannot determine local hostname[0;39m 
TID: N/A [33m2025-01-07 11:09:57[0;39m [31m[main][0;39m [31mWARN [0;39m [36mc.b.mybatisplus.core.metadata.TableInfoHelper[0;39m - [35mCan not find table primary key in Class: "com.trs.police.comparison.api.entity.SensingDevice".[0;39m 
TID: N/A [33m2025-01-07 11:09:57[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35minitializer namespace from System Property :null[0;39m 
TID: N/A [33m2025-01-07 11:09:57[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35minitializer namespace from System Environment :null[0;39m 
TID: N/A [33m2025-01-07 11:09:57[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35minitializer namespace from System Property :null[0;39m 
TID: N/A [33m2025-01-07 11:09:58[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mnew ips(1) service: DEFAULT_GROUP@@comparison-api@@DEFAULT -> [{"instanceId":"*********#10011#DEFAULT#DEFAULT_GROUP@@comparison-api","ip":"*********","port":10011,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@comparison-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}][0;39m 
TID: N/A [33m2025-01-07 11:09:58[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcurrent ips:(1) service: DEFAULT_GROUP@@comparison-api@@DEFAULT -> [{"instanceId":"*********#10011#DEFAULT#DEFAULT_GROUP@@comparison-api","ip":"*********","port":10011,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@comparison-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}][0;39m 
TID: N/A [33m2025-01-07 11:09:58[0;39m [31m[main][0;39m [34mINFO [0;39m [36mc.trs.police.comparison.api.spi.third.ControlTest[0;39m - [35mStarted ControlTest in 18.808 seconds (JVM running for 19.71)[0;39m 
TID: N/A [33m2025-01-07 11:09:58[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.config.impl.ClientWorker[0;39m - [35m[fixed-nacos-svc_8848-ys-dev] [subscribe] comparison-api.properties+DEFAULT_GROUP+ys-dev[0;39m 
TID: N/A [33m2025-01-07 11:09:58[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.config.impl.CacheData[0;39m - [35m[fixed-nacos-svc_8848-ys-dev] [add-listener] ok, tenant=ys-dev, dataId=comparison-api.properties, group=DEFAULT_GROUP, cnt=1[0;39m 
TID: N/A [33m2025-01-07 11:09:58[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.config.impl.ClientWorker[0;39m - [35m[fixed-nacos-svc_8848-ys-dev] [subscribe] comparison-api+DEFAULT_GROUP+ys-dev[0;39m 
TID: N/A [33m2025-01-07 11:09:58[0;39m [31m[main][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.config.impl.CacheData[0;39m - [35m[fixed-nacos-svc_8848-ys-dev] [add-listener] ok, tenant=ys-dev, dataId=comparison-api, group=DEFAULT_GROUP, cnt=1[0;39m 
TID: N/A [33m2025-01-07 11:09:58[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Start destroying Publisher[0;39m 
TID: N/A [33m2025-01-07 11:09:58[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Start destroying common HttpClient[0;39m 
TID: N/A [33m2025-01-07 11:09:58[0;39m [31m[Thread-12][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.notify.NotifyCenter[0;39m - [35m[NotifyCenter] Destruction of the end[0;39m 
TID: N/A [33m2025-01-07 11:09:58[0;39m [31m[Thread-1][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.common.http.HttpClientBeanHolder[0;39m - [35m[HttpClientBeanHolder] Destruction of the end[0;39m 
TID: N/A [33m2025-01-07 11:09:58[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin[0;39m 
TID: N/A [33m2025-01-07 11:09:58[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop[0;39m 
TID: N/A [33m2025-01-07 11:09:58[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.core.HostReactor do shutdown begin[0;39m 
TID: N/A [33m2025-01-07 11:09:59[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin[0;39m 
TID: N/A [33m2025-01-07 11:10:02[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop[0;39m 
TID: N/A [33m2025-01-07 11:10:02[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin[0;39m 
TID: N/A [33m2025-01-07 11:10:05[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop[0;39m 
TID: N/A [33m2025-01-07 11:10:05[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.core.HostReactor do shutdown stop[0;39m 
TID: N/A [33m2025-01-07 11:10:05[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin[0;39m 
TID: N/A [33m2025-01-07 11:10:05[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Start destroying NacosRestTemplate[0;39m 
TID: N/A [33m2025-01-07 11:10:05[0;39m [31m[SpringApplicationShutdownHook][0;39m [31mWARN [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35m[NamingHttpClientManager] Destruction of the end[0;39m 
TID: N/A [33m2025-01-07 11:10:05[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.alibaba.nacos.client.identify.CredentialWatcher[0;39m - [35m[null] CredentialWatcher is stopped[0;39m 
TID: N/A [33m2025-01-07 11:10:05[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.alibaba.nacos.client.identify.CredentialService[0;39m - [35m[null] CredentialService is freed[0;39m 
TID: N/A [33m2025-01-07 11:10:05[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.alibaba.nacos.client.naming[0;39m - [35mcom.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop[0;39m 
TID: N/A [33m2025-01-07 11:10:05[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mo.s.orm.jpa.LocalContainerEntityManagerFactoryBean[0;39m - [35mClosing JPA EntityManagerFactory for persistence unit 'default'[0;39m 
TID: N/A [33m2025-01-07 11:10:05[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.zaxxer.hikari.HikariDataSource[0;39m - [35mHikariPool-1 - Shutdown initiated...[0;39m 
TID: N/A [33m2025-01-07 11:10:06[0;39m [31m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mcom.zaxxer.hikari.HikariDataSource[0;39m - [35mHikariPool-1 - Shutdown completed.[0;39m 
