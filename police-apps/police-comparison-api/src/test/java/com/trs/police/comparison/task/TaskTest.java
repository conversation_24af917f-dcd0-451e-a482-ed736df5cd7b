package com.trs.police.comparison.task;

import com.trs.police.comparison.api.ComparisonApiApplication;
import com.trs.police.comparison.api.schedule.ZgDahuaDeviceInfoPullTask;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2025/4/29 10:30
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ComparisonApiApplication.class)
public class TaskTest {

    @Resource
    private ZgDahuaDeviceInfoPullTask task;

    /**
     * 测试拉取大华设备
     */
    @Test
    public void test() {
        task.pullAndSave();
    }

}
