package com.trs.police.comparison.api.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.trs.police.comparison.api.exception.BizException;
import com.trs.police.comparison.api.utils.DataServiceHelper;
import com.trs.police.comparison.api.dao.SubscribeAreaInfoMapper;
import com.trs.police.comparison.api.entity.SubscribeAreaInfo;
import com.trs.police.comparison.api.exception.UnauthorizedException;
import com.trs.police.comparison.api.entity.request.AreaFindRequest;
import com.trs.police.comparison.api.entity.response.AreaFindResponse;
import com.trs.police.comparison.api.service.AreaFindService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022年09月15日 17
 */
@Service
@Slf4j
@Validated
public class AreaFindServiceImpl implements AreaFindService {
    @Resource
    private SubscribeAreaInfoMapper subscribeAreaInfoMapper;

    @Override
    public AreaFindResponse execute(AreaFindRequest request) throws BizException, UnauthorizedException, InterruptedException, JsonProcessingException {
        final String userAccount = DataServiceHelper.UserSafeBox.getUserAccount();
        if (StringUtils.isEmpty(userAccount) || StringUtils.isEmpty(request.getAreaId())) {
            return new AreaFindResponse();
        }
        final List<SubscribeAreaInfo> subscribeAreaInfos = subscribeAreaInfoMapper.selectAreaIdsByUserNameAndAreaId(
                userAccount,
                request.getAreaId());
        AreaFindResponse response = new AreaFindResponse();
        response.setAreaInfo(subscribeAreaInfos);
        return response;
    }
}
