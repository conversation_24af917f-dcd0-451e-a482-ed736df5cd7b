package com.trs.police.comparison.api.service;

import com.trs.police.comparison.api.entity.request.AreaUpsertRequest;
import com.trs.police.comparison.api.entity.response.AreaUpsertResponse;

/**
 * 比对订阅服务
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface AreaUpsertService
    extends DataServiceEntranceAPIBaseService<AreaUpsertRequest, AreaUpsertResponse> {

  @Override
  default String getServiceId() {
    return "addAreaUpsert";
  }

  @Override
  default Class<AreaUpsertRequest> getRequestClass() {
    return AreaUpsertRequest.class;
  }

  @Override
  default Class<AreaUpsertResponse> getResponseClass() {
    return AreaUpsertResponse.class;
  }
}
