package com.trs.police.comparison.api.entity.request;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022年09月16日 09
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AreaDeleteRequest implements DataServiceAPIBaseRequest {

    private static final long serialVersionUID = 7304115229747767555L;

    /**
     * 区域id
     */
    @NotNull(message = "区域id不可为空")
    private String areaId;

}
