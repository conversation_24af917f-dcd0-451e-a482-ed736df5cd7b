package com.trs.police.comparison.api.entity.response;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 比对订阅查询服务返回实体
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/15 17:21
 */
@Data
@NoArgsConstructor
public class CompareSubscriptionResponse extends NonIdempotentStandardResponse implements DataServiceAPIBaseResponse {

  public static CompareSubscriptionResponse success() {
    CompareSubscriptionResponse response = new CompareSubscriptionResponse();
    response.setCode(200);
    return response;
  }

  public static CompareSubscriptionResponse success(String message) {
    CompareSubscriptionResponse response = new CompareSubscriptionResponse();
    response.setCode(200);
    response.setMessage(message);
    return response;
  }

  public static CompareSubscriptionResponse failed(String message) {
    CompareSubscriptionResponse response = new CompareSubscriptionResponse();
    response.setCode(503);
    response.setMessage(message);
    return response;
  }
}
