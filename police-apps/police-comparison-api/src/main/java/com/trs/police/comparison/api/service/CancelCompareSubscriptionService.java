package com.trs.police.comparison.api.service;

import com.trs.police.comparison.api.entity.request.CancelCompareSubscriptionRequest;
import com.trs.police.comparison.api.entity.response.CancelCompareSubscriptionResponse;

/**
 * <AUTHOR>
 * @version 1.0
 */
public interface CancelCompareSubscriptionService
    extends DataServiceEntranceAPIBaseService<
        CancelCompareSubscriptionRequest, CancelCompareSubscriptionResponse> {

  @Override
  default String getServiceId() {
    return "cancelComparison";
  }

  @Override
  default Class<CancelCompareSubscriptionRequest> getRequestClass() {
    return CancelCompareSubscriptionRequest.class;
  }

  @Override
  default Class<CancelCompareSubscriptionResponse> getResponseClass() {
    return CancelCompareSubscriptionResponse.class;
  }
}
