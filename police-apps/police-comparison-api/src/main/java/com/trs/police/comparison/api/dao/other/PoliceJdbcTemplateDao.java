package com.trs.police.comparison.api.dao.other;

import com.trs.police.comparison.api.config.properties.JdbcTemplateProperties;
import com.trs.police.comparison.api.entity.SensingDevice;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/17 9:46
 */
@Slf4j
@Repository
public class PoliceJdbcTemplateDao {


    private final NamedParameterJdbcTemplate myJdbcTemplate;

    public PoliceJdbcTemplateDao(JdbcTemplateProperties properties) {
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setDriverClassName(properties.getDriverClassName());
        dataSource.setJdbcUrl(properties.getUrl());
        dataSource.setUsername(properties.getUsername());
        dataSource.setPassword(properties.getPassword());
        dataSource.setMaximumPoolSize(properties.getMaxPoolSize());
        myJdbcTemplate = new NamedParameterJdbcTemplate(new JdbcTemplate(dataSource));
    }

    /**
     * 批量插入或更新
     * @param sensingDeviceList 设备列表
     */
    public void insertOrUpdate(List<SensingDevice> sensingDeviceList) {
        try {
            String sqlTemplate = "INSERT\n" +
                    "        INTO\n" +
                    "            police.t_control_warning_source (create_user_id,\n" +
                    "                                             create_dept_id,\n" +
                    "                                             create_time,\n" +
                    "                                             update_user_id,\n" +
                    "                                             update_dept_id,\n" +
                    "                                             update_time,\n" +
                    "                                             name,\n" +
                    "                                             code,\n" +
                    "                                             `type`,\n" +
                    "                                             category,\n" +
                    "                                             nesting_category,\n" +
                    "                                             district_name,\n" +
                    "                                             district_code,\n" +
                    "                                             dept,\n" +
                    "                                             address,\n" +
                    "                                             `point`,\n" +
                    "                                             unique_key,\n" +
                    "                                             is_legal,\n" +
                    "                                             source_id,\n" +
                    "                                             is_local,\n" +
                    "                                             source_provider)\n" +
                    "        VALUES\n" +
                    "            (1,\n" +
                    "             1,\n" +
                    "             :xxrksj,\n" +
                    "             1,\n" +
                    "             1,\n" +
                    "             :xxrksj,\n" +
                    "             :gzymc,\n" +
                    "             :gzybh,\n" +
                    "             :gzylx,\n" +
                    "             NULL,\n" +
                    "             NULL,\n" +
                    "             :xzqhmc,\n" +
                    "             :xzqhdm,\n" +
                    "             NULL,\n" +
                    "             :dzmc,\n" +
                    "             ST_GeomFromText(:point, 4326),\n" +
                    "             :uniqueKey,\n" +
                    "             1,\n" +
                    "             NULL,\n" +
                    "             1,\n" +
                    "             NULL) ON DUPLICATE KEY UPDATE name = :gzymc,district_name = :xzqhmc,\n" +
                    "            address = :dzmc,\n" +
                    "            type = :gzylx,\n" +
                    "            district_code = :xzqhdm,\n" +
                    "            point = ST_GeomFromText(:point, 4326),\n" +
                    "            update_time = :xxrksj";

            ArrayList<MapSqlParameterSource> parameterList = new ArrayList<>();
            for (SensingDevice sensingDevice : sensingDeviceList) {
                MapSqlParameterSource insertParameter = new MapSqlParameterSource();
                HashMap<String, Object> parameter = new HashMap<>(10);
                parameter.put(SensingDevice.XXRKSJ, sensingDevice.getXxrksj().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                parameter.put(SensingDevice.GZYMC, sensingDevice.getGzymc());
                parameter.put(SensingDevice.GZYLX, sensingDevice.getGzylx());
                parameter.put(SensingDevice.GZYBH, sensingDevice.getGzybh());
                parameter.put(SensingDevice.XZQHMC, sensingDevice.getXzqhmc());
                parameter.put(SensingDevice.XZQHDM, sensingDevice.getXzqhdm());
                parameter.put(SensingDevice.DZMC, sensingDevice.getDzmc());
                parameter.put(SensingDevice.POINT, sensingDevice.getPoint());
                parameter.put(SensingDevice.UNIQUE_KEY, sensingDevice.getUniqueKey());
                insertParameter.addValues(parameter);
                parameterList.add(insertParameter);
            }

            int[] ints = myJdbcTemplate.batchUpdate(sqlTemplate, parameterList.toArray(new MapSqlParameterSource[0]));
            log.info("批量插入或更新成功！结果 {}", ints);
        } catch (Exception e) {
            log.error("批量插入或更新失败！", e);
        }
    }
}