package com.trs.police.comparison.api.config.properties;

import lombok.Data;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024/11/11 14:19
 */
@Data
@Component
@ConfigurationProperties(prefix = ZgDaHuaTechnologyProperties.PREFIX)
@ConditionalOnProperty(prefix = ZgDaHuaTechnologyProperties.PREFIX, name = "enabled", havingValue = "true")
public class ZgDaHuaTechnologyProperties {

    public static final String PREFIX = "com.trs.subscription.zgdahua";

    private Boolean enabled = false;

    private String authorizePath;

    /**
     * 请求订阅
     */
    private String requestSubscribe;

    /**
     * 订阅的人像库id
     */
    private String subscribeFaceRepositoryId;

    /**
     * 人像布控任务订阅的任务id
     */
    private String subscribeFaceSurveyId;

    /**
     * 获取组织树
     */
    private String deviceAllOrgPath;

    /**
     * 获取设备列表
     */
    private String devicesInfoPath;

    /**
     * 取消订阅路径
     */
    private String cancelSubscribePath = "/members/remove";

    private String userName;

    private String password;

    private String endpoint;

    /**
     * 人像库ID
     */
    private String faceRepositoryId;

    /**
     * 车辆库ID
     */
    private String vehicleRepositoryId;

    /**
     * 车辆号牌类型 默认小汽车
     */
    private String vehicleNumberType;

    /**
     * 第三方接口版本
     */
    private String version;

    private String clientType;
}
