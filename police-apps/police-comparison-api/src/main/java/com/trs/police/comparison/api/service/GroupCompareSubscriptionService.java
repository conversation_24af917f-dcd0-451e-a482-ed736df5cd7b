package com.trs.police.comparison.api.service;

import com.trs.police.comparison.api.entity.request.GroupCompareSubscriptionRequest;
import com.trs.police.comparison.api.entity.response.GroupCompareSubscriptionResponse;

/**
 * 群体布控数据服务
 *
 * <AUTHOR>
 * @version 1.0
 **/
public interface GroupCompareSubscriptionService extends DataServiceEntranceAPIBaseService<GroupCompareSubscriptionRequest, GroupCompareSubscriptionResponse> {

  @Override
  default String getServiceId() {
    return "groupComparison";
  }

  @Override
  default Class<GroupCompareSubscriptionRequest> getRequestClass() {
    return GroupCompareSubscriptionRequest.class;
  }

  @Override
  default Class<GroupCompareSubscriptionResponse> getResponseClass() {
    return GroupCompareSubscriptionResponse.class;
  }
}
