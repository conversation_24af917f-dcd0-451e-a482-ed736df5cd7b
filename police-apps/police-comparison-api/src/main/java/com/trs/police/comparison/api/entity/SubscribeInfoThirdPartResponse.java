package com.trs.police.comparison.api.entity;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Getter;
import lombok.Setter;

/**
 * 布控推送第三方返回结果, 便于记录推送状态
 *
 * <AUTHOR>
 * @since 2024/11/12 10:13
 */
@Setter
@Getter
public class SubscribeInfoThirdPartResponse {

    public SubscribeInfoThirdPartResponse(String thirdPartName){
        this.thirdPartName = thirdPartName;
    }

    /**
     * 默认推送失败
     */
   boolean pushStatus;

    /**
     * 订阅信息表主键
     */
    private Long subscribeInfoId;

    /**
     * 第三方服务名称
     */
    private String thirdPartName;

    /**
     * 第三方服务推送结果
     */
    private JsonNode subscribeThirdInfo;
}
