package com.trs.police.comparison.api.service;

import com.trs.police.comparison.api.entity.request.SelectMemberCompareSubscriptionRequest;
import com.trs.police.comparison.api.entity.response.SelectMemberCompareSubscriptionResponse;

/**
 * 群体布控人员查询数据服务
 *
 * <AUTHOR>
 * @version 1.0
 **/
public interface SelectGroupMemberCompareSubscriptionService extends DataServiceEntranceAPIBaseService<SelectMemberCompareSubscriptionRequest, SelectMemberCompareSubscriptionResponse> {

  @Override
  default String getServiceId() {
    return "queryGroupMemberComparison";
  }

  @Override
  default Class<SelectMemberCompareSubscriptionRequest> getRequestClass() {
    return SelectMemberCompareSubscriptionRequest.class;
  }

  @Override
  default Class<SelectMemberCompareSubscriptionResponse> getResponseClass() {
    return SelectMemberCompareSubscriptionResponse.class;
  }
}
