package com.trs.police.comparison.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.comparison.api.dao.handler.Set2JsonArrayTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * t_subscribe_person_info
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName(value = "t_subscribe_person_info", autoResultMap = true, resultMap = "BaseResultMap")
public class SubscribePersonInfo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 人员标识符
     */
    private String identifier;
    /**
     * 标识符类型
     */
    private int identifierType;

    /**
     * 户籍地详细地址
     */
    private String registeredResidenceDetail;

    /**
     * 现住址详细地址
     */
    private String currentResidenceDetail;

    /**
     * 订阅用户
     */
    private String userName;
    /**
     * 初次订阅时间
     */
    private LocalDateTime createTime;
    /**
     * 最后订阅时间
     */
    private LocalDateTime updateTime;
    /**
     * 人员标签集合
     */
    @TableField(typeHandler = Set2JsonArrayTypeHandler.class)
    private Set<String> tags = new HashSet<>();

}
