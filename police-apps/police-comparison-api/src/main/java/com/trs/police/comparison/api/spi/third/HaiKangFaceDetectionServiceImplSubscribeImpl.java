package com.trs.police.comparison.api.spi.third;


import com.alibaba.fastjson.JSONObject;
import com.hikvision.artemis.sdk.ArtemisHttpUtil;
import com.hikvision.artemis.sdk.config.ArtemisConfig;
import com.trs.police.comparison.api.config.properties.HaiKangProperties;
import com.trs.police.comparison.api.constant.Constants;
import com.trs.police.comparison.api.constant.enums.IdentifierTypeEnum;
import com.trs.police.comparison.api.dao.SubscribeInfoDetailMapper;
import com.trs.police.comparison.api.entity.SubscribeInfo;
import com.trs.police.comparison.api.entity.SubscribeInfoThirdPartDetail;
import com.trs.police.comparison.api.entity.dto.AuditInformation;
import com.trs.police.comparison.api.service.SubscribeIdentifierBaseService;
import com.trs.police.comparison.api.utils.ProxyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.skywalking.apm.toolkit.trace.ActiveSpan;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 海康人像布控
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@ConditionalOnBean(value = {HaiKangProperties.class})
public class HaiKangFaceDetectionServiceImplSubscribeImpl implements SubscribeIdentifierBaseService {

    @Autowired
    private HaiKangProperties haiKangProperties;

    @Resource
    private SubscribeInfoDetailMapper subscribeInfoDetailMapper;

    /**
     * 人脸名单新增接口path
     */
    private static final String ADD_PERSON_PATH = "/artemis/api/aiapplication/v2/face/addPerson";
    /**
     * 名单库人员属性检索接口path
     */
    private static final String QUERY_PERSON_PATH = "/artemis/api/aiapplication/v2/face/queryPersonByAttrWithPage";
    /**
     * 单张人脸图片检测建模接口path
     */
    private static final String IMAGE_DETECT_PATH = "/artemis/api/aibasic/v1/face/imageDetectionAndModeling";

    /**
     * 名单库是否已有该人像
     * @param subscribeInfo 身份证号
     * @return true OR false
     */
    private Boolean existPortraitPicture (SubscribeInfo subscribeInfo, Map<Long, List<SubscribeInfoThirdPartDetail>> detailMap){
        String certificateNumber = subscribeInfo.getIdentifier();
        baseSet();
        Map<String, String> path = new HashMap<>(1) {
            {
                put("https://", QUERY_PERSON_PATH);
            }
        };

        JSONObject jsonBody = new JSONObject();
        jsonBody.put("pageNo",1);
        jsonBody.put("pageSize",2);
        jsonBody.put("personLibId",haiKangProperties.getPersonLibId());
        jsonBody.put("certificateType",111);
        jsonBody.put("certificateNumber",certificateNumber);
        String body = jsonBody.toString();

        String result = ArtemisHttpUtil.doPostStringArtemis(path, body, null,
                null,"application/json");
        if (Objects.isNull(result)) {
            log.info("调用海康名单库人员属性检索接口无响应内容");
            return false;
        }
        JSONObject jsonResult = JSONObject.parseObject(result);
        if(checkResponseSuccessful(jsonResult)){
            log.info("请求海康名单库人员属性检索接口成功,响应结果:{}", result);
            JSONObject jsonData = jsonResult.getJSONObject("data");
            Integer total = jsonData.getInteger("total");
            log.info("检索结果total为[{}]",total);
            if(total > 0){
                log.info("检测结果已存在该人像");
                // 修改状态为已推送
                List<SubscribeInfoThirdPartDetail> details = detailMap.get(subscribeInfo.getId());
                if (details == null) {
                    SubscribeInfoThirdPartDetail subscribeInfoThirdPartDetail = new SubscribeInfoThirdPartDetail();
                    subscribeInfoThirdPartDetail.setSubscribeInfoId(subscribeInfo.getId());
                    subscribeInfoThirdPartDetail.setPushTime(LocalDateTime.now());
                    subscribeInfoThirdPartDetail.setFirstPushTime(LocalDateTime.now());
                    subscribeInfoThirdPartDetail.setThirdPartName(ProxyUtils.getOriginalType(this));
                    subscribeInfoThirdPartDetail.setPushCount(0);
                    subscribeInfoThirdPartDetail.setPushStatus(Constants.PUSH_SUCCESS);
                    subscribeInfoDetailMapper.insert(subscribeInfoThirdPartDetail);
                } else {
                    for (SubscribeInfoThirdPartDetail detail : details) {
                        detail.setPushStatus(Constants.PUSH_SUCCESS);
                    }
                    subscribeInfoDetailMapper.batchUpdatePushStatusByPrimaryKey(details);
                }
                return true;
            }
            log.info("检测结果不存在该人像");
            return false;
        }
        log.warn("请求依图检索人像库失败, 请求体:{}, 响应结果:{}",body,result);
        return false;
    }

    private void baseSet() {
        ArtemisConfig artemisConfig = new ArtemisConfig();
        //平台IP 和端口
        artemisConfig.setHost(haiKangProperties.getHost());
        //合作方key
        artemisConfig.setAppKey(haiKangProperties.getAppKey());
        //合作方 Secret
        artemisConfig.setAppSecret(haiKangProperties.getAppSecret());
    }

    /**
     * 检测人脸图片建模是否符合要求
     * @param imageData 人像数据base64
     * @return true OR false
     */
    public Boolean checkFacePic(String imageData){
        baseSet();
        Map<String, String> path = new HashMap<>(1) {
            {
                put("https://", IMAGE_DETECT_PATH);
            }
        };
        JSONObject jsonBody = new JSONObject();
        jsonBody.put("imageData",imageData);
        String body = jsonBody.toString();
        String result = ArtemisHttpUtil.doPostStringArtemis(path, body, null,
                null,"application/json");
        if (Objects.isNull(result)) {
            log.info("调用海康单张人脸图片检测建模接口无响应内容");
            return false;
        }
        JSONObject jsonResult = JSONObject.parseObject(result);
        if(checkResponseSuccessful(jsonResult)){
            log.info("请求海康单张人脸图片检测建模接口成功,响应结果:{}", result);
            String jsonMsg = jsonResult.getString("msg");
            JSONObject jsonData = jsonResult.getJSONObject("data");
            Integer total = jsonData.getInteger("total");
            if( total.equals(1) && "SUCCESS".equals(jsonMsg)){
                log.info("海康人脸检测通过！");
                return true;
            }
            log.info("海康人脸检测不通过！");
        }
        log.warn("请求海康单张人脸图片检测建模接口失败, 请求体:{}, 响应结果:{}",body,result);
        return false;
    }

    /**
     * 人脸名单新增
     * @param subscribeInfo 人脸信息
     */
    public Boolean addPerson(SubscribeInfo subscribeInfo){
        baseSet();
        Map<String, String> path = new HashMap<>(1) {
            {
                put("https://", ADD_PERSON_PATH);
            }
        };
        JSONObject jsonBody = new JSONObject();
        jsonBody.put("personLibId",haiKangProperties.getPersonLibId());
        jsonBody.put("name",subscribeInfo.getIdentifier());
        jsonBody.put("certificateType",111);
        jsonBody.put("certificateNumber",subscribeInfo.getIdentifier());
        jsonBody.put("imageData",subscribeInfo.getFaceInfo());
        String body = jsonBody.toString();
        log.info("开始调用海康人脸名单新增接口，入参body为[{}]",body);
        String result = ArtemisHttpUtil.doPostStringArtemis(path, body, null,
                null,"application/json");
        if (Objects.isNull(result)) {
            log.info("调用海康人脸名单新增接口无响应内容");
            return false;
        }
        JSONObject jsonResult = JSONObject.parseObject(result);
        if(checkResponseSuccessful(jsonResult)) {
            log.info("请求海康人脸名单新增接口成功,响应结果:{}", result);
            if (IdentifierTypeEnum.ID_CARD.getCode() == subscribeInfo.getIdentifierType()) {
                ActiveSpan.tag("idCard", subscribeInfo.getIdentifier());
            }
        return true;
        }
        log.warn("请求海康人脸名单新增接口失败, 请求体:{}, 响应结果:{}",body,result);
        return false;
    }

    /**
     * 判断返回值是否成功
     * @param jsonResult 返回结果对象
     * @return true OR false
     */
    private boolean checkResponseSuccessful(JSONObject jsonResult){
        final String code = jsonResult.getString("code");
        return "0".equals(code);
    }

    /**
     * 获取ys 码表配置的第三方平台英文码值
     *
     * @return 第三方平台英文码值
     */
    @Override
    public Integer getThirdPartNameCode() {
        return 6;
    }

    @Override
    @Trace(operationName = "com.trs.police.comparison.api.spi.third.HaiKangFaceDetectionServiceImplSubscribeImpl.subscribeIdentifier")
    public void subscribeIdentifier(List<SubscribeInfo> subscribeInfoList, String thirdPartName, AuditInformation auditInformation) {
        try {
            if (subscribeInfoList == null) {
                return;
            }
            ArrayList<SubscribeInfoThirdPartDetail> newSubscribeSuccesses = new ArrayList<>();
            ArrayList<SubscribeInfoThirdPartDetail> subscribeSuccesses = new ArrayList<>();
            List<SubscribeInfoThirdPartDetail> subscribeInfoThirdPartDetailList = subscribeInfoDetailMapper.selectFailedByInfoIds(subscribeInfoList.stream().map(SubscribeInfo::getId).collect(Collectors.toList()), thirdPartName);
            Map<Long, List<SubscribeInfoThirdPartDetail>> detailMap = subscribeInfoThirdPartDetailList.stream().collect(Collectors.groupingBy(SubscribeInfoThirdPartDetail::getSubscribeInfoId));
            for (SubscribeInfo subscribeInfo : subscribeInfoList) {
                //只推送有人像的数据,为空默认成功
                if (StringUtils.isEmpty(subscribeInfo.getFaceInfo())) {
                    SubscribeInfoThirdPartDetail subscribeInfoThirdPartDetail = new SubscribeInfoThirdPartDetail();
                    subscribeInfoThirdPartDetail.setSubscribeInfoId(subscribeInfo.getId());
                    subscribeInfoThirdPartDetail.setPushTime(LocalDateTime.now());
                    subscribeInfoThirdPartDetail.setFirstPushTime(LocalDateTime.now());
                    subscribeInfoThirdPartDetail.setThirdPartName(thirdPartName);
                    subscribeInfoThirdPartDetail.setPushCount(1);
                    subscribeInfoThirdPartDetail.setPushStatus(Constants.PUSH_SUCCESS);
                    newSubscribeSuccesses.add(subscribeInfoThirdPartDetail);
                    continue;
                }
                String personId = subscribeInfo.getIdentifier();
                // 只对证件号码发起布控订阅，非证件号默认成功
                if (1 != subscribeInfo.getIdentifierType()) {
                    SubscribeInfoThirdPartDetail subscribeInfoThirdPartDetail = new SubscribeInfoThirdPartDetail();
                    subscribeInfoThirdPartDetail.setSubscribeInfoId(subscribeInfo.getId());
                    subscribeInfoThirdPartDetail.setPushTime(LocalDateTime.now());
                    subscribeInfoThirdPartDetail.setFirstPushTime(LocalDateTime.now());
                    subscribeInfoThirdPartDetail.setThirdPartName(thirdPartName);
                    subscribeInfoThirdPartDetail.setPushCount(1);
                    subscribeInfoThirdPartDetail.setPushStatus(Constants.PUSH_SUCCESS);
                    newSubscribeSuccesses.add(subscribeInfoThirdPartDetail);
                    continue;
                }
                // 查询名单库中是否已经含有该人像，不含有该人像且人像质量能够检测出建模时才推送
                if (!existPortraitPicture(subscribeInfo, detailMap) && checkFacePic(subscribeInfo.getFaceInfo())) {
                    log.info("人像不存在且人像图片符合要求，开始推送增加人员名单");
                    List<SubscribeInfoThirdPartDetail> details = detailMap.get(subscribeInfo.getId());
                    if (addPerson(subscribeInfo)) {
                        if (CollectionUtils.isEmpty(details)) {
                            //从未推送过
                            SubscribeInfoThirdPartDetail subscribeInfoThirdPartDetail = new SubscribeInfoThirdPartDetail();
                            subscribeInfoThirdPartDetail.setSubscribeInfoId(subscribeInfo.getId());
                            subscribeInfoThirdPartDetail.setPushTime(LocalDateTime.now());
                            subscribeInfoThirdPartDetail.setFirstPushTime(LocalDateTime.now());
                            subscribeInfoThirdPartDetail.setThirdPartName(thirdPartName);
                            subscribeInfoThirdPartDetail.setPushCount(1);
                            subscribeInfoThirdPartDetail.setPushStatus(Constants.PUSH_SUCCESS);
                            newSubscribeSuccesses.add(subscribeInfoThirdPartDetail);
                        } else {
                            //推送失败过
                            SubscribeInfoThirdPartDetail subscribeInfoThirdPartDetail = details.get(0);
                            subscribeInfoThirdPartDetail.setPushTime(LocalDateTime.now());
                            Integer pushCount = subscribeInfoThirdPartDetail.getPushCount() + 1;
                            subscribeInfoThirdPartDetail.setPushCount(pushCount);
                            subscribeInfoThirdPartDetail.setPushStatus(Constants.PUSH_SUCCESS);
                            subscribeSuccesses.add(subscribeInfoThirdPartDetail);
                        }
                    } else {
                        //推送失败了
                        if (CollectionUtils.isEmpty(details)) {
                            //从未推送过
                            SubscribeInfoThirdPartDetail subscribeInfoThirdPartDetail = new SubscribeInfoThirdPartDetail();
                            subscribeInfoThirdPartDetail.setSubscribeInfoId(subscribeInfo.getId());
                            subscribeInfoThirdPartDetail.setPushTime(LocalDateTime.now());
                            subscribeInfoThirdPartDetail.setFirstPushTime(LocalDateTime.now());
                            subscribeInfoThirdPartDetail.setThirdPartName(thirdPartName);
                            subscribeInfoThirdPartDetail.setPushCount(1);
                            subscribeInfoThirdPartDetail.setPushStatus(Constants.PUSH_FAILED);
                            newSubscribeSuccesses.add(subscribeInfoThirdPartDetail);
                        } else {
                            //推送失败过
                            SubscribeInfoThirdPartDetail subscribeInfoThirdPartDetail = details.get(0);
                            subscribeInfoThirdPartDetail.setPushTime(LocalDateTime.now());
                            Integer pushCount = subscribeInfoThirdPartDetail.getPushCount() + 1;
                            subscribeInfoThirdPartDetail.setPushCount(pushCount);
                            subscribeInfoThirdPartDetail.setPushStatus(Constants.PUSH_FAILED);
                            subscribeSuccesses.add(subscribeInfoThirdPartDetail);
                        }
                    }
                } else {
                    //已经存在人像，人像质量不合格默认成功
                    SubscribeInfoThirdPartDetail subscribeInfoThirdPartDetail = new SubscribeInfoThirdPartDetail();
                    subscribeInfoThirdPartDetail.setSubscribeInfoId(subscribeInfo.getId());
                    subscribeInfoThirdPartDetail.setPushTime(LocalDateTime.now());
                    subscribeInfoThirdPartDetail.setThirdPartName(thirdPartName);
                    subscribeInfoThirdPartDetail.setPushCount(1);
                    subscribeInfoThirdPartDetail.setPushStatus(Constants.PUSH_SUCCESS);
                    newSubscribeSuccesses.add(subscribeInfoThirdPartDetail);
                }
            }
            //保存推送状态
            if (!newSubscribeSuccesses.isEmpty()) {
                subscribeInfoDetailMapper.insertBatch(newSubscribeSuccesses);
            }
            //根据主键更新推送状态
            if (!subscribeSuccesses.isEmpty()) {
                subscribeInfoDetailMapper.batchUpdatePushStatusByPrimaryKey(subscribeSuccesses);
            }
        } catch (Exception e) {
            log.error("海康人像布控执行出错", e);
        }
    }

    @Override
    public void cancelSubscribeIdentifier(List<SubscribeInfo> subscribeInfoList, String subscribeCode, String userAccount, AuditInformation auditInformation) {

    }
}
