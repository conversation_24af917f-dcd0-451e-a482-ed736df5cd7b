package com.trs.police.comparison.api.spi.third;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONPath;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.trs.police.comparison.api.config.properties.SenseTimeProperties;
import com.trs.police.comparison.api.constant.Constants;
import com.trs.police.comparison.api.constant.enums.IdentifierTypeEnum;
import com.trs.police.comparison.api.dao.SubscribeInfoDetailMapper;
import com.trs.police.comparison.api.entity.SubscribeInfo;
import com.trs.police.comparison.api.entity.SubscribeInfoThirdPartDetail;
import com.trs.police.comparison.api.entity.dto.AuditInformation;
import com.trs.police.comparison.api.entity.dto.KuangShiResultInfo;
import com.trs.police.comparison.api.entity.dto.SenseTimeSubscribeResultInfo;
import com.trs.police.comparison.api.entity.dto.SenseTimeFaceInfo;
import com.trs.police.comparison.api.entity.dto.SenseTimeFaceInfo.ImageInfo;
import com.trs.police.comparison.api.service.SubscribeIdentifierBaseService;
import com.trs.police.comparison.api.utils.ProxyUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.text.StringSubstitutor;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpStatus;
import org.apache.http.entity.ContentType;
import org.apache.skywalking.apm.toolkit.trace.ActiveSpan;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商汤人像布控
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@ConditionalOnBean(value = {SenseTimeProperties.class})
public class SenseTimeFaceDetectionServiceImplSubscribeImpl implements SubscribeIdentifierBaseService {
    @Resource
    private SubscribeInfoDetailMapper subscribeInfoDetailMapper;
    @Autowired
    private SenseTimeProperties senseTimeProperties;

    @Resource
    private OkHttpClient okHttpClient;

    private Token token;

    /**
     * 上传图片接口
     */
    private static final String UPLOAD_IMAGE_API = "/whale-openapi/whale-general-service/upload";

    /**
     * 新增布控接口 删除人像也是它
     */
    private static final String SUBSCRIPTION_API = "/whale-openapi/portraits";

    /**
     * 获取token接口
     */
    private static final String TOKEN_API = "/uums/auth/token";
    @Qualifier("jacksonObjectMapper")
    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 发起布控
     *
     * @param imageUrl 图片地址
     * @param identifier 特征值
     * @return java.lang.Boolean
     **/
    private Pair<Boolean, String> sendSubscriptionRequest(String imageUrl, String identifier) {
        log.info("开始请求商汤布控");
        String accessToken = getAccessToken();
        if (StringUtils.isEmpty(accessToken)) {
            return Pair.of(Boolean.FALSE, "{}");
        }

        List<SenseTimeFaceInfo.ImageInfo> images = new ArrayList<>();
        images.add(ImageInfo.builder().url(imageUrl).build());
        SenseTimeFaceInfo senseTimeFaceInfo = new SenseTimeFaceInfo()
            .setImages(images)
            .setIdentityId(identifier)
            .setTargetName(DigestUtils.md5Hex(identifier))
            .setTarLibSerial(senseTimeProperties.getTarLibSerial());

        String faceInfoStr = JSON.toJSONString(senseTimeFaceInfo);

        Request request = new Request.Builder()
            .url(senseTimeProperties.getEndpoint() + SUBSCRIPTION_API)
            .addHeader("Content-Type", ContentType.APPLICATION_JSON.getMimeType())
            .addHeader("accessToken", accessToken)
            .post(RequestBody.create(
                MediaType.parse(ContentType.APPLICATION_JSON.getMimeType()),
                faceInfoStr))
            .build();

        log.info("{} 开始布控", identifier);
        try (Response response = okHttpClient.newCall(request).execute()) {
            if (Objects.isNull(response.body())) {
                log.error("调用商汤新增布控接口无响应内容");
                return Pair.of(Boolean.FALSE, "{}");
            }

            String responseBodyTxt = response.body().string();
            int responseCode = response.code();

            String successed = String.valueOf(JSONPath.read(responseBodyTxt, "$.success"));
            if (responseCode == HttpStatus.SC_OK && "true".equalsIgnoreCase(successed)) {
                log.info("商汤布控成功, 请求地址:{}, 请求报文:{}, 响应报文:{}",
                    senseTimeProperties.getEndpoint() + SUBSCRIPTION_API,
                    faceInfoStr,
                    responseBodyTxt
                );
                return Pair.of(Boolean.TRUE, responseBodyTxt);
            } else if ("030004010501".equals(String.valueOf(JSONPath.read(responseBodyTxt, "$.errorCode")))) {
                log.info("商汤布控失败——目标一存在, 请求地址:{}, 请求报文:{}, 响应报文:{}",
                    senseTimeProperties.getEndpoint() + SUBSCRIPTION_API,
                    faceInfoStr,
                    responseBodyTxt
                );
                return Pair.of(Boolean.TRUE, responseBodyTxt);
            } else {
                log.error("商汤布控失败, 请求地址:{}, 请求报文:{}, 响应code:{}, 响应报文:{}",
                    senseTimeProperties.getEndpoint() + SUBSCRIPTION_API,
                    faceInfoStr,
                    responseCode,
                    responseBodyTxt
                );
                return Pair.of(Boolean.FALSE, responseBodyTxt);
            }
        } catch (IOException e) {
            log.error("向商汤布控异常", e);
            return Pair.of(Boolean.FALSE, "{}");
        }
    }

    /**
     * 发起添加图片请求
     *
     * @param pictureBase64 人像图片base64
     */
    private String sendUploadImageRequest(String pictureBase64) {
        String accessToken = getAccessToken();
        if (StringUtils.isEmpty(accessToken)) {
            return null;
        }

        byte[] fileBytes = Base64.getDecoder().decode(pictureBase64);
        MultipartBody multipartBody = new MultipartBody.Builder()
            .setType(MultipartBody.FORM)
            .addFormDataPart("file", "/image.jpeg",
                RequestBody.create(MediaType.parse(ContentType.APPLICATION_OCTET_STREAM.getMimeType()), fileBytes)
            ).build();

        Request request = new Request.Builder().url(senseTimeProperties.getEndpoint() + UPLOAD_IMAGE_API)
            .addHeader("accessToken", accessToken)
            .post(multipartBody)
            .build();

        try (Response response = okHttpClient.newCall(request).execute()) {
            if (Objects.isNull(response.body())) {
                log.error("调用商汤上传图片接口无响应内容");
                return null;
            }

            String responseBodyText = response.body().string();
            if(JSON.parseObject(responseBodyText).getBoolean("success") && "0".equals(JSON.parseObject(responseBodyText).getString("errorCode")) && "ok".equals(JSON.parseObject(responseBodyText).getString("errorMsg"))){
                log.info("向商汤上传图片成功,请求url:{},响应报文:{}",
                        senseTimeProperties.getEndpoint() + UPLOAD_IMAGE_API,
                        responseBodyText
                );
                return JSON.parseObject(responseBodyText).getString("data");
            }else{
                log.error("向商汤上传图片异常 {}", JSON.parseObject(responseBodyText).getString("errorMsg"));
                return null;
            }
        } catch (IOException e) {
            log.error("向商汤上传图片异常", e);
            return null;
        }
    }

    /**
     * 请求token
     */
    private String getAccessToken() {
//        if (Objects.nonNull(this.token) && !this.token.isExpired()) {
//            return this.token.getAccessToken();
//        }

        Map<String, String> param = Map.of(
            "grant_type", "password",
            "username", senseTimeProperties.getUsername(),
            "password", senseTimeProperties.getPassword()
        );

        Request request = new Request.Builder()
            .url(senseTimeProperties.getEndpoint() + TOKEN_API)
            .header(HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())
            .post(RequestBody.create(
                MediaType.parse(ContentType.APPLICATION_JSON.getMimeType()),
                JSON.toJSONString(param)))
            .build();

        try (Response response = okHttpClient.newCall(request).execute()) {
            if (Objects.isNull(response.body())) {
                log.error("调用商汤获取token接口无响应内容");
                return null;
            }

            log.info("request token param is {}", param);
            String responseBodyText = response.body().string();
            if(!response.isSuccessful() && !"0".equals(JSON.parseObject(responseBodyText).getString("errorCode"))){
                log.error("请求商汤 token 失败, responseCode:{}, responseBody:{}", response.code(), responseBodyText);
                throw new RuntimeException();
            }else {
                token = JSON.parseObject(responseBodyText).getObject("data", Token.class);
                log.info("token is {}", token.getAccessToken());
                return token.getAccessToken();
            }
        } catch (IOException e) {
            log.error("请求商汤 token 失败", e);
            return null;
        }
    }

    /**
     * 获取ys 码表配置的第三方平台英文码值
     *
     * @return 第三方平台英文码值
     */
    @Override
    public Integer getThirdPartNameCode() {
        return 8;
    }

    @Override
    @Trace(operationName = "com.trs.police.comparison.api.spi.third.SenseTimeFaceDetectionServiceImplSubscribeImpl.subscribeIdentifier")
    public void subscribeIdentifier(List<SubscribeInfo> subscribeInfoList, String thirdPartName, AuditInformation auditInformation) {
        if(subscribeInfoList == null) {
            return;
        }
        log.info("开始发起商汤人像布控");
        ArrayList<SubscribeInfoThirdPartDetail> newSubscribeSuccesses = new ArrayList<>();
        ArrayList<SubscribeInfoThirdPartDetail> subscribeSuccesses = new ArrayList<>();
        List<SubscribeInfoThirdPartDetail> subscribeInfoThirdPartDetailList = subscribeInfoDetailMapper.selectFailedByInfoIds(subscribeInfoList.stream().map(SubscribeInfo::getId).collect(Collectors.toList()), thirdPartName);
        Map<Long, List<SubscribeInfoThirdPartDetail>> detailMap = subscribeInfoThirdPartDetailList.stream().collect(Collectors.groupingBy(SubscribeInfoThirdPartDetail::getSubscribeInfoId));
        for (SubscribeInfo subscribeInfo : subscribeInfoList) {
            try {
                //只推送有人像的数据,为空默认成功
                if (StringUtils.isEmpty(subscribeInfo.getFaceInfo())) {
                    SubscribeInfoThirdPartDetail subscribeInfoThirdPartDetail = new SubscribeInfoThirdPartDetail();
                    subscribeInfoThirdPartDetail.setSubscribeInfoId(subscribeInfo.getId());
                    subscribeInfoThirdPartDetail.setPushTime(LocalDateTime.now());
                    subscribeInfoThirdPartDetail.setFirstPushTime(LocalDateTime.now());
                    subscribeInfoThirdPartDetail.setThirdPartName(thirdPartName);
                    subscribeInfoThirdPartDetail.setPushCount(1);
                    subscribeInfoThirdPartDetail.setPushStatus(Constants.PUSH_SUCCESS);
                    newSubscribeSuccesses.add(subscribeInfoThirdPartDetail);
                    continue;
                }
                // 只对证件号码发起布控订阅，非证件号默认成功
                if (1 != subscribeInfo.getIdentifierType()) {
                    SubscribeInfoThirdPartDetail subscribeInfoThirdPartDetail = new SubscribeInfoThirdPartDetail();
                    subscribeInfoThirdPartDetail.setSubscribeInfoId(subscribeInfo.getId());
                    subscribeInfoThirdPartDetail.setPushTime(LocalDateTime.now());
                    subscribeInfoThirdPartDetail.setFirstPushTime(LocalDateTime.now());
                    subscribeInfoThirdPartDetail.setThirdPartName(thirdPartName);
                    subscribeInfoThirdPartDetail.setPushCount(1);
                    subscribeInfoThirdPartDetail.setPushStatus(Constants.PUSH_SUCCESS);
                    newSubscribeSuccesses.add(subscribeInfoThirdPartDetail);
                    continue;
                }
                log.info("{} 上传 人像到商汤", subscribeInfo.getIdentifier());
                // 上传图片
                String imageUrl = sendUploadImageRequest(subscribeInfo.getFaceInfo());
                List<SubscribeInfoThirdPartDetail> details = detailMap.get(subscribeInfo.getId());
                if (StringUtils.isNotEmpty(imageUrl)) {
                    // 发起布控
                    Pair<Boolean, String> booleanStringPair = sendSubscriptionRequest(imageUrl, subscribeInfo.getIdentifier());

                    if (Boolean.TRUE.equals(booleanStringPair.getLeft())) {
                        if (IdentifierTypeEnum.ID_CARD.getCode() == subscribeInfo.getIdentifierType()) {
                            ActiveSpan.tag("idCard", subscribeInfo.getIdentifier());
                        }
                        if (CollectionUtils.isEmpty(details)) {
                            //从未推送过
                            SubscribeInfoThirdPartDetail subscribeInfoThirdPartDetail = new SubscribeInfoThirdPartDetail();
                            subscribeInfoThirdPartDetail.setSubscribeInfoId(subscribeInfo.getId());
                            subscribeInfoThirdPartDetail.setPushTime(LocalDateTime.now());
                            subscribeInfoThirdPartDetail.setFirstPushTime(LocalDateTime.now());
                            subscribeInfoThirdPartDetail.setThirdPartName(thirdPartName);
                            subscribeInfoThirdPartDetail.setPushCount(1);
                            subscribeInfoThirdPartDetail.setPushStatus(Constants.PUSH_SUCCESS);
                            subscribeInfoThirdPartDetail.setSubscribeThirdInfo(objectMapper.readTree(booleanStringPair.getRight()));
                            newSubscribeSuccesses.add(subscribeInfoThirdPartDetail);
                        } else {
                            //推送失败过
                            SubscribeInfoThirdPartDetail subscribeInfoThirdPartDetail = details.get(0);
                            subscribeInfoThirdPartDetail.setPushTime(LocalDateTime.now());
                            Integer pushCount = subscribeInfoThirdPartDetail.getPushCount() + 1;
                            subscribeInfoThirdPartDetail.setPushCount(pushCount);
                            subscribeInfoThirdPartDetail.setPushStatus(Constants.PUSH_SUCCESS);
                            subscribeInfoThirdPartDetail.setSubscribeThirdInfo(objectMapper.readTree(booleanStringPair.getRight()));
                            subscribeSuccesses.add(subscribeInfoThirdPartDetail);
                        }
                    }else{
                        //推送失败了
                        if (CollectionUtils.isEmpty(details)) {
                            //从未推送过
                            SubscribeInfoThirdPartDetail subscribeInfoThirdPartDetail = new SubscribeInfoThirdPartDetail();
                            subscribeInfoThirdPartDetail.setSubscribeInfoId(subscribeInfo.getId());
                            subscribeInfoThirdPartDetail.setPushTime(LocalDateTime.now());
                            subscribeInfoThirdPartDetail.setFirstPushTime(LocalDateTime.now());
                            subscribeInfoThirdPartDetail.setThirdPartName(thirdPartName);
                            subscribeInfoThirdPartDetail.setPushCount(1);
                            subscribeInfoThirdPartDetail.setPushStatus(Constants.PUSH_FAILED);
                            subscribeInfoThirdPartDetail.setSubscribeThirdInfo(objectMapper.readTree(booleanStringPair.getRight()));
                            newSubscribeSuccesses.add(subscribeInfoThirdPartDetail);
                        } else {
                            //推送失败过
                            SubscribeInfoThirdPartDetail subscribeInfoThirdPartDetail = details.get(0);
                            subscribeInfoThirdPartDetail.setPushTime(LocalDateTime.now());
                            Integer pushCount = subscribeInfoThirdPartDetail.getPushCount() + 1;
                            subscribeInfoThirdPartDetail.setPushCount(pushCount);
                            subscribeInfoThirdPartDetail.setPushStatus(Constants.PUSH_FAILED);
                            subscribeInfoThirdPartDetail.setSubscribeThirdInfo(objectMapper.readTree(booleanStringPair.getRight()));
                            subscribeSuccesses.add(subscribeInfoThirdPartDetail);
                        }
                    }
                }else {
                    //图片上传失败，推送失败
                    if (CollectionUtils.isEmpty(details)) {
                        //从未推送过
                        SubscribeInfoThirdPartDetail subscribeInfoThirdPartDetail = new SubscribeInfoThirdPartDetail();
                        subscribeInfoThirdPartDetail.setSubscribeInfoId(subscribeInfo.getId());
                        subscribeInfoThirdPartDetail.setPushTime(LocalDateTime.now());
                        subscribeInfoThirdPartDetail.setFirstPushTime(LocalDateTime.now());
                        subscribeInfoThirdPartDetail.setThirdPartName(thirdPartName);
                        subscribeInfoThirdPartDetail.setPushCount(1);
                        subscribeInfoThirdPartDetail.setPushStatus(Constants.PUSH_FAILED);
                        newSubscribeSuccesses.add(subscribeInfoThirdPartDetail);
                    } else {
                        //推送失败过
                        SubscribeInfoThirdPartDetail subscribeInfoThirdPartDetail = details.get(0);
                        subscribeInfoThirdPartDetail.setPushTime(LocalDateTime.now());
                        Integer pushCount = subscribeInfoThirdPartDetail.getPushCount() + 1;
                        subscribeInfoThirdPartDetail.setPushCount(pushCount);
                        subscribeInfoThirdPartDetail.setPushStatus(Constants.PUSH_FAILED);
                        subscribeSuccesses.add(subscribeInfoThirdPartDetail);
                    }
                }
            } catch (Exception e) {
                log.error("在商汤人像布控订阅失败", e);
            }
        }
        //保存推送状态
        if (!newSubscribeSuccesses.isEmpty()){
            subscribeInfoDetailMapper.insertBatch(newSubscribeSuccesses);
        }
        //根据主键更新推送状态
        if(!subscribeSuccesses.isEmpty()){
            subscribeInfoDetailMapper.batchUpdatePushStatusByPrimaryKey(subscribeSuccesses);
        }
    }

    @Override
    public void cancelSubscribeIdentifier(List<SubscribeInfo> subscribeInfoList, String subscribeCode, String userAccount, AuditInformation auditInformation) {
        try {
            String accessToken = getAccessToken();
            if (StringUtils.isEmpty(accessToken)) {
                log.error("获取商汤token失败");
                return;
            }
            if (subscribeInfoList.isEmpty()) {
                log.error("需要撤控的数据为空 {}", com.alibaba.fastjson2.JSON.toJSONString(subscribeInfoList));
                return;
            }
            // 查询布控情况, 没有在第三方布控成功的无法撤控
            List<SubscribeInfoThirdPartDetail> list = subscribeInfoDetailMapper.selectSuccessDetailByInfoIds(subscribeInfoList.stream().map(SubscribeInfo::getId).collect(Collectors.toList()), ProxyUtils.getOriginalType(this));
            // 构造请求参数，可以批量撤控
            List<SubscribeInfoThirdPartDetail> collect = list.stream().filter(item -> item.getSubscribeThirdInfo() != null)
                    .peek(item -> {
                        try {
                            HashMap<String, String> faceIdMap = new HashMap<>();
                            SenseTimeSubscribeResultInfo resultInfo = objectMapper.readValue(objectMapper.writeValueAsString(item.getSubscribeThirdInfo()), SenseTimeSubscribeResultInfo.class);
                            faceIdMap.put("tarLibSerial", resultInfo.getData().getTarLibSerial());
                            faceIdMap.put("targetSerial", resultInfo.getData().getTargetSerial());
                            String cancelSubscribeUrl = SUBSCRIPTION_API + "?tarLibSerial=${tarLibSerial}&targetSerial=${targetSerial}";
                            String url = StringSubstitutor.replace(cancelSubscribeUrl, faceIdMap);
                            // 发起撤控请求
                            final Request request =
                                    new Request.Builder()
                                            .url(url)
                                            .delete()
                                            .header("accessToken", accessToken)
                                            .build();
                            try (Response execute = okHttpClient.newCall(request).execute()) {
                                ResponseBody body = execute.body();
                                if (Objects.isNull(body)) {
                                    log.error("商汤人脸撤控无响应内容");
                                    throw new RuntimeException("商汤人脸撤控无响应内容");
                                }
                                String responseText = body.string();
                                SenseTimeSubscribeResultInfo cancelResult = objectMapper.readValue(responseText, SenseTimeSubscribeResultInfo.class);
                                if (0 == cancelResult.getData().getSenseType() && Boolean.TRUE.equals(cancelResult.getSuccess())) {
                                    // 修改状态为已撤控
                                    item.setPushStatus(2);
                                    item.setSubscribeThirdInfo(objectMapper.readValue(objectMapper.writeValueAsString(cancelResult), JsonNode.class));
                                }
                            }
                        } catch (Exception e) {
                            log.error("商汤取消人脸布控失败, 错误信息为 {}", e.getMessage(), e);
                        }
                    }).collect(Collectors.toList());
            // 记录撤控状态
            if (!collect.isEmpty()) {
                subscribeInfoDetailMapper.batchUpdatePushStatusByPrimaryKey(collect);
            }
        } catch (Exception e) {
            log.error("取消订阅商汤人像出错, 错误信息为 {}", e.getMessage(), e);
        }
    }

    /**
     * 商汤接口token信息
     */
    @Data
    public static class Token implements Serializable {

        private static final long serialVersionUID = -2479566810269223761L;
        private String accessToken;
        private boolean api;
        private Integer expiresIn;
        private String refreshToken;
        /**
         * 自定义数据项,用于计算token过期时间
         */
        private LocalDateTime createTime = LocalDateTime.now();

        public boolean isExpired() {
            return LocalDateTime.now().isBefore(createTime.minusSeconds(expiresIn.longValue() - 120));
        }
    }
}
