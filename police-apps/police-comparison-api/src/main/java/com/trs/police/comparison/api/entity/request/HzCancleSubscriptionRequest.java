package com.trs.police.comparison.api.entity.request;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 合众感知引擎布控请求参数
 *
 * <AUTHOR>
 **/
@Data
@Accessors(chain = true)
public class HzCancleSubscriptionRequest {

    /**
     * 消息流水号
     */
    private String messageSequence;
    /**
     * 感知申请类型
     */
    private String gzsqlx;
    /**
     * 感知数据的数任务组集合，最多支持100条
     */
    private List<TaskParam> tasks;


    @Data
    @Accessors(chain = true)
    public static class TaskParam {

        // 任务编号
        private String rwbh;
        // 规则集合
        private List<RuleParam> rules;
        // 事由 布控、撤控、续控事由
        private String sy;
        // 申请人姓名
        private String sqrxm;
        // 申请人身份号码
        private String sqrsfhm;
        // 申请人联系号码
        private String sqrlxhm;
        // 申请单位
        private String sqdw;
        // 申请单位机构代码
        private String sqdwjgdm;
        // 申请时间
        private String sqsj;
        // 审批结果
        private String spjg;
        // 审批人姓名
        private String sprxm;
        // 审批人身份号码
        private String sprsfhm;
        // 审批人联系号码
        private String sprlxhm;
        // 审批单位机构代码
        private String spdwjgdm;
        // 审批单位
        private String spdwmc;
        // 审批单位联系号码
        private String spdwlxhm;
        // 审批时间
        private String spsj;
    }

    @Data
    public static class RuleParam {
        // 对象业务主键编号
        private String dxywzjbh;
    }

}
