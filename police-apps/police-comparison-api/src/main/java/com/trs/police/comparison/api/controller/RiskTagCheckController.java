package com.trs.police.comparison.api.controller;

import com.trs.police.comparison.api.entity.vo.ResultVO;
import com.trs.police.comparison.api.service.TagRuleCheckService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2025/4/2 16:11
 */
@RestController
public class RiskTagCheckController {

    @Resource
    private TagRuleCheckService tagRuleCheckService;

    @PostMapping("/riskTagCheck")
    public ResultVO<String> getRiskTagCheckResult(String nr, Integer tagId) {
        return tagRuleCheckService.checkTagRule(nr, tagId);
    }

}
