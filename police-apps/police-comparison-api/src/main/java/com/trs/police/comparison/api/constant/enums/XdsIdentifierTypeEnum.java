package com.trs.police.comparison.api.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 对比订阅服务中新东盛订阅类型
 *
 * <AUTHOR>
 * @Date 2023/4/11 17:59
 **/
@Getter
@AllArgsConstructor
public enum XdsIdentifierTypeEnum {

  // 身份证
  ID_CARD("1"),
  // 车牌号
  PLATE_NO("2"),
  // 手机号
  PHONE_NO("3"),
  MAC("4"),
  // 护照
  PASSPORT("5"),
  IMSI("6"),
  IMEI("8");

  private String code;
}
