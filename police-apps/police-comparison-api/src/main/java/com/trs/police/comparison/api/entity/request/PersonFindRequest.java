package com.trs.police.comparison.api.entity.request;



import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 删除人员标签
 *
 * <AUTHOR>
 * @version 1.0
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PersonFindRequest implements DataServiceAPIBaseRequest {

    private static final long serialVersionUID = 8057550913114227777L;

    /**
     * 人员标识符
     */
    private String identifier;

    /**
     * 标识符类型
     */
    private int identifierType;

    /**
     * 人员标签
     */
    private String tag;

}
