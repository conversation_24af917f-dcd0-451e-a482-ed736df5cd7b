package com.trs.police.comparison.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.comparison.api.dao.handler.JsonArrayTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 群体布控人员表实体
 *
 * <AUTHOR>
 * @version 1.0
 **/

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value ="t_subscribe_group_member", autoResultMap = true)
public class SubscribeGroupMember implements Serializable {

  private static final long serialVersionUID = -1811255400217051536L;

  /**
   * 主键
   */
  @TableId(type = IdType.AUTO)
  private Long id;

  /**
   * 人员id
   */
  private String memberId;

  /**
   * 人员所属群体id集合
   */
  @TableField(value = "monitor_ids", typeHandler = JsonArrayTypeHandler.class, jdbcType = JdbcType.LONGVARCHAR)
  private List<String> monitorIds;

  /**
   * 特征值信息集合
   */
  @TableField(value = "identifier_infos", typeHandler = JsonArrayTypeHandler.class, jdbcType = JdbcType.LONGVARCHAR)
  private List<String> identifierInfos;

  /**
   * 订阅用户
   */
  private String userName;

  /**
   * 初次订阅时间
   */
  private LocalDateTime createTime;

  /**
   * 最后订阅时间
   */
  private LocalDateTime updateTime;
}
