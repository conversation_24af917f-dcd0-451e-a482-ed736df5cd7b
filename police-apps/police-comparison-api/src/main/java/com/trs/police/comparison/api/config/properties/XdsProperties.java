package com.trs.police.comparison.api.config.properties;

import lombok.Data;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 新东盛订阅接口信息
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "com.trs.subscription.xds")
@ConditionalOnProperty(prefix = "com.trs.subscription.xds", name = "enabled", havingValue = "true")
public class XdsProperties {

    private Boolean enabled = false;

    private String url;

    private String appCode;

}
