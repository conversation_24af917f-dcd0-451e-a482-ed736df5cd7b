package com.trs.police.comparison.api.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.comparison.api.entity.SensingDevice;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 感知源
 * <AUTHOR>
 */
@Mapper
public interface SensingDeviceMapper extends BaseMapper<SensingDevice> {

    /**
     * 保存设备信息
     * @param sensingDevice 设备信息
     */
    void insertOrUpdate(@Param("sensingDevice") SensingDevice sensingDevice);

}