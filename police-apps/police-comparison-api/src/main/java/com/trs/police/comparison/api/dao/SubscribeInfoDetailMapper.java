package com.trs.police.comparison.api.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.trs.police.comparison.api.entity.SubscribeInfoThirdPartDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SubscribeInfoDetailMapper  extends BaseMapper<SubscribeInfoThirdPartDetail> {
    int insertBatch(@Param("list") List<SubscribeInfoThirdPartDetail> records);

    int batchUpdatePushStatusByPrimaryKey(@Param("list")  List<SubscribeInfoThirdPartDetail> list);

    int updatePushStatusByPrimaryKey(@Param("record") SubscribeInfoThirdPartDetail record);

    int batchUpdatePushCountByPrimaryKey(@Param("list") List<SubscribeInfoThirdPartDetail> records);


    void deleteByUserAndSubscribeCode(@Param("userName") String userName, @Param("subscribeCode") String subscribeCode);

    List<String> selectAllThirdPartName();

    List<SubscribeInfoThirdPartDetail> selectNotSuccessList();

    Integer selectPushCountById(@Param("id") Long Id);

    SubscribeInfoThirdPartDetail selectFailedByInfoId(@Param("infoId") Long infoId, @Param("thirdPartName") String thirdPartName);

    /**
     * 批量查询失败的布控
     * @param infoIds 布控信息id集合
     * @param thirdPartName 第三方名称
     * @return 布控失败信息列表
     */
    List<SubscribeInfoThirdPartDetail> selectFailedByInfoIds(@Param("infoIds") List<Long> infoIds, @Param("thirdPartName") String thirdPartName);

    /**
     * 批量查询第三方布控结果
     * @param infoIds 布控信息id集合
     * @param thirdPartName 第三方名称
     * @return 第三方布控结果列表
     */
    List<SubscribeInfoThirdPartDetail> selectDetailByInfoIds(@Param("infoIds") List<Long> infoIds, @Param("thirdPartName") String thirdPartName);

    /**
     * 批量查询第三方布控成功的结果, 用于撤控
     * @param infoIds 布控信息id集合
     * @param thirdPartName 第三方名称
     * @return 第三方布控结果列表
     */
    List<SubscribeInfoThirdPartDetail> selectSuccessDetailByInfoIds(@Param("infoIds") List<Long> infoIds, @Param("thirdPartName") String thirdPartName);
}
