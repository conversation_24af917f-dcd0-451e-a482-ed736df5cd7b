package com.trs.police.comparison.api.schedule;

import com.alibaba.fastjson.JSONPath;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.trs.police.comparison.api.config.properties.ZgDaHuaTechnologyProperties;
import com.trs.police.comparison.api.dao.SensingDeviceMapper;
import com.trs.police.comparison.api.dao.other.PoliceJdbcTemplateDao;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.http.entity.ContentType;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.text.MessageFormat;
import java.util.*;

import static org.springframework.http.HttpHeaders.CONTENT_TYPE;

/**
 * 获取自贡大华的组织信息
 * <AUTHOR>
 * @since 2024/12/10 17:19
 */
@Slf4j
@Component
@ConditionalOnBean(ZgDaHuaTechnologyProperties.class)
public class ZgDahuaDeviceInfoPullTask {

    private static final String TZ_DATE_TIME_FORMAT = "yyyyMMdd'T'HHmmss'Z'";

    private static final String VERSION_HEADER_FIELD = "X-Api-Version";

    private static final String X_SUBJECT_TOKEN = "X-Subject-Token";

    private static final String RANDOM_KEY_JSON_PATH = "$.randomKey";

    private static final String TOKEN_JSON_PATH = "$.token";

    private static final String TOTAL_COUNT_JSON_PATH = "$.totalCount";

    private static final String DEVICE_JSON_PATH = "$.devices";

    private static final String USER_NAME = "userName";

    private static final String ORG_CODE = "orgCode";

    private static final String SIGNATURE = "signature";

    private static final String RANDOM_KEY = "randomKey";

    private static final String ENCRYPT_TYPE = "encryptType";

    private static final String CLIENT_TYPE = "clientType";

    private static final String WIN_PC = "winpc";

    private final ZgDaHuaTechnologyProperties properties;

    private final SensingDeviceMapper sensingDeviceMapper;

    private final PoliceJdbcTemplateDao policeJdbcTemplateDao;

    private final ObjectMapper objectMapper = new ObjectMapper();

    private final OkHttpClient okHttpClient;

    public ZgDahuaDeviceInfoPullTask(ZgDaHuaTechnologyProperties properties,
                                     SensingDeviceMapper sensingDeviceMapper,
                                     PoliceJdbcTemplateDao policeJdbcTemplateDao,
                                     OkHttpClient okHttpClient){
        this.properties = properties;
        this.okHttpClient = okHttpClient;
        this.sensingDeviceMapper = sensingDeviceMapper;
        this.policeJdbcTemplateDao = policeJdbcTemplateDao;
    }

    /**
     * 任务已迁移到control 模块
     */
    //@Scheduled(cron = "${com.trs.subscription.dahua.device.cron}")
    public void pullAndSave() {
       try {
//            String token = getToken();
//            log.info("请求自贡大华平台获取token成功, token为:{}", token);
//            // 获取组织信息
//            OrgEntity orgEntity = getOrgInfo(token);
//            if (orgEntity == null) {
//                throw  new RuntimeException("获取组织信息为空");
//            }
//            List<OrgInfo> orgInfoList = orgEntity.getResults();
//            //遍历组织信息
//            if (CollectionUtils.isEmpty(orgInfoList)){
//                throw new RuntimeException("获取组织信息为空");
//            }
//            ArrayList<SensingDevice> allDevice = new ArrayList<>();
//            ArrayList<String> channelCodeList = new ArrayList<>();
//            for (OrgInfo org : orgInfoList) {
//                log.info("组织编码为:{}", org.getOrgCode());
//                // 获取组织下的设备信息
//                DeviceEntity deviceEntity = getDeviceInfo(org.getOrgCode(), token);
//                if (deviceEntity == null){
//                    log.info("未获得设备信息");
//                    continue;
//                }
//                List<DeviceInfo> devices = deviceEntity.getDevices();
//                if (!CollectionUtils.isEmpty(devices)){
//                    // 感知源信息
//                    for (DeviceInfo device : devices) {
//                        SensingDevice sensingDevice = new SensingDevice();
//                        // 感知源编码
//                        sensingDevice.setGzybh(device.code == null ? null : device.code.replace("D",""));
//                        // 感知源名称
//                        sensingDevice.setGzymc(device.getName());
//                        // 地址名称
//                        sensingDevice.setDzmc(device.getName());
//                        // 行政区划代码
//                        if (org.getOrgCode() != null){
//                            sensingDevice.setXzqhdm(org.getOrgCode().substring(0,6));
//                        }
//                        if (!CollectionUtils.isEmpty(device.getUnits())) {
//                            for (Units unit : device.getUnits()) {
//                                if (!CollectionUtils.isEmpty(unit.getChannels())) {
//                                    for (Channels channel : unit.getChannels()) {
//                                            // 保存通道编码
//                                            channelCodeList.add(channel.getChannelCode());
//                                    }
//                                }
//                            }
//
//                        }
//                        if (!CollectionUtils.isEmpty(device.getUnits()) && !CollectionUtils.isEmpty(device.getUnits().get(0).getChannels()) && device.getUnits().get(0).getChannels().get(0).getGpsX() != null && device.getUnits().get(0).getChannels().get(0).getGpsY() != null) {
//                            //默认是人像类型
//                            sensingDevice.setGzylx(5);
//                            if (sensingDevice.getGzybh() != null) {
//                                sensingDevice.setUniqueKey(sensingDevice.getGzybh() + "-" + sensingDevice.getGzylx());
//                            }
//                            // 经度
//                            sensingDevice.setJdwgs84(Float.valueOf(device.getUnits().get(0).getChannels().get(0).getGpsX()));
//                            // 纬度
//                            sensingDevice.setWdwgs84(Float.valueOf(device.getUnits().get(0).getChannels().get(0).getGpsY()));
//                            sensingDevice.setPoint("POINT (" + sensingDevice.getWdwgs84() + " " + sensingDevice.getJdwgs84() + ")");
//                        }
//                        sensingDevice.setXxrksj(LocalDateTime.now());
//                        allDevice.add(sensingDevice);
//                    }
//                }
//            }
//            //policeJdbcTemplateDao.insertOrUpdate(allDevice);
//            // 推送预警订阅
//            pushSubscribe(channelCodeList);
        } catch (Exception e) {
            log.error("请求自贡大华平台获取设备信息失败", e);
            throw new RuntimeException("请求自贡大华平台获取设备信息失败", e);
        }
    }

    private void pushSubscribe(ArrayList<String> channelCodeList) {
        try {
            HashMap<String, Object> subscribeAlertMessageParams = new HashMap<>();
            ArrayList<String> repositoryIds = new ArrayList<>();
            repositoryIds.add(properties.getFaceRepositoryId());
            subscribeAlertMessageParams.put("range", 2);
            subscribeAlertMessageParams.put("types", "4");
            subscribeAlertMessageParams.put("repositoryIds", repositoryIds);
            subscribeAlertMessageParams.put("channelCodes", channelCodeList);
            subscribeAlertMessageParams.put("surveyIds", properties.getSubscribeFaceSurveyId());
            String jsonParams = objectMapper.writeValueAsString(subscribeAlertMessageParams);
            //请求
            final Request request =
                    new Request.Builder()
                            .url(properties.getEndpoint() + properties.getRequestSubscribe())
                            .put(
                                    RequestBody.create(
                                            MediaType.parse(ContentType.APPLICATION_JSON.getMimeType()),
                                            jsonParams
                                    ))
                            .addHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())
                            .addHeader(X_SUBJECT_TOKEN, getToken())
                            .build();
            try (Response result = okHttpClient.newCall(request).execute()) {
                final int responseCode = result.code();
                final ResponseBody responseBody = result.body();
                log.info(
                        "请求订阅, requestBody:{} , responseCode:{}",
                        objectMapper.writeValueAsString(jsonParams), responseCode);
                final String responseBodyTxt =
                        Objects.requireNonNull(responseBody, "请求订阅 okhttp 错误,responseBody为空")
                                .string();
                log.info(
                        "请求订阅, requestBody:{} , responseCode:{}, responseBody:{}",
                        objectMapper.writeValueAsString(jsonParams), responseCode, responseBodyTxt);
                log.info("请求订阅 成功, 响应内容为:{}", responseBodyTxt);
            } catch (Exception e) {
                //http请求出错
                log.error("请求订阅, 错误信息为 {}", e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new RuntimeException("请求订阅 失败", e);
        }
    }

    private DeviceEntity getDeviceInfo(String orgCode, String token) {
        try {
            HashMap<String, String> params = new HashMap<>();
            params.put(ORG_CODE, orgCode);
            //请求组织列表
            final Request request =
                    new Request.Builder()
                            .url(properties.getEndpoint() + properties.getDevicesInfoPath())
                            .post(RequestBody.create(MediaType.parse("application/json"), objectMapper.writeValueAsBytes(params)))
                            .addHeader(X_SUBJECT_TOKEN, token)
                            .build();
            try (Response result = okHttpClient.newCall(request).execute()) {
                final int responseCode = result.code();
                final ResponseBody responseBody = result.body();

                final String responseBodyTxt =
                        Objects.requireNonNull(responseBody, "请求自贡大华平台请求设备列表 okhttp 错误,responseBody为空")
                                .string();

                if (!checkDeviceListResponseSuccessful(responseBodyTxt)) {
                    log.error(
                            "请求自贡大华平台请求设备列表 okhttp 错误, ,responseCode:{} ,responseBody:{}",
                            responseCode,
                            responseBodyTxt);
                    throw new RuntimeException(
                            MessageFormat.format(
                                    "请求自贡大华平台请求设备列表 okhttp 错误,http 状态码:{0} ,响应内容:{1}", responseCode, responseBodyTxt));
                }
                log.info("请求自贡大华平台请求设备列表 成功, 响应内容为:{}", responseBodyTxt);
                return objectMapper.readValue(responseBodyTxt, DeviceEntity.class);
            } catch (Exception e) {
                //http请求出错
                log.error("获取设备信息出错, 错误信息为 {}", e.getMessage(), e);
            }
        } catch (Exception e) {
            log.error("请求自贡大华平台获取设备信息失败", e);
        }
        return null;
    }

    private OrgEntity getOrgInfo(String token) {
        try {
            //请求组织列表
            final Request request =
                    new Request.Builder()
                            .url(properties.getEndpoint() + properties.getDeviceAllOrgPath())
                            .get()
                            .addHeader(X_SUBJECT_TOKEN, token)
                            .build();
            try (Response result = okHttpClient.newCall(request).execute()) {
                final int responseCode = result.code();
                final ResponseBody responseBody = result.body();

                final String responseBodyTxt =
                        Objects.requireNonNull(responseBody, "请求自贡大华平台请求组织列表 okhttp 错误,responseBody为空")
                                .string();

                if (!checkOrgListResponseSuccessful(responseBodyTxt)) {
                    log.error(
                            "请求自贡大华平台请求组织列表 okhttp 错误, ,responseCode:{} ,responseBody:{}",
                            responseCode,
                            responseBodyTxt);
                    throw new RuntimeException(
                            MessageFormat.format(
                                    "请求自贡大华平台请求组织列表 okhttp 错误,http 状态码:{0} ,响应内容:{1}", responseCode, responseBodyTxt));
                }
                log.info("请求自贡大华平台请求组织列表 成功, 响应内容为:{}", responseBodyTxt);
                return objectMapper.readValue(responseBodyTxt, OrgEntity.class);
            } catch (Exception e) {
                //http请求出错
                log.error("获取组织信息出错, 错误信息为 {}", e.getMessage(), e);
            }
        } catch (Exception e) {
            log.error("请求自贡大华平台获取组织信息失败", e);

        }
        return null;
    }

    /**
     * 获取token
     * 不同平台的token获取方式不同，需要子类自己实现
     *
     * @return 第三方返回的token
     */
    String getToken() {
        // 第一次会话
        FirstResponse firstResponse = getFirstResponse();
        // 获取token前生成签名
        String signature = getSignature(properties.getPassword(), properties.getUserName(), firstResponse.getRealm(), firstResponse.getRandomKey(), firstResponse.getEncryptType());
        // 获取token
        String token = null;
        try {
            HashMap<String, String> params = new HashMap<>(2);
            params.put(USER_NAME, properties.getUserName());
            params.put(SIGNATURE, signature);
            params.put(RANDOM_KEY, firstResponse.getRandomKey());
            params.put(ENCRYPT_TYPE, firstResponse.getEncryptType());
            params.put(CLIENT_TYPE, properties.getClientType());

            String jsonParams = objectMapper.writeValueAsString(params);
            //请求token
            final Request request =
                    new Request.Builder()
                            .url(properties.getEndpoint() + properties.getAuthorizePath())
                            .post(
                                    RequestBody.create(
                                            MediaType.parse(ContentType.APPLICATION_JSON.getMimeType()),
                                            jsonParams
                                    ))
                            .addHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())
                            .build();
            try (Response result = okHttpClient.newCall(request).execute()) {
                final int responseCode = result.code();
                final ResponseBody responseBody = result.body();

                final String responseBodyTxt =
                        Objects.requireNonNull(responseBody, "请求自贡大华平台请求token okhttp 错误,responseBody为空")
                                .string();
                log.debug(
                        "请求自贡大华平台请求token, requestBody:{} , responseCode:{}, responseBody:{}",
                        objectMapper.writeValueAsString(jsonParams), responseCode, responseBodyTxt);
                //获取realm 和 randomKey
                if (!checkTokenResponseSuccessful(responseBodyTxt)) {
                    log.error(
                            "请求自贡大华平台请求token okhttp 错误,requestBody:{} ,responseCode:{} ,responseBody:{}",
                            objectMapper.writeValueAsString(jsonParams),
                            responseCode,
                            responseBodyTxt);
                    throw new RuntimeException(
                            MessageFormat.format(
                                    "请求自贡大华平台请求token okhttp 错误,http 状态码:{0} ,响应内容:{1}, 请求参数:{2}", responseCode, responseBodyTxt, jsonParams));
                }
                log.info("请求自贡大华平台获取 token 成功, 响应内容为:{}", responseBodyTxt);
                // 如果不是成功获取到token, 这里无法正确转换为WithTokenEntity对象
                WithTokenEntity withTokenEntity = objectMapper.readValue(responseBodyTxt, WithTokenEntity.class);

                token = withTokenEntity.getToken();
            } catch (Exception e) {
                //http请求出错
                log.error("请求自贡大华平台第一次交互获取授权信息接口okhttp错误, 错误信息为 {}", e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new RuntimeException("请求自贡大华 获取randomKey 以及 realm 失败", e);
        }
        return token;
    }

    boolean checkTokenResponseSuccessful(String resultTxt) {
        final String token =
                String.valueOf(
                        JSONPath.read(
                                Optional.ofNullable(resultTxt).orElse("{}"),
                                TOKEN_JSON_PATH));
        //空串或空对象则返回false
        return !"null".equals(token);
    }

    boolean checkOrgListResponseSuccessful(String resultTxt) {
        final String totalCount =
                String.valueOf(
                        JSONPath.read(
                                Optional.ofNullable(resultTxt).orElse("{}"),
                                TOTAL_COUNT_JSON_PATH));
        //空串或空对象则返回false
        return !"null".equals(totalCount);
    }

    boolean checkDeviceListResponseSuccessful(String resultTxt) {
        final String totalCount =
                String.valueOf(
                        JSONPath.read(
                                Optional.ofNullable(resultTxt).orElse("{}"),
                                DEVICE_JSON_PATH));
        //空串或空对象则返回false
        return !"null".equals(totalCount);
    }

    boolean checkFirstResponseSuccessful(String resultTxt) {
        final String randomKey =
                String.valueOf(
                        JSONPath.read(
                                Optional.ofNullable(resultTxt).orElse("{}"),
                                RANDOM_KEY_JSON_PATH));
        //空串或空对象则返回false
        return !"null".equals(randomKey);
    }

    private FirstResponse getFirstResponse() {
        FirstResponse firstResponse = new FirstResponse();
        try {
            HashMap<String, String> params = new HashMap<>(2);
            params.put(USER_NAME, properties.getUserName());
            params.put(CLIENT_TYPE, WIN_PC);

            String jsonParams = objectMapper.writeValueAsString(params);
            //推送
            final Request request =
                    new Request.Builder()
                            .url(properties.getEndpoint() + properties.getAuthorizePath())
                            .post(
                                    RequestBody.create(
                                            MediaType.parse(ContentType.APPLICATION_JSON.getMimeType()),
                                            jsonParams
                                    ))
                            .addHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())
                            .build();
            try (Response result = okHttpClient.newCall(request).execute()) {
                final int responseCode = result.code();
                final ResponseBody responseBody = result.body();

                final String responseBodyTxt =
                        Objects.requireNonNull(responseBody, "请求自贡大华平台第一次交互获取授权信息接口okhttp错误,responseBody为空")
                                .string();
                log.debug(
                        "请求自贡大华平台第一次交互获取授权信息接口, requestBody:{} , responseCode:{}, responseBody:{}",
                        objectMapper.writeValueAsString(jsonParams),
                        responseCode,
                        responseBodyTxt
                );
                //获取realm 和 randomKey
                if (!checkFirstResponseSuccessful(responseBodyTxt)) {
                    log.error(
                            "请求自贡大华平台第一次交互获取授权信息接口okhttp错误,requestBody:{} ,responseCode:{} ,responseBody:{}",
                            objectMapper.writeValueAsString(jsonParams),
                            responseCode,
                            responseBodyTxt);
                    throw new RuntimeException(
                            MessageFormat.format(
                                    "请求自贡大华平台第一次交互获取授权信息接口okhttp错误,http 状态码:{0} ,响应内容:{1}, 请求参数:{2}", responseCode, responseBodyTxt, jsonParams));
                }
                // 请求成功
                firstResponse = objectMapper.readValue(responseBodyTxt, FirstResponse.class);
            } catch (Exception e) {
                //http请求出错
                log.error("请求自贡大华平台第一次交互获取授权信息接口okhttp错误, 错误信息为 {}", e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new RuntimeException("请求自贡大华 获取randomKey 以及 realm 失败", e);
        }
        return firstResponse;
    }


    private String getSignature(String password, String userName, String realm, String randomKey, String encryptType) {
        String signature;
        if (!"MD5".equals(encryptType)) {
            throw new RuntimeException("加密方式只支持MD5");
        }
        try {
            signature = calculateMD5WithLowerCase(password);
            signature = calculateMD5WithLowerCase(userName + signature);
            signature = calculateMD5WithLowerCase(signature);
            signature = calculateMD5WithLowerCase(userName + ":" + realm + ":" + signature);
            signature = calculateMD5WithLowerCase(signature + ":" + randomKey);
        } catch (Exception e){
            throw new RuntimeException("获取签名失败", e);
        }
        return signature;
    }

    public static String calculateMD5WithLowerCase(String s) {
        try {
            MessageDigest md5MessageDigest = MessageDigest.getInstance("MD5");
            byte[] bytes = s.getBytes(StandardCharsets.UTF_8);
            md5MessageDigest.update(bytes);
            byte[] byteArray = md5MessageDigest.digest();
            char[] hexDigits = {'0','1','2','3','4','5','6','7','8','9','a','b','c','d','e','f'};
            char[] charArray = new char[byteArray.length * 2];
            int index = 0;
            for (byte b : byteArray) {
                charArray[index++] = hexDigits[b>>4 & 0xf];
                charArray[index++] = hexDigits[b & 0xf];
            }

            return new String(charArray);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Getter
    @Setter
    static class FirstResponse {
        private String realm;

        private String randomKey;

        private String encryptType;

        private String method;

        private String clientType;
    }

    @Getter
    @Setter
    @JsonIgnoreProperties(ignoreUnknown = true)
    static class WithTokenEntity {
        private String token;

        private Integer duration;

        private String userName;

        private String userId;

        private String userCode;

        private String serviceAbility;

        private String lastLoginTime;
    }

    @Getter
    @Setter
    @JsonIgnoreProperties(ignoreUnknown = true)
    static class OrgEntity {
        private List<OrgInfo> results;

        private Integer totalCount;
    }

    @Getter
    @Setter
    @JsonIgnoreProperties(ignoreUnknown = true)
    static class OrgInfo{

        /**
         * 组织类型
         */
        private String orgType;

        /**
         * 组织名称
         */
        private String name;

        /**
         * 自定义编码
         */
        private String sn;

        /**
         * 组织编码
         */
        private String orgCode;
    }

    @Getter
    @Setter
    @JsonIgnoreProperties(ignoreUnknown = true)
    static class DeviceEntity {
        private List<DeviceInfo> devices;
    }

    @Getter
    @Setter
    @JsonIgnoreProperties(ignoreUnknown = true)
    static class DeviceInfo {
        private String code;

        private String name;

        private List<Units> units;
    }

    @Getter
    @Setter
    @JsonIgnoreProperties(ignoreUnknown = true)
    static class Units {
        private String deviceCode;

        private String unitType;

        private List<Channels> channels;
    }

    @Getter
    @Setter
    @JsonIgnoreProperties(ignoreUnknown = true)
    static class Channels {
        private String deviceCode;

        private String channelCode;

        /**
         *
         */
        private String channelType;

        private String cameraFunctions;

        private String gpsX;

        private String gpsY;
    }

    public static void main(String[] args) throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
//        String json = "{\"devices\":[{\"code\":\"123\",\"name\":\"设备1\",\"units\":[{\"deviceCode\":\"123\",\"unitType\":\"1\",\"channels\":[{\"deviceCode\":\"123\",\"channelType\":\"1\",\"cameraFunctions\":\"8\",\"gpsX\":\"103\",\"gpsY\":\"28\"}]}]}]}";
//
//        System.out.println(objectMapper.writeValueAsString(objectMapper.readValue(json, ZgDahuaDeviceInfoPullTask.DeviceEntity.class)));
        String json = "{\"results\":[{\"orgType\":\"1\",\"name\":\"组织1\",\"sn\":\"1234567890\",\"orgCode\":\"1\"}],\"totalCount\":1}";
        OrgEntity orgEntity = objectMapper.readValue(json, OrgEntity.class);
        System.out.println(objectMapper.writeValueAsString(orgEntity));
    }
}
