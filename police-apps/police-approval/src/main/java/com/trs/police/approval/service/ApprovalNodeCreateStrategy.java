package com.trs.police.approval.service;

import com.trs.police.common.core.dto.NodeCreateStrategyDTO;
import com.trs.police.common.core.vo.approval.NodeCreateStrategyResult;

/**
 * 审批节点的创建配置
 *
 * <AUTHOR>
 */
public interface ApprovalNodeCreateStrategy {

    /**
     * 策略名称
     *
     * @return 策略名称
     */
    String strategyName();

    /**
     * 获取创建节点的信息
     *
     * @param dto 参数
     * @return 创建节点的信息
     */
    NodeCreateStrategyResult createInfo(NodeCreateStrategyDTO dto);
}
