package com.trs.police.approval.service;

import com.trs.police.approval.constant.enums.NodeStatusEnum;
import com.trs.police.approval.domain.entity.NodeConfigEntity;
import com.trs.police.approval.domain.entity.NodeEntity;
import com.trs.police.approval.domain.entity.NodeResultEntity;
import com.trs.police.approval.domain.entity.ProcessConfigEntity;
import com.trs.police.approval.domain.vo.RoleLevelVOV2;
import com.trs.police.approval.mapper.ApprovalNodeConfigMapper;
import com.trs.police.approval.mapper.ApprovalNodeMapper;
import com.trs.police.approval.mapper.ApprovalNodeResultMapper;
import com.trs.police.approval.mapper.ApprovalProcessConfigMapper;
import com.trs.police.common.core.constant.ApprovalNodeFieldPramConstant;
import com.trs.police.common.core.dto.approval.ApprovalNodeFieldParam;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.utils.AreaUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 审批节点服务
 *
 * <AUTHOR>
 */
@Component
public class ApprovalNodeService {

    @Resource
    private ApprovalNodeConfigMapper approvalNodeConfigMapper;

    @Resource
    private ApprovalNodeMapper approvalNodeMapper;

    @Resource
    private ApprovalNodeResultMapper approvalNodeResultMapper;

    @Resource
    private ApprovalProcessConfigMapper processConfigMapper;


    /**
     * 获取下一节点
     *
     * @param nodeConfig 当前流程配置
     * @param node 当前节点
     * @return 下一个节点
     */
    public Optional<NodeEntity> getNextNode(NodeConfigEntity nodeConfig, NodeEntity node) {
        List<NodeConfigEntity> configEntities = approvalNodeConfigMapper.selectByProcessConfigId(nodeConfig.getProcessConfigId());
        List<NodeConfigEntity> list = configEntities.stream()
                .filter(config -> config.getOrderNumber() >= nodeConfig.getOrderNumber() + 1)
                .sorted(Comparator.comparingInt(NodeConfigEntity::getOrderNumber))
                .collect(Collectors.toList());
        if (list.isEmpty()) {
            return Optional.empty();
        }
        for (NodeConfigEntity nextConfig : list) {
            NodeEntity nodeEntity = approvalNodeMapper.selectByProcessIdAndNodeConfigId(node.getProcessId(), nextConfig.getId());
            // 有些动态节点是可有可无的
            if (Objects.nonNull(nodeEntity)) {
                return Optional.ofNullable(nodeEntity);
            }
        }
        return Optional.empty();
    }

    /**
     * 当前节点是否已经通过
     *
     * @param node 当前节点
     * @return 是否通过
     */
    public Boolean currentNodeIsPass(NodeEntity node) {
        List<NodeResultEntity> allResults = approvalNodeResultMapper.selectByNodeId(node.getId())
                .stream()
                .filter(result -> result.getResult().equals(NodeStatusEnum.PASSED))
                .collect(Collectors.toList());
        return allResults.size() == node.getApprovers().length;
    }

    /**
     * 获取非空的节点配置列表
     *
     * @param processConfigId 流程配置id
     * @param currentUser 发起协作的操作人员
     * @param dynamicNodeParams 动态节点参数
     * @return 非空的节点配置列表
     */
    public List<NodeConfigEntity> getNotEmptyNodeConfig(Long processConfigId, CurrentUser currentUser, Map<String, List<ApprovalNodeFieldParam>> dynamicNodeParams) {
        List<NodeConfigEntity> configEntities = approvalNodeConfigMapper.selectByProcessConfigId(processConfigId);
        final List<NodeConfigEntity> nodeConfigs = filterConfig(configEntities, currentUser);
        if (nodeConfigs.isEmpty()) {
            ProcessConfigEntity processConfigEntity = processConfigMapper.selectById(processConfigId);
            throw new TRSException(String.format("审批节点配置[t_approval_node_config]为空，审批[%s]自动通过！", processConfigEntity.getTemplateName()));
        }
        // 动态节点参数
        initDynamicNodeParams(nodeConfigs, dynamicNodeParams);
        return nodeConfigs;
    }

    private List<NodeConfigEntity> filterConfig(List<NodeConfigEntity> conf, CurrentUser currentUser) {
        return conf.stream()
                .filter(config -> filterByAreaType(currentUser, config))
                .collect(Collectors.toList());

    }

    private Boolean filterByAreaType(CurrentUser currentUser, NodeConfigEntity nodeConfig) {
        // 没有配置审批信息，通过其它方式获取的审批者 所以直接返回
        if (Objects.isNull(nodeConfig.getApprovalRoles())) {
            return true;
        }
        boolean isOld = Stream.of(nodeConfig.getApprovalRoles())
                .allMatch(vo -> vo.old());
        // v1版本 只配置roleId，level
        if (isOld) {
            return true;
        }
        // v2版本
        String code = currentUser.getDept().getDistrictCode();
        Integer levelCode = AreaUtils.level(code).getCode();
        // 发起人部门地域类型过滤
        return Stream.of(nodeConfig.getApprovalRoles())
                .allMatch(vo -> Objects.isNull(vo.getPromoterAreaType()) || levelCode.equals(vo.getPromoterAreaType()));
    }


    private void initDynamicNodeParams(List<NodeConfigEntity> nodeConfigs, Map<String, List<ApprovalNodeFieldParam>> dynamicNodeParams) {
        if (Objects.isNull(dynamicNodeParams) || dynamicNodeParams.isEmpty()) {
            return;
        }
        for (NodeConfigEntity node : nodeConfigs) {
            RoleLevelVOV2[] approvalRoles = node.getApprovalRoles();
            if (Objects.isNull(approvalRoles) || approvalRoles.length == 0) {
                continue;
            }
            RoleLevelVOV2[] rs = Stream.of(approvalRoles)
                    .map(role -> {
                        if (Objects.nonNull(role.getType()) && dynamicNodeParams.containsKey(role.getType())) {
                            return initNodeProperties(role, dynamicNodeParams.get(role.getType()));
                        } else {
                            return Arrays.asList(role);
                        }
                    })
                    .flatMap(List::stream)
                    .toArray(RoleLevelVOV2[]::new);
            node.setApprovalRoles(rs);
        }
    }

    private List<RoleLevelVOV2> initNodeProperties(RoleLevelVOV2 role, List<ApprovalNodeFieldParam> params) {
        if (CollectionUtils.isEmpty(params)) {
            return Collections.singletonList(role);
        }
        // 校验是否存在
        List<RoleLevelVOV2> result = Arrays.asList(role);
        for (ApprovalNodeFieldParam param : params) {
            if (ApprovalNodeFieldPramConstant.CHILD_DEPT_TYPE.equals(param.getFieldName())) {
                result = result.stream()
                        .map(item -> param.getValue()
                                    .stream()
                                    .map(Long::valueOf)
                                    .map(type -> {
                                        RoleLevelVOV2 r = new RoleLevelVOV2();
                                        BeanUtils.copyProperties(item, r);
                                        r.setChildDeptType(type);
                                        return r;
                                    })
                                    .collect(Collectors.toList())
                        ).flatMap(List::stream)
                        .collect(Collectors.toList());
            }
        }
        return result;
    }
}
