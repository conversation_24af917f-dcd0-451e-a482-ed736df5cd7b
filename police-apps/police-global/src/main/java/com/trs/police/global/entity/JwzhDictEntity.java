package com.trs.police.global.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 导入码表
 *
 * <AUTHOR>
 * @since 2023/1/31 17:26
 **/
@TableName("JWZH_DICT2")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class JwzhDictEntity {


    private Integer id;

    private Integer glid;
    /**
     * 类型
     */
    private String zdbh;
    /**
     * 类型名称
     */
    private String zdmc;
    /**
     * 代码
     */
    private String dm;
    /**
     * 中文名
     */
    private String ct;
}
