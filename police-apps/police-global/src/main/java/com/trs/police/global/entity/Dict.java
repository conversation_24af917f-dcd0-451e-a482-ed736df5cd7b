package com.trs.police.global.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.vo.DictListVO;
import com.trs.police.common.core.vo.LabelPoliceKindRelationVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 字典表
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "t_dict")
public class Dict implements Serializable {

    private static final long serialVersionUID = 8164142332012636899L;
    /**
     * 数据主键（Mysql 推荐使用连续自增的整数）
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 上级节点id(仅仅用于字典表维护 )
     */
    @TableField(value = "p_id")
    private Long pId;
    /**
     * 码表类型
     */
    @TableField(value = "type")
    private String type;
    /**
     * 码值
     */
    @TableField(value = "code")
    private Long code;
    /**
     * 显示名称
     */
    @TableField(value = "name")
    private String name;
    /**
     * 上级代码(同一类型码表本身码值的上下关系)
     */
    @TableField(value = "p_code")
    private Long pCode;
    /**
     * 对码表的说明(同一类型码表应该具有同样的说明)
     */
    @TableField(value = "dict_desc")
    private String dictDesc;
    /**
     * 排序字段
     */
    @TableField(value = "show_number")
    private Integer showNumber;
    /**
     * 编码标准 如 公安部标准  国家标准等
     */
    @TableField(value = "standard")
    private String standard;
    /**
     * 其他标记
     */
    @TableField(value = "flag")
    private String flag;

    private String color;

    /**
     * 状态 0=停用 1=启用
     */
    @TableField(value = "status")
    private Integer status;

    @TableField(exist = false)
    private List<Dict> children = new ArrayList<>();

    /**
     * 标签
     */
    @TableField(exist = false)
    private List<LabelPoliceKindRelationVO> labels;

    /**
     * dto
     *
     * @return {@link DictDto}
     */
    public DictDto toDto() {
        return new DictDto(this.getId(), this.getPId(), this.getCode(), this.pCode, this.getName(),
            this.getShowNumber(), this.getDictDesc(), this.color, this.type,
            this.children.stream().map(Dict::toDto).collect(Collectors.toList()), this.getFlag(),this.getLabels());
    }

    /**
     * dto
     *
     * @return {@link DictListVO}
     */
    public DictListVO toDto2(){
        DictListVO vo = new DictListVO();
        vo.setId(this.getId());
        vo.setPid(this.getPId());
        vo.setCode(this.getCode());
        vo.setType(this.getType());
        vo.setName(this.getName());
        vo.setPCode(this.getPCode());
        vo.setDictDesc(this.getDictDesc());
        vo.setFlag(this.getFlag());
        vo.setShowNumber(this.getShowNumber());
        vo.setColor(this.getColor());
        vo.setChildren(this.children.stream().map(Dict::toDto2).collect(Collectors.toList()));
        return vo;
    }
}
