package com.trs.police.global.dto;

import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.global.entity.CalendarEntity;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * dto
 */
@Data
public class CalendarAddDTO {

    /**
     * id
     */
    private Long id;
    /**
     * 内容
     */
    private String content;
    
    /**
     * 日期
     */
    private String date;

    /**
     * risk:风险
     */
    private String type;

    /**
     * dto转entity
     *
     * @param currentUser currentUser
     * @return entity
     */
    public CalendarEntity toEntity(CurrentUser currentUser) {
        CalendarEntity entity = new CalendarEntity();
        entity.setContent(this.content);
        entity.setDate(TimeUtils.stringToDate(this.date));
        entity.setCreateUserId(currentUser.getId());
        entity.setCreateDeptId(currentUser.getDeptId());
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateUserId(currentUser.getId());
        entity.setUpdateDeptId(currentUser.getDeptId());
        entity.setType(this.type);
        entity.setUpdateTime(LocalDateTime.now());
        return entity;
    }
}