package com.trs.police.global.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.global.entity.RequestParamsEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @Date 2023/1/6 10:03
 */
@Mapper
public interface RequestParamsMapper extends BaseMapper<RequestParamsEntity> {

    /**
     * 根据module获取参数
     *
     * @param module 模块
     * @return 参数
     */
    @ResultMap("mybatis-plus_RequestParamsEntity")
    @Select("select params from t_request_params where module = #{module} ")
    RequestParamsEntity getByTypeAndModule(@Param("module") String module);
}
