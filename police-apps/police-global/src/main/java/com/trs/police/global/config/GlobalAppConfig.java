package com.trs.police.global.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * global 服务相关配置
 *
 * <AUTHOR>
 * @date 2022/09/08
 */
@Component
@ConfigurationProperties(prefix = "global.app")
@Data
public class GlobalAppConfig {

    /**
     * 顶级行政区划
     */
    private String topLevelDistrictCode;
}
