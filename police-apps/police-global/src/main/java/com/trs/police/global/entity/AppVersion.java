package com.trs.police.global.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/11/30
 */
@TableName(value = "t_app_version",autoResultMap = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AppVersion {

    /**
     * id
     */
    private Long id;

    /**
     * app版本code
     */
    private String versionCode;

    /**
     * app版本名
     */
    private String versionName;

    /**
     * 更新日志
     */
    private String log;
}
