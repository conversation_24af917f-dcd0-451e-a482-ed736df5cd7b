package com.trs.police.global.service.impl;

import com.trs.common.exception.ServiceException;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.utils.OkHttpUtil;
import com.trs.police.global.constant.SourceConstants;
import com.trs.police.global.service.ISourceService;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.springframework.stereotype.Service;

import java.util.Base64;
import java.util.List;
import java.util.regex.Pattern;

/**
 * @ClassName SourceServiceImpl
 * @Description 相关资源数据获取
 * <AUTHOR>
 * @Date 2024/6/20 10:08
 **/
@Service
public class SourceServiceImpl implements ISourceService {

    @Override
    public List<String> image(String type, String urlBase64) throws ServiceException {
        String result = "";
        if (SourceConstants.IMAGE_TYPE_SEARCH_ARCHIVE.equals(type)) {
            result = requestImage(urlBase64);
        }
        return List.of(result);
    }

    private String requestImage(String urlBase64) {
        String imageUrl = new String(Base64.getDecoder().decode(urlBase64));
        final byte[] response = OkHttpUtil.getInstance().getBytes(imageUrl);
        return "data:image/png;base64," + Base64.getEncoder().encodeToString(response);
    }

    @Override
    public Boolean checkHost(String host) throws ServiceException {
        final String hostWhiteNamesRegex = getHostWhiteNamesRegex();
        if (StringUtils.isEmpty(hostWhiteNamesRegex)) {
            return true;
        }
        final boolean result = Pattern.matches(hostWhiteNamesRegex, host);
        if (!result) {
            throw new ServiceException("非法访问！");
        }
        return true;
    }

    private String getHostWhiteNamesRegex() {
        return BeanFactoryHolder.getEnv().getProperty("global.source.white-names.regex", "");
    }

}
