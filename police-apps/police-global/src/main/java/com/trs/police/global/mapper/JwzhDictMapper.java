package com.trs.police.global.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.common.core.vo.JwzhDictVO;
import com.trs.police.global.entity.JwzhDictEntity;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 导入码表mapper
 *
 * <AUTHOR>
 * @since 2023/1/31 17:25
 **/
@Mapper
public interface JwzhDictMapper extends BaseMapper<JwzhDictEntity> {

    /**
     * 查询码表
     *
     * @param type 类型
     * @return 码值
     */
    List<JwzhDictVO> selectTree(@Param("type") String type);

    /**
     * 查询
     *
     * @param code code
     * @param type 类型
     * @return 实体
     */
    @Select("select * from JWZH_DICT2 where zdbh=#{type} and dm =#{code}")
    List<JwzhDictVO> selectByTypeAndCode(@Param("type") String type, @Param("code") String code);

}
