DELIMITER $$
DROP PROCEDURE IF EXISTS `add_index` $$
CREATE PROCEDURE add_index()
BEGIN
    IF NOT EXISTS( SELECT * FROM  information_schema.statistics WHERE table_schema=(select database()) AND table_name='t_spacetime_collision_result' AND index_name = 't_spacetime_collision_display_type_index')
    THEN
        ALTER TABLE t_spacetime_collision_result ADD INDEX `t_spacetime_collision_display_type_index`(`display_type`) USING BTREE;
    END IF;

    IF NOT EXISTS( SELECT * FROM  information_schema.statistics WHERE table_schema=(select database()) AND table_name='t_spacetime_collision_result' AND index_name = 't_spacetime_collision_gzylx_index')
    THEN
        ALTER TABLE t_spacetime_collision_result ADD INDEX `t_spacetime_collision_gzylx_index`(`gzylx`) USING BTREE;
    END IF;
END $$
DELIMITER ;
CALL add_index;