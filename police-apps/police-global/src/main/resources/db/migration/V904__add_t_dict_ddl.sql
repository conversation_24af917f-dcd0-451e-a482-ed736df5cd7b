DELIMITER $$
DROP PROCEDURE IF EXISTS `insert_dict_data` $$
CREATE PROCEDURE insert_dict_data()
BEGIN
    DECLARE topid INT;
    DECLARE jwdb INT;
    DECLARE zwdb INT;
    DELETE FROM t_dict WHERE type LIKE 'db_task_tracing_type%';
    INSERT INTO t_dict (`type`, code, name, p_code, dict_desc, show_number, standard, flag, color, status)
    VALUES ('db_task_tracing_type_group', 0, '盯办类别', 0, 'top', 0, NULL, NULL, NULL, 1);
    SET topid = LAST_INSERT_ID();
    update t_dict set p_id = topid where type = 'db_task_tracing_type_group' and code = 0;

-- 警务督办
    INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color, status)
    VALUES (topid, 'db_task_tracing_type_one', 100, '警务督办', 0, 'group_one', 1, NULL, null, NULL, 1);
    SET jwdb = LAST_INSERT_ID();

    INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color, status)
    VALUES (jwdb, 'db_task_tracing_type_one', 1, '领导批示', 100, 'type', 2, NULL, null, NULL, 1),
           (jwdb, 'db_task_tracing_type_one', 2, '敏感案事件', 100, 'type', 3, NULL, null, NULL, 1),
           (jwdb, 'db_task_tracing_type_one', 3, '上级交办', 100, 'type', 4, NULL, null, NULL, 1);
-- 政务督办
    INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color, status)
    VALUES (topid, 'db_task_tracing_type_two', 101, '政务督办', 0, 'group_two', 1, NULL, NULL, NULL, 1);
    SET zwdb = LAST_INSERT_ID();
    INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color, status)
    VALUES (zwdb, 'db_task_tracing_type_two', 1, '领导批示', 101, 'type', 2, NULL, NULL, NULL, 1),
           (zwdb, 'db_task_tracing_type_two', 4, '会议决策', 101, 'type', 3, NULL, NULL, NULL, 1),
           (zwdb, 'db_task_tracing_type_two', 3, '上级交办', 101, 'type', 4, NULL, NULL, NULL, 1),
           (zwdb, 'db_task_tracing_type_two', 5, '重点工作', 101, 'type', 5, NULL, NULL, NULL, 1);
END $$
DELIMITER ;
CALL insert_dict_data;
DROP PROCEDURE IF EXISTS insert_dict_data;