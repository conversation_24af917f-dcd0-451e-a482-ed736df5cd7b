UPDATE t_profile_module SET cn_name='档案管理', en_name='archive', `type`='personV2', pid=NULL, is_archive=0, show_order=1, table_schema='{}', form_schema='{}', list_schema='{"name": "人员信息", "type": "LIST_SCHEMA", "table": "t_profile_person", "fields": [{"db": {"table": "t_profile_person", "column": "photo", "mapping": "json_to_image_array", "jdbcType": "json_object_array"}, "name": "photo", "listSchema": {"style": {"align": "center", "fixed": "left", "ellipsis": true}, "schema": {"type": "photo", "title": "照片"}, "properties": {"isPhoto": true, "copyable": false, "editable": false, "required": false, "sortable": false, "isRelatedShow": true}}}, {"db": {"table": "t_profile_person", "column": "name", "jdbcType": "string"}, "name": "name", "listSchema": {"style": {"align": "center", "ellipsis": true}, "schema": {"type": "string", "title": "姓名"}, "properties": {"isName": true, "copyable": false, "editable": false, "required": false, "sortable": false, "isRelatedShow": true}}}, {"db": {"table": "t_profile_person", "column": "id_number", "jdbcType": "string"}, "name": "idNumber", "listSchema": {"style": {"align": "center", "ellipsis": false}, "schema": {"type": "string", "title": "证件号码"}, "properties": {"href": "/ys-app/archives/person/details?id={value}", "copyable": false, "editable": false, "required": false, "sortable": false, "isRelatedShow": true, "validateOption": {"pattern": "/[0-9]/"}}}}, {"db": {"table": "t_profile_person", "column": "person_label", "mapping": "label_id_array_to_name", "jdbcType": "label_id_array"}, "name": "personLabel", "listSchema": {"style": {"align": "center", "width": 400, "ellipsis": true}, "filter": {"key": "personLabel", "type": "multiple-tree", "value": ["&&person_label&&"], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "人员标签"}, "schema": {"type": "array", "title": "人员标签"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 2, "isRelatedShow": true}}}, {"db": {"table": "t_profile_person_police_control", "column": "control_person", "mapping": "user_id_array_to_user_name_array", "jdbcType": "user_id_array", "databaseRelation": {"type": "FOREIGN_KEY", "table": "t_profile_person_control", "column": "person_id"}}, "name": "dutyPolice", "listSchema": {"style": {"align": "center"}, "schema": {"type": "array", "title": "责任民警"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_profile_person_police_control", "column": "control_station", "mapping": "dept_code_to_dept_name", "jdbcType": "dept_code", "databaseRelation": {"type": "FOREIGN_KEY", "table": "t_profile_person_control", "column": "person_id"}}, "name": "dutyPoliceStation", "listSchema": {"style": {"align": "center"}, "filter": {"key": "dutyPoliceStation", "type": "multiple-tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "deptCode", "children": "children"}, "displayName": "责任派出所"}, "schema": {"type": "string", "title": "责任派出所"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "isRelatedShow": true}}}, {"db": {"table": "t_profile_person", "column": "registered_residence", "mapping": "district_code_to_name", "jdbcType": "district"}, "name": "registeredResidence", "listSchema": {"style": {"align": "center"}, "filter": {"key": "registeredResidence", "type": "multiple-tree", "value": ["&&district&&"], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "户籍地所在区"}, "schema": {"type": "string", "title": "户籍地所在区"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_person", "column": "create_time", "mapping": "date_time_to_text", "jdbcType": "datetime"}, "name": "createTime", "listSchema": {"style": {"align": "center"}, "filter": {"key": "createTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "录入时间"}, "schema": {"type": "string", "title": "录入时间"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": true}}}, {"db": {"table": "t_profile_person", "column": "update_time", "mapping": "date_time_to_text", "jdbcType": "datetime"}, "name": "updateTime", "listSchema": {"style": {"align": "center"}, "filter": {"key": "updateTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "更新时间"}, "schema": {"type": "string", "title": "更新时间"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": true, "sortDefault": "descending"}}}, {"db": {"table": "t_profile_person", "column": "profile_status", "mapping": "archive_code_to_name", "jdbcType": "string"}, "name": "profileStatus", "listSchema": {"style": {"align": "center", "ellipsis": true}, "schema": {"type": "string", "title": "档案状态"}, "properties": {"isName": true, "copyable": false, "editable": false, "required": false, "sortable": false, "isRelatedShow": true}}}], "selectable": true, "searchFields": [{"key": "idNumber", "name": "身份证号"}, {"key": "name", "name": "姓名"}], "profileDataPermission": [{"db": {"table": "t_profile_person", "column": "person_label", "jdbcType": "string"}, "type": "personLabel"}]}', is_add=0, database_relation='{}', show_schema_type=NULL, add_schema_type=NULL, is_operation_content=1, is_mobile_content=1, is_web_content=1, is_fk_content=0 WHERE id=1575;

UPDATE t_profile_module SET cn_name='相关事件', en_name='relatedEvent', `type`='personV2', pid=1390, is_archive=1, show_order=9, table_schema=NULL, form_schema=NULL, list_schema='{"name": "相关事件", "type": "LIST_SCHEMA", "table": "t_profile_event", "fields": [{"db": {"table": "t_profile_person_event_relation", "column": "event_type", "mapping": "dict_code_to_name", "jdbcType": "number", "databaseRelation": {"type": "RELATION_TABLE", "table": "t_profile_person_group_relation", "joinTo": {"table": "t_profile_event", "column": "id", "joinColumn": "event_id"}, "joinFrom": {"table": "t_profile_person", "column": "id", "joinColumn": "person_id"}}}, "dict": {"type": "profile_event_type", "codeToId": true}, "name": "event_type", "listSchema": {"style": {"align": "center"}, "schema": {"type": "select", "title": "事件类别"}, "properties": {"copyable": false, "editable": true, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_event", "column": "name", "jdbcType": "string"}, "name": "name", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "事件名称"}, "properties": {"href": "/ys-app/archives/police-archive-event/details?id={value}", "copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_event", "column": "create_dept_id", "mapping": "dept_id_to_dept_name", "jdbcType": "number"}, "name": "create_dept_id", "listSchema": {"style": {"align": "center"}, "filter": {"key": "create_dept_id", "type": "tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "id", "children": "children"}, "displayName": "录入单位"}, "schema": {"type": "string", "title": "录入单位"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_event", "column": "create_time", "mapping": "date_time_to_general_string", "jdbcType": "datetime"}, "name": "createTime", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "录入时间"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}], "selectable": false, "extendFields": [{"table": "t_profile_person_event_relation", "value": 4, "column": "police_kind", "jdbcType": "integer"}], "searchFields": [{"key": "name", "name": "事件名称"}]}', is_add=1, database_relation='{"type": "RELATION_TABLE", "table": "t_profile_person_event_relation", "joinTo": {"table": "t_profile_event", "column": "id", "joinColumn": "event_id"}, "joinFrom": {"table": "t_profile_person", "column": "id", "joinColumn": "person_id"}, "extendCondition": [{"value": 4, "column": "police_kind"}]}', show_schema_type='LIST_SCHEMA', add_schema_type='LIST_SCHEMA', is_operation_content=1, is_mobile_content=1, is_web_content=1, is_fk_content=0 WHERE id=1412;
UPDATE t_profile_module SET cn_name='工作措施', en_name='gzcs', `type`='personV2', pid=1390, is_archive=1, show_order=2, table_schema=NULL, form_schema=NULL, list_schema='{"name": "工作措施", "type": "LIST_SCHEMA", "table": "t_profile_person_gzcs", "fields": [{"db": {"table": "t_profile_person_gzcs", "column": "detail", "jdbcType": "string"}, "name": "detail", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "工作措施"}, "properties": {"copyable": false, "editable": true, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_person_gzcs", "column": "data_source", "mapping": "dict_code_to_name", "jdbcType": "integer"}, "dict": {"type": "profile_person_gzcs_data_source", "codeToId": true}, "name": "data_source", "listSchema": {"style": {"align": "center"}, "schema": {"type": "select", "title": "数据来源"}, "properties": {"copyable": false, "editable": true, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_person_gzcs", "column": "data_source_remark", "jdbcType": "string"}, "name": "data_source_remark", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "数据来源描述"}, "properties": {"copyable": false, "editable": true, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_person_gzcs", "column": "create_dept_id", "mapping": "dept_id_to_dept_name", "jdbcType": "number"}, "name": "create_dept_id", "listSchema": {"style": {"align": "center"}, "filter": {"key": "create_dept_id", "type": "tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "id", "children": "children"}, "displayName": "录入单位"}, "schema": {"type": "string", "title": "录入单位"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_person_gzcs", "column": "create_time", "mapping": "date_time_to_general_string", "jdbcType": "datetime"}, "name": "createTime", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "录入时间"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}], "selectable": false, "extendFields": [{"table": "t_profile_person_gzcs", "value": 4, "column": "police_kind", "jdbcType": "integer"}], "searchFields": [{"key": "name", "name": "工作措施"}]}', is_add=1, database_relation='{"type": "FOREIGN_KEY", "table": "t_profile_person_gzcs", "column": "person_id", "idColumn": "id", "primaryColumn": "id", "extendCondition": [{"value": 4, "column": "police_kind"}]}', show_schema_type='LIST_SCHEMA', add_schema_type='LIST_SCHEMA', is_operation_content=1, is_mobile_content=1, is_web_content=1, is_fk_content=0 WHERE id=1405;
UPDATE t_profile_module SET cn_name='风险点信息', en_name='riskPoint', `type`='personV2', pid=1390, is_archive=1, show_order=1, table_schema='{"name": "风险点信息", "type": "TABLE_SCHEMA", "table": "t_profile_person_risk_za", "fields": [{"db": {"table": "t_profile_person_risk_za", "column": "control_level", "mapping": "dict_code_to_name", "jdbcType": "string"}, "dict": {"type": "profile_person_control_level_za"}, "name": "control_level", "tableSchema": {"span": 1, "type": "string", "title": "风险级别", "copyable": false}}, {"db": {"table": "t_profile_person_risk_za", "column": "work_unit", "jdbcType": "string"}, "name": "work_unit", "tableSchema": {"span": 1, "type": "string", "title": "工作单位", "copyable": false}}, {"db": {"table": "t_profile_person_risk_za", "column": "work_duty", "jdbcType": "string"}, "name": "work_duty", "tableSchema": {"span": 1, "type": "string", "title": "职务", "copyable": false}}, {"db": {"table": "t_profile_person_risk_za", "column": "work_duty_type", "mapping": "dict_code_to_name", "jdbcType": "string"}, "dict": {"type": "profile_person_duty_type"}, "name": "work_duty_type", "tableSchema": {"span": 1, "type": "string", "title": "职务类型", "copyable": false}}, {"db": {"table": "t_profile_person_risk_za", "column": "event_place", "jdbcType": "string"}, "name": "event_place", "tableSchema": {"span": 1, "type": "string", "title": "事发地", "copyable": false}}, {"db": {"table": "t_profile_person_risk_za", "column": "sksflry", "mapping": "dict_code_to_name", "jdbcType": "string"}, "dict": {"type": "yes_or_not"}, "name": "sksflry", "tableSchema": {"span": 1, "type": "string", "title": "是否三跨三分离人员", "copyable": false}}, {"db": {"table": "t_profile_person_risk_za", "column": "demand_situation", "jdbcType": "string"}, "name": "demand_situation", "tableSchema": {"span": 1, "type": "string", "title": "诉求情况", "copyable": false}}, {"db": {"table": "t_profile_person_risk_za", "column": "work_difficulty", "jdbcType": "string"}, "name": "work_difficulty", "tableSchema": {"span": 1, "type": "string", "title": "工作难点", "copyable": false}}], "moduleUi": {"column": 3, "bordered": true}}', form_schema='{"name": "风险点信息", "type": "FORM_SCHEMA", "table": "t_profile_person_risk_za", "fields": [{"db": {"table": "t_profile_person_risk_za", "column": "control_level", "mapping": "dict_code_to_name", "jdbcType": "integer"}, "dict": {"type": "profile_person_control_level_za"}, "name": "control_level", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "radio", "titleLocation": "left"}}, "schema": {"type": "number", "title": "管控级别"}}}, {"db": {"table": "t_profile_person_risk_za", "column": "work_unit", "jdbcType": "string"}, "name": "work_unit", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "工作单位"}}}, {"db": {"table": "t_profile_person_risk_za", "column": "work_duty", "jdbcType": "string"}, "name": "work_duty", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "职务"}}}, {"db": {"table": "t_profile_person_risk_za", "column": "work_duty_type", "mapping": "dict_code_to_name", "jdbcType": "integer"}, "dict": {"type": "profile_person_duty_type"}, "name": "work_duty_type", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "radio", "titleLocation": "left"}}, "schema": {"type": "number", "title": "职务类型"}}}, {"db": {"table": "t_profile_person_risk_za", "column": "event_place", "jdbcType": "string"}, "name": "event_place", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "事发地"}}}, {"db": {"table": "t_profile_person_risk_za", "column": "sksflry", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "yes_or_not"}, "name": "sksflry", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "radio", "fieldNames": {"label": "name", "value": "code"}, "titleLocation": "left"}}, "schema": {"type": "number", "title": "是否三跨三分离人员"}}}, {"db": {"table": "t_profile_person_risk_za", "column": "demand_situation", "jdbcType": "string"}, "name": "demand_situation", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "诉求情况"}}}, {"db": {"table": "t_profile_person_risk_za", "column": "work_difficulty", "jdbcType": "string"}, "name": "work_difficulty", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "工作难点"}}}], "required": []}', list_schema=NULL, is_add=1, database_relation='{"type": "FOREIGN_KEY", "table": "t_profile_person", "column": "person_id"}', show_schema_type='TABLE_SCHEMA', add_schema_type='FORM_SCHEMA', is_operation_content=1, is_mobile_content=1, is_web_content=1, is_fk_content=0 WHERE id=1404;
UPDATE t_profile_module SET cn_name='相关群体', en_name='relatedGroup', `type`='personV2', pid=1390, is_archive=1, show_order=8, table_schema=NULL, form_schema=NULL, list_schema='{"name": "相关群体", "type": "LIST_SCHEMA", "table": "t_profile_group", "fields": [{"db": {"table": "t_profile_group", "column": "name", "jdbcType": "string"}, "name": "name", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "群体名称"}, "properties": {"href": "/ys-app/archives/police-archive-group/details?id={value}", "copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_person_group_relation", "column": "group_work", "mapping": "dict_code_to_name", "jdbcType": "number", "databaseRelation": {"type": "RELATION_TABLE", "table": "t_profile_person_group_relation", "joinTo": {"table": "t_profile_group", "column": "id", "joinColumn": "group_id"}, "joinFrom": {"table": "t_profile_person", "column": "id", "joinColumn": "person_id"}}}, "dict": {"type": "profile_person_group_work", "codeToId": true}, "name": "group_work", "listSchema": {"style": {"align": "center"}, "schema": {"type": "select", "title": "群体分工"}, "properties": {"copyable": false, "editable": true, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_profile_group", "column": "create_dept_id", "mapping": "dept_id_to_dept_name", "jdbcType": "number"}, "name": "createDept", "listSchema": {"style": {"align": "center"}, "filter": {"key": "createDept", "type": "tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "id", "children": "children"}, "displayName": "录入单位"}, "schema": {"type": "string", "title": "录入单位"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1}}}], "selectable": false, "extendFields": [{"table": "t_profile_person_group_relation", "value": 4, "column": "police_kind", "jdbcType": "integer"}], "searchFields": [{"key": "name", "name": "群体名称"}]}', is_add=1, database_relation='{"type": "RELATION_TABLE", "table": "t_profile_person_group_relation", "joinTo": {"table": "t_profile_group", "column": "id", "joinColumn": "group_id"}, "joinFrom": {"table": "t_profile_person", "column": "id", "joinColumn": "person_id"}, "extendCondition": [{"value": 4, "column": "police_kind"}]}', show_schema_type='LIST_SCHEMA', add_schema_type='LIST_SCHEMA', is_operation_content=1, is_mobile_content=1, is_web_content=1, is_fk_content=0 WHERE id=1411;

UPDATE t_profile_module SET cn_name='档案管理', en_name='archive', `type`='clue', pid=NULL, is_archive=0, show_order=1, table_schema=NULL, form_schema=NULL, list_schema='{"name": "相关线索", "type": "LIST_SCHEMA", "table": "t_profile_clue", "fields": [{"db": {"table": "t_profile_clue", "column": "name", "jdbcType": "string"}, "name": "name", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "线索名称"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "isRelatedShow": true}}}, {"db": {"table": "t_profile_clue", "column": "code", "jdbcType": "string"}, "name": "code", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "线索编号"}, "properties": {"href": "/ys-app/archives/clue/details?id={value}", "isName": true, "copyable": false, "editable": false, "required": false, "sortable": false, "isRelatedShow": true}}}, {"db": {"table": "t_profile_clue", "column": "source", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "profile_clue_source_type"}, "name": "source", "listSchema": {"style": {"align": "center"}, "filter": {"key": "source", "type": "select", "value": ["%%profile_clue_source_type%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "线索来源"}, "schema": {"type": "string", "title": "线索来源"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1, "isRelatedShow": true}}}, {"db": {"table": "t_profile_clue", "column": "detail", "jdbcType": "string"}, "name": "detail", "listSchema": {"style": {"align": "center", "ellipsis": true}, "schema": {"type": "string", "title": "线索内容"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "isRelatedShow": true}}}, {"db": {"table": "t_profile_clue", "column": "tags", "jdbcType": "string"}, "name": "tags", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "智能打标"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_clue", "column": "target_location", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "profile_clue_location"}, "name": "target_location", "listSchema": {"style": {"align": "center"}, "filter": {"key": "target_location", "type": "select", "value": ["%%profile_clue_location%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "指向地点"}, "schema": {"type": "string", "title": "指向地点"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_profile_clue", "column": "target_time", "mapping": "date_time_to_general_string", "jdbcType": "dateTime"}, "name": "target_time", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "指向日期"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_clue", "column": "action_type", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "profile_clue_action_type"}, "name": "action_type", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "行为方式"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_profile_clue", "column": "group_type", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "profile_clue_group_type"}, "name": "group_type", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "群体类型"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_clue", "column": "clue_label", "mapping": "label_id_array_to_name", "jdbcType": "label_id_array"}, "name": "clueLabel", "listSchema": {"style": {"align": "left"}, "schema": {"type": "array", "title": "线索标签"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 2}}}, {"db": {"table": "t_profile_clue", "column": "report_dept", "mapping": "dept_id_to_dept_name", "jdbcType": "number"}, "name": "report_dept", "listSchema": {"style": {"align": "center", "width": 120}, "filter": {"key": "report_dept", "type": "tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "id", "children": "children"}, "displayName": "上报单位"}, "schema": {"type": "string", "title": "上报单位"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "isRelatedShow": true}}}, {"db": {"table": "t_profile_clue", "column": "report_time", "mapping": "date_time_to_general_string", "jdbcType": "datetime"}, "name": "report_time", "listSchema": {"style": {"align": "center", "width": 120}, "filter": {"key": "report_time", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "上报时间", "defaultValue": {"range": "3"}}, "schema": {"type": "string", "title": "上报时间"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": true, "sortDefault": "descending"}}}], "selectable": true, "searchFields": [{"key": "detail", "name": "线索内容"}, {"key": "code", "name": "线索编号"}]}', is_add=0, database_relation='{}', show_schema_type='LIST_SCHEMA', add_schema_type='LIST_SCHEMA', is_operation_content=1, is_mobile_content=1, is_web_content=1, is_fk_content=0 WHERE id=301;