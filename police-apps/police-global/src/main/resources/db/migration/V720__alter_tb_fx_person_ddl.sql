DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS( SELECT * FROM information_schema.columns WHERE table_schema=(select database()) AND table_name='tb_fx_person' AND column_name='care_status')
    THEN
ALTER TABLE tb_fx_person ADD care_status INT DEFAULT 0 NULL COMMENT '关注状态';
END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;