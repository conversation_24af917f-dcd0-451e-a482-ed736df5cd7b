drop procedure if exists add_col;
-- 定义新的分隔符
DELIMITER //
-- 创建存储过程
CREATE PROCEDURE add_col()-- 新增一个存储过程
BEGIN

    IF not EXISTS(SELECT 1
                  FROM t_district
                  WHERE `code`='000000')
    THEN
		INSERT INTO `t_district` (`id`, `code`, `name`, `level`, `p_code`, `contour`, `center`, `path`) VALUES (211, '000000', '中华人民共和国', 0, '0', ST_GeomFromText('GEOMETRYCOLLECTION(POLYGON((72.80498613 40.06865347, 77.60799891 42.41708939, 86.76566471 49.76268374, 93.01562206 46.62549398, 98.80844287 43.49950957, 108.71888678 42.95018439, 114.70072769 46.86974087, 115.85202066 50.66458126, 122.52512946 54.38625138, 136.78384845 48.13185068, 131.58512645 42.31026795, 124.94187864 39.28255235, 121.37969987 34.14369403, 124.40339946 27.22567109, 121.63783138 20.71804764, 112.26302743 3.43640892, 107.7509117 5.42855305, 109.2644763 17.1304473, 100.6376429 20.805068, 78.45798122 30.4278064, 72.65397861 39.89643428, 72.80498613 40.06865347)))'), ST_GeomFromText('POINT(104.065735 30.659462)'), '-');

END IF;


END //
-- 还原默认分隔符
DELIMITER ;
-- 调用存储过程
call add_col();
-- 清除存储过程
drop procedure if exists add_col;