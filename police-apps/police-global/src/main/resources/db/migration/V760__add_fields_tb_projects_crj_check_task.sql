-- 补充字段
DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS(
    SELECT * FROM information_schema.columns
    WHERE table_schema=(select database()) AND table_name='tb_projects_crj_check_task' AND column_name='check_key'
    )
THEN
ALTER TABLE tb_projects_crj_check_task ADD check_key varchar(255) NULL COMMENT '任务所属key';
END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;
