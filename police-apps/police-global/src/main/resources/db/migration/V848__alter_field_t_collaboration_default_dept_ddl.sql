DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_collaboration_default_dept' AND column_name='show_dept_types')
    THEN
        ALTER TABLE t_collaboration_default_dept ADD show_dept_types varchar(255) NULL COMMENT '显示部门类型,多个用逗号分隔' ;
    END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;