DELIMITER $$
DROP PROCEDURE IF EXISTS `add_district_code` $$
CREATE PROCEDURE add_district_code()
BEGIN
    IF NOT EXISTS(
        SELECT * FROM  information_schema.columns
        WHERE table_schema=(select database())
          AND table_name='t_calender_info' AND column_name='district_code'
    )
    THEN
        ALTER TABLE t_calender_info ADD COLUMN district_code VARCHAR(10) NULL COMMENT '地域代码';
        UPDATE t_calender_info ci set ci.district_code = (
            SELECT SUBSTR(d.code, 1, 6) FROM t_dept d WHERE d.id = ci.create_dept_id);
        ALTER TABLE t_calender_info MODIFY COLUMN district_code VARCHAR(10) NOT NULL COMMENT '地域代码';
    END IF;
END $$
DELIMITER ;
CALL add_district_code;
DROP PROCEDURE IF EXISTS `add_district_code`;