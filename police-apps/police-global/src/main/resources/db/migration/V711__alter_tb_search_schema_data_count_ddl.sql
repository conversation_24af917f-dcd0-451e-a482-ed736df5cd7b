DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='tb_search_schema_data_count' AND column_name='increment_number')
    THEN
        ALTER TABLE tb_search_schema_data_count ADD increment_number BIGINT(20) DEFAULT 0 NULL COMMENT '增量';
    END IF;
    IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='tb_search_schema_data_count' AND column_name='data_status')
        THEN
            ALTER TABLE tb_search_schema_data_count ADD data_status INT(2) DEFAULT 0 NULL COMMENT '表接入数据状态';
        END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;