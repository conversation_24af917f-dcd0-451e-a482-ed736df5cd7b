DROP PROCEDURE IF EXISTS insert_t_dict_plan_category;
CREATE PROCEDURE insert_t_dict_plan_category()
BEGIN
    DECLARE pid INT;
    DECLARE pCode INT;

    select id into pid from t_dict where type = 'plan_category_group' and code = 0;
    select code into pCode from t_dict where type = 'plan_category_group' and code = 0;

    INSERT INTO t_dict(`p_id`, `type`, `code`,`name`, `p_code`)
    SELECT pid, 'plan_category', 2, '情报预警',pCode
    FROM dual
    WHERE NOT EXISTS (
        SELECT * FROM t_dict
        WHERE type = 'exponent_category' AND code = 2
    );
END;
CALL insert_t_dict_plan_category();
DROP PROCEDURE IF EXISTS insert_t_dict_plan_category;