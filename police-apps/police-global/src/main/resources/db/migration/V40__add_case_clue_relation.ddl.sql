CREATE TABLE IF NOT EXISTS `t_profile_case_clue_relation`
(
    `id`             bigint    NOT NULL AUTO_INCREMENT,
    `create_user_id` bigint         DEFAULT NULL,
    `create_dept_id` bigint         DEFAULT NULL,
    `create_time`    timestamp NULL DEFAULT NULL,
    `update_user_id` bigint         DEFAULT NULL,
    `update_dept_id` bigint         DEFAULT NULL,
    `update_time`    timestamp NULL DEFAULT NULL,
    `case_id`        bigint    NOT NULL COMMENT '案件id',
    `clue_id`        bigint    NOT NULL COMMENT '线索id',
    PRIMARY KEY (`id`),
    UNIQUE KEY `t_case_clue_case_id_clue_id_IDX` (`case_id`, `clue_id`)
) COMMENT ='案件-线索关联表';
CREATE INDEX t_case_clue_relation_case_id_IDX USING BTREE ON t_profile_case_clue_relation (case_id);
CREATE INDEX t_case_clue_relation_clue_id_IDX USING BTREE ON t_profile_case_clue_relation (clue_id);

INSERT IGNORE INTO t_profile_module
(id, cn_name, en_name, `type`, pid, is_archive, show_order, table_schema, form_schema, list_schema, is_add, database_relation, show_schema_type, add_schema_type, is_operation_content)
VALUES(410, '关联线索', 'relatedClue', 'case', 407, 1, 10, NULL, NULL, '{"name": "相关线索", "type": "LIST_SCHEMA", "table": "t_profile_clue", "fields": [{"db": {"table": "t_profile_clue", "column": "name", "jdbcType": "string"}, "name": "name", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "线索名称"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_clue", "column": "clue_label", "mapping": "label_id_array_to_name", "jdbcType": "label_id_array"}, "name": "clueLabel", "listSchema": {"style": {"align": "left"}, "filter": {"key": "clueLabel", "type": "multiple-tree", "value": ["&&clue_label&&"], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "线索标签"}, "schema": {"type": "array", "title": "线索标签"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 2}}}, {"db": {"table": "t_profile_clue", "column": "source", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "profile_clue_source"}, "name": "source", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "线索来源"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_profile_clue", "column": "disposal_status", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "profile_clue_disposal_status"}, "name": "disposal_status", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "处置状态"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_profile_clue", "column": "emergency_level", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "profile_clue_emergency_level"}, "name": "emergency_level", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "紧急程度"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_profile_clue", "column": "create_time", "mapping": "date_time_to_text", "jdbcType": "datetime"}, "name": "createTime", "listSchema": {"style": {"align": "center", "width": 120}, "schema": {"type": "string", "title": "录入时间"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": true}}}], "selectable": false, "searchFields": [{"key": "name", "name": "线索名称"}]}', 1, '{"type": "RELATION_TABLE", "table": "t_profile_case_clue_relation", "column": "id", "joinTo": {"table": "t_profile_clue", "column": "id", "joinColumn": "clue_id"}, "joinFrom": {"table": "t_profile_case", "column": "id", "joinColumn": "case_id"}}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1);
