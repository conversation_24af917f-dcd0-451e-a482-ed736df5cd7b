UPDATE t_request_params
SET module='statistic-regular', params='{"filterParams": [{"key": "dept", "type": "tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "deptId", "children": "children"}, "displayName": "组织"}, {"key": "level", "type": "option", "value": ["%%control_regular_monitor_level%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "常控级别"}, {"key": "countPeriod", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "统计周期", "defaultValue": {"range": "3"}}], "searchFields": [{"key": "deptName", "name": "单位名称"}]}'
WHERE id=33;

INSERT IGNORE INTO t_schedule_job_config
(module, operation, cron, description, enable, `type`)
VALUES('106', 'synchronize', '0 0 0 * * ?', NULL, 1, 'CRON_JOB');
