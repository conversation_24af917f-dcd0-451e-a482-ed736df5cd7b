INSERT IGNORE INTO `t_warning_source_type` (`id`, `dict_code`, `en_name`, `cn_name`, `content_template`, `properties`, `identifier_type`, `image_columns`, `moye_en_name`, `extend`) VALUES (1, 3, 'scstldpsj', '四川省铁路订票数据', '#eventTime+\' \'+#name+\'监测到: \'+#personName+\'(\'+#identifier+\')购买了由\'+#trackDetail_cfd+\'飞往\'+#trackDetail_mdd+\'的\'+#trackDetail_jpzt+#trackDetail_hbh+\'次航班，起飞时间为\'+#trackDetail_qfsj+\'，到达时间为\'+#trackDetail_ddsj', '[{\"code\": 1, \"name\": \"预警原因\"}, {\"code\": 2, \"name\": \"活动时间、地点\"}, {\"code\": 3, \"name\": \"活动人员\"}, {\"code\": 4, \"name\": \"活动照片\"}, {\"code\": 5, \"name\": \"轨迹信息\"}, {\"code\": 6, \"name\": \"感知源\"}]', NULL, '[\"image1\", \"image2\"]', 'tldp', '{\"to\": \"#dz?:\'--\'\", \"from\": \"#fz?:\'--\'\", \"info\": {\"座位号\": \"(#cxh?:\'--\')+\'车\'+(#zwh?:\'--\')+\'号\'\", \"乘车日期\": \"T(com.trs.police.common.core.utils.TimeUtil).warningTimeFormatter(#ccrq,\'M月d日\')\", \"购票日期\": \"T(com.trs.police.common.core.utils.TimeUtil).warningTimeFormatter(#spsj,\'M月d日HH:mm:ss\')\"}, \"trainNumber\": \"(#cc?:\'--\')+\'次\'\"}');
INSERT IGNORE INTO `t_warning_source_type` (`id`, `dict_code`, `en_name`, `cn_name`, `content_template`, `properties`, `identifier_type`, `image_columns`, `moye_en_name`, `extend`) VALUES (2, 3, 'tldp', '铁路订票', '#eventTime+\' \'+#name+\'监测到: \'+#personName+\'(\'+#identifier+\')购买了\'+T(com.trs.police.common.core.utils.TimeUtil).timeStringFormatter(#trackDetail_ccrq)+\'由\'+#trackDetail_cfhcz+\'开往\'+#trackDetail_ddcz+\'的\'+#trackDetail_cc+\'次列车车票\'', '[{\"code\": 1, \"name\": \"预警原因\"}, {\"code\": 2, \"name\": \"活动时间、地点\"}, {\"code\": 3, \"name\": \"活动人员\"}, {\"code\": 4, \"name\": \"活动照片\"}, {\"code\": 5, \"name\": \"轨迹信息\"}, {\"code\": 6, \"name\": \"感知源\"}]', NULL, '[\"image1\", \"image2\"]', 'tldp', '{\"to\": \"#ddcz?:\'--\'\", \"from\": \"#cfhcz?:\'--\'\", \"info\": {\"座位号\": \"(#cxh?:\'--\')+\'车\'+(#zwh?:\'--\')+\'号\'\", \"乘车日期\": \"T(com.trs.police.common.core.utils.TimeUtil).timeStringFormatter(#ccrq)\", \"购票日期\": \"T(com.trs.police.common.core.utils.TimeUtil).timeStringFormatter(#gpsj)\"}, \"trainNumber\": \"(#cc?:\'--\')+\'次\'\"}');
INSERT IGNORE INTO `t_warning_source_type` (`id`, `dict_code`, `en_name`, `cn_name`, `content_template`, `properties`, `identifier_type`, `image_columns`, `moye_en_name`, `extend`) VALUES (3, 3, 'jdrzsj', '旅店入住数据', '#eventTime+\' \'+#name+\'监测到: \'+#personName+\'(\'+#idNumber+\')于\' + T(com.trs.police.common.core.utils.TimeUtil).timeStringFormatter(#trackDetail_rzldsj) + \'入住\' + #trackDetail_ldmc + #trackDetail_ldfh +\'房\'', '[{\"code\": 1, \"name\": \"预警原因\"}, {\"code\": 2, \"name\": \"活动时间、地点\"}, {\"code\": 3, \"name\": \"活动人员\"}, {\"code\": 4, \"name\": \"活动照片\"}, {\"code\": 6, \"name\": \"感知源\"}]', NULL, '[\"image1\", \"image2\"]', 'hotel', NULL);
INSERT IGNORE INTO `t_warning_source_type` (`id`, `dict_code`, `en_name`, `cn_name`, `content_template`, `properties`, `identifier_type`, `image_columns`, `moye_en_name`, `extend`) VALUES (4, 3, 'hkkksj', '海康卡口数据', '#eventTime+\' \'+#name+\'监测到: \'+#personName+\'(\'+#idNumber+\')于\' + T(com.trs.police.common.core.utils.TimeUtil).timeStringFormatter(#trackDetail_cjsj) + \'驾驶车辆\' + #identifier + \'通过\' + #trackDetail_sbmc', '[{\"code\": 1, \"name\": \"预警原因\"}, {\"code\": 2, \"name\": \"活动时间、地点\"}, {\"code\": 3, \"name\": \"活动人员\"}, {\"code\": 4, \"name\": \"活动照片\"}, {\"code\": 6, \"name\": \"感知源\"}]', 8, '[\"image1\", \"image2\"]', 'video', NULL);
