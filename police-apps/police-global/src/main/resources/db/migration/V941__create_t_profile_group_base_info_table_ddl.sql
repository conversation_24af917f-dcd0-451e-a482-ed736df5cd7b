CREATE TABLE IF NOT EXISTS `t_profile_group_base_info_relation` (
   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
   `create_dept_id` bigint DEFAULT NULL COMMENT '创建单位主键',
   `create_user_id` bigint DEFAULT NULL COMMENT '创建用户主键',
   `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
   `update_user_id` bigint DEFAULT NULL COMMENT '更新用户主键',
   `update_dept_id` bigint DEFAULT NULL COMMENT '更新单位主键',
   `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
   `group_id` bigint DEFAULT NULL COMMENT '群体id',
   `basic_info` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '基本情况',
   `deleted` tinyint DEFAULT 0 NULL COMMENT '是否删除',
   PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;


CREATE TABLE IF NOT EXISTS `t_profile_group_work_measures_relation` (
   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
   `create_dept_id` bigint DEFAULT NULL COMMENT '创建单位主键',
   `create_user_id` bigint DEFAULT NULL COMMENT '创建用户主键',
   `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
   `update_user_id` bigint DEFAULT NULL COMMENT '更新用户主键',
   `update_dept_id` bigint DEFAULT NULL COMMENT '更新单位主键',
   `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
   `group_id` bigint DEFAULT NULL COMMENT '群体id',
   `work_measures` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '工作措施',
   `deleted` tinyint DEFAULT 0 NULL COMMENT '是否删除',
   PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE IF NOT EXISTS `t_profile_group_realtime_trend_relation` (
   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
   `create_dept_id` bigint DEFAULT NULL COMMENT '创建单位主键',
   `create_user_id` bigint DEFAULT NULL COMMENT '创建用户主键',
   `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
   `update_user_id` bigint DEFAULT NULL COMMENT '更新用户主键',
   `update_dept_id` bigint DEFAULT NULL COMMENT '更新单位主键',
   `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
   `group_id` bigint DEFAULT NULL COMMENT '群体id',
   `realtime_trend` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '现实动向',
   `deleted` tinyint DEFAULT 0 NULL COMMENT '是否删除',
   PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

 CREATE TABLE IF NOT EXISTS `t_profile_group_punish_info_relation` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
    `create_dept_id` bigint DEFAULT NULL COMMENT '创建单位主键',
    `create_user_id` bigint DEFAULT NULL COMMENT '创建用户主键',
    `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `update_user_id` bigint DEFAULT NULL COMMENT '更新用户主键',
    `update_dept_id` bigint DEFAULT NULL COMMENT '更新单位主键',
    `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
    `group_id` bigint DEFAULT NULL COMMENT '群体id',
    `punish_info` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '打处情况',
    `deleted` tinyint DEFAULT 0 NULL COMMENT '是否删除',
    PRIMARY KEY (`id`)
     ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;