CREATE TABLE IF NOT EXISTS `tb_workbook` (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '表格名称',
  `create_user_id` bigint DEFAULT NULL,
  `create_user_real_name` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL,
  `create_user_dept_id` bigint DEFAULT NULL,
  `create_user_dept_name` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB;

CREATE TABLE IF NOT EXISTS `tb_workbook_relation` (
  `id` int NOT NULL AUTO_INCREMENT,
  `list_id` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '表格加载id',
  `data_id` bigint DEFAULT NULL COMMENT '业务id',
  `relation_type` int DEFAULT NULL COMMENT '关联类型',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB;

CREATE TABLE IF NOT EXISTS `luckysheet` (
  `id` bigint NOT NULL,
  `block_id` varchar(200) COLLATE utf8mb4_bin NOT NULL,
  `row_col` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `st_index` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `list_id` varchar(200) COLLATE utf8mb4_bin NOT NULL,
  `st_status` int DEFAULT NULL,
  `json_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin,
  `st_order` int DEFAULT NULL,
  `is_delete` int DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `lib` (`list_id`,`st_index`,`block_id`),
  KEY `luckysheet_is_delete_IDX` (`is_delete`) USING BTREE,
  KEY `luckysheet_st_status_IDX` (`st_status`) USING BTREE,
  KEY `luckysheet_st_order_IDX` (`st_order`) USING BTREE,
  CONSTRAINT `luckysheet_chk_1` CHECK (json_valid(`json_data`))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;