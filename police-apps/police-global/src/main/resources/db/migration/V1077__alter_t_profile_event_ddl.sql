DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_profile_group' AND column_name='resolve_difficulty')
    THEN
ALTER TABLE t_profile_group ADD resolve_difficulty int NULL COMMENT '化解难度';
END IF;
    IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_profile_person' AND column_name='resolve_difficulty')
    THEN
ALTER TABLE t_profile_person ADD resolve_difficulty int NULL COMMENT '化解难度';
END IF;
    IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_profile_person' AND column_name='resolve_difficulty_info')
    THEN
ALTER TABLE t_profile_person ADD resolve_difficulty_info text NULL COMMENT '化解难度评判依据';
END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;


INSERT INTO t_dict(p_id, `type`, `code`,`name`, `dict_desc`, `status`)
SELECT 0, 'profile_resolve_difficulty_group', 0, '化解难度', '0', 1
FROM dual
WHERE NOT EXISTS (
        SELECT * FROM t_dict
        WHERE type = 'profile_resolve_difficulty_group' AND code = 0
    );

INSERT INTO t_dict(p_id, `type`, `code`,`name`, `dict_desc`, `status`)
SELECT (SELECT id FROM t_dict WHERE type = 'profile_resolve_difficulty_group' AND code = 0),
       'profile_resolve_difficulty', 1, '容易', '1', 1
FROM dual
WHERE NOT EXISTS (
        SELECT * FROM t_dict
        WHERE type = 'profile_resolve_difficulty' AND code = 1
    );
INSERT INTO t_dict(p_id, `type`, `code`,`name`, `dict_desc`, `status`)
SELECT (SELECT id FROM t_dict WHERE type = 'profile_resolve_difficulty_group' AND code = 0),
       'profile_resolve_difficulty', 2, '较易', '2', 1
FROM dual
WHERE NOT EXISTS (
        SELECT * FROM t_dict
        WHERE type = 'profile_resolve_difficulty' AND code = 2
    );
INSERT INTO t_dict(p_id, `type`, `code`,`name`, `dict_desc`, `status`)
SELECT (SELECT id FROM t_dict WHERE type = 'profile_resolve_difficulty_group' AND code = 0),
       'profile_resolve_difficulty', 3, '中等', '3', 1
FROM dual
WHERE NOT EXISTS (
        SELECT * FROM t_dict
        WHERE type = 'profile_resolve_difficulty' AND code = 3
    );
INSERT INTO t_dict(p_id, `type`, `code`,`name`, `dict_desc`, `status`)
SELECT (SELECT id FROM t_dict WHERE type = 'profile_resolve_difficulty_group' AND code = 0),
       'profile_resolve_difficulty', 4, '困难', '4', 1
FROM dual
WHERE NOT EXISTS (
        SELECT * FROM t_dict
        WHERE type = 'profile_resolve_difficulty' AND code = 4
    );
INSERT INTO t_dict(p_id, `type`, `code`,`name`, `dict_desc`, `status`)
SELECT (SELECT id FROM t_dict WHERE type = 'profile_resolve_difficulty_group' AND code = 0),
       'profile_resolve_difficulty', 5, '极难', '5', 1
FROM dual
WHERE NOT EXISTS (
        SELECT * FROM t_dict
        WHERE type = 'profile_resolve_difficulty' AND code = 5
    );
