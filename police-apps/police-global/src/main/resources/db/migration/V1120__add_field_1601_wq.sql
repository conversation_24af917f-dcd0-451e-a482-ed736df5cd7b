DROP PROCEDURE IF EXISTS AddColumnIfNotExists;
DELIMITER //
CREATE PROCEDURE AddColumnIfNotExists(IN tableName VARCHAR(64), IN columnName VARCHAR(64), IN columnDetails VARCHAR(64))
BEGIN
    DECLARE existing INT DEFAULT 0;

    SELECT COUNT(*)
    INTO existing
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = tableName AND COLUMN_NAME = columnName;
    IF existing = 0 THEN
        SET @s = CONCAT('ALTER TABLE ', tableName, ' ADD ', columnName, ' ', columnDetails);
        PREPARE stmt FROM @s;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END IF;
END //
DELIMITER ;
CALL AddColumnIfNotExists('t_profile_person', 'approval_detail', 'JSON NULL COMMENT \'审批详情\'');
CALL AddColumnIfNotExists('t_profile_event', 'approval_detail', 'JSON NULL COMMENT \'审批详情\'');
CALL AddColumnIfNotExists('t_profile_group', 'approval_detail', 'JSON NULL COMMENT \'审批详情\'');
CALL AddColumnIfNotExists('t_approval_process', 'operate_module', 'INTEGER NULL COMMENT \'操作模块\'');
DROP PROCEDURE IF EXISTS AddColumnIfNotExists;

INSERT INTO t_operate_module (id,cn_name,en_name,send_notice_type,is_todo,is_system_app,is_todo_leader,enable,path_name,is_todo_for_app,is_app)
SELECT 143,'权限中心','permissionCenter',3,0,0,0,0,'权限中心',0,0
WHERE NOT EXISTS (
    SELECT 1 FROM t_operate_module WHERE id = 143
) LIMIT 1;