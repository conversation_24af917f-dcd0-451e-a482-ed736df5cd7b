UPDATE `t_profile_module` SET `table_schema` = '{\"name\": \"人员信息\", \"type\": \"TABLE_SCHEMA\", \"table\": \"t_profile_person\", \"fields\": [{\"db\": {\"table\": \"t_profile_person\", \"column\": \"id_number\", \"jdbcType\": \"string\"}, \"name\": \"id_number\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"证件号码\", \"copyable\": true}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"姓名\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"id_type\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"string\"}, \"dict\": {\"type\": \"id_type\"}, \"name\": \"id_type\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"证件类型\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"gender\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"string\"}, \"dict\": {\"type\": \"gender\"}, \"name\": \"gender\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"性别\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"former_name\", \"jdbcType\": \"string\"}, \"name\": \"former_name\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"曾用名\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"nick_name\", \"jdbcType\": \"string\"}, \"name\": \"nick_name\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"绰号\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"nation\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"nation\"}, \"name\": \"nation\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"民族\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"political_status\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"profile_political_status\"}, \"name\": \"political_status\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"政治面貌\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"martial_status\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"profile_martial_status\"}, \"name\": \"martial_status\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"婚姻状况\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"current_job\", \"jdbcType\": \"string\"}, \"name\": \"current_job\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"现职业\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"current_position\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"profile_current_position\"}, \"name\": \"current_position\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"目前所在地\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"person_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"json_id_array\"}, \"name\": \"person_label\", \"tableSchema\": {\"span\": 1, \"type\": \"label\", \"title\": \"人员标签\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"registered_residence\", \"mapping\": \"district_code_to_name\", \"jdbcType\": \"string\"}, \"name\": \"registered_residence\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"户籍地\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"registered_residence_detail\", \"jdbcType\": \"string\"}, \"name\": \"registered_residence_detail\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"户籍地详细地址\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"current_residence\", \"mapping\": \"district_code_to_name\", \"jdbcType\": \"string\"}, \"name\": \"current_residence\", \"tree\": {\"root\": \"510500\", \"type\": \"district\"}, \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"现住址\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"current_residence_detail\", \"jdbcType\": \"string\"}, \"name\": \"current_residence_detail\", \"tableSchema\": {\"span\": 4, \"type\": \"string\", \"title\": \"现住址详细地址\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"main_demand\", \"jdbcType\": \"string\"}, \"name\": \"main_demand\", \"tableSchema\": {\"span\": 4, \"type\": \"string\", \"title\": \"主要诉求\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"work_measures\", \"jdbcType\": \"string\"}, \"name\": \"work_measures\", \"tableSchema\": {\"span\": 4, \"type\": \"string\", \"title\": \"工作措施\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"petition_info\", \"jdbcType\": \"string\"}, \"name\": \"petition_info\", \"tableSchema\": {\"span\": 4, \"type\": \"string\", \"title\": \"进京赴省上访情况\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"punish_info\", \"jdbcType\": \"string\"}, \"name\": \"punish_info\", \"tableSchema\": {\"span\": 4, \"type\": \"string\", \"title\": \"被打击处理情况\", \"copyable\": false}}], \"moduleUi\": {\"column\": 4, \"bordered\": true}}', `form_schema` = '{\"name\": \"人员信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_person\", \"fields\": [{\"db\": {\"table\": \"t_profile_person\", \"column\": \"id_type\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"id_type\"}, \"name\": \"idType\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"证件类型\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"photo\", \"jdbcType\": \"json_image_array\"}, \"name\": \"photo\", \"formSchema\": {\"ui\": {\"ui:options\": {\"style\": {\"position\": \"absolute\"}, \"width\": \"0.5\", \"action\": \"/upload/imgs\", \"widget\": \"upload\", \"isShowTitle\": false, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"array\", \"items\": {\"type\": \"object\"}, \"title\": \"照片\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"id_number\", \"jdbcType\": \"string\"}, \"name\": \"idNumber\", \"formSchema\": {\"ui\": {\"ui:options\": {\"style\": {\"display\": \"block\"}, \"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"证件号码\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"formSchema\": {\"ui\": {\"ui:options\": {\"style\": {\"display\": \"block\"}, \"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"姓名\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"current_position\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"profile_current_position\"}, \"name\": \"nowLocation\", \"formSchema\": {\"ui\": {\"ui:options\": {\"style\": {\"display\": \"block\"}, \"width\": \"0.5\", \"widget\": \"radio\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"目前所在地\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"person_label\", \"jdbcType\": \"json_id_array\"}, \"name\": \"personLabel\", \"tree\": {\"root\": \"person\", \"type\": \"label\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"style\": {\"display\": \"block\"}, \"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": true, \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"array\", \"items\": {\"type\": \"array\"}, \"title\": \"人员标签\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"gender\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"gender\"}, \"name\": \"gender\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"radio\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"性别\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"former_name\", \"jdbcType\": \"string\"}, \"name\": \"usedName\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"曾用名\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"nick_name\", \"jdbcType\": \"string\"}, \"name\": \"nickName\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"绰号\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"nation\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"nation\"}, \"name\": \"nation\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"民族\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"political_status\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"profile_political_status\"}, \"name\": \"politicalStatus\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"radio\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"政治面貌\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"martial_status\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"profile_martial_status\"}, \"name\": \"maritalStatus\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"radio\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"婚姻状态\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"control_level\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"profile_person_control_level\"}, \"name\": \"control_level\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"radio\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"人员级别\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"current_job\", \"jdbcType\": \"string\"}, \"name\": \"currentJob\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"现职业\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"registered_residence\", \"jdbcType\": \"string\"}, \"name\": \"registerArea\", \"tree\": {\"root\": \"510000\", \"type\": \"district\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"name\", \"value\": \"code\", \"children\": \"children\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"户籍地\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"registered_residence_detail\", \"jdbcType\": \"string\"}, \"name\": \"registerAreaInfo\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"详细地址\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"current_residence\", \"jdbcType\": \"string\"}, \"name\": \"address\", \"tree\": {\"root\": \"510000\", \"type\": \"district\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"name\", \"value\": \"code\", \"children\": \"children\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"现住址\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"current_residence_detail\", \"jdbcType\": \"string\"}, \"name\": \"addressInfo\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"详细地址\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"work_target\", \"jdbcType\": \"string\"}, \"dict\": {\"type\": \"profile_work_target\"}, \"name\": \"work_target\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"radio\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"工作目标\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"main_demand\", \"jdbcType\": \"string\"}, \"name\": \"mainDemand\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"textarea\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"风险背景\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"work_measures\", \"jdbcType\": \"string\"}, \"name\": \"measure\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"textarea\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"工作措施\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"petition_info\", \"jdbcType\": \"string\"}, \"name\": \"petition\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"textarea\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"化解难点\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"punish_info\", \"jdbcType\": \"string\"}, \"name\": \"handling\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"textarea\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"维权及打处情况\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"person_type\", \"jdbcType\": \"integer\"}, \"name\": \"personType\", \"formSchema\": {\"ui\": {\"ui:options\": {\"style\": {\"display\": \"none\"}, \"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"integer\", \"title\": \"档案类型\", \"default\": 0}}}], \"required\": [\"idType\", \"idNumber\", \"name\", \"registerArea\", \"personLabel\"]}' WHERE `id` = 3;