delete from t_profile_module where id in (1651, 1652, 1654, 1655, 1656, 1657, 1658, 1659, 1660, 1661, 1662, 1663);
INSERT INTO t_profile_module
(id, cn_name, en_name, `type`, pid, is_archive, show_order, table_schema, form_schema, list_schema, is_add, database_relation, show_schema_type, add_schema_type, is_operation_content, is_mobile_content, is_web_content, is_fk_content)
VALUES(1651, '家庭关系', 'family', 'police', NULL, 1, 6, NULL, NULL, '{"name": "家庭关系", "type": "LIST_SCHEMA", "table": "t_police_family_relation", "fields": [{"db": {"table": "t_police_family_relation", "column": "relation", "jdbcType": "string"}, "name": "relation", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "关系"}, "properties": {"copyable": false, "required": true, "sortable": false}}}, {"db": {"table": "t_police_family_relation", "column": "name", "jdbcType": "string"}, "name": "name", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "姓名"}, "properties": {"copyable": false, "required": true, "sortable": false}}}, {"db": {"table": "t_police_family_relation", "column": "id_number", "jdbcType": "string"}, "name": "id_number", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "身份证号"}, "properties": {"copyable": true, "required": false, "sortable": false}}}, {"db": {"table": "t_police_family_relation", "column": "work_unit", "jdbcType": "string"}, "name": "work_unit", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "工作单位"}, "properties": {"copyable": false, "required": false, "sortable": false}}}, {"db": {"table": "t_police_family_relation", "column": "phone_number", "jdbcType": "string"}, "name": "phone_number", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "联系方式"}, "properties": {"copyable": false, "required": false, "sortable": false}}}], "selectable": false, "searchFields": []}', 1, '{"type": "FOREIGN_KEY", "table": "t_police_family_relation", "column": "profile_id", "idColumn": "id", "primaryColumn": "id"}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO t_profile_module
(id, cn_name, en_name, `type`, pid, is_archive, show_order, table_schema, form_schema, list_schema, is_add, database_relation, show_schema_type, add_schema_type, is_operation_content, is_mobile_content, is_web_content, is_fk_content)
VALUES(1652, '人员信息', 'person', 'police', NULL, 1, 1, '{"name": "人员信息", "type": "TABLE_SCHEMA", "table": "t_profile_police", "fields": [{"db": {"table": "t_profile_police", "column": "name", "jdbcType": "string"}, "name": "name", "tableSchema": {"span": 1, "type": "string", "title": "姓名", "copyable": false}}, {"db": {"table": "t_profile_police", "column": "id_number", "jdbcType": "string"}, "name": "id_number", "tableSchema": {"span": 1, "type": "string", "title": "身份证号", "copyable": true}}, {"db": {"table": "t_profile_police", "column": "gender", "mapping": "dict_code_to_name", "jdbcType": "string"}, "dict": {"type": "gender"}, "name": "gender", "tableSchema": {"span": 1, "type": "string", "title": "性别", "copyable": false}}, {"db": {"table": "t_profile_police", "column": "birthday", "mapping": "date_to_general_string", "jdbcType": "string"}, "name": "birthday", "tableSchema": {"span": 1, "type": "string", "title": "出生日期", "copyable": false}}, {"db": {"table": "t_profile_police", "column": "police_number", "jdbcType": "string"}, "name": "police_number", "tableSchema": {"span": 1, "type": "string", "title": "警号", "copyable": false}}, {"db": {"table": "t_profile_police", "column": "nation", "mapping": "dict_code_to_name", "jdbcType": "integer"}, "dict": {"type": "nation"}, "name": "nation", "tableSchema": {"span": 1, "type": "string", "title": "民族", "copyable": false}}, {"db": {"table": "t_profile_police", "column": "political_status", "mapping": "dict_code_to_name", "jdbcType": "integer"}, "dict": {"type": "profile_political_status"}, "name": "political_status", "tableSchema": {"span": 1, "type": "string", "title": "政治面貌", "copyable": false}}, {"db": {"table": "t_profile_police", "column": "martial_status", "mapping": "dict_code_to_name", "jdbcType": "integer"}, "dict": {"type": "profile_martial_status"}, "name": "martial_status", "tableSchema": {"span": 1, "type": "string", "title": "婚姻状况", "copyable": false}}, {"db": {"table": "t_profile_police", "column": "tel", "mapping": "json_array_to_string", "jdbcType": "json_id_array"}, "name": "tel", "tableSchema": {"span": 2, "type": "label", "title": "电话号码", "copyable": true}}, {"db": {"table": "t_profile_police", "column": "join_work_date", "mapping": "date_to_general_string", "jdbcType": "string"}, "name": "join_work_date", "tableSchema": {"span": 1, "type": "string", "title": "参加工作日期", "copyable": false}}, {"db": {"table": "t_profile_police", "column": "join_public_security_work_date", "mapping": "date_to_general_string", "jdbcType": "string"}, "name": "join_public_security_work_date", "tableSchema": {"span": 1, "type": "string", "title": "参加公安工作日期", "copyable": false}}, {"db": {"table": "t_profile_police", "column": "registered_residence", "mapping": "district_code_to_name", "jdbcType": "string"}, "name": "registered_residence", "tableSchema": {"span": 2, "type": "string", "title": "户籍地", "copyable": false}}, {"db": {"table": "t_profile_police", "column": "registered_residence_detail", "jdbcType": "string"}, "name": "registered_residence_detail", "tableSchema": {"span": 2, "type": "string", "title": "户籍地详细地址", "copyable": false}}, {"db": {"table": "t_profile_police", "column": "current_residence", "mapping": "district_code_to_name", "jdbcType": "string"}, "name": "current_residence", "tree": {"root": "000000", "type": "district"}, "tableSchema": {"span": 2, "type": "string", "title": "现住址", "copyable": false}}, {"db": {"table": "t_profile_police", "column": "current_residence_detail", "jdbcType": "string"}, "name": "current_residence_detail", "tableSchema": {"span": 2, "type": "string", "title": "现住址详细地址", "copyable": false}}], "moduleUi": {"column": 4, "bordered": true}}', '{"name": "人员信息", "type": "FORM_SCHEMA", "table": "t_profile_police", "fields": [{"db": {"table": "t_profile_police", "column": "name", "jdbcType": "string"}, "name": "name", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "姓名"}}}, {"db": {"table": "t_profile_police", "column": "photo", "jdbcType": "json_image_array"}, "name": "photo", "formSchema": {"ui": {"ui:options": {"style": {"position": "absolute"}, "width": "0.5", "action": "/upload/imgs", "widget": "upload", "isShowTitle": false, "titleLocation": "left"}}, "schema": {"type": "array", "items": {"type": "object"}, "title": "照片"}}}, {"db": {"table": "t_profile_police", "column": "id_number", "jdbcType": "string"}, "name": "idNumber", "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "身份证号"}}}, {"db": {"table": "t_profile_police", "column": "gender", "jdbcType": "integer"}, "dict": {"type": "gender"}, "name": "gender", "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "0.5", "widget": "radio", "titleLocation": "left"}}, "schema": {"type": "number", "title": "性别"}}}, {"db": {"table": "t_profile_police", "column": "birthday", "jdbcType": "timeString"}, "name": "birthday", "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "0.5", "widget": "datePicker", "showTime": false, "timeFormat": "YYYY-MM-DD", "titleLocation": "left"}}, "schema": {"type": "string", "title": "出生日期"}}}, {"db": {"table": "t_profile_police", "column": "police_number", "jdbcType": "string"}, "name": "police_number", "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "警号"}}}, {"db": {"table": "t_profile_police", "column": "nation", "jdbcType": "integer"}, "dict": {"type": "nation"}, "name": "nation", "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "0.5", "widget": "select", "titleLocation": "left"}}, "schema": {"type": "number", "title": "民族"}}}, {"db": {"table": "t_profile_police", "column": "political_status", "jdbcType": "integer"}, "dict": {"type": "profile_political_status"}, "name": "political_status", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "select", "titleLocation": "left"}}, "schema": {"type": "number", "title": "政治面貌"}}}, {"db": {"table": "t_profile_police", "column": "martial_status", "jdbcType": "integer"}, "dict": {"type": "profile_martial_status"}, "name": "maritalStatus", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "radio", "titleLocation": "left"}}, "schema": {"type": "number", "title": "婚姻状况"}}}, {"db": {"table": "t_profile_police", "column": "tel", "jdbcType": "json_string_array"}, "name": "tel", "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "0.5", "widget": "select", "multiple": true, "inputable": true, "titleLocation": "left"}}, "schema": {"type": "array", "items": {"type": "string", "pattern": "1(3[0-9]|4[01456879]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])[0-9]{8}"}, "title": "联系方式"}, "errorSchema": {"err:options": {"pattern": "电话号码格式异常"}}}}, {"db": {"table": "t_profile_police", "column": "join_work_date", "jdbcType": "timeString"}, "name": "join_work_date", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "datePicker", "showTime": false, "timeFormat": "YYYY-MM-DD", "titleLocation": "left"}}, "schema": {"type": "string", "title": "参加工作日期"}}}, {"db": {"table": "t_profile_police", "column": "join_public_security_work_date", "jdbcType": "timeString"}, "name": "join_public_security_work_date", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "datePicker", "showTime": false, "timeFormat": "YYYY-MM-DD", "titleLocation": "left"}}, "schema": {"type": "string", "title": "参加公安工作日期"}}}, {"db": {"table": "t_profile_police", "column": "registered_residence", "jdbcType": "string"}, "name": "registerArea", "tree": {"root": "000000", "type": "district"}, "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "cascader", "multiple": false, "fieldNames": {"label": "name", "value": "code", "children": "children"}, "titleLocation": "left"}}, "schema": {"type": "string", "title": "户籍地"}}}, {"db": {"table": "t_profile_police", "column": "registered_residence_detail", "jdbcType": "string"}, "name": "registerAreaInfo", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "详细地址"}}}, {"db": {"table": "t_profile_police", "column": "current_residence", "jdbcType": "string"}, "name": "address", "tree": {"root": "510500", "type": "district"}, "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "cascader", "multiple": false, "fieldNames": {"label": "name", "value": "code", "children": "children"}, "titleLocation": "left"}}, "schema": {"type": "string", "title": "现住址"}}}, {"db": {"table": "t_profile_police", "column": "current_residence_detail", "jdbcType": "string"}, "name": "addressInfo", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "详细地址"}}}], "required": ["idNumber", "name"]}', '{}', 1, '{"type": "PRIMARY_KEY", "table": "t_profile_police", "column": "id"}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 0, 1, 1, 1);
INSERT INTO t_profile_module
(id, cn_name, en_name, `type`, pid, is_archive, show_order, table_schema, form_schema, list_schema, is_add, database_relation, show_schema_type, add_schema_type, is_operation_content, is_mobile_content, is_web_content, is_fk_content)
VALUES(1654, '职级信息', 'rank', 'police', NULL, 1, 2, NULL, NULL, '{"name": "职级信息", "type": "LIST_SCHEMA", "table": "t_police_rank_relation", "fields": [{"db": {"table": "t_police_rank_relation", "column": "start_time", "jdbcType": "timestamp"}, "name": "start_time", "listSchema": {"style": {"align": "center", "format": "string"}, "schema": {"type": "datetime", "title": "任职开始时间"}, "properties": {"format": "YYYY-MM", "picker": "month", "copyable": false, "editable": true, "required": true, "showTime": false, "sortable": false}}}, {"db": {"table": "t_police_rank_relation", "column": "end_time", "jdbcType": "timestamp"}, "name": "end_time", "listSchema": {"style": {"align": "center", "format": "string"}, "schema": {"type": "datetime", "title": "任职结束时间"}, "properties": {"format": "YYYY-MM", "picker": "month", "copyable": false, "editable": true, "required": false, "showTime": false, "sortable": false}}}, {"db": {"table": "t_police_rank_relation", "column": "rank_series", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "police_rz_xl", "codeToId": true}, "name": "rank_series", "listSchema": {"style": {"align": "left"}, "schema": {"type": "select", "title": "职级序列"}, "properties": {"copyable": false, "editable": true, "required": true, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_police_rank_relation", "column": "rank_code", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "police_zj", "codeToId": true}, "name": "ranks", "listSchema": {"style": {"align": "left"}, "schema": {"type": "treeSelect", "title": "职级"}, "properties": {"copyable": false, "editable": true, "required": true, "sortable": false, "parentSelectable": false}}}, {"db": {"exist": false, "table": "t_police_rank_relation", "column": "TIMESTAMPDIFF( YEAR, start_time , COALESCE(end_time , CURDATE()))", "jdbcType": "number", "databaseRelation": {"type": "FOREIGN_KEY", "column": "id"}}, "name": "time", "listSchema": {"style": {"align": "left"}, "schema": {"type": "string", "title": "任职年限（年）"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1}}}], "selectable": false, "searchFields": []}', 1, '{"type": "FOREIGN_KEY", "table": "t_police_rank_relation", "column": "profile_id", "idColumn": "id", "primaryColumn": "id"}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO t_profile_module
(id, cn_name, en_name, `type`, pid, is_archive, show_order, table_schema, form_schema, list_schema, is_add, database_relation, show_schema_type, add_schema_type, is_operation_content, is_mobile_content, is_web_content, is_fk_content)
VALUES(1655, '履历信息', 'resume', 'police', NULL, 1, 3, NULL, NULL, '{"name": "履历信息", "type": "LIST_SCHEMA", "table": "t_police_resume_relation", "fields": [{"db": {"table": "t_police_resume_relation", "column": "start_time", "jdbcType": "timestamp"}, "name": "start_time", "listSchema": {"style": {"align": "center", "format": "string"}, "schema": {"type": "datetime", "title": "任职开始时间"}, "properties": {"format": "YYYY-MM", "picker": "month", "copyable": false, "editable": true, "required": true, "showTime": false, "sortable": false}}}, {"db": {"table": "t_police_resume_relation", "column": "end_time", "jdbcType": "timestamp"}, "name": "end_time", "listSchema": {"style": {"align": "center", "format": "string"}, "schema": {"type": "datetime", "title": "任职结束时间"}, "properties": {"format": "YYYY-MM", "picker": "month", "copyable": false, "editable": true, "required": false, "showTime": false, "sortable": false}}}, {"db": {"table": "t_police_resume_relation", "column": "position", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "police_ll_rzzw", "codeToId": true}, "name": "position", "listSchema": {"style": {"align": "left"}, "schema": {"type": "select", "title": "任职职务"}, "properties": {"copyable": false, "editable": true, "required": true, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_police_resume_relation", "column": "position_level", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "police_ll_rzzwjb", "codeToId": true}, "name": "position_level", "listSchema": {"style": {"align": "left"}, "schema": {"type": "select", "title": "任职职务级别"}, "properties": {"copyable": false, "editable": true, "required": false, "sortable": false}}}, {"db": {"table": "t_police_resume_relation", "column": "department", "jdbcType": "string"}, "name": "department", "listSchema": {"style": {"align": "left"}, "schema": {"type": "string", "title": "任职部门"}, "properties": {"copyable": false, "editable": true, "required": false, "sortable": false, "instrLength": 1}}}], "selectable": false, "searchFields": []}', 1, '{"type": "FOREIGN_KEY", "table": "t_police_resume_relation", "column": "profile_id", "idColumn": "id", "primaryColumn": "id"}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO t_profile_module
(id, cn_name, en_name, `type`, pid, is_archive, show_order, table_schema, form_schema, list_schema, is_add, database_relation, show_schema_type, add_schema_type, is_operation_content, is_mobile_content, is_web_content, is_fk_content)
VALUES(1656, '援藏援疆经历', 'assisTibetXinjiang', 'police', NULL, 1, 4, NULL, NULL, '{"name": "援藏援疆经历", "type": "LIST_SCHEMA", "table": "t_police_assist_tibet_xinjiang_relation", "fields": [{"db": {"table": "t_police_assist_tibet_xinjiang_relation", "column": "service_start_time", "mapping": "date_to_month", "jdbcType": "timestamp"}, "name": "service_start_time", "listSchema": {"style": {"align": "center", "format": "string"}, "schema": {"type": "datetime", "title": "任职开始时间"}, "properties": {"format": "YYYY-MM", "picker": "month", "copyable": false, "editable": true, "required": true, "showTime": false, "sortable": false}}}, {"db": {"table": "t_police_assist_tibet_xinjiang_relation", "column": "service_end_time", "mapping": "date_to_month", "jdbcType": "timestamp"}, "name": "service_end_time", "listSchema": {"style": {"align": "center", "format": "string"}, "schema": {"type": "datetime", "title": "任职结束时间"}, "properties": {"format": "YYYY-MM", "picker": "month", "copyable": false, "editable": true, "required": false, "showTime": false, "sortable": false}}}, {"db": {"table": "t_police_assist_tibet_xinjiang_relation", "column": "service_area", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "police_czyj_fudq", "codeToId": true}, "name": "service_area", "listSchema": {"style": {"align": "left"}, "schema": {"type": "select", "title": "服务地区"}, "properties": {"copyable": false, "editable": true, "required": true, "sortable": false, "instrLength": 1}}}, {"db": {"exist": false, "table": "t_police_assist_tibet_xinjiang_relation", "column": "(TIMESTAMPDIFF( MONTH, service_start_time , COALESCE(service_end_time , CURDATE())))", "jdbcType": "number", "databaseRelation": {"type": "FOREIGN_KEY", "column": "id"}}, "name": "time", "listSchema": {"style": {"align": "left"}, "schema": {"type": "string", "title": "服务期限（月）"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1}}}], "selectable": false, "searchFields": []}', 1, '{"type": "FOREIGN_KEY", "table": "t_police_assist_tibet_xinjiang_relation", "column": "profile_id", "idColumn": "id", "primaryColumn": "id"}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO t_profile_module
(id, cn_name, en_name, `type`, pid, is_archive, show_order, table_schema, form_schema, list_schema, is_add, database_relation, show_schema_type, add_schema_type, is_operation_content, is_mobile_content, is_web_content, is_fk_content)
VALUES(1657, '教育经历', 'educationExperience', 'police', NULL, 1, 5, NULL, NULL, '{"name": "教育经历", "type": "LIST_SCHEMA", "table": "t_police_education_experience_relation", "fields": [{"db": {"table": "t_police_education_experience_relation", "column": "start_time", "jdbcType": "timestamp"}, "name": "start_time", "listSchema": {"style": {"align": "center", "format": "string"}, "schema": {"type": "datetime", "title": "在校开始时间"}, "properties": {"format": "YYYY-MM", "picker": "month", "copyable": false, "editable": true, "required": true, "showTime": false, "sortable": false}}}, {"db": {"table": "t_police_education_experience_relation", "column": "end_time", "jdbcType": "timestamp"}, "name": "end_time", "listSchema": {"style": {"align": "center", "format": "string"}, "schema": {"type": "datetime", "title": "在校结束时间"}, "properties": {"format": "YYYY-MM", "picker": "month", "copyable": false, "editable": true, "required": false, "showTime": false, "sortable": false}}}, {"db": {"table": "t_police_education_experience_relation", "column": "graduation_school", "jdbcType": "number"}, "name": "graduation_school", "listSchema": {"style": {"align": "left"}, "schema": {"type": "string", "title": "毕业学校"}, "properties": {"copyable": false, "editable": true, "required": true, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_police_education_experience_relation", "column": "degree", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "t_degree", "codeToId": true}, "name": "degree", "listSchema": {"style": {"align": "left"}, "schema": {"type": "select", "title": "学历"}, "properties": {"copyable": false, "editable": true, "required": true, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_police_education_experience_relation", "column": "major", "jdbcType": "string"}, "name": "major", "listSchema": {"style": {"align": "left"}, "schema": {"type": "string", "title": "专业"}, "properties": {"copyable": false, "editable": true, "required": false, "sortable": false}}}], "selectable": false, "searchFields": []}', 1, '{"type": "FOREIGN_KEY", "table": "t_police_education_experience_relation", "column": "profile_id", "idColumn": "id", "primaryColumn": "id"}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO t_profile_module
(id, cn_name, en_name, `type`, pid, is_archive, show_order, table_schema, form_schema, list_schema, is_add, database_relation, show_schema_type, add_schema_type, is_operation_content, is_mobile_content, is_web_content, is_fk_content)
VALUES(1658, '专业技术', 'professionalTechnology', 'police', NULL, 1, 7, NULL, NULL, '{"name": "专业技术", "type": "LIST_SCHEMA", "table": "t_police_professional_technology_relation", "fields": [{"db": {"table": "t_police_professional_technology_relation", "column": "acquisition_time", "jdbcType": "timestamp"}, "name": "acquisition_time", "listSchema": {"style": {"align": "center", "format": "string"}, "schema": {"type": "datetime", "title": "日期"}, "properties": {"format": "YYYY-MM-DD", "picker": "date", "copyable": false, "editable": true, "required": true, "showTime": false, "sortable": false}}}, {"db": {"table": "t_police_professional_technology_relation", "column": "technology", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "police_zyjs", "codeToId": true}, "name": "technology", "listSchema": {"style": {"align": "left"}, "schema": {"type": "select", "title": "专业技术名称"}, "properties": {"copyable": false, "editable": true, "required": true, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_police_professional_technology_relation", "column": "description", "jdbcType": "number"}, "name": "description", "listSchema": {"style": {"align": "left"}, "schema": {"type": "string", "title": "简述"}, "properties": {"copyable": false, "editable": true, "required": false, "sortable": false}}}], "selectable": false, "searchFields": []}', 1, '{"type": "FOREIGN_KEY", "table": "t_police_professional_technology_relation", "column": "profile_id", "idColumn": "id", "primaryColumn": "id"}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO t_profile_module
(id, cn_name, en_name, `type`, pid, is_archive, show_order, table_schema, form_schema, list_schema, is_add, database_relation, show_schema_type, add_schema_type, is_operation_content, is_mobile_content, is_web_content, is_fk_content)
VALUES(1659, '立功受奖', 'lgsj', 'police', NULL, 1, 8, NULL, NULL, '{"name": "立功受奖", "type": "LIST_SCHEMA", "table": "t_police_professional_lgsj_relation", "fields": [{"db": {"table": "t_police_professional_lgsj_relation", "column": "acquisition_time", "jdbcType": "timestamp"}, "name": "acquisition_time", "listSchema": {"style": {"align": "center", "format": "string"}, "schema": {"type": "datetime", "title": "立功时间"}, "properties": {"format": "YYYY-MM-DD", "picker": "date", "copyable": false, "editable": true, "required": true, "showTime": false, "sortable": false}}}, {"db": {"table": "t_police_professional_lgsj_relation", "column": "lgsj", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "police_lgsj", "codeToId": true}, "name": "lgsj", "listSchema": {"style": {"align": "left"}, "schema": {"type": "select", "title": "立功登记"}, "properties": {"copyable": false, "editable": true, "required": true, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_police_professional_lgsj_relation", "column": "description", "jdbcType": "number"}, "name": "description", "listSchema": {"style": {"align": "left"}, "schema": {"type": "string", "title": "立功事迹简述"}, "properties": {"copyable": false, "editable": true, "required": false, "sortable": false}}}], "selectable": false, "searchFields": []}', 1, '{"type": "FOREIGN_KEY", "table": "t_police_professional_lgsj_relation", "column": "profile_id", "idColumn": "id", "primaryColumn": "id"}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO t_profile_module
(id, cn_name, en_name, `type`, pid, is_archive, show_order, table_schema, form_schema, list_schema, is_add, database_relation, show_schema_type, add_schema_type, is_operation_content, is_mobile_content, is_web_content, is_fk_content)
VALUES(1660, '违纪违规', 'wgwj', 'police', NULL, 1, 9, NULL, NULL, '{"name": "违纪违规", "type": "LIST_SCHEMA", "table": "t_police_professional_wgwj_relation", "fields": [{"db": {"table": "t_police_professional_wgwj_relation", "column": "record_time", "jdbcType": "timestamp"}, "name": "record_time", "listSchema": {"style": {"align": "center", "format": "string"}, "schema": {"type": "datetime", "title": "档案时间"}, "properties": {"format": "YYYY-MM-DD", "picker": "date", "copyable": false, "editable": true, "required": true, "showTime": false, "sortable": false}}}, {"db": {"table": "t_police_professional_wgwj_relation", "column": "wt_type", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "police_wgwj", "codeToId": true}, "name": "wt_type", "listSchema": {"style": {"align": "left"}, "schema": {"type": "select", "title": "问题类型"}, "properties": {"copyable": false, "editable": true, "required": true, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_police_professional_wgwj_relation", "column": "description", "jdbcType": "number"}, "name": "description", "listSchema": {"style": {"align": "left"}, "schema": {"type": "string", "title": "问题详情"}, "properties": {"copyable": false, "editable": true, "required": false, "sortable": false}}}], "selectable": false, "searchFields": []}', 1, '{"type": "FOREIGN_KEY", "table": "t_police_professional_wgwj_relation", "column": "profile_id", "idColumn": "id", "primaryColumn": "id"}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO t_profile_module
(id, cn_name, en_name, `type`, pid, is_archive, show_order, table_schema, form_schema, list_schema, is_add, database_relation, show_schema_type, add_schema_type, is_operation_content, is_mobile_content, is_web_content, is_fk_content)
VALUES(1661, '年度考核', 'ndkh', 'police', NULL, 1, 10, NULL, NULL, '{"name": "年度考核", "type": "LIST_SCHEMA", "table": "t_police_professional_ndkh_relation", "fields": [{"db": {"table": "t_police_professional_ndkh_relation", "column": "assessment_time", "mapping": "date_to_year", "jdbcType": "timestamp"}, "name": "assessment_time", "listSchema": {"style": {"align": "center", "format": "string"}, "schema": {"type": "datetime", "title": "考核年份"}, "properties": {"format": "YYYY", "picker": "year", "copyable": false, "editable": true, "required": true, "showTime": false, "sortable": false}}}, {"db": {"table": "t_police_professional_ndkh_relation", "column": "assessment_result", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "police_assessment_result", "codeToId": true}, "name": "assessment_result", "listSchema": {"style": {"align": "left"}, "schema": {"type": "select", "title": "考核结果"}, "properties": {"copyable": false, "editable": true, "required": true, "sortable": false, "instrLength": 1}}}], "selectable": false, "searchFields": []}', 1, '{"type": "FOREIGN_KEY", "table": "t_police_professional_ndkh_relation", "column": "profile_id", "idColumn": "id", "primaryColumn": "id"}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO t_profile_module
(id, cn_name, en_name, `type`, pid, is_archive, show_order, table_schema, form_schema, list_schema, is_add, database_relation, show_schema_type, add_schema_type, is_operation_content, is_mobile_content, is_web_content, is_fk_content)
VALUES(1662, '民主测评', 'democraticEvaluation', 'police', NULL, 1, 11, NULL, NULL, '{"name": "民主测评", "type": "LIST_SCHEMA", "table": "t_police_democratic_evaluation_relation", "fields": [{"db": {"table": "t_police_democratic_evaluation_relation", "column": "evaluation_year", "mapping": "date_to_year", "jdbcType": "timestamp"}, "name": "evaluation_year", "listSchema": {"style": {"align": "center", "format": "string"}, "schema": {"type": "datetime", "title": "评价年份"}, "properties": {"format": "YYYY", "picker": "year", "copyable": false, "editable": true, "required": true, "showTime": false, "sortable": false}}}, {"db": {"table": "t_police_democratic_evaluation_relation", "column": "a_ticket_rank", "jdbcType": "number"}, "name": "a_ticket_rank", "listSchema": {"style": {"align": "left"}, "schema": {"type": "string", "title": "A票排名"}, "properties": {"copyable": false, "editable": true, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_police_democratic_evaluation_relation", "column": "a_ticket_excellent_count", "jdbcType": "number"}, "name": "a_ticket_excellent_count", "listSchema": {"style": {"align": "left"}, "schema": {"type": "string", "title": "A票优秀票数"}, "properties": {"copyable": false, "editable": true, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_police_democratic_evaluation_relation", "column": "a_ticket_total_count", "jdbcType": "number"}, "name": "a_ticket_total_count", "listSchema": {"style": {"align": "left"}, "schema": {"type": "string", "title": "A票总票数"}, "properties": {"copyable": false, "editable": true, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_police_democratic_evaluation_relation", "column": "b_ticket_rank", "jdbcType": "number"}, "name": "b_ticket_rank", "listSchema": {"style": {"align": "left"}, "schema": {"type": "string", "title": "B票排名"}, "properties": {"copyable": false, "editable": true, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_police_democratic_evaluation_relation", "column": "b_ticket_excellent_count", "jdbcType": "number"}, "name": "b_ticket_excellent_count", "listSchema": {"style": {"align": "left"}, "schema": {"type": "string", "title": "B票优秀票数"}, "properties": {"copyable": false, "editable": true, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_police_democratic_evaluation_relation", "column": "b_ticket_total_count", "jdbcType": "number"}, "name": "b_ticket_total_count", "listSchema": {"style": {"align": "left"}, "schema": {"type": "string", "title": "B票总票数"}, "properties": {"copyable": false, "editable": true, "required": false, "sortable": false, "instrLength": 1}}}], "selectable": false, "searchFields": []}', 1, '{"type": "FOREIGN_KEY", "table": "t_police_democratic_evaluation_relation", "column": "profile_id", "idColumn": "id", "primaryColumn": "id"}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO t_profile_module
(id, cn_name, en_name, `type`, pid, is_archive, show_order, table_schema, form_schema, list_schema, is_add, database_relation, show_schema_type, add_schema_type, is_operation_content, is_mobile_content, is_web_content, is_fk_content)
VALUES(1663, '档案管理', 'archive', 'police', NULL, 0, 1, '{}', '{}', '{"name": "人员信息", "type": "LIST_SCHEMA", "table": "t_profile_police", "fields": [{"db": {"table": "t_profile_police", "column": "photo", "mapping": "json_to_image_array", "jdbcType": "json_object_array"}, "name": "photo", "listSchema": {"style": {"align": "center", "fixed": "left", "ellipsis": true}, "schema": {"type": "photo", "title": "照片"}, "properties": {"isPhoto": true, "copyable": false, "editable": false, "required": false, "sortable": false, "isRelatedShow": true}}}, {"db": {"table": "t_profile_police", "column": "name", "jdbcType": "string"}, "name": "name", "listSchema": {"style": {"align": "center", "ellipsis": true}, "schema": {"type": "string", "title": "姓名"}, "properties": {"isName": true, "copyable": false, "editable": false, "required": false, "sortable": false, "isRelatedShow": true}}}, {"db": {"table": "t_profile_police", "column": "id_number", "jdbcType": "string"}, "name": "idNumber", "listSchema": {"style": {"align": "center", "ellipsis": false}, "schema": {"type": "string", "title": "身份证号"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "validateOption": {"pattern": "/[0-9]/"}}}}, {"db": {"table": "t_profile_police", "column": "registered_residence", "mapping": "district_code_to_name", "jdbcType": "district"}, "name": "registeredResidence", "listSchema": {"style": {"align": "center"}, "filter": {"key": "registeredResidence", "type": "multiple-tree", "value": ["&&district&&"], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "户籍地"}, "schema": {"type": "string", "title": "户籍地"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_police", "column": "create_time", "mapping": "date_time_to_text", "jdbcType": "datetime"}, "name": "createTime", "listSchema": {"style": {"align": "center"}, "filter": {"key": "createTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "录入时间"}, "schema": {"type": "string", "title": "录入时间"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": true}}}, {"db": {"table": "t_profile_police", "column": "update_time", "mapping": "date_time_to_text", "jdbcType": "datetime"}, "name": "updateTime", "listSchema": {"style": {"align": "center"}, "filter": {"key": "updateTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "更新时间"}, "schema": {"type": "string", "title": "更新时间"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": true, "sortDefault": "descending"}}}], "selectable": true, "searchFields": [{"key": "idNumber", "name": "身份证号"}, {"key": "name", "name": "姓名"}]}', 0, '{}', NULL, NULL, 1, 1, 1, 0);