DELIMITER $$
DROP PROCEDURE IF EXISTS `insert_dict_data` $$
CREATE PROCEDURE insert_dict_data()
BEGIN
    DECLARE pid INT;
		DELETE FROM t_dict WHERE type like 'fxmbzt_type%';
    INSERT INTO t_dict (`type`,code,name,p_code,show_number,standard,flag,color,status) VALUES
        ('fxmbzt_type_group',0,'发现目标状态类型',0,0,NULL,NULL,NULL,1);
		SET pid = LAST_INSERT_ID();
		UPDATE t_dict SET p_id = pid WHERE type = 'fxmbzt_type_group' and code = 0;

		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'fxmbzt_type',6,'未发现',0,NULL,6,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'fxmbzt_type',2,'在京',0,NULL,5,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
		(pid,'fxmbzt_type',3,'在北戴河',0,NULL,4,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
		(pid,'fxmbzt_type',4,'途中',0,NULL,3,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
		(pid,'fxmbzt_type',5,'订购票',0,NULL,2,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
		(pid,'fxmbzt_type',7,'在本地',0,NULL,1,NULL,null,NULL,1);
END $$
DELIMITER ;
CALL insert_dict_data;
DROP PROCEDURE IF EXISTS insert_dict_data;

DELIMITER $$
DROP PROCEDURE IF EXISTS `insert_dict_data` $$
CREATE PROCEDURE insert_dict_data()
BEGIN
    DECLARE pid INT;
		DELETE FROM t_dict WHERE type like 'czjg_type%';
    INSERT INTO t_dict (`type`,code,name,p_code,show_number,standard,flag,color,status) VALUES
        ('czjg_type_group',0,'处置结果类型',0,0,NULL,NULL,NULL,1);
		SET pid = LAST_INSERT_ID();
		UPDATE t_dict SET p_id = pid WHERE type = 'czjg_type_group' and code = 0;

		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'czjg_type',41,'未关注',0,NULL,13,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'czjg_type',42,'已关注',0,NULL,12,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
		(pid,'czjg_type',97,'源头稳控',0,NULL,11,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
		(pid,'czjg_type',98,'中途劝返',0,NULL,10,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
		(pid,'czjg_type',99,'在京查控',0,NULL,9,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
		(pid,'czjg_type',91,'错误预警',0,NULL,8,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
		(pid,'czjg_type',32,'已经营',0,NULL,7,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
		(pid,'czjg_type',31,'未经营',0,NULL,6,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
		(pid,'czjg_type',90,'其他',0,NULL,5,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
		(pid,'czjg_type',21,'未管控',0,NULL,4,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
		(pid,'czjg_type',12,'已抓获',0,NULL,3,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
		(pid,'czjg_type',11,'未抓获',0,NULL,2,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
		(pid,'czjg_type',22,'已管控',0,NULL,1,NULL,null,NULL,1);
END $$
DELIMITER ;
CALL insert_dict_data;
DROP PROCEDURE IF EXISTS insert_dict_data;