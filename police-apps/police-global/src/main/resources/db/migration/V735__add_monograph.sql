INSERT INTO t_application_system (name,module,url,icon,show_order,module_id,description,is_app,is_pc)
SELECT '填报线索','intelligence.xiansuo',null,'{"type": "default", "iconUrl": null, "iconText": null, "background": null, "iconDefault": "icon-filling-clue"}',1,124,'情指行线索-填报线索',1,0
WHERE NOT EXISTS (
    SELECT 1 FROM t_application_system WHERE name = '填报线索' and module = 'intelligence.xiansuo'
) LIMIT 1;


CREATE TABLE IF NOT EXISTS t_monograph (
	id BIGINT auto_increment NOT NULL,
	create_time DATETIME NULL,
	create_user_id BIGINT NULL,
	create_dept_id BIGINT NULL,
	update_time DATETIME NULL,
	update_user_id BIGINT NULL,
	update_dept_id BIGINT NULL,
	name varchar(100) NULL COMMENT '名称',
	cover_image varchar(1024) NULL COMMENT '封面图',
	completion DOUBLE NULL COMMENT '完成度',
	monograph_status INT NULL COMMENT '状态',
	monograph_content TEXT NULL COMMENT '移除了html文本的正文',
	start_time DATETIME NULL COMMENT '开始时间',
	end_time DATETIME NULL COMMENT '结束时间',
	template_id BIGINT NULL COMMENT '模板id',
	monograph_type INT NULL COMMENT '专刊类型',
	cycle_start_time DATETIME NULL COMMENT '周期开始时间（非专刊开始时间，用于区分不同周期）',
	header text COLLATE utf8mb4_bin COMMENT '头部html文本（前端显示用到）',
	CONSTRAINT t_monograph_pk PRIMARY KEY (id)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_bin;


CREATE TABLE IF NOT EXISTS t_monograph_comment (
    id BIGINT auto_increment NOT NULL,
	create_time DATETIME NULL,
	create_user_id BIGINT NULL,
	create_dept_id BIGINT NULL,
	update_time DATETIME NULL,
	update_user_id BIGINT NULL,
	update_dept_id BIGINT NULL,
	monograph_id BIGINT NULL COMMENT '专刊id',
	directory_id BIGINT NULL COMMENT '目录id',
	comment TEXT NULL COMMENT '评论内容',
	create_user_name varchar(100) NULL COMMENT '创建用户名称',
	create_dept_name varchar(100) NULL COMMENT '创建部门名称',
	CONSTRAINT t_monograph_comment_pk PRIMARY KEY (id)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_bin;

CREATE TABLE IF NOT EXISTS t_monograph_dept_relation (
    id BIGINT auto_increment NOT NULL,
	create_time DATETIME NULL,
	create_user_id BIGINT NULL,
	create_dept_id BIGINT NULL,
	update_time DATETIME NULL,
	update_user_id BIGINT NULL,
	update_dept_id BIGINT NULL,
	monograph_id BIGINT NULL COMMENT '专刊id',
	dept_id BIGINT NULL COMMENT '关联部门id',
	relation_type INT NULL COMMENT '关联关系',
	relation_status INT NULL COMMENT '状态',
	being_urged INT NULL COMMENT '状态',
	dept_content TEXT NULL COMMENT '填写内容',
	CONSTRAINT t_monograph_dept_relation_pk PRIMARY KEY (id)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_bin;

CREATE TABLE IF NOT EXISTS t_monograph_directory (
    id BIGINT auto_increment NOT NULL,
	create_time DATETIME NULL,
	create_user_id BIGINT NULL,
	create_dept_id BIGINT NULL,
	update_time DATETIME NULL,
	update_user_id BIGINT NULL,
	update_dept_id BIGINT NULL,
	template_id BIGINT NULL COMMENT '模板id',
	directory_name varchar(100) NULL COMMENT '目录名称',
	directory_level INT NULL COMMENT '目录级别',
	peer_sorting INT NULL COMMENT '同级排序',
	pid BIGINT NULL COMMENT '父id',
	template_key varchar(100) NULL COMMENT 'key',
	CONSTRAINT t_monograph_directory_pk PRIMARY KEY (id)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_bin;

CREATE TABLE IF NOT EXISTS t_monograph_directory_dept_relation (
    id BIGINT auto_increment NOT NULL,
	create_time DATETIME NULL,
	create_user_id BIGINT NULL,
	create_dept_id BIGINT NULL,
	update_time DATETIME NULL,
	update_user_id BIGINT NULL,
	update_dept_id BIGINT NULL,
	monograph_id BIGINT NULL COMMENT '专刊id',
	directory_id BIGINT NULL COMMENT '目录id',
	dept_id BIGINT NULL COMMENT '关联部门id',
	relation_status INT NULL COMMENT '状态',
	directory_content TEXT NULL COMMENT '填写内容',
	CONSTRAINT t_monograph_directory_dept_relation_pk PRIMARY KEY (id)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_bin;

CREATE TABLE IF NOT EXISTS t_monograph_template (
    id BIGINT auto_increment NOT NULL,
	create_time DATETIME NULL,
	create_user_id BIGINT NULL,
	create_dept_id BIGINT NULL,
	update_time DATETIME NULL,
	update_user_id BIGINT NULL,
	update_dept_id BIGINT NULL,
	template_name varchar(100) NULL COMMENT '模板名称',
	title_template varchar(1024) NULL COMMENT '专刊标题模板',
	cover_image varchar(1024) NULL COMMENT '封面图',
	monograph_type INT NULL COMMENT '模板周期类型',
	start_offset BIGINT NULL COMMENT '周期内开始时间偏移量（单位秒）',
	end_offset BIGINT NULL COMMENT '周期内结束时间偏移量（单位秒）',
	template_doc_path varchar(200) NULL COMMENT '模板路径值',
	template_doc_path_type INT NULL COMMENT '0 jar file 1 oss',
	header_html text COLLATE utf8mb4_bin COMMENT '头部模板',
    header_params_map text COLLATE utf8mb4_bin COMMENT '头部模板参数映射',
    visible_departments text COLLATE utf8mb4_bin COMMENT '可见部门',
	CONSTRAINT t_monograph_template_pk PRIMARY KEY (id)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_bin;

CREATE TABLE IF NOT EXISTS t_monograph_directory_dept_conf (
    id BIGINT auto_increment NOT NULL,
	dept_id BIGINT NULL COMMENT '部门id',
	directory_id BIGINT NULL COMMENT '目录id',
	CONSTRAINT t_monograph_directory_dept_conf_pk PRIMARY KEY (id)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_bin;
