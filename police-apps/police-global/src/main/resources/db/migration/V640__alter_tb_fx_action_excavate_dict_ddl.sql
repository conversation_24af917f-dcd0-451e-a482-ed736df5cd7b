DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='tb_fx_action_excavate_dict' AND column_name='formula')
    THEN
ALTER TABLE tb_fx_action_excavate_dict ADD formula varchar(100) NULL COMMENT '计算公式';
END IF;
    IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='tb_fx_action_excavate_dict' AND column_name='fx_tags')
    THEN
ALTER TABLE tb_fx_action_excavate_dict ADD fx_tags JSON NULL COMMENT '反邪标签对象';
END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;