delete from t_profile_module where id = 606;
delete from t_profile_module where id = 608;
INSERT INTO t_profile_module (id, cn_name,en_name,`type`,pid,is_archive,show_order,table_schema,form_schema,list_schema,is_add,database_relation,show_schema_type,add_schema_type,is_operation_content,is_mobile_content,is_web_content,is_fk_content) VALUES
    (606, '档案管理','archive','goods',NULL,0,1,'{}','{}','{"name": "物品信息", "type": "LIST_SCHEMA", "table": "t_profile_goods", "fields": [{"db": {"table": "t_profile_goods", "column": "photo", "mapping": "json_to_image_array", "jdbcType": "json_object_array"}, "name": "photo", "listSchema": {"style": {"align": "center", "fixed": "left", "ellipsis": true}, "schema": {"type": "photo", "title": "照片"}, "properties": {"isPhoto": true, "copyable": false, "editable": false, "required": false, "sortable": false, "isRelatedShow": true}}}, {"db": {"table": "t_profile_goods", "column": "name", "jdbcType": "string"}, "name": "name", "listSchema": {"style": {"align": "center", "ellipsis": true}, "schema": {"type": "string", "title": "名称"}, "properties": {"isName": true, "copyable": false, "editable": false, "required": false, "sortable": false, "isRelatedShow": true}}}, {"db": {"table": "t_profile_goods", "column": "category", "mapping": "dict_code_array_to_name", "jdbcType": "label_id_array"}, "dict": {"type": "goods_archives_category"}, "name": "category", "listSchema": {"style": {"align": "center"}, "filter": {"key": "category", "type": "select", "value": ["%%goods_goods_archives_category_group%%"], "fieldNames": {"label": "name", "value": "code"}, "displayName": "类别"}, "schema": {"type": "array", "title": "类别"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_profile_goods", "column": "sub_category", "mapping": "dict_code_array_to_name", "jdbcType": "label_id_array"}, "dict": {"type": "goods_archives_category"}, "name": "sub_category", "listSchema": {"style": {"align": "center"}, "filter": {"key": "sub_category", "type": "select", "value": ["%%goods_goods_archives_category_group%%"], "fieldNames": {"label": "name", "value": "code"}, "displayName": "细类"}, "schema": {"type": "array", "title": "细类"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_profile_goods", "column": "create_time", "mapping": "date_time_to_text", "jdbcType": "datetime"}, "name": "createTime", "listSchema": {"style": {"align": "center"}, "filter": {"key": "createTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "录入时间"}, "schema": {"type": "string", "title": "录入时间"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": true}}}, {"db": {"table": "t_profile_goods", "column": "update_time", "mapping": "date_time_to_text", "jdbcType": "datetime"}, "name": "updateTime", "listSchema": {"style": {"align": "center"}, "filter": {"key": "updateTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "更新时间"}, "schema": {"type": "string", "title": "更新时间"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": true, "sortDefault": "descending"}}}], "selectable": true, "searchFields": [{"key": "idNumber", "name": "身份证号"}, {"key": "name", "name": "名称"}], "profileDataPermission": []}',0,'{}',NULL,NULL,1,1,1,0),
    (608, '物品信息','goods','goods',607,1,3,'{"name": "物品信息", "type": "TABLE_SCHEMA", "table": "t_profile_goods", "fields": [{"db": {"table": "t_profile_goods", "column": "name", "jdbcType": "string"}, "name": "name", "tableSchema": {"span": 1, "type": "string", "title": "名称", "copyable": false}}, {"db": {"table": "t_profile_goods", "column": "category", "mapping": "dict_code_array_to_name", "jdbcType": "json_id_array"}, "dict": {"type": "goods_archives_category"}, "name": "category", "tableSchema": {"span": 1, "type": "label", "title": "类别", "copyable": false}}, {"db": {"table": "t_profile_goods", "column": "sub_category", "mapping": "dict_code_array_to_name", "jdbcType": "json_id_array"}, "dict": {"type": "goods_archives_category"}, "name": "sub_category", "tableSchema": {"span": 1, "type": "label", "title": "细类", "copyable": false}}, {"db": {"table": "t_profile_goods", "column": "status", "mapping": "dict_code_to_name", "jdbcType": "string"}, "dict": {"type": "goods_status"}, "name": "status", "tableSchema": {"span": 1, "type": "string", "title": "状态", "copyable": false}}, {"db": {"table": "t_profile_goods", "column": "focus_status", "mapping": "dict_code_to_name", "jdbcType": "string"}, "dict": {"type": "goods_focus_status"}, "name": "focus_status", "tableSchema": {"span": 1, "type": "string", "title": "关注状态", "copyable": false}}, {"db": {"table": "t_profile_goods", "column": "location", "jdbcType": "string"}, "name": "location", "tableSchema": {"span": 1, "type": "string", "title": "位置", "copyable": false}}], "moduleUi": {"column": 3, "bordered": true}}','{"name": "物品信息", "type": "FORM_SCHEMA", "table": "t_profile_goods", "fields": [{"db": {"table": "t_profile_goods", "column": "name", "jdbcType": "string"}, "name": "name", "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "名称"}}}, {"db": {"table": "t_profile_goods", "column": "photo", "jdbcType": "json_image_array"}, "name": "photo", "formSchema": {"ui": {"ui:options": {"style": {"top": "0", "right": "0", "position": "absolute"}, "width": "0.5", "action": "/upload/imgs", "widget": "upload", "isShowTitle": false, "titleLocation": "left"}}, "schema": {"type": "array", "items": {"type": "object"}, "title": "照片"}}}, {"db": {"table": "t_profile_goods", "column": "category", "jdbcType": "json_id_array"}, "name": "category", "tree": {"root": "goods_goods_archives_category_group&&0", "type": "dict"}, "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "0.5", "widget": "cascader", "multiple": true, "fieldNames": {"label": "name", "value": "code", "children": "children"}, "titleLocation": "left"}}, "schema": {"type": "array", "items": {"type": "array"}, "title": "类别"}}}, {"db": {"table": "t_profile_goods", "column": "sub_category", "jdbcType": "json_id_array"}, "name": "subCategory", "tree": {"root": "goods_goods_archives_category_group", "type": "dict"}, "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "0.5", "widget": "cascader", "multiple": true, "fieldNames": {"label": "name", "value": "code", "children": "children"}, "titleLocation": "left"}}, "schema": {"type": "array", "items": {"type": "array"}, "title": "细类"}}}, {"db": {"table": "t_profile_goods", "column": "focus_status", "jdbcType": "integer"}, "dict": {"type": "goods_goods_focus_status_group"}, "name": "focusStatus", "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "0.5", "widget": "radio", "titleLocation": "left"}}, "schema": {"type": "number", "title": "关注状态"}}}, {"db": {"table": "t_profile_goods", "column": "location", "jdbcType": "string"}, "name": "location", "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "位置"}}}, {"db": {"table": "t_profile_goods", "column": "goods_describe", "jdbcType": "string"}, "name": "goodsDescribe", "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "1.0", "widget": "textarea", "titleLocation": "left"}}, "schema": {"type": "string", "title": "物品描述"}}}], "required": ["name"]}','{}',1,'{"type": "PRIMARY_KEY", "table": "t_profile_goods", "column": "id"}','TABLE_SCHEMA','FORM_SCHEMA',1,1,1,0);
