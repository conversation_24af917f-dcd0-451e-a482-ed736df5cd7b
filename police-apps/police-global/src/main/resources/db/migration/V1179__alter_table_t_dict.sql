DELIMITER $$
DROP PROCEDURE IF EXISTS insert_dict$$
CREATE PROCEDURE insert_dict()
BEGIN
    DECLARE pid INT;
    -- 删除已存在的数据
    DELETE FROM t_dict WHERE type LIKE 'profile_person_control_level_fk%';
    -- 插入分组
    INSERT INTO t_dict
    (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color)
    VALUES(NULL, 'profile_person_control_level_fk_group', 0, 'fk人员级别', 0, NULL, 0, NULL, NULL, NULL);

    SET pid = LAST_INSERT_ID();

    -- 插入子项
    INSERT INTO t_dict
    (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color)
    VALUES
    (pid, 'profile_person_control_level_fk', 1, '重中之重', 0, NULL, 1, NULL, NULL, NULL),
    (pid, 'profile_person_control_level_fk', 2, '高', 0, NULL, 2, NULL, NULL, NULL),
    (pid, 'profile_person_control_level_fk', 3, '中', 0, NULL, 3, NULL, NULL, NULL),
    (pid, 'profile_person_control_level_fk', 4, '低', 0, NULL, 4, NULL, NULL, NULL);
END$$

CALL insert_dict()$$
DROP PROCEDURE IF EXISTS insert_dict$$
DELIMITER ;