-- 人员列表调整风险分值的style跟mapping
UPDATE t_profile_module
SET
list_schema='{"name": "人员信息", "type": "LIST_SCHEMA", "table": "t_profile_person", "fields": [{"db": {"table": "t_profile_person", "column": "photo", "mapping": "json_to_image_array", "jdbcType": "json_object_array"}, "name": "photo", "listSchema": {"style": {"align": "center", "fixed": "left", "ellipsis": true}, "schema": {"type": "photo", "title": "照片"}, "properties": {"isPhoto": true, "copyable": false, "editable": false, "required": false, "sortable": false, "isRelatedShow": true}}}, {"db": {"table": "t_profile_person", "column": "name", "jdbcType": "string"}, "name": "name", "listSchema": {"style": {"align": "center", "ellipsis": true}, "schema": {"type": "string", "title": "姓名"}, "properties": {"isName": true, "copyable": false, "editable": false, "required": false, "sortable": false, "isRelatedShow": true}}}, {"db": {"table": "t_profile_person", "column": "id_number", "jdbcType": "string"}, "name": "idNumber", "listSchema": {"style": {"align": "center", "ellipsis": false}, "schema": {"type": "string", "title": "证件号码"}, "properties": {"href": "/ys-app/archives/person/details?id={value}", "copyable": false, "editable": false, "required": false, "sortable": false, "isRelatedShow": true, "validateOption": {"pattern": "/[0-9]/"}}}}, {"db": {"table": "t_profile_person", "column": "person_label", "mapping": "label_id_array_to_name", "jdbcType": "label_id_array"}, "name": "personLabel", "listSchema": {"style": {"align": "left", "ellipsis": true}, "filter": {"key": "personLabel", "type": "multiple-tree", "value": ["&&person_label&&"], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "人员标签"}, "schema": {"type": "array", "title": "人员标签"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 2, "isRelatedShow": true}}}, {"db": {"table": "t_profile_person", "column": "control_level", "mapping": "dict_code_to_name", "jdbcType": "string"}, "dict": {"type": "profile_person_control_level"}, "name": "control_level", "listSchema": {"style": {"align": "center"}, "filter": {"key": "control_level", "type": "select", "value": ["%%profile_person_control_level%%"], "fieldNames": {"label": "name", "value": "code"}, "displayName": "人员级别"}, "schema": {"type": "string", "title": "人员级别"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_profile_person_police_control", "column": "control_person", "mapping": "user_id_array_to_user_name_array", "jdbcType": "user_id_array", "databaseRelation": {"type": "FOREIGN_KEY", "table": "t_profile_person_control", "column": "person_id"}}, "name": "dutyPolice", "listSchema": {"style": {"align": "center"}, "schema": {"type": "array", "title": "责任民警"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_profile_person_police_control", "column": "control_station", "mapping": "dept_code_to_dept_name", "jdbcType": "dept_code", "databaseRelation": {"type": "FOREIGN_KEY", "table": "t_profile_person_control", "column": "person_id"}}, "name": "dutyPoliceStation", "listSchema": {"style": {"align": "center"}, "filter": {"key": "dutyPoliceStation", "type": "multiple-tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "deptCode", "children": "children"}, "displayName": "责任派出所"}, "schema": {"type": "string", "title": "责任派出所"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "isRelatedShow": true}}}, {"db": {"table": "t_profile_person", "column": "registered_residence", "mapping": "district_code_to_name", "jdbcType": "district"}, "name": "registeredResidence", "listSchema": {"style": {"align": "center"}, "filter": {"key": "registeredResidence", "type": "multiple-tree", "value": ["&&district&&"], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "户籍地"}, "schema": {"type": "string", "title": "户籍地"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_person", "column": "create_time", "mapping": "date_time_to_text", "jdbcType": "datetime"}, "name": "createTime", "listSchema": {"style": {"align": "center"}, "filter": {"key": "createTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "录入时间"}, "schema": {"type": "string", "title": "录入时间"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": true}}}, {"db": {"table": "t_profile_person", "column": "update_time", "mapping": "date_time_to_text", "jdbcType": "datetime"}, "name": "updateTime", "listSchema": {"style": {"align": "center"}, "filter": {"key": "updateTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "更新时间"}, "schema": {"type": "string", "title": "更新时间"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": true, "sortDefault": "descending"}}}], "selectable": true, "searchFields": [{"key": "idNumber", "name": "身份证号"}, {"key": "name", "name": "姓名"}], "profileDataPermission": [{"db": {"table": "t_profile_person", "column": "person_label", "jdbcType": "string"}, "type": "personLabel"}]}'
WHERE id = 1;

