CREATE TABLE IF NOT EXISTS `t_risk_person_focus` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `risk_person_id` bigint(20) NOT NULL COMMENT '风险人员id',
    `id_card` varchar(50) NOT NULL COMMENT '身份证号码',
    `comment` text DEFAULT NULL COMMENT '评论',
    create_time DATETIME NULL,
    create_user_id BIGINT NULL,
    create_dept_id BIGINT NULL,
    update_time DATETIME NULL,
    update_user_id BIGINT NULL,
    update_dept_id BIGINT NULL,
    PRIMARY KEY (`id`) USING BTREE
    ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='风险人员-关注表';