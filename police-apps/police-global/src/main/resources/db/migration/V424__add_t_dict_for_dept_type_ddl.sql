DELIMITER $$
DROP PROCEDURE IF EXISTS `insert_dict` $$
CREATE PROCEDURE insert_dict()
BEGIN
    DECLARE ppid INT;
    -- 删除历史数据
    delete from t_dict where `type` = 'dept_type' and `code` = 6 and name = '实战服务中心';
    delete from t_dict where `type` = 'dept_type' and `code` = 7 and name = '主侦警种研判岗';
    delete from t_dict where `type` = 'dept_type' and `code` = 8 and name = '其他警种研判岗';
    delete from t_dict where `type` = 'dept_type' and `code` = 9 and name = '作战单元';
    -- 插入新纪录
    INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color) VALUES (0,'dept_type',6,'实战服务中心',NULL,NULL,7,NULL,NULL,NULL);
    INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color) VALUES (0,'dept_type',7,'主侦警种研判岗',NULL,NULL,8,NULL,NULL,NULL);
    INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color) VALUES (0,'dept_type',8,'其他警种研判岗',NULL,NULL,9,NULL,NULL,NULL);
    INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color) VALUES (0,'dept_type',9,'作战单元',NULL,NULL,10,NULL,NULL,NULL);
    -- 更新PId
    SET ppid = (select id from t_dict where `type`='dept_type_group');
    update t_dict set p_id = ppid where `type` = 'dept_type' and `code` IN(6,7,8,9);
END $$
DELIMITER ;
CALL insert_dict;
DROP PROCEDURE IF EXISTS `insert_dict`;
