CREATE TABLE IF NOT EXISTS `tb_fx_qtxswj`  (
  `id` int(0) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `group_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '群体名称',
  `group_id_cards` json NULL COMMENT '群体下人员身份证号码',
  `group_size` int(0) NULL DEFAULT NULL COMMENT '群体人数',
  `risk_score` double NULL DEFAULT NULL COMMENT '风险总分',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '群体线索挖掘' ROW_FORMAT = Dynamic;