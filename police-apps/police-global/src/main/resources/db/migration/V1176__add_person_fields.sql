DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    -- 流入时间
    IF NOT EXISTS( SELECT * FROM information_schema.columns WHERE table_schema=(select database()) AND table_name='t_profile_person' AND column_name='inflow_time')
    THEN
        ALTER TABLE t_profile_person ADD inflow_time date DEFAULT NULL COMMENT '流入时间';
    END IF;

    -- 十类人员类别
    IF NOT EXISTS( SELECT * FROM information_schema.columns WHERE table_schema=(select database()) AND table_name='t_profile_person' AND column_name='is_slrylb')
    THEN
        ALTER TABLE t_profile_person ADD is_slrylb INTEGER DEFAULT NULL COMMENT '十类人员类别';
    END IF;

    -- 职业类别
    IF NOT EXISTS( SELECT * FROM information_schema.columns WHERE table_schema=(select database()) AND table_name='t_profile_person' AND column_name='occupation_category')
    THEN
        ALTER TABLE t_profile_person ADD occupation_category varchar(255) DEFAULT NULL COMMENT '职业类别';
    END IF;

    -- 工作类别
    IF NOT EXISTS( SELECT * FROM information_schema.columns WHERE table_schema=(select database()) AND table_name='t_profile_person' AND column_name='work_category')
    THEN
        ALTER TABLE t_profile_person ADD work_category varchar(255) DEFAULT NULL COMMENT '工作类别';
    END IF;

    -- 务工地址
    IF NOT EXISTS( SELECT * FROM information_schema.columns WHERE table_schema=(select database()) AND table_name='t_profile_person' AND column_name='work_address')
    THEN
        ALTER TABLE t_profile_person ADD work_address varchar(255) DEFAULT NULL COMMENT '务工地址';
    END IF;

    -- 务工地址详情
    IF NOT EXISTS( SELECT * FROM information_schema.columns WHERE table_schema=(select database()) AND table_name='t_profile_person' AND column_name='work_address_detail')
    THEN
        ALTER TABLE t_profile_person ADD work_address_detail varchar(255) DEFAULT NULL COMMENT '务工地址详情';
    END IF;

    -- 基础摸排地址
    IF NOT EXISTS( SELECT * FROM information_schema.columns WHERE table_schema=(select database()) AND table_name='t_profile_person' AND column_name='basic_investigation_address')
    THEN
        ALTER TABLE t_profile_person ADD basic_investigation_address varchar(255) DEFAULT NULL COMMENT '基础摸排地址';
    END IF;

    -- 基础摸排地址详情
    IF NOT EXISTS( SELECT * FROM information_schema.columns WHERE table_schema=(select database()) AND table_name='t_profile_person' AND column_name='basic_investigation_address_detail')
    THEN
        ALTER TABLE t_profile_person ADD basic_investigation_address_detail varchar(255) DEFAULT NULL COMMENT '基础摸排地址详情';
    END IF;

    -- 居住事由
    IF NOT EXISTS( SELECT * FROM information_schema.columns WHERE table_schema=(select database()) AND table_name='t_profile_person' AND column_name='residence_reason')
    THEN
        ALTER TABLE t_profile_person ADD residence_reason text DEFAULT NULL COMMENT '居住事由';
    END IF;

    -- 具体研判情况
    IF NOT EXISTS( SELECT * FROM information_schema.columns WHERE table_schema=(select database()) AND table_name='t_profile_person' AND column_name='specific_analysis')
    THEN
        ALTER TABLE t_profile_person ADD specific_analysis text DEFAULT NULL COMMENT '具体研判情况';
    END IF;

    -- 其他违法犯罪情况
    IF NOT EXISTS( SELECT * FROM information_schema.columns WHERE table_schema=(select database()) AND table_name='t_profile_person' AND column_name='other_illegal_activities')
    THEN
        ALTER TABLE t_profile_person ADD other_illegal_activities text DEFAULT NULL COMMENT '其他违法犯罪情况';
    END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;
