CREATE TABLE IF NOT EXISTS t_profile_sthy_fkxx
(
    id          bigint       NOT NULL AUTO_INCREMENT COMMENT '主键',
    xzqhdm      varchar(12)  NULL COMMENT '行政区划单位',
    fkdbh       varchar(64)  NOT NULL COMMENT '反馈单编号',
    jjdbh       varchar(64)  NULL COMMENT '接警单编号',
    pjdbh       varchar(64)  NOT NULL COMMENT '派警单编号',
    fklyh       varchar(255) NULL COMMENT '反馈录音号',
    fkdwdm      varchar(12)  NULL COMMENT '反馈单位代码',
    fkybh       varchar(64)  NULL COMMENT '反馈员编号',
    fkyxm       varchar(50)  NULL COMMENT '反馈员姓名',
    fksj        timestamp(6) NULL COMMENT '反馈时间',
    cjsj01      timestamp(6) NULL COMMENT '实际出警时间',
    ddxcsj      timestamp(6) NULL COMMENT '到达现场时间',
    xcclwbsj    timestamp(6) NULL COMMENT '现场处理完毕时间',
    jqlbdm      varchar(8)   NULL COMMENT '警情类别代码',
    jqlxdm      varchar(8)   NULL COMMENT '警情类型代码',
    jqxldm      varchar(8)   NULL COMMENT '警情细类代码',
    jqzldm      varchar(8)   NULL COMMENT '警情之类代码',
    jqfssj      timestamp(6) NULL COMMENT '警情发生时间',
    bjdz        varchar(200) NULL COMMENT '报警地址',
    jqdz        varchar(200) NULL COMMENT '警情地址',
    fkdwxzb     numeric      NULL COMMENT '反馈定位x坐标（处置现场位置）',
    fkdwyzb     numeric      NULL COMMENT '反馈定位y坐标（处置现场位置）',
    cjczqk      text         NULL COMMENT '出警处置情况',
    cdcc        int          NULL DEFAULT 0 COMMENT '出动车次',
    cdrc        int          NULL DEFAULT 0 COMMENT '出动人次',
    cdct        varchar(500) NULL COMMENT '出动船艇',
    jzrs        int          NULL DEFAULT 0 COMMENT '救助人数',
    jzrssm      varchar(500) NULL COMMENT '救助人数说明',
    ssrs        int          NULL DEFAULT 0 COMMENT '受伤人数',
    ssrssm      varchar(500) NULL COMMENT '受伤人数说明',
    swrs        int          NULL DEFAULT 0 COMMENT '死亡人数',
    swrssm      varchar(500) NULL COMMENT '死亡人数说明',
    jqcljgdm    varchar(64)  NULL COMMENT '警情处理结果代码',
    jqcljgsm    text         NULL COMMENT '警情处理结果说明',
    tqqkdm      varchar(64)  NULL COMMENT '天气情况代码',
    ssqkms      text         NULL COMMENT '损失情况描述',
    zhrs        int          NULL DEFAULT 0 COMMENT '抓获人数',
    sars        int          NULL DEFAULT 0 COMMENT '涉案人数',
    tprs        int          NULL DEFAULT 0 COMMENT '逃跑人数',
    jtsgxtdm    varchar(64)  NULL COMMENT '交通事故形态代码',
    sfzzwxp     varchar(64)  NULL COMMENT '是否装载危险品',
    jtsgccyydm  varchar(64)  NULL COMMENT '交通事故初查原因代码',
    njddm       varchar(64)  NULL COMMENT '能见度代码',
    lmzkdm      varchar(64)  NULL COMMENT '路面状况代码',
    shjdcs      int          NULL DEFAULT 0 COMMENT '损坏机动车数',
    shfjdcs     int          NULL DEFAULT 0 COMMENT '损坏非机动车数',
    dllxdm      varchar(64)  NULL COMMENT '道路类型代码',
    jqclztdm    varchar(64)  NULL COMMENT '警情处理状态代码',
    cjsj        timestamp(6) NULL COMMENT '创建时间',
    gxsj        timestamp(6) NULL COMMENT '更新时间',
    jdxz        varchar(64)  NULL COMMENT '乡镇（街道）',
    sdcs        varchar(64)  NULL COMMENT '属地村社',
    sfqz        varchar(64)  NULL COMMENT '是否取证',
    yjdwdm      varchar(12)  NULL COMMENT '移交单位代码',
    yjdwlxr     varchar(32)  NULL COMMENT '移交单位联系人名称',
    yjdwfkqk    text         NULL COMMENT '移交单位反馈情况',
    yjdwfkqksm  text         NULL COMMENT '移交单位反馈情况说明',
    ajbh        varchar(64)  NULL COMMENT '案件编号',
    nrbq        varchar(800) NULL COMMENT '警情标签',
    jqbwdm      varchar(32)  NULL COMMENT '警情部位代码',
    fklx        int          NULL COMMENT '',
    jqfsdy      varchar(200) NULL COMMENT '警情发生地域',
    forcedm     varchar(200) NULL COMMENT '警力代码',
    forcetype   varchar(8)   NULL COMMENT '警力类型',
    dqywzt      varchar(32)  NULL COMMENT '当前业务状态',
    fkdwdmbs    varchar(200) NULL COMMENT '反馈单位短码',
    fkdwmc      varchar(128) NULL COMMENT '反馈单位名称',
    livestatus  varchar(64)  NULL COMMENT '现场状态',
    tjdwdmbs    varchar(64)  NULL COMMENT '提交单位短码',
    wxbs        varchar(64)  NULL COMMENT '无效标识',
    yjdwmc      varchar(128) NULL COMMENT '移交单位名称',
    yjdwdmbs    varchar(200) NULL COMMENT '移交单位短码',
    tzhgxdwdm   varchar(200) NULL COMMENT '调整后管辖单位代码',
    tzhgxdwmc   varchar(128) NULL COMMENT '调整后管辖单位名称',
    tzhgxdwdmbs varchar(200) NULL COMMENT '调整后管辖单位短码',
    yjdwlxrdm   varchar(200) NULL COMMENT '移交单位联系人代码',
    gcfkbs      bool         NULL COMMENT '过程反馈标识',
    PRIMARY KEY (`id`),
    UNIQUE INDEX `fkdbh_idx` (`fkdbh`) USING BTREE,
    INDEX `jjdbh_idx` (`jjdbh`) USING BTREE
);
