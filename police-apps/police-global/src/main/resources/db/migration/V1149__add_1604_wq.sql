SET @max_code = (SELECT COALESCE(MAX(`code`), 0) + 1 FROM t_dict where `type` = 'approval_type');
SET @pid = (select id from t_dict where type = 'approval_type' AND name = '档案');
INSERT INTO t_dict (p_id,`type`, code, name, p_code, dict_desc, show_number, standard, flag, color)
SELECT @pid,'approval_type', @max_code, '添加警种人员档案', 0, NULL, 1, NULL, NULL, NULL
FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM t_dict WHERE type = 'approval_type' AND name = '添加警种人员档案');

SET @max_code = (SELECT COALESCE(MAX(`code`), 0) + 1 FROM t_dict where `type` = 'approval_type');
SET @pid = (select id from t_dict where type = 'approval_type' AND name = '档案');
INSERT INTO t_dict (p_id,`type`, code, name, p_code, dict_desc, show_number, standard, flag, color)
SELECT @pid,'approval_type', @max_code, '添加警种群体档案', 0, NULL, 1, NULL, NULL, NULL
FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM t_dict WHERE type = 'approval_type' AND name = '添加警种群体档案');

SET @max_code = (SELECT COALESCE(MAX(`code`), 0) + 1 FROM t_dict where `type` = 'approval_type');
SET @pid = (select id from t_dict where type = 'approval_type' AND name = '档案');
INSERT INTO t_dict (p_id,`type`, code, name, p_code, dict_desc, show_number, standard, flag, color)
SELECT @pid,'approval_type', @max_code, '添加警种事件档案', 0, NULL, 1, NULL, NULL, NULL
FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM t_dict WHERE type = 'approval_type' AND name = '添加警种事件档案');