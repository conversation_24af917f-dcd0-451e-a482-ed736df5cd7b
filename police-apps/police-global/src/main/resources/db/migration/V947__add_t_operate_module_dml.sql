DELETE FROM t_operate_module where id IN (133);
INSERT INTO t_operate_module (id,cn_name,en_name,send_notice_type,pid,`path`,is_todo,is_system_app,is_todo_leader,enable,description,url,path_name,is_todo_for_app,is_app) VALUES
    (133,'人力情报','fight.humanIntelligence',3,NULL,NULL,0,0,0,0,NULL,NULL,'人力情报',0,0);

DELETE FROM t_operate_module where id IN (134);
INSERT INTO t_operate_module (id,cn_name,en_name,send_notice_type,pid,`path`,is_todo,is_system_app,is_todo_leader,enable,description,url,path_name,is_todo_for_app,is_app) VALUES
    (134,'人员档案','personV2',3,NULL,NULL,0,0,0,0,NULL,NULL,'人员档案',0,0);

DELETE FROM t_operate_module where id IN (135);
INSERT INTO t_operate_module (id,cn_name,en_name,send_notice_type,pid,`path`,is_todo,is_system_app,is_todo_leader,enable,description,url,path_name,is_todo_for_app,is_app) VALUES
    (135,'群体档案','groupV2',3,NULL,NULL,0,0,0,0,NULL,NULL,'群体档案',0,0);

DELETE FROM t_operate_module where id IN (136);
INSERT INTO t_operate_module (id,cn_name,en_name,send_notice_type,pid,`path`,is_todo,is_system_app,is_todo_leader,enable,description,url,path_name,is_todo_for_app,is_app) VALUES
    (136,'线索档案','clueV2',3,NULL,NULL,0,0,0,0,NULL,NULL,'线索档案',0,0);

DELETE FROM t_operate_module where id IN (137);
INSERT INTO t_operate_module (id,cn_name,en_name,send_notice_type,pid,`path`,is_todo,is_system_app,is_todo_leader,enable,description,url,path_name,is_todo_for_app,is_app) VALUES
    (137,'事件档案','eventV2',3,NULL,NULL,0,0,0,0,NULL,NULL,'事件档案',0,0);