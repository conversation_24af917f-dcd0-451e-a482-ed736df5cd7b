DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS( SELECT * FROM information_schema.columns WHERE table_schema=(select database()) AND table_name='t_profile_person' AND column_name='control_level')
    THEN
        ALTER TABLE t_profile_person ADD COLUMN `control_level` int default NULL COMMENT '管控级别';
    END IF;
END $$
DELIMITER ;
CALL add_column;