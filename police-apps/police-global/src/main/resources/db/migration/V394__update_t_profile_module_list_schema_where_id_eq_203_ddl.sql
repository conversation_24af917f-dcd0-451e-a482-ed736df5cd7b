UPDATE t_profile_module
SET database_relation='{"name":"相关人员","type":"LIST_SCHEMA","table":"t_profile_person","fields":[{"db":{"table":"t_profile_person","column":"name","jdbcType":"string"},"name":"name","listSchema":{"style":{"align":"center"},"schema":{"type":"string","title":"人员名称"},"properties":{"href":"/ys-app/archives/person/details?id={value}","copyable":false,"editable":false,"required":false,"sortable":false}}},{"db":{"table":"t_profile_person","column":"id_number","jdbcType":"number"},"name":"idNumber","listSchema":{"style":{"align":"center"},"schema":{"type":"string","title":"身份证号"},"properties":{"copyable":false,"editable":false,"required":false,"sortable":false,"instrLength":1}}},{"db":{"table":"t_profile_person","column":"risk_score","jdbcType":"number"},"name":"riskScore","listSchema":{"style":{"align":"center"},"schema":{"type":"number","title":"风险分值"},"properties":{"copyable":false,"editable":false,"required":false,"sortable":false,"instrLength":1}}},{"db":{"table":"t_profile_person","column":"risk_level","jdbcType":"string"},"name":"riskLevel","listSchema":{"style":{"align":"center"},"schema":{"type":"string","title":"风险等级"},"properties":{"copyable":false,"editable":false,"required":false,"sortable":false,"instrLength":1}}},{"db":{"table":"t_profile_person","column":"person_label","mapping":"label_id_array_to_name","jdbcType":"label_id_array"},"name":"personLabel","listSchema":{"style":{"align":"left"},"filter":{"key":"personLabel","type":"multiple-tree","value":["&&person_label&&"],"fieldNames":{"label":"name","value":"id","children":"children"},"displayName":"人员标签"},"schema":{"type":"array","title":"人员标签"},"properties":{"copyable":false,"editable":false,"required":false,"sortable":false,"instrLength":2}}},{"db":{"table":"t_profile_person_event_relation","column":"risk_label_ids","mapping":"risk_label_id_array_to_name","jdbcType":"string","databaseRelation":{"type":"RELATION_TABLE","table":"t_profile_person_event_relation","joinTo":{"table":"t_profile_person","column":"id","joinColumn":"person_id"},"joinFrom":{"table":"t_profile_event","column":"id","joinColumn":"event_id"}}},"name":"riskLabels","listSchema":{"style":{"align":"left"},"schema":{"type":"risk_label_array","title":"风险标识"},"properties":{"copyable":false,"editable":true,"required":false,"sortable":false,"instrLength":1}}}],"selectable":false,"searchFields":[{"key":"name","name":"人员名称"}]}'
WHERE id = 203;