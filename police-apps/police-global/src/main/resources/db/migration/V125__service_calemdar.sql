CREATE TABLE IF NOT EXISTS `t_service_config` (
                                    `id` bigint NOT NULL AUTO_INCREMENT,
                                    `service_level` bigint DEFAULT NULL COMMENT '勤务等级',
                                    `regular_status` json DEFAULT NULL COMMENT '常控状态',
                                    `regular_level` json DEFAULT NULL COMMENT '常控级别',
                                    `days` bigint DEFAULT NULL COMMENT '时间跨度',
                                    `times` bigint DEFAULT NULL COMMENT '次数',
                                    PRIMARY KEY (`id`)
) comment '工作记录配置表';

CREATE  TABLE IF NOT EXISTS `t_service_calendar` (
                                      `id` bigint NOT NULL,
                                      `time` timestamp NULL DEFAULT NULL,
                                      `level` int DEFAULT NULL,
                                      PRIMARY KEY (`id`)
) comment '勤务日历表';