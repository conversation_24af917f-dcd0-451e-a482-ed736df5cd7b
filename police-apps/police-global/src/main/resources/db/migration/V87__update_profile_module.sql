TRUNCATE TABLE t_profile_module;
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (1, '档案管理', 'archive', 'person', NULL, 0, 1, '{}', '{}', '{\"name\": \"人员信息\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_person\", \"fields\": [{\"db\": {\"table\": \"t_profile_person\", \"column\": \"photo\", \"mapping\": \"json_to_image_array\", \"jdbcType\": \"json_object_array\"}, \"name\": \"photo\", \"listSchema\": {\"style\": {\"align\": \"center\", \"fixed\": \"left\", \"ellipsis\": true}, \"schema\": {\"type\": \"photo\", \"title\": \"照片\"}, \"properties\": {\"isPhoto\": true, \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": true}, \"schema\": {\"type\": \"string\", \"title\": \"姓名\"}, \"properties\": {\"isName\": true, \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"id_number\", \"jdbcType\": \"string\"}, \"name\": \"idNumber\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": false}, \"schema\": {\"type\": \"string\", \"title\": \"证件号码\"}, \"properties\": {\"href\": \"/ys-app/archives/person/details?id={value}\", \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"isRelatedShow\": true, \"validateOption\": {\"pattern\": \"/[0-9]/\"}}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"person_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"label_id_array\"}, \"name\": \"personLabel\", \"listSchema\": {\"style\": {\"align\": \"left\", \"ellipsis\": true}, \"filter\": {\"key\": \"personLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&person_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"人员标签\"}, \"schema\": {\"type\": \"array\", \"title\": \"人员标签\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_person\", \"mapping\": \"user_id_array_to_user_name_array\", \"jdbcType\": \"user_id_array\", \"databaseRelation\": {\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_person_control\", \"column\": \"person_id\"}}, \"name\": \"dutyPolice\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"array\", \"title\": \"责任民警\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_station\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"dept_code\", \"databaseRelation\": {\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_person_control\", \"column\": \"person_id\"}}, \"name\": \"dutyPoliceStation\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"dutyPoliceStation\", \"type\": \"multiple-tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"displayName\": \"责任派出所\"}, \"schema\": {\"type\": \"string\", \"title\": \"责任派出所\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"registered_residence\", \"mapping\": \"district_code_to_name\", \"jdbcType\": \"district\"}, \"name\": \"registeredResidence\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"registeredResidence\", \"type\": \"multiple-tree\", \"value\": [\"&&district&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"户籍地\"}, \"schema\": {\"type\": \"string\", \"title\": \"户籍地\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"create_time\", \"mapping\": \"date_time_to_text\", \"jdbcType\": \"datetime\"}, \"name\": \"createTime\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"createTime\", \"type\": \"timeParams\", \"value\": [{\"id\": \"1\", \"name\": \"今天\"}, {\"id\": \"11\", \"name\": \"昨天\"}, {\"id\": \"2\", \"name\": \"本周\"}, {\"id\": \"12\", \"name\": \"上周\"}, {\"id\": \"3\", \"name\": \"本月\"}, {\"id\": \"13\", \"name\": \"上月\"}, {\"id\": \"4\", \"name\": \"本季\"}, {\"id\": \"14\", \"name\": \"上季\"}, {\"id\": \"99\", \"name\": \"自定义\"}], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\"}, \"displayName\": \"录入时间\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"update_time\", \"mapping\": \"date_time_to_text\", \"jdbcType\": \"datetime\"}, \"name\": \"updateTime\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"updateTime\", \"type\": \"timeParams\", \"value\": [{\"id\": \"1\", \"name\": \"今天\"}, {\"id\": \"11\", \"name\": \"昨天\"}, {\"id\": \"2\", \"name\": \"本周\"}, {\"id\": \"12\", \"name\": \"上周\"}, {\"id\": \"3\", \"name\": \"本月\"}, {\"id\": \"13\", \"name\": \"上月\"}, {\"id\": \"4\", \"name\": \"本季\"}, {\"id\": \"14\", \"name\": \"上季\"}, {\"id\": \"99\", \"name\": \"自定义\"}], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\"}, \"displayName\": \"更新时间\"}, \"schema\": {\"type\": \"string\", \"title\": \"更新时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true, \"sortDefault\": \"descending\"}}}], \"selectable\": true, \"searchFields\": [{\"key\": \"idNumber\", \"name\": \"身份证号\"}, {\"key\": \"name\", \"name\": \"姓名\"}]}', 0, '{}', NULL, NULL, 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (2, '基本信息', 'basicInfo', 'person', NULL, 1, 2, NULL, NULL, NULL, 1, NULL, NULL, NULL, 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (3, '人员信息', 'person', 'person', 2, 1, 3, '{\"name\": \"人员信息\", \"type\": \"TABLE_SCHEMA\", \"table\": \"t_profile_person\", \"fields\": [{\"db\": {\"table\": \"t_profile_person\", \"column\": \"id_number\", \"jdbcType\": \"string\"}, \"name\": \"id_number\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"证件号码\", \"copyable\": true}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"姓名\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"id_type\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"string\"}, \"dict\": {\"type\": \"id_type\"}, \"name\": \"id_type\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"证件类型\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"gender\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"string\"}, \"dict\": {\"type\": \"gender\"}, \"name\": \"gender\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"性别\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"former_name\", \"jdbcType\": \"string\"}, \"name\": \"former_name\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"曾用名\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"nick_name\", \"jdbcType\": \"string\"}, \"name\": \"nick_name\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"绰号\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"nation\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"nation\"}, \"name\": \"nation\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"民族\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"political_status\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"profile_political_status\"}, \"name\": \"political_status\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"政治面貌\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"religious_belief\", \"jdbcType\": \"string\"}, \"name\": \"religious_belief\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"宗教信仰\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"martial_status\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"profile_martial_status\"}, \"name\": \"martial_status\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"婚姻状况\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"current_job\", \"jdbcType\": \"string\"}, \"name\": \"current_job\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"现职业\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"current_position\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"profile_current_position\"}, \"name\": \"current_position\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"目前所在地\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"person_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"json_id_array\"}, \"name\": \"person_label\", \"tableSchema\": {\"span\": 1, \"type\": \"label\", \"title\": \"人员标签\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"registered_residence\", \"mapping\": \"district_code_to_name\", \"jdbcType\": \"string\"}, \"name\": \"registered_residence\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"户籍地\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"registered_residence_detail\", \"jdbcType\": \"string\"}, \"name\": \"registered_residence_detail\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"户籍地详细地址\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"current_residence\", \"mapping\": \"district_code_to_name\", \"jdbcType\": \"string\"}, \"name\": \"current_residence\", \"tree\": {\"root\": \"510500\", \"type\": \"district\"}, \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"现住址\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"current_residence_detail\", \"jdbcType\": \"string\"}, \"name\": \"current_residence_detail\", \"tableSchema\": {\"span\": 4, \"type\": \"string\", \"title\": \"现住址详细地址\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"main_demand\", \"jdbcType\": \"string\"}, \"name\": \"main_demand\", \"tableSchema\": {\"span\": 4, \"type\": \"string\", \"title\": \"主要诉求\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"work_measures\", \"jdbcType\": \"string\"}, \"name\": \"work_measures\", \"tableSchema\": {\"span\": 4, \"type\": \"string\", \"title\": \"工作措施\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"petition_info\", \"jdbcType\": \"string\"}, \"name\": \"petition_info\", \"tableSchema\": {\"span\": 4, \"type\": \"string\", \"title\": \"进京赴省上访情况\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"punish_info\", \"jdbcType\": \"string\"}, \"name\": \"punish_info\", \"tableSchema\": {\"span\": 4, \"type\": \"string\", \"title\": \"被打击处理情况\", \"copyable\": false}}], \"moduleUi\": {\"column\": 4, \"bordered\": true}}', '{\"name\": \"人员信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_person\", \"fields\": [{\"db\": {\"table\": \"t_profile_person\", \"column\": \"id_type\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"id_type\"}, \"name\": \"idType\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"证件类型\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"photo\", \"jdbcType\": \"json_image_array\"}, \"name\": \"photo\", \"formSchema\": {\"ui\": {\"ui:options\": {\"style\": {\"position\": \"absolute\"}, \"width\": \"0.5\", \"action\": \"/upload/imgs\", \"widget\": \"upload\", \"isShowTitle\": false, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"array\", \"items\": {\"type\": \"object\"}, \"title\": \"照片\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"id_number\", \"jdbcType\": \"string\"}, \"name\": \"idNumber\", \"formSchema\": {\"ui\": {\"ui:options\": {\"style\": {\"display\": \"block\"}, \"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"证件号码\", \"pattern\": \"^([1-6][1-9]|50)\\\\d{4}(18|19|20)\\\\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\\\\d{3}[0-9Xx]$\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"formSchema\": {\"ui\": {\"ui:options\": {\"style\": {\"display\": \"block\"}, \"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"姓名\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"current_position\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"profile_current_position\"}, \"name\": \"nowLocation\", \"formSchema\": {\"ui\": {\"ui:options\": {\"style\": {\"display\": \"block\"}, \"width\": \"0.5\", \"widget\": \"radio\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"目前所在地\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"person_label\", \"jdbcType\": \"json_id_array\"}, \"name\": \"personLabel\", \"tree\": {\"root\": \"person\", \"type\": \"label\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"style\": {\"display\": \"block\"}, \"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": true, \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"array\", \"items\": {\"type\": \"array\"}, \"title\": \"人员标签\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"registered_residence\", \"jdbcType\": \"string\"}, \"name\": \"registerArea\", \"tree\": {\"root\": \"510500\", \"type\": \"district\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"name\", \"value\": \"code\", \"children\": \"children\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"户籍地\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"registered_residence_detail\", \"jdbcType\": \"string\"}, \"name\": \"registerAreaInfo\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"户籍地详细地址\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"current_residence\", \"jdbcType\": \"string\"}, \"name\": \"address\", \"tree\": {\"root\": \"510500\", \"type\": \"district\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"name\", \"value\": \"code\", \"children\": \"children\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"现住址\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"current_residence_detail\", \"jdbcType\": \"string\"}, \"name\": \"addressInfo\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"现住址详细地址\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"main_demand\", \"jdbcType\": \"string\"}, \"name\": \"mainDemand\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"textarea\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"主要诉求\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"work_measures\", \"jdbcType\": \"string\"}, \"name\": \"measure\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"textarea\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"工作措施\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"petition_info\", \"jdbcType\": \"string\"}, \"name\": \"petition\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"textarea\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"进京赴省上访情况\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"punish_info\", \"jdbcType\": \"string\"}, \"name\": \"handling\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"textarea\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"被打击处理情况\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"gender\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"gender\"}, \"name\": \"gender\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"radio\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"性别\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"former_name\", \"jdbcType\": \"string\"}, \"name\": \"usedName\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"曾用名\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"nick_name\", \"jdbcType\": \"string\"}, \"name\": \"nickName\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"绰号\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"nation\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"nation\"}, \"name\": \"nation\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"民族\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"political_status\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"profile_political_status\"}, \"name\": \"politicalStatus\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"radio\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"政治面貌\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"religious_belief\", \"jdbcType\": \"string\"}, \"name\": \"religious\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"宗教信仰\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"martial_status\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"profile_martial_status\"}, \"name\": \"maritalStatus\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"radio\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"婚姻状态\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"current_job\", \"jdbcType\": \"string\"}, \"name\": \"currentJob\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"现职业\"}}}], \"required\": [\"idType\", \"idNumber\", \"name\", \"registerArea\", \"registerAreaInfo\"]}', '{}', 1, '{\"type\": \"PRIMARY_KEY\", \"table\": \"t_profile_person\", \"column\": \"id\"}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (4, '车辆信息', 'vehicle', 'person', 2, 1, 4, '{}', NULL, '{\"name\": \"车辆信息\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_vehicle\", \"fields\": [{\"db\": {\"table\": \"t_profile_vehicle\", \"column\": \"type\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"json_object_array\"}, \"dict\": {\"type\": \"profile_vehicle_type\"}, \"name\": \"type\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"select\", \"title\": \"类型\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_vehicle\", \"column\": \"car_number\", \"jdbcType\": \"string\"}, \"name\": \"carNumber\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"车牌号\"}, \"properties\": {\"copyable\": true, \"editable\": true, \"required\": false, \"sortable\": false, \"validate\": [{\"message\": \"格式错误\", \"pattern\": \"^([京津晋冀蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼渝川贵云藏陕甘青宁新][ABCDEFGHJKLMNPQRSTUVWXYZ][1-9DF][1-9ABCDEFGHJKLMNPQRSTUVWXYZ]\\\\d{3}[1-9DF]|[京津晋冀蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼渝川贵云藏陕甘青宁新][ABCDEFGHJKLMNPQRSTUVWXYZ][\\\\dABCDEFGHJKLNMxPQRSTUVWXYZ]{5})$\"}]}}}, {\"db\": {\"table\": \"t_profile_vehicle\", \"column\": \"owner\", \"jdbcType\": \"string\"}, \"name\": \"owner\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"车辆所有人\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_vehicle\", \"column\": \"source\", \"mapping\": \"source_to_name\", \"jdbcType\": \"string\"}, \"name\": \"source\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"来源\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"modifyDefaultValue\": \"{\\\"type\\\": 1}\", \"displayDefaultValue\": \"手动录入\"}}}], \"selectable\": false, \"searchFields\": []}', 1, '{\"type\": \"JSON_ID_ARRAY\", \"table\": \"t_profile_person\", \"column\": \"vehicle_ids\"}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (5, '虚拟身份', 'virtualIdentity', 'person', 2, 1, 5, NULL, NULL, '{\"name\": \"虚拟身份\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_virtual_identity\", \"fields\": [{\"db\": {\"table\": \"t_profile_virtual_identity\", \"column\": \"type\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"virtual_identity_type\"}, \"name\": \"type\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"select\", \"title\": \"类型\"}, \"properties\": {\"default\": 1, \"copyable\": false, \"editable\": true, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_virtual_identity\", \"column\": \"virtual_number\", \"jdbcType\": \"string\"}, \"name\": \"virtual_number\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"虚拟号码\"}, \"properties\": {\"copyable\": true, \"editable\": true, \"required\": false, \"sortable\": false, \"validate\": [{\"message\": \"手机号错误\", \"pattern\": \"/^1(3[0-9]|4[01456879]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])\\\\d{8}$/\", \"conditions\": \"{type}=4\"}, {\"message\": \"qq号错误\", \"pattern\": \"[1-9][0-9]{4,14}\", \"conditions\": \"{type}=5\"}, {\"message\": \"MAC地址错误\", \"pattern\": \"/^([0-9a-fA-F]{2})(([/\\\\s:-][0-9a-fA-F]{2}){5})$/\", \"conditions\": \"{type}=1\"}, {\"message\": \"IMEI地址错误\", \"pattern\": \"/^[\\\\d]{15}(?:[\\\\d]{2})?$/\", \"conditions\": \"{type}=3\"}, {\"message\": \"IMSI地址错误\", \"pattern\": \" /^[\\\\d]{15}(?:[\\\\d]{2})?$/\", \"conditions\": \"{type}=2\"}]}}}, {\"db\": {\"table\": \"t_profile_virtual_identity\", \"column\": \"source\", \"mapping\": \"source_to_name\", \"jdbcType\": \"string\"}, \"name\": \"source\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"来源\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"modifyDefaultValue\": \"{\\\"type\\\": 1}\", \"displayDefaultValue\": \"手动录入\"}}}], \"selectable\": false, \"searchFields\": []}', 1, '{\"type\": \"JSON_ID_ARRAY\", \"table\": \"t_profile_person\", \"column\": \"virtual_identity_ids\"}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (6, '管控信息', 'control', 'person', NULL, 1, 6, NULL, NULL, NULL, 1, NULL, NULL, NULL, 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (7, '政府管控信息', 'govControl', 'person', 6, 1, 7, '{\"name\": \"政府管控信息\", \"table\": \"t_profile_person_government_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government\", \"jdbcType\": \"string\"}, \"name\": \"control_government\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"党政责任部门\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government_person\", \"jdbcType\": \"string\"}, \"name\": \"control_government_person\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"党政责任人\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government_contact\", \"jdbcType\": \"string\"}, \"name\": \"control_government_contact\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"联系方式\", \"copyable\": true}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community\", \"jdbcType\": \"string\"}, \"name\": \"control_community\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任街道社区\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community_person\", \"jdbcType\": \"string\"}, \"name\": \"control_community_person\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"社区责任人\", \"config\": {\"fieldName\": \"control_community_person\", \"processType\": \"string\", \"processConfig\": null}, \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community_contact\", \"jdbcType\": \"string\"}, \"name\": \"control_community_contact\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"联系方式\", \"copyable\": true}}], \"moduleUi\": {\"column\": 3, \"bordered\": true}}', '{\"name\": \"政府管控信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_person_government_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government\", \"jdbcType\": \"string\"}, \"name\": \"controlGovernment\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"党政责任部门\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government_person\", \"jdbcType\": \"string\"}, \"name\": \"controlGovernmentPerson\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"党政责任人\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government_contact\", \"jdbcType\": \"string\"}, \"name\": \"controlGovernmentContact\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"党政责任人联系方式\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community\", \"jdbcType\": \"string\"}, \"name\": \"controlCommunity\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"责任街道社区\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community_person\", \"jdbcType\": \"string\"}, \"name\": \"controlCommunityPerson\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"社区责任人\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community_contact\", \"jdbcType\": \"string\"}, \"name\": \"controlCommunityContact\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"社区责任人联系方式\"}}}], \"required\": []}', NULL, 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_person_government_control\", \"column\": \"person_id\"}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (8, '公安管控信息', 'policeControl', 'person', 6, 1, 8, '{\"name\": \"公安管控信息\", \"type\": \"TABLE_SCHEMA\", \"table\": \"t_profile_person_police_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_bureau\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"control_bureau\", \"tree\": {\"type\": \"dept\"}, \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任分局\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_bureau_leader\", \"mapping\": \"user_id_array_to_single_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_bureau_leader\", \"tableSchema\": {\"span\": 1, \"type\": \"user\", \"title\": \"责任领导\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_police\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"control_police\", \"tree\": {\"type\": \"dept\"}, \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任警种\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_police_leader\", \"mapping\": \"user_id_array_to_single_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_police_leader\", \"tableSchema\": {\"span\": 1, \"type\": \"user\", \"title\": \"责任领导\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_station\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"control_station\", \"tree\": {\"type\": \"dept\"}, \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任派出所\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_station_leader\", \"mapping\": \"user_id_array_to_single_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_station_leader\", \"tableSchema\": {\"span\": 1, \"type\": \"user\", \"title\": \"责任领导\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_person\", \"mapping\": \"user_id_array_to_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_person\", \"tableSchema\": {\"span\": 2, \"type\": \"multiUser\", \"title\": \"责任民警\", \"copyable\": false}}], \"moduleUi\": {\"column\": 2, \"bordered\": true}}', '{\"name\": \"公安管控信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_person_police_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_bureau\", \"jdbcType\": \"string\"}, \"name\": \"controlBureau\", \"tree\": {\"root\": \"510500000000\", \"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"责任分局\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_bureau_leader\", \"jdbcType\": \"number\"}, \"name\": \"controlBureauLeader\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlBureau\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"责任分局领导\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_police\", \"jdbcType\": \"string\"}, \"name\": \"controlPolice\", \"tree\": {\"root\": \"510500000000\", \"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"责任警种\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_police_leader\", \"jdbcType\": \"number\"}, \"name\": \"controlPoliceLeader\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlPolice\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"责任警种领导\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_station\", \"jdbcType\": \"string\"}, \"name\": \"controlStation\", \"tree\": {\"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"责任派出所\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_station_leader\", \"jdbcType\": \"number\"}, \"name\": \"controlStationLeader\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlStation\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"责任派出所领导\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_person\", \"jdbcType\": \"json_id_array\"}, \"name\": \"controlPerson\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"1\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlStation\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"array\", \"items\": {\"type\": \"number\"}, \"title\": \"责任民警\", \"minItems\": 1}}}], \"required\": [\"controlPerson\", \"controlStationLeader\", \"controlStation\"]}', NULL, 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_person\", \"column\": \"person_id\"}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (9, '人员轨迹', 'track', 'person', NULL, 1, 20, NULL, NULL, NULL, 0, NULL, 'NO_SCHEMA', 'NO_SCHEMA', 0);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (10, '相关业务', 'relatedService', 'person', NULL, 1, 10, NULL, NULL, NULL, 0, NULL, NULL, NULL, 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (11, '相关预警', 'relatedWarning', 'person', 10, 1, 11, NULL, NULL, NULL, 0, NULL, 'NO_SCHEMA', NULL, 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (12, '相关布控', 'relatedMonitor', 'person', 10, 1, 12, NULL, NULL, NULL, 0, NULL, 'NO_SCHEMA', 'NO_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (13, '相关档案', 'relatedArchive', 'person', NULL, 1, 13, NULL, NULL, NULL, 1, NULL, '', NULL, 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (14, '相关群体', 'relatedGroup', 'person', 13, 1, 14, NULL, NULL, '{\"name\": \"相关群体\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_group\", \"fields\": [{\"db\": {\"table\": \"t_profile_group\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"群体名称\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_group\", \"column\": \"group_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"label_id_array\"}, \"name\": \"groupLabel\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"groupLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&group_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"群体标签\"}, \"schema\": {\"type\": \"array\", \"title\": \"群体标签\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2}}}, {\"db\": {\"table\": \"t_profile_person_group_relation\", \"column\": \"activity_level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\", \"databaseRelation\": {\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_group_relation\", \"joinTo\": {\"table\": \"t_profile_group\", \"column\": \"id\", \"joinColumn\": \"group_id\"}, \"joinFrom\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}}}, \"dict\": {\"type\": \"profile_activity_level\"}, \"name\": \"activityLevel\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"select\", \"title\": \"活跃程度\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_group\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"createDept\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"createDept\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}], \"selectable\": false, \"searchFields\": [{\"key\": \"name\", \"name\": \"群体名称\"}]}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_group_relation\", \"joinTo\": {\"table\": \"t_profile_group\", \"column\": \"id\", \"joinColumn\": \"group_id\"}, \"joinFrom\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (15, '相关事件', 'relatedEvent', 'person', 13, 1, 15, NULL, NULL, '{\"name\": \"事件档案\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_event\", \"fields\": [{\"db\": {\"table\": \"t_profile_event\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"事件名称\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"create_dept_id\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"create_dept_id\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"create_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"datetime\"}, \"name\": \"create_time\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}], \"selectable\": false, \"searchFields\": [{\"key\": \"name\", \"name\": \"事件名称\"}]}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_event_relation\", \"joinTo\": {\"table\": \"t_profile_event\", \"column\": \"id\", \"joinColumn\": \"event_id\"}, \"joinFrom\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (16, '相关线索', 'relatedClue', 'person', 13, 1, 16, NULL, NULL, '{\"name\": \"相关线索\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_clue\", \"fields\": [{\"db\": {\"table\": \"t_profile_clue\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"线索名称\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"clue_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"label_id_array\"}, \"name\": \"clueLabel\", \"listSchema\": {\"style\": {\"align\": \"left\"}, \"filter\": {\"key\": \"clueLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&clue_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"线索标签\"}, \"schema\": {\"type\": \"array\", \"title\": \"线索标签\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"source\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_source\"}, \"name\": \"source\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"线索来源\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"disposal_status\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_disposal_status\"}, \"name\": \"disposal_status\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"处置状态\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"emergency_level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_emergency_level\"}, \"name\": \"emergency_level\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"紧急程度\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"create_time\", \"mapping\": \"date_time_to_text\", \"jdbcType\": \"datetime\"}, \"name\": \"createTime\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true}}}], \"selectable\": false, \"searchFields\": [{\"key\": \"name\", \"name\": \"线索名称\"}]}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_clue_relation\", \"joinTo\": {\"table\": \"t_profile_clue\", \"column\": \"id\", \"joinColumn\": \"clue_id\"}, \"joinFrom\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (17, '背景关系', 'background', 'person', NULL, 1, 17, NULL, NULL, NULL, 1, NULL, NULL, NULL, 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (18, '家庭关系', 'family', 'person', 17, 1, 18, NULL, NULL, '{\"name\": \"家庭关系\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_family_relation\", \"fields\": [{\"db\": {\"table\": \"t_profile_family_relation\", \"column\": \"relation\", \"jdbcType\": \"string\"}, \"name\": \"relation\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"关系\"}, \"properties\": {\"copyable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_family_relation\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"姓名\"}, \"properties\": {\"copyable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_family_relation\", \"column\": \"id_number\", \"jdbcType\": \"string\"}, \"name\": \"id_number\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"身份证号\"}, \"properties\": {\"copyable\": true, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_family_relation\", \"column\": \"current_location\", \"jdbcType\": \"string\"}, \"name\": \"current_location\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"现住址\"}, \"properties\": {\"copyable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_family_relation\", \"column\": \"phone_number\", \"jdbcType\": \"string\"}, \"name\": \"phone_number\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"联系方式\"}, \"properties\": {\"copyable\": false, \"required\": false, \"sortable\": false}}}], \"selectable\": false, \"searchFields\": []}', 1, '{\"type\": \"JSON_ID_ARRAY\", \"table\": \"t_profile_person\", \"column\": \"family_relation_ids\"}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (19, '社会关系', 'society', 'person', 17, 1, 19, NULL, NULL, '{\"name\": \"社会关系\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_social_relation\", \"fields\": [{\"db\": {\"table\": \"t_profile_social_relation\", \"column\": \"relation\", \"jdbcType\": \"string\"}, \"name\": \"relation\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"关系\"}, \"properties\": {\"copyable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_social_relation\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"姓名\"}, \"properties\": {\"copyable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_social_relation\", \"column\": \"id_number\", \"jdbcType\": \"string\"}, \"name\": \"id_number\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"身份证号\"}, \"properties\": {\"copyable\": true, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_social_relation\", \"column\": \"current_location\", \"jdbcType\": \"string\"}, \"name\": \"current_location\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"现住址\"}, \"properties\": {\"copyable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_social_relation\", \"column\": \"phone_number\", \"jdbcType\": \"string\"}, \"name\": \"phone_number\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"联系方式\"}, \"properties\": {\"copyable\": false, \"required\": false, \"sortable\": false}}}], \"selectable\": false, \"searchFields\": []}', 1, '{\"type\": \"JSON_ID_ARRAY\", \"table\": \"t_profile_person\", \"column\": \"social_relation_ids\"}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (20, '相关常控', 'relatedRegular', 'person', 10, 1, 19, NULL, NULL, '{}', 0, '{}', 'NO_SCHEMA', 'NO_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (101, '档案管理', 'archive', 'group', NULL, 0, 1, '{}', '{}', '{\"name\": \"群体信息\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_group\", \"fields\": [{\"db\": {\"table\": \"t_profile_group\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 100, \"ellipsis\": true}, \"schema\": {\"type\": \"string\", \"title\": \"名称\"}, \"properties\": {\"href\": \"/ys-app/archives/group/details?id={value}\", \"isName\": true, \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_group\", \"column\": \"group_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"label_id_array\"}, \"name\": \"groupLabel\", \"listSchema\": {\"style\": {\"align\": \"left\", \"width\": 120, \"ellipsis\": true}, \"filter\": {\"key\": \"groupLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&group_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"群体类别\"}, \"schema\": {\"type\": \"array\", \"title\": \"群体类别\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_person_group_relation\", \"column\": \"count(1)\", \"jdbcType\": \"number\", \"databaseRelation\": {\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_person_group_relation\", \"column\": \"group_id\"}}, \"name\": \"personCount\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 100, \"ellipsis\": true}, \"schema\": {\"type\": \"string\", \"title\": \"关联人数\"}, \"properties\": {\"href\": \"/ys-app/archives/group/details?id={value}#relatedPerson\", \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_group\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"dept_id\"}, \"name\": \"createDept\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"filter\": {\"key\": \"createDept\", \"type\": \"multiple-tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"创建单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"创建单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_group\", \"column\": \"create_time\", \"mapping\": \"date_time_to_text\", \"jdbcType\": \"datetime\"}, \"name\": \"createTime\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"filter\": {\"key\": \"createTime\", \"type\": \"timeParams\", \"value\": [{\"id\": \"1\", \"name\": \"今天\"}, {\"id\": \"11\", \"name\": \"昨天\"}, {\"id\": \"2\", \"name\": \"本周\"}, {\"id\": \"12\", \"name\": \"上周\"}, {\"id\": \"3\", \"name\": \"本月\"}, {\"id\": \"13\", \"name\": \"上月\"}, {\"id\": \"4\", \"name\": \"本季\"}, {\"id\": \"14\", \"name\": \"上季\"}, {\"id\": \"99\", \"name\": \"自定义\"}], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\"}, \"displayName\": \"录入时间\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true}}}, {\"db\": {\"table\": \"t_profile_group\", \"column\": \"update_time\", \"mapping\": \"date_time_to_text\", \"jdbcType\": \"datetime\"}, \"name\": \"updateTime\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"filter\": {\"key\": \"updateTime\", \"type\": \"timeParams\", \"value\": [{\"id\": \"1\", \"name\": \"今天\"}, {\"id\": \"11\", \"name\": \"昨天\"}, {\"id\": \"2\", \"name\": \"本周\"}, {\"id\": \"12\", \"name\": \"上周\"}, {\"id\": \"3\", \"name\": \"本月\"}, {\"id\": \"13\", \"name\": \"上月\"}, {\"id\": \"4\", \"name\": \"本季\"}, {\"id\": \"14\", \"name\": \"上季\"}, {\"id\": \"99\", \"name\": \"自定义\"}], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\"}, \"displayName\": \"更新时间\"}, \"schema\": {\"type\": \"string\", \"title\": \"更新时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true, \"sortDefault\": \"descending\"}}}], \"selectable\": true, \"searchFields\": [{\"key\": \"name\", \"name\": \"群体名称\"}]}', 0, NULL, NULL, NULL, 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (102, '基本信息', 'basicInfo', 'group', NULL, 1, 2, NULL, NULL, NULL, 1, NULL, NULL, NULL, 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (103, '群体信息', 'group', 'group', 102, 1, 3, '{\"name\": \"群体信息\", \"type\": \"TABLE_SCHEMA\", \"table\": \"t_profile_group\", \"fields\": [{\"db\": {\"table\": \"t_profile_group\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"群体名称\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group\", \"column\": \"group_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"json_id_array\"}, \"name\": \"group_label\", \"tableSchema\": {\"span\": 1, \"type\": \"label\", \"title\": \"群体类别\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group\", \"column\": \"create_time\", \"mapping\": \"date_time_to_text\", \"jdbcType\": \"create_time\"}, \"name\": \"create_time\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"录入时间\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"create_dept_id\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"录入单位\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group\", \"column\": \"basic_info\", \"jdbcType\": \"string\"}, \"name\": \"basic_info\", \"tableSchema\": {\"span\": 4, \"type\": \"string\", \"title\": \"基本情况\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group\", \"column\": \"main_demand\", \"jdbcType\": \"string\"}, \"name\": \"main_demand\", \"tableSchema\": {\"span\": 4, \"type\": \"string\", \"title\": \"主要诉求\", \"copyable\": false}}], \"moduleUi\": {\"column\": 4, \"bordered\": true}}', '{\"name\": \"群体信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_group\", \"fields\": [{\"db\": {\"table\": \"t_profile_group\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"1\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"群体名称\"}}}, {\"db\": {\"table\": \"t_profile_group\", \"column\": \"group_label\", \"jdbcType\": \"json_id_array\"}, \"name\": \"groupLabel\", \"tree\": {\"root\": \"group\", \"type\": \"label\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"1\", \"widget\": \"cascader\", \"multiple\": true, \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"array\", \"items\": {\"type\": \"array\"}, \"title\": \"群体类别\"}}}, {\"db\": {\"table\": \"t_profile_group\", \"column\": \"basic_info\", \"jdbcType\": \"string\"}, \"name\": \"basic_info\", \"formSchema\": {\"ui\": {\"ui:options\": {\"style\": {\"display\": \"block\"}, \"width\": \"1\", \"widget\": \"textarea\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"基本情况\"}}}, {\"db\": {\"table\": \"t_profile_group\", \"column\": \"main_demand\", \"jdbcType\": \"string\"}, \"name\": \"main_demand\", \"formSchema\": {\"ui\": {\"ui:options\": {\"style\": {\"display\": \"block\"}, \"width\": \"1\", \"widget\": \"textarea\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"主要诉求\"}}}], \"required\": [\"name\"]}', NULL, 1, '{\"type\": \"PRIMARY_KEY\", \"table\": \"t_profile_group\", \"column\": \"id\"}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (104, '管控信息', 'control', 'group', NULL, 1, 4, NULL, NULL, NULL, 1, NULL, NULL, NULL, 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (105, '政府管控信息', 'govControl', 'group', 104, 1, 5, '{\"name\": \"政府管控信息\", \"table\": \"t_profile_group_government_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_government\", \"jdbcType\": \"string\"}, \"name\": \"control_government\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"党政责任部门\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_government_person\", \"jdbcType\": \"string\"}, \"name\": \"control_government_person\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"党政责任人\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_government_contact\", \"jdbcType\": \"string\"}, \"name\": \"control_government_contact\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"联系方式\", \"copyable\": true}}, {\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_community\", \"jdbcType\": \"string\"}, \"name\": \"control_community\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任街道社区\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_community_person\", \"jdbcType\": \"string\"}, \"name\": \"control_community_person\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"社区责任人\", \"config\": {\"fieldName\": \"control_community_person\", \"processType\": \"string\", \"processConfig\": null}, \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_community_contact\", \"jdbcType\": \"string\"}, \"name\": \"control_community_contact\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"联系方式\", \"copyable\": true}}], \"moduleUi\": {\"column\": 3, \"bordered\": true}}', '{\"name\": \"政府管控信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_group_government_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_government\", \"jdbcType\": \"string\"}, \"name\": \"controlGovernment\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"党政责任部门\"}}}, {\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_government_person\", \"jdbcType\": \"string\"}, \"name\": \"controlGovernmentPerson\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"党政责任人\"}}}, {\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_government_contact\", \"jdbcType\": \"string\"}, \"name\": \"controlGovernmentContact\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"联系方式\"}}}, {\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_community\", \"jdbcType\": \"string\"}, \"name\": \"controlCommunity\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"责任街道社区\"}}}, {\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_community_person\", \"jdbcType\": \"string\"}, \"name\": \"controlCommunityPerson\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"社区责任人\"}}}, {\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_community_contact\", \"jdbcType\": \"string\"}, \"name\": \"controlCommunityContact\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"联系方式\"}}}], \"required\": []}', NULL, 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_group_government_control\", \"column\": \"group_id\"}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (106, '公安管控信息', 'policeControl', 'group', 104, 1, 6, '{\"name\": \"公安管控信息\", \"type\": \"TABLE_SCHEMA\", \"table\": \"t_profile_group_police_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_bureau\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"control_bureau\", \"tree\": {\"type\": \"dept\"}, \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任分局\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_bureau_leader\", \"mapping\": \"user_id_array_to_single_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_bureau_leader\", \"tableSchema\": {\"span\": 1, \"type\": \"user\", \"title\": \"责任领导\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_police\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"control_police\", \"tree\": {\"type\": \"dept\"}, \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任警种\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_police_leader\", \"mapping\": \"user_id_array_to_single_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_police_leader\", \"tableSchema\": {\"span\": 1, \"type\": \"user\", \"title\": \"责任领导\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_station\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"control_station\", \"tree\": {\"type\": \"dept\"}, \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任派出所\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_station_leader\", \"mapping\": \"user_id_array_to_single_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_station_leader\", \"tableSchema\": {\"span\": 1, \"type\": \"user\", \"title\": \"责任领导\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_person\", \"mapping\": \"user_id_array_to_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_person\", \"tableSchema\": {\"span\": 2, \"type\": \"multiUser\", \"title\": \"责任民警\", \"copyable\": false}}], \"moduleUi\": {\"column\": 2, \"bordered\": true}}', '{\"name\": \"公安管控信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_group_police_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_bureau\", \"jdbcType\": \"string\"}, \"name\": \"controlBureau\", \"tree\": {\"root\": \"510500000000\", \"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"责任分局\"}}}, {\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_bureau_leader\", \"jdbcType\": \"number\"}, \"name\": \"controlBureauLeader\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlBureau\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"责任分局领导\"}}}, {\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_police\", \"jdbcType\": \"string\"}, \"name\": \"controlPolice\", \"tree\": {\"root\": \"510500000000\", \"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"责任警种\"}}}, {\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_police_leader\", \"jdbcType\": \"number\"}, \"name\": \"controlPoliceLeader\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlPolice\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"责任警种领导\"}}}, {\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_station\", \"jdbcType\": \"string\"}, \"name\": \"controlStation\", \"tree\": {\"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"责任派出所\"}}}, {\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_station_leader\", \"jdbcType\": \"number\"}, \"name\": \"controlStationLeader\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlStation\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"责任派出所领导\"}}}, {\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_person\", \"jdbcType\": \"json_id_array\"}, \"name\": \"controlPerson\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"1\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlStation\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"array\", \"items\": {\"type\": \"number\"}, \"title\": \"责任民警\", \"minItems\": 1}}}], \"required\": [\"controlPerson\", \"controlStationLeader\", \"controlStation\"]}', NULL, 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_group_control\", \"column\": \"group_id\"}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (107, '敏感时间节点', 'sensitiveTime', 'group', 110, 1, 8, NULL, NULL, '{\"name\": \"敏感时间节点\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_sensitive_time\", \"fields\": [{\"db\": {\"table\": \"t_profile_sensitive_time\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"节点名称\"}, \"properties\": {\"copyable\": true, \"editable\": true, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_sensitive_time\", \"column\": \"start_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"timestamp\"}, \"name\": \"start_time\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"datetime\", \"title\": \"起始时间\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_sensitive_time\", \"column\": \"end_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"timestamp\"}, \"name\": \"end_time\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"datetime\", \"title\": \"结束时间\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_sensitive_time\", \"column\": \"remark\", \"jdbcType\": \"string\"}, \"name\": \"remark\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"备注\"}, \"properties\": {\"copyable\": true, \"editable\": true, \"required\": false, \"sortable\": false}}}], \"selectable\": false, \"searchFields\": []}', 1, '{\"type\": \"JSON_ID_ARRAY\", \"table\": \"t_profile_group\", \"column\": \"sensitive_time_ids\"}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (108, '相关档案', 'relatedArchive', 'group', NULL, 1, 9, NULL, NULL, NULL, 1, NULL, NULL, NULL, 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (109, '相关人员', 'relatedPerson', 'group', 108, 1, 10, NULL, NULL, '{\"name\": \"相关人员\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_person\", \"fields\": [{\"db\": {\"table\": \"t_profile_person\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"人员名称\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"person_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"label_id_array\"}, \"name\": \"personLabel\", \"listSchema\": {\"style\": {\"align\": \"left\"}, \"filter\": {\"key\": \"personLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&person_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"人员标签\"}, \"schema\": {\"type\": \"array\", \"title\": \"人员标签\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2}}}, {\"db\": {\"table\": \"t_profile_person_group_relation\", \"column\": \"activity_level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\", \"databaseRelation\": {\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_group_relation\", \"joinTo\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}, \"joinFrom\": {\"table\": \"t_profile_group\", \"column\": \"id\", \"joinColumn\": \"group_id\"}}}, \"dict\": {\"type\": \"profile_activity_level\"}, \"name\": \"activityLevel\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"select\", \"title\": \"活跃程度\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"createDept\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"createDept\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}], \"selectable\": false, \"searchFields\": [{\"key\": \"name\", \"name\": \"人员名称\"}]}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_group_relation\", \"joinTo\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}, \"joinFrom\": {\"table\": \"t_profile_group\", \"column\": \"id\", \"joinColumn\": \"group_id\"}}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (110, '时间节点', 'time', 'group', NULL, 1, 7, NULL, NULL, '{\"name\": \"敏感时间节点\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_sensitive_time\", \"fields\": [{\"db\": {\"table\": \"t_profile_sensitive_time\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"节点名称\"}, \"properties\": {\"copyable\": true, \"editable\": true, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_sensitive_time\", \"column\": \"start_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"timestamp\"}, \"name\": \"start_time\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"datetime\", \"title\": \"起始时间\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_sensitive_time\", \"column\": \"end_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"timestamp\"}, \"name\": \"end_time\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"datetime\", \"title\": \"结束时间\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_sensitive_time\", \"column\": \"remark\", \"jdbcType\": \"string\"}, \"name\": \"remark\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"备注\"}, \"properties\": {\"copyable\": true, \"editable\": true, \"required\": false, \"sortable\": false}}}], \"selectable\": false, \"searchFields\": []}', 1, '{\"type\": \"JSON_ID_ARRAY\", \"table\": \"t_profile_group\", \"column\": \"sensitive_time_ids\"}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (111, '相关线索', 'relatedClue', 'group', 108, 1, 11, NULL, NULL, '{\"name\": \"相关线索\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_clue\", \"fields\": [{\"db\": {\"table\": \"t_profile_clue\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"线索名称\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"clue_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"label_id_array\"}, \"name\": \"clueLabel\", \"listSchema\": {\"style\": {\"align\": \"left\"}, \"filter\": {\"key\": \"clueLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&clue_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"线索标签\"}, \"schema\": {\"type\": \"array\", \"title\": \"线索标签\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"source\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_source\"}, \"name\": \"source\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"线索来源\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"disposal_status\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_disposal_status\"}, \"name\": \"disposal_status\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"处置状态\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"emergency_level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_emergency_level\"}, \"name\": \"emergency_level\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"紧急程度\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"create_time\", \"mapping\": \"date_time_to_text\", \"jdbcType\": \"datetime\"}, \"name\": \"createTime\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true}}}], \"selectable\": false, \"searchFields\": [{\"key\": \"name\", \"name\": \"线索名称\"}]}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_group_clue_relation\", \"joinTo\": {\"table\": \"t_profile_clue\", \"column\": \"id\", \"joinColumn\": \"clue_id\"}, \"joinFrom\": {\"table\": \"t_profile_group\", \"column\": \"id\", \"joinColumn\": \"group_id\"}}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (112, '群体材料', 'groupFiles', 'group', 108, 1, 12, NULL, NULL, NULL, 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_group_file_relation\", \"column\": \"id\", \"joinTo\": {\"table\": \"t_file_info\", \"column\": \"id\", \"joinColumn\": \"file_id\"}, \"joinFrom\": {\"table\": \"t_profile_group\", \"column\": \"id\", \"joinColumn\": \"group_id\"}}', 'FILE_SCHEMA', 'FILE_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (201, '档案管理', 'archive', 'event', NULL, 0, 1, NULL, NULL, '{\"name\": \"事件档案\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_event\", \"fields\": [{\"db\": {\"table\": \"t_profile_event\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"事件名称\"}, \"properties\": {\"href\": \"/ys-app/archives/event/details?id={value}\", \"isName\": true, \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"event_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"label_id_array\"}, \"name\": \"eventLabel\", \"listSchema\": {\"style\": {\"align\": \"left\"}, \"filter\": {\"key\": \"eventLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&event_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"事件类别\"}, \"schema\": {\"type\": \"array\", \"title\": \"事件类别\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_event_level\"}, \"name\": \"level\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"level\", \"type\": \"select\", \"value\": [\"%%profile_event_level%%\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"code\"}, \"displayName\": \"事件级别\"}, \"schema\": {\"type\": \"string\", \"title\": \"事件级别\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"related_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"datetime\"}, \"name\": \"relatedTime\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"schema\": {\"type\": \"string\", \"title\": \"指向时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"related_address\", \"mapping\": \"map_location_string\", \"jdbcType\": \"string\"}, \"name\": \"relatedAddress\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"指向地址\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"source\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_event_source\"}, \"name\": \"source\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"事件来源\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"status\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_event_status\"}, \"name\": \"status\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"事件状态\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"create_dept_id\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"filter\": {\"key\": \"create_dept_id\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"create_time\", \"mapping\": \"date_time_to_text\", \"jdbcType\": \"datetime\"}, \"name\": \"createTime\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true, \"sortDefault\": \"descending\"}}}], \"selectable\": true, \"searchFields\": [{\"key\": \"name\", \"name\": \"事件名称\"}, {\"key\": \"relatedAddress\", \"name\": \"事发地点\"}]}', 0, '{}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (202, '基本信息', 'basicInfo', 'event', NULL, 1, 2, '{}', '{}', '{}', 1, '{}', '', '', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (203, '涉及人员', 'relatedPerson', 'event', 207, 1, 5, NULL, NULL, '{\"name\": \"相关人员\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_person\", \"fields\": [{\"db\": {\"table\": \"t_profile_person\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"人员名称\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"person_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"label_id_array\"}, \"name\": \"personLabel\", \"listSchema\": {\"style\": {\"align\": \"left\"}, \"filter\": {\"key\": \"personLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&person_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"人员标签\"}, \"schema\": {\"type\": \"array\", \"title\": \"人员标签\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"createDept\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"createDept\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}], \"selectable\": false, \"searchFields\": [{\"key\": \"name\", \"name\": \"人员名称\"}]}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_event_relation\", \"joinTo\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}, \"joinFrom\": {\"table\": \"t_profile_event\", \"column\": \"id\", \"joinColumn\": \"event_id\"}}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (204, '涉及群体', 'relatedGroup', 'event', 207, 1, 6, NULL, NULL, '{\"name\": \"相关群体\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_group\", \"fields\": [{\"db\": {\"table\": \"t_profile_group\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"群体名称\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_group\", \"column\": \"group_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"label_id_array\"}, \"name\": \"groupLabel\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"groupLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&group_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"群体标签\"}, \"schema\": {\"type\": \"array\", \"title\": \"群体标签\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2}}}, {\"db\": {\"table\": \"t_profile_group\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"createDept\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"createDept\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}], \"selectable\": false, \"searchFields\": [{\"key\": \"name\", \"name\": \"群体名称\"}]}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_group_event_relation\", \"joinTo\": {\"table\": \"t_profile_group\", \"column\": \"id\", \"joinColumn\": \"group_id\"}, \"joinFrom\": {\"table\": \"t_profile_event\", \"column\": \"id\", \"joinColumn\": \"event_id\"}}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (205, '事件材料', 'eventFiles', 'event', 207, 1, 7, NULL, NULL, '{}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_event_file_relation\", \"column\": \"id\", \"joinTo\": {\"table\": \"t_file_info\", \"column\": \"id\", \"joinColumn\": \"file_id\"}, \"joinFrom\": {\"table\": \"t_profile_event\", \"column\": \"id\", \"joinColumn\": \"event_id\"}}', 'FILE_SCHEMA', 'FILE_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (206, '相关线索', 'relatedClue', 'event', 2070, 1, 8, NULL, NULL, '{\"name\": \"相关线索\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_clue\", \"fields\": [{\"db\": {\"table\": \"t_profile_clue\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"线索名称\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"clue_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"label_id_array\"}, \"name\": \"clueLabel\", \"listSchema\": {\"style\": {\"align\": \"left\"}, \"filter\": {\"key\": \"clueLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&clue_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"线索标签\"}, \"schema\": {\"type\": \"array\", \"title\": \"线索标签\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"source\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_source\"}, \"name\": \"source\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"线索来源\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"disposal_status\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_disposal_status\"}, \"name\": \"disposal_status\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"处置状态\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"emergency_level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_emergency_level\"}, \"name\": \"emergency_level\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"紧急程度\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"create_time\", \"mapping\": \"date_time_to_text\", \"jdbcType\": \"datetime\"}, \"name\": \"createTime\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true}}}], \"selectable\": false, \"searchFields\": [{\"key\": \"name\", \"name\": \"线索名称\"}]}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_event_clue_relation\", \"joinTo\": {\"table\": \"t_profile_clue\", \"column\": \"id\", \"joinColumn\": \"clue_id\"}, \"joinFrom\": {\"table\": \"t_profile_event\", \"column\": \"id\", \"joinColumn\": \"event_id\"}}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (207, '相关档案', 'relatedArchive', 'event', NULL, 1, 4, NULL, NULL, '{}', 1, '{}', '', '', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (208, '事件信息', 'eventInfo', 'event', 202, 1, 3, '{\"name\": \"事件信息\", \"type\": \"TABLE_SCHEMA\", \"table\": \"t_profile_event\", \"fields\": [{\"db\": {\"table\": \"t_profile_event\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"tableSchema\": {\"span\": 2, \"type\": \"string\", \"title\": \"事件名称\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"event_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"json_id_array\"}, \"name\": \"event_label\", \"tableSchema\": {\"span\": 1, \"type\": \"label\", \"title\": \"事件类别\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"source\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_event_source\"}, \"name\": \"source\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"事件来源\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"related_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"timestamp\"}, \"name\": \"related_time\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"指向时间\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"related_address\", \"mapping\": \"map_location\", \"jdbcType\": \"string\"}, \"name\": \"related_address\", \"tableSchema\": {\"span\": 2, \"type\": \"mapLocation\", \"title\": \"指向地址\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_event_level\"}, \"name\": \"level\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"事件级别\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"belong_location\", \"mapping\": \"district_code_to_name\", \"jdbcType\": \"string\"}, \"name\": \"belong_location\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"归属地\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"person_estimation\", \"jdbcType\": \"number\"}, \"name\": \"person_estimation\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"估计人数\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"risk_level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_event_risk_level\"}, \"name\": \"risk_level\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"风险等级\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"control_station\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"control_station\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"主管单位\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"status\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_event_status\"}, \"name\": \"status\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"事件状态\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"detail\", \"jdbcType\": \"string\"}, \"name\": \"detail\", \"tableSchema\": {\"span\": 2, \"type\": \"string\", \"title\": \"事件详情\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"disposal_result\", \"jdbcType\": \"string\"}, \"name\": \"disposal_result\", \"tableSchema\": {\"span\": 2, \"type\": \"string\", \"title\": \"处置结果\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"description\", \"jdbcType\": \"string\"}, \"name\": \"description\", \"tableSchema\": {\"span\": 2, \"type\": \"string\", \"title\": \"简要说明\", \"copyable\": false}}], \"moduleUi\": {\"column\": 2, \"bordered\": true}}', '{\"name\": \"事件信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_event\", \"fields\": [{\"db\": {\"table\": \"t_profile_event\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"1\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"事件名称\"}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"event_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"json_id_array\"}, \"name\": \"event_label\", \"tree\": {\"root\": \"event\", \"type\": \"label\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": true, \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"array\", \"items\": {\"type\": \"array\"}, \"title\": \"事件类别\", \"minItems\": 1}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"source\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_event_source\"}, \"name\": \"source\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"事件来源\"}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"related_time\", \"mapping\": \"date_time_to_timestamp\", \"jdbcType\": \"timestamp\"}, \"name\": \"related_time\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"datePicker\", \"showTime\": true, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"指向时间\"}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_event_level\"}, \"name\": \"level\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"radio\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"事件级别\"}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"related_address\", \"jdbcType\": \"map\"}, \"name\": \"related_address\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"1\", \"widget\": \"map\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"指向地址\"}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"belong_location\", \"mapping\": \"district_code_to_name\", \"jdbcType\": \"string\"}, \"name\": \"belong_location\", \"tree\": {\"root\": \"510500\", \"type\": \"district\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"name\", \"value\": \"code\", \"children\": \"children\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"归属地\"}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"person_estimation\", \"jdbcType\": \"number\"}, \"name\": \"person_estimation\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"估计人数\"}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"risk_level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_event_risk_level\"}, \"name\": \"risk_level\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"radio\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"风险等级\"}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"control_station\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"control_station\", \"tree\": {\"root\": \"510500000000\", \"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"主管单位\"}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"detail\", \"jdbcType\": \"string\"}, \"name\": \"detail\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"1\", \"widget\": \"textarea\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"事件详情\"}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"disposal_result\", \"jdbcType\": \"string\"}, \"name\": \"disposal_result\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"1\", \"widget\": \"textarea\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"处置结果\"}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"description\", \"jdbcType\": \"string\"}, \"name\": \"description\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"1\", \"widget\": \"textarea\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"简要说明\"}}}], \"required\": [\"name\", \"event_label\", \"source\", \"related_time\"]}', '{}', 1, '{\"type\": \"PRIMARY_KEY\", \"table\": \"t_profile_event\", \"column\": \"id\"}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (301, '档案管理', 'archive', 'clue', NULL, 0, 1, NULL, NULL, '{\"name\": \"相关线索\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_clue\", \"fields\": [{\"db\": {\"table\": \"t_profile_clue\", \"column\": \"code\", \"jdbcType\": \"string\"}, \"name\": \"code\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"线索编号\"}, \"properties\": {\"href\": \"/ys-app/archives/clue/details?id={value}\", \"isName\": true, \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"source\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_source_type\"}, \"name\": \"source\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"source\", \"type\": \"select\", \"value\": [\"%%profile_clue_source_type%%\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"code\", \"children\": \"children\"}, \"displayName\": \"线索来源\"}, \"schema\": {\"type\": \"string\", \"title\": \"线索来源\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"detail\", \"jdbcType\": \"string\"}, \"name\": \"detail\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"线索内容\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_risk_clue_relation\", \"column\": \"count(1)\", \"jdbcType\": \"string\", \"databaseRelation\": {\"type\": \"FOREIGN_KEY\", \"table\": \"t_risk_clue_relation\", \"column\": \"risk_id\", \"joinFrom\": {\"table\": \"t_profile_risk\", \"joinColumn\": \"id\"}}}, \"name\": \"riskCount\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": true}, \"schema\": {\"type\": \"string\", \"title\": \"关联风险\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"tags\", \"jdbcType\": \"string\"}, \"name\": \"tags\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"智能打标\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"target_location\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_location\"}, \"name\": \"target_location\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"target_location\", \"type\": \"select\", \"value\": [\"%%profile_clue_location%%\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"code\", \"children\": \"children\"}, \"displayName\": \"指向地点\"}, \"schema\": {\"type\": \"string\", \"title\": \"指向地点\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"action_type\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_action_type\"}, \"name\": \"action_type\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"行为方式\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"group_type\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_group_type\"}, \"name\": \"group_type\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"群体类型\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"report_dept\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"report_dept\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"filter\": {\"key\": \"report_dept\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"上报单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"上报单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"report_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"datetime\"}, \"name\": \"report_time\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"filter\": {\"key\": \"report_time\", \"type\": \"timeParams\", \"value\": [{\"id\": \"1\", \"name\": \"今天\"}, {\"id\": \"11\", \"name\": \"昨天\"}, {\"id\": \"2\", \"name\": \"本周\"}, {\"id\": \"12\", \"name\": \"上周\"}, {\"id\": \"3\", \"name\": \"本月\"}, {\"id\": \"13\", \"name\": \"上月\"}, {\"id\": \"4\", \"name\": \"本季\"}, {\"id\": \"14\", \"name\": \"上季\"}, {\"id\": \"99\", \"name\": \"自定义\"}], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\"}, \"displayName\": \"上报时间\"}, \"schema\": {\"type\": \"string\", \"title\": \"上报时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true, \"sortDefault\": \"descending\"}}}], \"selectable\": true, \"searchFields\": [{\"key\": \"detail\", \"name\": \"线索内容\"}, {\"key\": \"code\", \"name\": \"线索编号\"}]}', 0, '{}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (302, '基本信息', 'basicInfo', 'clue', NULL, 1, 2, '{}', '{}', '{}', 1, '{}', '', '', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (303, '涉及人员', 'relatedPerson', 'clue', 309, 1, 3, NULL, NULL, '{\"name\": \"相关人员\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_person\", \"fields\": [{\"db\": {\"table\": \"t_profile_person\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"人员名称\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"person_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"label_id_array\"}, \"name\": \"personLabel\", \"listSchema\": {\"style\": {\"align\": \"left\"}, \"filter\": {\"key\": \"personLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&person_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"人员标签\"}, \"schema\": {\"type\": \"array\", \"title\": \"人员标签\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"createDept\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"createDept\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}], \"selectable\": false, \"searchFields\": [{\"key\": \"name\", \"name\": \"人员名称\"}]}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_clue_relation\", \"joinTo\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}, \"joinFrom\": {\"table\": \"t_profile_clue\", \"column\": \"id\", \"joinColumn\": \"clue_id\"}}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (304, '涉及群体', 'relatedGroup', 'clue', 309, 1, 4, NULL, NULL, '{\"name\": \"相关群体\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_group\", \"fields\": [{\"db\": {\"table\": \"t_profile_group\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"群体名称\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_group\", \"column\": \"group_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"label_id_array\"}, \"name\": \"groupLabel\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"groupLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&group_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"群体标签\"}, \"schema\": {\"type\": \"array\", \"title\": \"群体标签\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2}}}, {\"db\": {\"table\": \"t_profile_group\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"createDept\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"createDept\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}], \"selectable\": false, \"searchFields\": [{\"key\": \"name\", \"name\": \"群体名称\"}]}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_group_clue_relation\", \"joinTo\": {\"table\": \"t_profile_group\", \"column\": \"id\", \"joinColumn\": \"group_id\"}, \"joinFrom\": {\"table\": \"t_profile_clue\", \"column\": \"id\", \"joinColumn\": \"clue_id\"}}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (305, '线索材料', 'clueArchive', 'clue', 309, 1, 5, NULL, NULL, NULL, 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_clue_file_relation\", \"column\": \"id\", \"joinTo\": {\"table\": \"t_file_info\", \"column\": \"id\", \"joinColumn\": \"file_id\"}, \"joinFrom\": {\"table\": \"t_profile_clue\", \"column\": \"id\", \"joinColumn\": \"clue_id\"}}', 'FILE_SCHEMA', 'FILE_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (306, '工作指令', 'command', 'clue', 310, 1, 6, NULL, NULL, NULL, 0, NULL, NULL, NULL, 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (307, '合成作战', 'composite', 'clue', 310, 1, 7, NULL, NULL, NULL, 0, NULL, NULL, NULL, 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (308, '线索信息', 'basicInfo', 'clue', 302, 1, 2, '{\"name\": \"线索信息\", \"type\": \"TABLE_SCHEMA\", \"table\": \"t_profile_clue\", \"fields\": [{\"db\": {\"table\": \"t_profile_clue\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"线索标题\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"related_phone\", \"jdbcType\": \"string\"}, \"name\": \"related_phone\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"联系电话\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"target_location\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_location\"}, \"name\": \"target_location\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"指向地点\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"action_type\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_action_type\"}, \"name\": \"action_type\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"行为方式\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"group_type\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_group_type\"}, \"name\": \"group_type\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"群体类型\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"activity_method\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_activity_method\"}, \"name\": \"activity_method\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"维权方式\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"related_target\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_related_target\"}, \"name\": \"related_target\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"涉及对象\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"related_count\", \"jdbcType\": \"string\"}, \"name\": \"related_count\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"涉及人数\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"target_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"number\"}, \"name\": \"target_time\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"指向日期\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"related_district\", \"jdbcType\": \"string\"}, \"name\": \"related_district\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"涉及市州\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"report_dept\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"report_dept\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"上报单位\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"report_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"number\"}, \"name\": \"report_time\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"上报时间\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"detail\", \"jdbcType\": \"string\"}, \"name\": \"detail\", \"tableSchema\": {\"span\": 4, \"type\": \"string\", \"title\": \"线索内容\", \"copyable\": false}}], \"moduleUi\": {\"column\": 4, \"bordered\": true}}', '{\"name\": \"线索信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_clue\", \"fields\": [{\"db\": {\"table\": \"t_profile_clue\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"formSchema\": {\"ui\": {\"ui:options\": {\"style\": {\"display\": \"block\"}, \"width\": \"1\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"线索名称\"}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"clue_label\", \"jdbcType\": \"json_id_array\"}, \"name\": \"clueLabel\", \"tree\": {\"root\": \"clue\", \"type\": \"label\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"style\": {\"display\": \"block\"}, \"width\": \"1\", \"widget\": \"cascader\", \"multiple\": true, \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"array\", \"items\": {\"type\": \"array\"}, \"title\": \"线索类别\"}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"detail\", \"jdbcType\": \"string\"}, \"name\": \"detail\", \"formSchema\": {\"ui\": {\"ui:options\": {\"style\": {\"display\": \"block\"}, \"width\": \"1\", \"widget\": \"textarea\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"线索详情\"}}}], \"required\": [\"name\"]}', '{}', 1, '{\"type\": \"PRIMARY_KEY\", \"table\": \"t_profile_clue\", \"column\": \"id\"}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (309, '相关档案', 'relatedArchive', 'clue', NULL, 1, 2, '{}', '{}', '{}', 1, '{}', '', '', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (310, '相关业务', 'relatedService', 'clue', NULL, 0, 5, NULL, NULL, NULL, 0, '{}', '', '', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (401, '档案管理', 'archive', 'case', NULL, 0, 1, NULL, NULL, '{\"name\": \"案件信息\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_case\", \"fields\": [{\"db\": {\"table\": \"JWZH_XSAJ_AJ\", \"column\": \"ajmc\", \"jdbcType\": \"string\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"JWZH_XSAJ_AJ\", \"column\": \"asjbh\", \"joinFrom\": {\"table\": \"t_profile_case\", \"joinColumn\": \"asjbh\"}}}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\", \"fixed\": \"left\", \"width\": 300, \"ellipsis\": true}, \"schema\": {\"type\": \"string\", \"title\": \"案件名称\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_case\", \"column\": \"asjbh\", \"jdbcType\": \"string\"}, \"name\": \"idNumber\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": true}, \"schema\": {\"type\": \"string\", \"title\": \"案事件编号\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"JWZH_XSAJ_AJ\", \"column\": \"ajywztmxdm\", \"mapping\": \"jwzh_dict_to_name\", \"jdbcType\": \"jwzh\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"JWZH_XSAJ_AJ\", \"column\": \"asjbh\", \"joinFrom\": {\"table\": \"t_profile_case\", \"joinColumn\": \"asjbh\"}}}, \"dict\": {\"type\": \"BD_D_XSAJYWZTMXDM\"}, \"name\": \"ajywztmxdm\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": false}, \"filter\": {\"key\": \"ajywztmxdm\", \"type\": \"tree\", \"value\": [\"##BD_D_XSAJYWZTMXDM##\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"code\", \"children\": \"children\"}, \"displayName\": \"案件状态\"}, \"schema\": {\"type\": \"string\", \"title\": \"案件状态\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"JWZH_XSAJ_AJ\", \"column\": \"ajlbdm\", \"mapping\": \"jwzh_dict_to_name\", \"jdbcType\": \"jwzh\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"JWZH_XSAJ_AJ\", \"column\": \"asjbh\", \"joinFrom\": {\"table\": \"t_profile_case\", \"joinColumn\": \"asjbh\"}}}, \"dict\": {\"type\": \"GA_D_XSAJLBDM\"}, \"name\": \"ajlbdm\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": false}, \"filter\": {\"key\": \"ajlbdm\", \"type\": \"tree\", \"value\": [\"##GA_D_XSAJLBDM##\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"code\", \"children\": \"children\"}, \"displayName\": \"案件大类\", \"defaultValue\": \"05000000\"}, \"schema\": {\"type\": \"string\", \"title\": \"案件大类\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"JWZH_XSAJ_AJ\", \"column\": \"ajxlbdm\", \"mapping\": \"jwzh_dict_to_name\", \"jdbcType\": \"jwzh\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"JWZH_XSAJ_AJ\", \"column\": \"asjbh\", \"joinFrom\": {\"table\": \"t_profile_case\", \"joinColumn\": \"asjbh\"}}}, \"dict\": {\"type\": \"GA_D_XSAJXALBDM\"}, \"name\": \"ajxlbdm\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": false}, \"filter\": {\"key\": \"ajxlbdm\", \"type\": \"tree\", \"value\": [\"##GA_D_XSAJXALBDM##\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"code\", \"children\": \"children\"}, \"displayName\": \"案件细类\"}, \"schema\": {\"type\": \"string\", \"title\": \"案件细类\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_case\", \"column\": \"important_level\", \"mapping\": \"dict_code_to_dict\", \"jdbcType\": \"string\"}, \"dict\": {\"type\": \"profile_case_importent_level\"}, \"name\": \"important_level\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": false}, \"filter\": {\"key\": \"important_level\", \"type\": \"select\", \"value\": [\"%%profile_case_importent_level%%\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"code\", \"children\": \"children\"}, \"displayName\": \"重要程度\"}, \"schema\": {\"type\": \"string\", \"title\": \"重要程度\"}, \"colorable\": true, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_case_clue\", \"column\": \"count(1)\", \"jdbcType\": \"number\", \"databaseRelation\": {\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_case\", \"column\": \"case_id\"}}, \"name\": \"caseClueCount\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 100, \"ellipsis\": true}, \"filter\": {\"key\": \"caseClueCount\", \"type\": \"option\", \"value\": [{\"id\": \"0\", \"name\": \"无线索\"}, {\"id\": \"1\", \"name\": \"有线索\"}], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\"}, \"displayName\": \"是否有线索\"}, \"schema\": {\"type\": \"string\", \"title\": \"关联线索\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_fight_composite_case_event_relation\", \"column\": \"count(1)\", \"jdbcType\": \"number\", \"databaseRelation\": {\"type\": \"FOREIGN_KEY\", \"table\": \"t_fight_composite_case_event_relation\", \"column\": \"case_event_id\"}}, \"name\": \"compositeCount\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 100, \"ellipsis\": true}, \"filter\": {\"key\": \"compositeCount\", \"type\": \"option\", \"value\": [{\"id\": \"0\", \"name\": \"未发起\"}, {\"id\": \"1\", \"name\": \"已发起\"}], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\"}, \"displayName\": \"是否发起作战\"}, \"schema\": {\"type\": \"string\", \"title\": \"相关作战数\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_case_related_suspect\", \"column\": \"count(1)\", \"jdbcType\": \"number\", \"databaseRelation\": {\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_case\", \"column\": \"case_id\"}}, \"name\": \"suspectCount\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 100, \"ellipsis\": true}, \"filter\": {\"key\": \"suspectCount\", \"type\": \"option\", \"value\": [{\"id\": \"0\", \"name\": \"未关联\"}, {\"id\": \"1\", \"name\": \"已关联\"}], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\"}, \"displayName\": \"是否关联嫌疑人\"}, \"schema\": {\"type\": \"string\", \"title\": \"关联嫌疑人\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_case\", \"column\": \"series_case\", \"mapping\": \"json_array_to_length\", \"jdbcType\": \"json\"}, \"name\": \"seriesCaseCount\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 100, \"ellipsis\": true}, \"schema\": {\"type\": \"string\", \"title\": \"串案\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_case\", \"column\": \"intelligent_classify\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"string\"}, \"dict\": {\"type\": \"profile_case_intelligent_classification\"}, \"name\": \"intelligent_classify\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": false}, \"filter\": {\"key\": \"intelligent_classify\", \"type\": \"select\", \"value\": [\"%%profile_case_intelligent_classification%%\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"code\", \"children\": \"children\"}, \"displayName\": \"智能分类\"}, \"schema\": {\"type\": \"string\", \"title\": \"智能分类\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_case\", \"column\": \"description\", \"mapping\": \"case_label_array_to_name\", \"jdbcType\": \"case_label_array\"}, \"name\": \"description\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 250, \"ellipsis\": true}, \"filter\": {\"key\": \"description\", \"type\": \"multiple-tree\", \"value\": [\"@@case_description@@\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"code\", \"children\": \"children\"}, \"displayName\": \"案件标签描述\"}, \"schema\": {\"type\": \"caseLabel\", \"title\": \"案件标签描述\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_case\", \"column\": \"infringement_target\", \"mapping\": \"case_label_array_to_name\", \"jdbcType\": \"case_label_array\"}, \"name\": \"infringement_target\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 250, \"ellipsis\": true}, \"filter\": {\"key\": \"infringement_target\", \"type\": \"multiple-tree\", \"value\": [\"@@case_infringement_target@@\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"code\", \"children\": \"children\"}, \"displayName\": \"侵害目标标签\"}, \"schema\": {\"type\": \"caseLabel\", \"title\": \"侵害目标标签\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_case\", \"column\": \"suspected_person\", \"mapping\": \"case_label_array_to_name\", \"jdbcType\": \"case_label_array\"}, \"name\": \"suspected_person\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 250, \"ellipsis\": true}, \"filter\": {\"key\": \"suspected_person\", \"type\": \"multiple-tree\", \"value\": [\"@@case_suspected_person@@\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"code\", \"children\": \"children\"}, \"displayName\": \"嫌疑人标签\"}, \"schema\": {\"type\": \"caseLabel\", \"title\": \"嫌疑人标签\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_case\", \"column\": \"suspected_vehicle\", \"mapping\": \"case_label_array_to_name\", \"jdbcType\": \"case_label_array\"}, \"name\": \"suspected_vehicle\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 250, \"ellipsis\": true}, \"filter\": {\"key\": \"suspected_vehicle\", \"type\": \"multiple-tree\", \"value\": [\"@@case_suspected_vehicle@@\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"code\", \"children\": \"children\"}, \"displayName\": \"嫌疑车辆标签\"}, \"schema\": {\"type\": \"caseLabel\", \"title\": \"嫌疑车辆标签\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"JWZH_XSAJ_AJ\", \"column\": \"asjfsdd_xzqhdm\", \"mapping\": \"district_code_to_name\", \"jdbcType\": \"district\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"JWZH_XSAJ_AJ\", \"column\": \"asjbh\", \"joinFrom\": {\"table\": \"t_profile_case\", \"joinColumn\": \"asjbh\"}}}, \"name\": \"asjfsdd_xzqhdm\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": false}, \"filter\": {\"key\": \"asjfsdd_xzqhdm\", \"type\": \"multiple-tree\", \"value\": [\"&&district&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"发生地区\"}, \"schema\": {\"type\": \"string\", \"title\": \"发生地区\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"JWZH_XSAJ_AJ\", \"column\": \"zbr_xm\", \"jdbcType\": \"string\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"JWZH_XSAJ_AJ\", \"column\": \"asjbh\", \"joinFrom\": {\"table\": \"t_profile_case\", \"joinColumn\": \"asjbh\"}}}, \"name\": \"zbr_xm\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": false}, \"schema\": {\"type\": \"string\", \"title\": \"主办人\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"JWZH_XSAJ_AJ\", \"column\": \"badw_gajgjgdm\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"dept_code\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"JWZH_XSAJ_AJ\", \"column\": \"asjbh\", \"joinFrom\": {\"table\": \"t_profile_case\", \"joinColumn\": \"asjbh\"}}}, \"name\": \"badw_gajgjgdm\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": false}, \"filter\": {\"key\": \"badw_gajgjgdm\", \"type\": \"multiple-tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"deptCode\", \"children\": \"children\"}, \"displayName\": \"主办单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"主办单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"JWZH_XSAJ_AJ\", \"column\": \"xt_lrsj\", \"mapping\": \"string_to_date_time\", \"jdbcType\": \"stringTime\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"JWZH_XSAJ_AJ\", \"column\": \"asjbh\", \"joinFrom\": {\"table\": \"t_profile_case\", \"joinColumn\": \"asjbh\"}}}, \"name\": \"xt_lrsj\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"xt_lrsj\", \"type\": \"timeParams\", \"value\": [{\"id\": \"1\", \"name\": \"今天\"}, {\"id\": \"11\", \"name\": \"昨天\"}, {\"id\": \"2\", \"name\": \"本周\"}, {\"id\": \"12\", \"name\": \"上周\"}, {\"id\": \"3\", \"name\": \"本月\"}, {\"id\": \"13\", \"name\": \"上月\"}, {\"id\": \"4\", \"name\": \"本季\"}, {\"id\": \"14\", \"name\": \"上季\"}, {\"id\": \"99\", \"name\": \"自定义\"}], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\"}, \"displayName\": \"录入时间\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true}}}, {\"db\": {\"table\": \"t_profile_case\", \"column\": \"update_time\", \"mapping\": \"date_time_to_text\", \"jdbcType\": \"datetime\"}, \"name\": \"update_time\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"update_time\", \"type\": \"timeParams\", \"value\": [{\"id\": \"1\", \"name\": \"今天\"}, {\"id\": \"11\", \"name\": \"昨天\"}, {\"id\": \"2\", \"name\": \"本周\"}, {\"id\": \"12\", \"name\": \"上周\"}, {\"id\": \"3\", \"name\": \"本月\"}, {\"id\": \"13\", \"name\": \"上月\"}, {\"id\": \"4\", \"name\": \"本季\"}, {\"id\": \"14\", \"name\": \"上季\"}, {\"id\": \"99\", \"name\": \"自定义\"}], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\"}, \"displayName\": \"更新时间\"}, \"schema\": {\"type\": \"string\", \"title\": \"更新时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true, \"sortDefault\": \"descending\"}}}], \"selectable\": true, \"searchFields\": [{\"key\": \"name\", \"name\": \"案件名称\"}, {\"key\": \"idNumber\", \"name\": \"案件编号\"}]}', 0, '{}', '', '', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (402, '案件信息', 'basicInfo', 'case', NULL, 1, 2, '{}', '{}', '{}', 1, '{}', NULL, NULL, 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (403, '基本信息', 'caseInfo', 'case', 402, 1, 3, '{\"name\": \"案件信息\", \"type\": \"TABLE_SCHEMA\", \"table\": \"t_profile_case\", \"fields\": [{\"db\": {\"table\": \"JWZH_XSAJ_AJ\", \"column\": \"ajmc\", \"jdbcType\": \"string\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"JWZH_XSAJ_AJ\", \"column\": \"asjbh\", \"joinFrom\": {\"table\": \"t_profile_case\", \"joinColumn\": \"asjbh\"}}}, \"name\": \"ajmc\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"案件名称\", \"copyable\": true}}, {\"db\": {\"table\": \"t_profile_case\", \"column\": \"asjbh\", \"jdbcType\": \"string\"}, \"name\": \"asjbh\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"案事件编号\", \"copyable\": true}}, {\"db\": {\"table\": \"t_profile_case\", \"column\": \"important_level\", \"mapping\": \"dict_code_to_dict\", \"jdbcType\": \"string\"}, \"dict\": {\"type\": \"profile_case_importent_level\"}, \"name\": \"important_level\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"重要程度\", \"copyable\": false, \"colorable\": true}}, {\"db\": {\"table\": \"t_profile_case\", \"column\": \"intelligent_classify\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"string\"}, \"dict\": {\"type\": \"profile_case_intelligent_classification\"}, \"name\": \"intelligent_classify\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"智能分类\", \"copyable\": false, \"titleSuffixIcon\": \"AI\"}}, {\"db\": {\"table\": \"JWZH_XSAJ_AJ\", \"column\": \"ajywztmxdm\", \"mapping\": \"jwzh_dict_to_name\", \"jdbcType\": \"jwzh\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"JWZH_XSAJ_AJ\", \"column\": \"asjbh\", \"joinFrom\": {\"table\": \"t_profile_case\", \"joinColumn\": \"asjbh\"}}}, \"dict\": {\"type\": \"BD_D_XSAJYWZTMXDM\"}, \"name\": \"ajywztmxdm\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"案件状态\", \"copyable\": false}}, {\"db\": {\"table\": \"JWZH_XSAJ_AJ\", \"column\": \"ajlbdm\", \"mapping\": \"jwzh_dict_to_name\", \"jdbcType\": \"jwzh\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"JWZH_XSAJ_AJ\", \"column\": \"asjbh\", \"joinFrom\": {\"table\": \"t_profile_case\", \"joinColumn\": \"asjbh\"}}}, \"dict\": {\"type\": \"GA_D_XSAJLBDM\"}, \"name\": \"ajlbdm\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"案件大类\", \"copyable\": false}}, {\"db\": {\"table\": \"JWZH_XSAJ_AJ\", \"column\": \"ajxlbdm\", \"mapping\": \"jwzh_dict_to_name\", \"jdbcType\": \"jwzh\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"JWZH_XSAJ_AJ\", \"column\": \"asjbh\", \"joinFrom\": {\"table\": \"t_profile_case\", \"joinColumn\": \"asjbh\"}}}, \"dict\": {\"type\": \"GA_D_XSAJXALBDM\"}, \"name\": \"ajxlbdm\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"案件细类\", \"copyable\": false}}, {\"db\": {\"table\": \"JWZH_XSAJ_AJ\", \"column\": \"zbr_xm\", \"jdbcType\": \"string\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"JWZH_XSAJ_AJ\", \"column\": \"asjbh\", \"joinFrom\": {\"table\": \"t_profile_case\", \"joinColumn\": \"asjbh\"}}}, \"name\": \"zbr_xm\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"主办人\", \"copyable\": false}}, {\"db\": {\"table\": \"JWZH_XSAJ_AJ\", \"column\": \"badw_gajgjgdm\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"dept_code\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"JWZH_XSAJ_AJ\", \"column\": \"asjbh\", \"joinFrom\": {\"table\": \"t_profile_case\", \"joinColumn\": \"asjbh\"}}}, \"name\": \"badw_gajgjgdm\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"主办单位\", \"copyable\": false}}, {\"db\": {\"table\": \"JWZH_XSAJ_AJ\", \"column\": \"xt_lrsj\", \"mapping\": \"string_to_date_time\", \"jdbcType\": \"stringTime\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"JWZH_XSAJ_AJ\", \"column\": \"asjbh\", \"joinFrom\": {\"table\": \"t_profile_case\", \"joinColumn\": \"asjbh\"}}}, \"name\": \"xt_lrsj\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"录入时间\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_case\", \"column\": \"point\", \"mapping\": \"map_location\", \"jdbcType\": \"string\"}, \"name\": \"point\", \"tableSchema\": {\"span\": 2, \"type\": \"mapLocation\", \"title\": \"案发地点\", \"copyable\": false}}, {\"db\": {\"table\": \"JWZH_XSAJ_AJ\", \"column\": \"jyaq\", \"jdbcType\": \"string\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"JWZH_XSAJ_AJ\", \"column\": \"asjbh\", \"joinFrom\": {\"table\": \"t_profile_case\", \"joinColumn\": \"asjbh\"}}}, \"name\": \"jyaq\", \"tableSchema\": {\"span\": 4, \"type\": \"string\", \"title\": \"简要案情\", \"copyable\": false}}], \"moduleUi\": {\"column\": 4, \"bordered\": true}}', '{\"name\": \"人员信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_case\", \"fields\": [{\"db\": {\"table\": \"t_profile_case\", \"column\": \"intelligent_classify\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"profile_case_intelligent_classification\"}, \"name\": \"intelligent_classify\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"智能分类\"}}}, {\"db\": {\"table\": \"t_profile_case\", \"column\": \"important_level\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"profile_case_importent_level\"}, \"name\": \"important_level\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"重要程度\"}, \"colorable\": true}}], \"required\": []}', NULL, 1, '{\"type\": \"PRIMARY_KEY\", \"table\": \"t_profile_case\", \"column\": \"id\"}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (404, '案件标签', 'caseLabel', 'case', 402, 1, 4, NULL, NULL, NULL, 1, NULL, 'NO_SCHEMA', 'NO_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (405, '涉案人员', 'relatedPerson', 'case', 402, 1, 5, NULL, NULL, NULL, 1, NULL, 'NO_SCHEMA', 'NO_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (406, '合成作战', 'relatedComposite', 'case', 411, 1, 6, NULL, NULL, '{}', 1, '{}', 'NO_SCHEMA', 'NO_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (407, '侦办结果', 'caseResult', 'case', NULL, 1, 7, '{}', '{}', '{}', 1, '{}', NULL, NULL, 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (408, '疑似嫌疑人', 'maySuspect', 'case', 407, 1, 8, NULL, NULL, NULL, 1, NULL, 'NO_SCHEMA', 'NO_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (409, '串案', 'caseConnection', 'case', 407, 1, 9, NULL, NULL, NULL, 1, NULL, 'NO_SCHEMA', 'NO_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (410, '关联线索', 'relatedClue', 'case', 4070, 1, 10, NULL, NULL, '{\"name\": \"相关线索\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_clue\", \"fields\": [{\"db\": {\"table\": \"t_profile_clue\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"线索名称\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"clue_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"label_id_array\"}, \"name\": \"clueLabel\", \"listSchema\": {\"style\": {\"align\": \"left\"}, \"filter\": {\"key\": \"clueLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&clue_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"线索标签\"}, \"schema\": {\"type\": \"array\", \"title\": \"线索标签\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"source\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_source\"}, \"name\": \"source\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"线索来源\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"disposal_status\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_disposal_status\"}, \"name\": \"disposal_status\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"处置状态\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"emergency_level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_emergency_level\"}, \"name\": \"emergency_level\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"紧急程度\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"create_time\", \"mapping\": \"date_time_to_text\", \"jdbcType\": \"datetime\"}, \"name\": \"createTime\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true}}}], \"selectable\": false, \"searchFields\": [{\"key\": \"name\", \"name\": \"线索名称\"}]}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_case_clue_relation\", \"column\": \"id\", \"joinTo\": {\"table\": \"t_profile_clue\", \"column\": \"id\", \"joinColumn\": \"clue_id\"}, \"joinFrom\": {\"table\": \"t_profile_case\", \"column\": \"id\", \"joinColumn\": \"case_id\"}}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (411, '侦办过程', 'caseProcess', 'case', NULL, 1, 3, NULL, NULL, NULL, 1, NULL, NULL, NULL, NULL);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (412, '涉案线索', 'caseClue', 'case', 411, 1, 11, NULL, NULL, NULL, 1, NULL, 'NO_SCHEMA', 'NO_SCHEMA', NULL);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (501, '档案管理', 'archive', 'jq', NULL, 0, 1, NULL, NULL, '{\"name\": \"警情信息\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_jq\", \"fields\": [{\"db\": {\"table\": \"STHY_JJDB\", \"column\": \"jqclztdm\", \"mapping\": \"dict_code_to_dict\", \"jdbcType\": \"string\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"STHY_JJDB\", \"column\": \"jjdbh\", \"joinFrom\": {\"table\": \"t_profile_jq\", \"joinColumn\": \"jjdbh\"}}}, \"dict\": {\"type\": \"sthy_jqzt\"}, \"name\": \"jqclztdm\", \"listSchema\": {\"style\": {\"align\": \"center\", \"fixed\": \"left\", \"ellipsis\": true}, \"filter\": {\"key\": \"jqclztdm\", \"type\": \"select\", \"value\": [\"%%sthy_jqzt%%\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"dictDesc\", \"children\": \"children\"}, \"displayName\": \"警情状态\"}, \"schema\": {\"type\": \"string\", \"title\": \"\"}, \"colorable\": true, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_jq\", \"column\": \"jjdbh\", \"jdbcType\": \"string\"}, \"name\": \"jjdbh\", \"listSchema\": {\"style\": {\"align\": \"center\", \"fixed\": \"left\", \"ellipsis\": false}, \"schema\": {\"type\": \"string\", \"title\": \"警情编号\"}, \"properties\": {\"href\": \"/ys-app/archives/jq/details?id={value}\", \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"STHY_JJDB\", \"column\": \"jqdjdm\", \"mapping\": \"dict_code_to_dict\", \"jdbcType\": \"string\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"STHY_JJDB\", \"column\": \"jjdbh\", \"joinFrom\": {\"table\": \"t_profile_jq\", \"joinColumn\": \"jjdbh\"}}}, \"dict\": {\"type\": \"sthy_jqdj\"}, \"name\": \"jqdjdm\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": true}, \"filter\": {\"key\": \"jqdjdm\", \"type\": \"option\", \"value\": [\"%%sthy_jqdj%%\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"dictDesc\", \"children\": \"children\"}, \"displayName\": \"警情等级\"}, \"schema\": {\"type\": \"string\", \"title\": \"警情等级\"}, \"colorable\": true, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"STHY_JJDB\", \"column\": \"jqlbdm\", \"jdbcType\": \"string\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"STHY_JJDB\", \"column\": \"jjdbh\", \"joinFrom\": {\"table\": \"t_profile_jq\", \"joinColumn\": \"jjdbh\"}}}, \"name\": \"jqlbdm\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": true}, \"schema\": {\"type\": \"string\", \"title\": \"警情类别\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"STHY_JJDB\", \"column\": \"bjnr\", \"jdbcType\": \"string\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"STHY_JJDB\", \"column\": \"jjdbh\", \"joinFrom\": {\"table\": \"t_profile_jq\", \"joinColumn\": \"jjdbh\"}}}, \"name\": \"bjnr\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": true}, \"schema\": {\"type\": \"string\", \"title\": \"报警内容\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"STHY_JJDB\", \"column\": \"jqbq\", \"jdbcType\": \"string\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"STHY_JJDB\", \"column\": \"jjdbh\", \"joinFrom\": {\"table\": \"t_profile_jq\", \"joinColumn\": \"jjdbh\"}}}, \"name\": \"jqbq\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": true}, \"schema\": {\"type\": \"string\", \"title\": \"警情标签\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_risk_jq_relation\", \"column\": \"count(1)\", \"jdbcType\": \"string\", \"databaseRelation\": {\"type\": \"FOREIGN_KEY\", \"table\": \"t_risk_jq_relation\", \"column\": \"jq_id\", \"joinFrom\": {\"table\": \"t_profile_jq\", \"joinColumn\": \"id\"}}}, \"name\": \"riskCount\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": true}, \"schema\": {\"type\": \"string\", \"title\": \"关联风险\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"STHY_JJDB\", \"column\": \"lxdh\", \"jdbcType\": \"string\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"STHY_JJDB\", \"column\": \"jjdbh\", \"joinFrom\": {\"table\": \"t_profile_jq\", \"joinColumn\": \"jjdbh\"}}}, \"name\": \"lxdh\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": true}, \"schema\": {\"type\": \"string\", \"title\": \"联系电话\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"STHY_JJDB\", \"column\": \"jqlyfs\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"string\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"STHY_JJDB\", \"column\": \"jjdbh\", \"joinFrom\": {\"table\": \"t_profile_jq\", \"joinColumn\": \"jjdbh\"}}}, \"dict\": {\"type\": \"sthy_jqly\"}, \"name\": \"jqlyfs\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": true}, \"filter\": {\"key\": \"jqlyfs\", \"type\": \"select\", \"value\": [\"%%sthy_jqly%%\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"dictDesc\", \"children\": \"children\"}, \"displayName\": \"警情来源\"}, \"schema\": {\"type\": \"string\", \"title\": \"警情来源\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"STHY_JJDB\", \"column\": \"jjlx\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"string\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"STHY_JJDB\", \"column\": \"jjdbh\", \"joinFrom\": {\"table\": \"t_profile_jq\", \"joinColumn\": \"jjdbh\"}}}, \"dict\": {\"type\": \"sthy_jjlx\"}, \"name\": \"jjlx\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": true}, \"filter\": {\"key\": \"jjlx\", \"type\": \"select\", \"value\": [\"%%sthy_jjlx%%\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"dictDesc\", \"children\": \"children\"}, \"displayName\": \"接警类型\"}, \"schema\": {\"type\": \"string\", \"title\": \"接警类型\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"STHY_JJDB\", \"column\": \"lhlx\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"string\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"STHY_JJDB\", \"column\": \"jjdbh\", \"joinFrom\": {\"table\": \"t_profile_jq\", \"joinColumn\": \"jjdbh\"}}}, \"dict\": {\"type\": \"sthy_lhlx\"}, \"name\": \"lhlx\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": true}, \"filter\": {\"key\": \"lhlx\", \"type\": \"select\", \"value\": [\"%%sthy_lhlx%%\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"dictDesc\", \"children\": \"children\"}, \"displayName\": \"来话类型\"}, \"schema\": {\"type\": \"string\", \"title\": \"来话类型\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"STHY_JJDB\", \"column\": \"xzqhdm\", \"mapping\": \"district_code_to_name\", \"jdbcType\": \"district\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"STHY_JJDB\", \"column\": \"jjdbh\", \"joinFrom\": {\"table\": \"t_profile_jq\", \"joinColumn\": \"jjdbh\"}}}, \"name\": \"xzqhdm\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": true}, \"filter\": {\"key\": \"xzqhdm\", \"type\": \"multiple-tree\", \"value\": [\"&&district&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"行政区划\"}, \"schema\": {\"type\": \"string\", \"title\": \"行政区划\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"STHY_JJDB\", \"column\": \"gxdwdm\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"dept_code\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"STHY_JJDB\", \"column\": \"jjdbh\", \"joinFrom\": {\"table\": \"t_profile_jq\", \"joinColumn\": \"jjdbh\"}}}, \"name\": \"gxdwdm\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": true}, \"filter\": {\"key\": \"gxdwdm\", \"type\": \"multiple-tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"deptCode\", \"children\": \"children\"}, \"displayName\": \"管辖单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"管辖单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"STHY_JJDB\", \"column\": \"bjsj\", \"mapping\": \"date_time_to_text\", \"jdbcType\": \"datetime\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"STHY_JJDB\", \"column\": \"jjdbh\", \"joinFrom\": {\"table\": \"t_profile_jq\", \"joinColumn\": \"jjdbh\"}}}, \"name\": \"bjsj\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"bjsj\", \"type\": \"timeParams\", \"value\": [{\"id\": \"1\", \"name\": \"今天\"}, {\"id\": \"11\", \"name\": \"昨天\"}, {\"id\": \"2\", \"name\": \"本周\"}, {\"id\": \"12\", \"name\": \"上周\"}, {\"id\": \"3\", \"name\": \"本月\"}, {\"id\": \"13\", \"name\": \"上月\"}, {\"id\": \"4\", \"name\": \"本季\"}, {\"id\": \"14\", \"name\": \"上季\"}, {\"id\": \"99\", \"name\": \"自定义\"}], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\"}, \"displayName\": \"报警时间\"}, \"schema\": {\"type\": \"string\", \"title\": \"报警时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true}}}], \"selectable\": true, \"searchFields\": [{\"key\": \"jjdbh\", \"name\": \"警情编号\"}, {\"key\": \"bjnr\", \"name\": \"简要警情\"}]}', 1, '{}', NULL, NULL, 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (502, '警情信息', 'basicInfo', 'jq', NULL, 1, 2, NULL, NULL, NULL, 1, NULL, NULL, NULL, 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (503, '接警信息', 'jqInfo', 'jq', 502, 1, 3, '{\"name\": \"接警信息\", \"type\": \"TABLE_SCHEMA\", \"table\": \"t_profile_jq\", \"fields\": [{\"db\": {\"table\": \"STHY_JJDB\", \"column\": \"jjdwdm\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"string\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"STHY_JJDB\", \"column\": \"jjdbh\", \"joinFrom\": {\"table\": \"t_profile_jq\", \"joinColumn\": \"jjdbh\"}}}, \"name\": \"jjdwdm\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"接警单位\", \"copyable\": false}}, {\"db\": {\"table\": \"STHY_JJDB\", \"column\": \"jjlx\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"string\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"STHY_JJDB\", \"column\": \"jjdbh\", \"joinFrom\": {\"table\": \"t_profile_jq\", \"joinColumn\": \"jjdbh\"}}}, \"dict\": {\"type\": \"sthy_jjlx\"}, \"name\": \"jjlx\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"接警类型\", \"copyable\": false}}, {\"db\": {\"table\": \"STHY_JJDB\", \"column\": \"jqlyfs\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"string\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"STHY_JJDB\", \"column\": \"jjdbh\", \"joinFrom\": {\"table\": \"t_profile_jq\", \"joinColumn\": \"jjdbh\"}}}, \"dict\": {\"type\": \"sthy_jqly\"}, \"name\": \"jqlyfs\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"警情来源\", \"copyable\": false}}, {\"db\": {\"table\": \"STHY_JJDB\", \"column\": \"lhlx\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"string\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"STHY_JJDB\", \"column\": \"jjdbh\", \"joinFrom\": {\"table\": \"t_profile_jq\", \"joinColumn\": \"jjdbh\"}}}, \"dict\": {\"type\": \"sthy_lhlx\"}, \"name\": \"lhlx\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"来话类型\", \"copyable\": false}}, {\"db\": {\"table\": \"STHY_JJDB\", \"column\": \"bjrmc\", \"jdbcType\": \"string\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"STHY_JJDB\", \"column\": \"jjdbh\", \"joinFrom\": {\"table\": \"t_profile_jq\", \"joinColumn\": \"jjdbh\"}}}, \"name\": \"bjrmc\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"报警人名称\", \"copyable\": false}}, {\"db\": {\"table\": \"STHY_JJDB\", \"column\": \"bjdh\", \"jdbcType\": \"string\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"STHY_JJDB\", \"column\": \"jjdbh\", \"joinFrom\": {\"table\": \"t_profile_jq\", \"joinColumn\": \"jjdbh\"}}}, \"name\": \"bjdh\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"联系电话\", \"copyable\": false}}, {\"db\": {\"table\": \"STHY_JJDB\", \"column\": \"bjrzjhm\", \"jdbcType\": \"string\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"STHY_JJDB\", \"column\": \"jjdbh\", \"joinFrom\": {\"table\": \"t_profile_jq\", \"joinColumn\": \"jjdbh\"}}}, \"name\": \"bjrzjhm\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"身份证号\", \"copyable\": false}}, {\"db\": {\"table\": \"STHY_JJDB\", \"column\": \"bjsj\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"string\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"STHY_JJDB\", \"column\": \"jjdbh\", \"joinFrom\": {\"table\": \"t_profile_jq\", \"joinColumn\": \"jjdbh\"}}}, \"name\": \"bjsj\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"报警时间\", \"copyable\": false}}, {\"db\": {\"table\": \"STHY_JJDB\", \"column\": \"jqdz\", \"jdbcType\": \"string\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"STHY_JJDB\", \"column\": \"jjdbh\", \"joinFrom\": {\"table\": \"t_profile_jq\", \"joinColumn\": \"jjdbh\"}}}, \"name\": \"jqdz\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"警情地址\", \"copyable\": false}}, {\"db\": {\"table\": \"STHY_JJDB\", \"column\": \"bjnr\", \"jdbcType\": \"string\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"STHY_JJDB\", \"column\": \"jjdbh\", \"joinFrom\": {\"table\": \"t_profile_jq\", \"joinColumn\": \"jjdbh\"}}}, \"name\": \"bjnr\", \"tableSchema\": {\"span\": 3, \"type\": \"string\", \"title\": \"报警内容\", \"copyable\": false}}, {\"db\": {\"table\": \"STHY_JJDB\", \"column\": \"jqbq\", \"jdbcType\": \"string\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"STHY_JJDB\", \"column\": \"jjdbh\", \"joinFrom\": {\"table\": \"t_profile_jq\", \"joinColumn\": \"jjdbh\"}}}, \"name\": \"jqbq\", \"tableSchema\": {\"span\": 3, \"type\": \"string\", \"title\": \"警情标签\", \"copyable\": false}}], \"moduleUi\": {\"column\": 3, \"bordered\": true}}', NULL, NULL, 1, '{\"type\": \"PRIMARY_KEY\", \"table\": \"t_profile_jq\", \"column\": \"id\"}', 'TABLE_SCHEMA', NULL, 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (504, '反馈信息', 'fkInfo', 'jq', 502, 1, 4, NULL, NULL, NULL, 1, NULL, 'NO_SCHEMA', NULL, 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (505, '相关案件', 'relatedCase', 'jq', NULL, 1, 5, NULL, NULL, '{\"name\": \"案件信息\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_case\", \"fields\": [{\"db\": {\"table\": \"t_profile_case\", \"column\": \"important_level\", \"mapping\": \"dict_code_to_dict\", \"jdbcType\": \"string\"}, \"dict\": {\"type\": \"profile_case_importent_level\"}, \"name\": \"important_level\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": false}, \"schema\": {\"type\": \"string\", \"title\": \"重要程度\"}, \"colorable\": true, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"JWZH_XSAJ_AJ\", \"column\": \"ajmc\", \"jdbcType\": \"string\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"JWZH_XSAJ_AJ\", \"column\": \"asjbh\", \"joinFrom\": {\"table\": \"t_profile_case\", \"joinColumn\": \"asjbh\"}}}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\", \"fixed\": \"left\", \"ellipsis\": true}, \"schema\": {\"type\": \"string\", \"title\": \"案件名称\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_case\", \"column\": \"asjbh\", \"jdbcType\": \"string\"}, \"name\": \"idNumber\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": true}, \"schema\": {\"type\": \"string\", \"title\": \"案事件编号\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"JWZH_XSAJ_AJ\", \"column\": \"ajywztmxdm\", \"mapping\": \"jwzh_dict_to_name\", \"jdbcType\": \"jwzh\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"JWZH_XSAJ_AJ\", \"column\": \"asjbh\", \"joinFrom\": {\"table\": \"t_profile_case\", \"joinColumn\": \"asjbh\"}}}, \"dict\": {\"type\": \"BD_D_XSAJYWZTMXDM\"}, \"name\": \"ajywztmxdm\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": false}, \"schema\": {\"type\": \"string\", \"title\": \"案件状态\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"JWZH_XSAJ_AJ\", \"column\": \"ajlbdm\", \"mapping\": \"jwzh_dict_to_name\", \"jdbcType\": \"jwzh\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"JWZH_XSAJ_AJ\", \"column\": \"asjbh\", \"joinFrom\": {\"table\": \"t_profile_case\", \"joinColumn\": \"asjbh\"}}}, \"dict\": {\"type\": \"GA_D_XSAJLBDM\"}, \"name\": \"ajlbdm\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": false}, \"schema\": {\"type\": \"string\", \"title\": \"案件大类\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"JWZH_XSAJ_AJ\", \"column\": \"ajxlbdm\", \"mapping\": \"jwzh_dict_to_name\", \"jdbcType\": \"jwzh\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"JWZH_XSAJ_AJ\", \"column\": \"asjbh\", \"joinFrom\": {\"table\": \"t_profile_case\", \"joinColumn\": \"asjbh\"}}}, \"dict\": {\"type\": \"GA_D_XSAJXALBDM\"}, \"name\": \"ajxlbdm\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": false}, \"schema\": {\"type\": \"string\", \"title\": \"案件细类\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"JWZH_XSAJ_AJ\", \"column\": \"zbr_gmsfhm\", \"mapping\": \"user_id_card_to_user_name\", \"jdbcType\": \"user_id_card\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"JWZH_XSAJ_AJ\", \"column\": \"asjbh\", \"joinFrom\": {\"table\": \"t_profile_case\", \"joinColumn\": \"asjbh\"}}}, \"name\": \"zbr_gmsfhm\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": false}, \"schema\": {\"type\": \"string\", \"title\": \"主办人\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"JWZH_XSAJ_AJ\", \"column\": \"badw_gajgjgdm\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"dept_code\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"table\": \"JWZH_XSAJ_AJ\", \"column\": \"asjbh\", \"joinFrom\": {\"table\": \"t_profile_case\", \"joinColumn\": \"asjbh\"}}}, \"name\": \"badw_gajgjgdm\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": false}, \"schema\": {\"type\": \"string\", \"title\": \"主办单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_case\", \"column\": \"update_time\", \"mapping\": \"date_time_to_text\", \"jdbcType\": \"datetime\"}, \"name\": \"update_time\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"更新时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true, \"sortDefault\": \"descending\"}}}], \"selectable\": true, \"searchFields\": [{\"key\": \"name\", \"name\": \"案件名称\"}, {\"key\": \"idNumber\", \"name\": \"案件编号\"}]}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_case_jq_relation\", \"joinTo\": {\"table\": \"t_profile_case\", \"column\": \"id\", \"joinColumn\": \"case_id\"}, \"joinFrom\": {\"table\": \"t_profile_jq\", \"column\": \"id\", \"joinColumn\": \"jq_id\"}}', 'LIST_SCHEMA', NULL, 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (506, '相关风险', 'relatedRisk', 'jq', NULL, 1, 6, NULL, NULL, '{\"name\": \"风险信息\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_risk\", \"fields\": [{\"db\": {\"table\": \"t_risk\", \"column\": \"risk_status\", \"mapping\": \"dict_code_to_dict\", \"jdbcType\": \"string\"}, \"dict\": {\"type\": \"risk_status\"}, \"name\": \"risk_status\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": false}, \"schema\": {\"type\": \"string\", \"title\": \"风险状态\"}, \"colorable\": true, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_risk\", \"column\": \"title\", \"jdbcType\": \"string\"}, \"name\": \"title\", \"listSchema\": {\"style\": {\"align\": \"center\", \"fixed\": \"left\", \"ellipsis\": true}, \"schema\": {\"type\": \"string\", \"title\": \"风险名称\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_risk\", \"column\": \"risk_level\", \"mapping\": \"dict_code_to_dict\", \"jdbcType\": \"string\"}, \"dict\": {\"type\": \"risk_level\"}, \"name\": \"risk_level\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": false}, \"schema\": {\"type\": \"string\", \"title\": \"风险等级\"}, \"colorable\": true, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_risk\", \"column\": \"risk_code\", \"jdbcType\": \"string\"}, \"name\": \"risk_code\", \"listSchema\": {\"style\": {\"align\": \"center\", \"fixed\": \"left\", \"ellipsis\": true}, \"schema\": {\"type\": \"string\", \"title\": \"风险编号\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_risk\", \"column\": \"resolve_status\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"string\"}, \"dict\": {\"type\": \"risk_resolve_status\"}, \"name\": \"resolve_status\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": false}, \"schema\": {\"type\": \"string\", \"title\": \"化解结果\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_risk\", \"column\": \"hand_over_status\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"string\"}, \"dict\": {\"type\": \"risk_hand_over_status\"}, \"name\": \"hand_over_status\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": false}, \"schema\": {\"type\": \"string\", \"title\": \"化解单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_risk\", \"column\": \"source\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"string\"}, \"dict\": {\"type\": \"risk_source\"}, \"name\": \"source\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": false}, \"schema\": {\"type\": \"string\", \"title\": \"来源\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_risk\", \"column\": \"risk_type\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"string\"}, \"dict\": {\"type\": \"risk_type\"}, \"name\": \"risk_type\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": false}, \"schema\": {\"type\": \"string\", \"title\": \"类别\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_risk\", \"column\": \"responsible_dept\", \"mapping\": \"simple_dept_to_dept_name\", \"jdbcType\": \"dept_id\"}, \"name\": \"responsible_dept\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"主责单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_risk\", \"column\": \"handle_dept\", \"mapping\": \"simple_dept_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"handle_dept\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"处置单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}]}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_risk_jq_relation\", \"joinTo\": {\"table\": \"t_risk\", \"column\": \"id\", \"joinColumn\": \"risk_id\"}, \"joinFrom\": {\"table\": \"t_profile_jq\", \"column\": \"id\", \"joinColumn\": \"jq_id\"}}', 'LIST_SCHEMA', NULL, 1);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`) VALUES (601, '风险群体', 'riskRelatedGroup', 'risk', NULL, 0, 1, NULL, NULL, '{\"name\": \"相关群体\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_group\", \"fields\": [{\"db\": {\"table\": \"t_profile_group\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"群体名称\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_group\", \"column\": \"group_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"label_id_array\"}, \"name\": \"groupLabel\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"groupLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&group_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"群体标签\"}, \"schema\": {\"type\": \"array\", \"title\": \"群体标签\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2}}}, {\"db\": {\"table\": \"t_risk_group_relation\", \"column\": \"remark\", \"jdbcType\": \"string\", \"databaseRelation\": {\"type\": \"RELATION_TABLE\", \"table\": \"t_risk_group_relation\", \"joinTo\": {\"joinColumn\": \"group_id\"}, \"joinFrom\": {\"joinColumn\": \"risk_id\"}}}, \"name\": \"remark\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"备注\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_group\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"createDept\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"createDept\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}], \"selectable\": false, \"searchFields\": [{\"key\": \"name\", \"name\": \"群体名称\"}]}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_risk_group_relation\", \"joinTo\": {\"table\": \"t_profile_group\", \"column\": \"id\", \"joinColumn\": \"group_id\"}, \"joinFrom\": {\"table\": \"t_risk\", \"column\": \"id\", \"joinColumn\": \"risk_id\"}}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1);
