CREATE TABLE IF NOT EXISTS `t_warning_person_clue` (
 `id` bigint NOT NULL AUTO_INCREMENT,
 `create_user_id` bigint DEFAULT NULL,
 `create_dept_id` bigint DEFAULT NULL,
 `create_time` timestamp NULL DEFAULT NULL,
 `update_user_id` bigint DEFAULT NULL,
 `update_dept_id` bigint DEFAULT NULL,
 `update_time` timestamp NULL DEFAULT NULL,
 `warning_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '预警类型',
 `warning_level` int NOT NULL COMMENT '预警级别',
 `content` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '预警详情',
 `warning_time` timestamp NOT NULL COMMENT '预警时间',
 `monitor_id` bigint DEFAULT NULL COMMENT '布控id',
 `model_id` json NOT NULL COMMENT '模型id',
 `group_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
 `control_type` int DEFAULT NULL COMMENT '管控类型 1=布控 2=常控',
 `activity_address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '活动地点',
 `activity_time` timestamp NULL DEFAULT NULL COMMENT '活动时间',
 `warning_status` int DEFAULT NULL COMMENT '当前预警的状态eg:未有人签收时为 未签收状态，有一个人签收时为已签收',
 `person_label` json DEFAULT NULL,
 `area_id` json DEFAULT NULL COMMENT '命中的区域的id',
 `place_code` json DEFAULT NULL COMMENT '命中的场所code',
 `id_card` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '身份证',
 PRIMARY KEY (`id`) USING BTREE,
 KEY `idx_activity_time` (`activity_time`) USING BTREE,
 KEY `idx_warning_time` (`warning_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='预警表';

CREATE TABLE IF NOT EXISTS t_person_clue_score_record (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `score_items` varchar(1024) comment '计分项',
  `score_time` varchar(20) comment '计分时间',
  `score` double comment '分数',
  `id_card` varchar(30) comment '身份证',
  `type` varchar(10) comment '类别，1案件积分，2线索积分',
  `primary_key` varchar(128) comment '唯一值',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='预案启用记录表';