DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_control_monitor_warning_model' AND column_name='police_category')
    THEN
        ALTER TABLE `t_control_monitor_warning_model` ADD COLUMN `police_category` varchar(255) NULL COMMENT '警种';
    END IF;

    IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_control_monitor_warning_model' AND column_name='clue_num')
    THEN
        ALTER TABLE `t_control_monitor_warning_model` ADD COLUMN `clue_num` bigint default 0 COMMENT '已推送线索数';
    END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;

INSERT INTO `t_control_monitor_warning_model`(`title`, `detail`, `type`, `icon_url`, `important_area_id`, `place_code`, `enable_status`, `recommend_label`, `recommend_district`, `police_category`, `clue_num`) select  'FK首次入区', 'FK首次入区', 4, NULL, NULL, NULL, 1, NULL, NULL, NULL, 0 from DUAL where not EXISTS(SELECT * from t_control_monitor_warning_model where title = 'FK首次入区');

INSERT INTO `t_control_monitor_warning_model`(`title`, `detail`, `type`, `icon_url`, `important_area_id`, `place_code`, `enable_status`, `recommend_label`, `recommend_district`, `police_category`, `clue_num`) select  'FK同杆徘徊', 'FK同杆徘徊', 4, NULL, NULL, NULL, 1, NULL, NULL, NULL, 0 from DUAL where not EXISTS(SELECT * from t_control_monitor_warning_model where title = 'FK同杆徘徊');

INSERT INTO `t_control_monitor_warning_model`(`title`, `detail`, `type`, `icon_url`, `important_area_id`, `place_code`, `enable_status`, `recommend_label`, `recommend_district`, `police_category`, `clue_num`) select  '静默', '静默', 4, NULL, NULL, NULL, 1, NULL, NULL, NULL, 0 from DUAL where not EXISTS(SELECT * from t_control_monitor_warning_model where title = '静默');