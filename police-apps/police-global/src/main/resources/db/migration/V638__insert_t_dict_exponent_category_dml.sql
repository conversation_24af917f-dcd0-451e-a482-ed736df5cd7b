DROP PROCEDURE IF EXISTS insert_t_dict_exponent_category;
CREATE PROCEDURE insert_t_dict_exponent_category()
BEGIN
    DECLARE pid INT;
    DECLARE pCode INT;
    INSERT INTO t_dict(`type`, `code`, `name`, `p_code`)
    SELECT 'exponent_category_group', 0, '档案配置-指数类别',0
    FROM dual
    WHERE NOT EXISTS (
        SELECT * FROM t_dict
        WHERE type = 'exponent_category_group' AND code = 0
    );
    select id into pid from t_dict where type = 'exponent_category_group' and code = 0;
    select code into pCode from t_dict where type = 'exponent_category_group' and code = 0;

    INSERT INTO t_dict(`p_id`, `type`, `code`,`name`, `p_code`)
    SELECT pid, 'exponent_category', 1, '安全指数',pCode
    FROM dual
    WHERE NOT EXISTS (
        SELECT * FROM t_dict
        WHERE type = 'exponent_category' AND code = 1
    );
    INSERT INTO t_dict(`p_id`, `type`, `code`,`name`, `p_code`)
    SELECT pid, 'exponent_category', 2, '治安指数',pCode
    FROM dual
    WHERE NOT EXISTS (
        SELECT * FROM t_dict
        WHERE type = 'exponent_category' AND code = 2
    );
    INSERT INTO t_dict(`p_id`, `type`, `code`,`name`, `p_code`)
    SELECT pid, 'exponent_category', 3, '稳定指数',pCode
    FROM dual
    WHERE NOT EXISTS (
        SELECT * FROM t_dict
        WHERE type = 'exponent_category' AND code = 3
    );

END;
CALL insert_t_dict_exponent_category();
DROP PROCEDURE IF EXISTS insert_t_dict_exponent_category;