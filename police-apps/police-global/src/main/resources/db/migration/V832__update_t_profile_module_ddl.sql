UPDATE t_profile_module SET table_schema = '{"name": "事件信息", "type": "TABLE_SCHEMA", "table": "t_profile_event", "fields": [{"db": {"table": "t_profile_event", "column": "name", "jdbcType": "string"}, "name": "name", "tableSchema": {"span": 2, "type": "string", "title": "事件名称", "copyable": false}}, {"db": {"table": "t_profile_event", "column": "event_label", "mapping": "label_id_array_to_name", "jdbcType": "json_id_array"}, "name": "event_label", "tableSchema": {"span": 1, "type": "label", "title": "事件类别", "copyable": false}}, {"db": {"table": "t_profile_event", "column": "source", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "profile_event_source"}, "name": "source", "tableSchema": {"span": 1, "type": "string", "title": "事件来源", "copyable": false}}, {"db": {"table": "t_profile_event", "column": "related_time", "mapping": "date_time_to_general_string", "jdbcType": "timestamp"}, "name": "related_time", "tableSchema": {"span": 1, "type": "string", "title": "指向时间", "copyable": false}}, {"db": {"table": "t_profile_event", "column": "related_address", "mapping": "map_location", "jdbcType": "string"}, "name": "related_address", "tableSchema": {"span": 2, "type": "mapLocation", "title": "指向地址", "copyable": false}}, {"db": {"table": "t_profile_event", "column": "source_time", "mapping": "date_time_to_general_string", "jdbcType": "timestamp"}, "name": "source_time", "tableSchema": {"span": 1, "type": "string", "title": "原发时间", "copyable": false}}, {"db": {"table": "t_profile_event", "column": "level", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "profile_event_level"}, "name": "level", "tableSchema": {"span": 1, "type": "string", "title": "事件级别", "copyable": false}}, {"db": {"table": "t_profile_event", "column": "belong_location", "mapping": "district_code_to_name", "jdbcType": "string"}, "name": "belong_location", "tableSchema": {"span": 1, "type": "string", "title": "归属地", "copyable": false}}, {"db": {"table": "t_profile_event", "column": "person_estimation", "jdbcType": "number"}, "name": "person_estimation", "tableSchema": {"span": 1, "type": "string", "title": "估计人数", "copyable": false}}, {"db": {"table": "t_profile_event", "column": "risk_level", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "profile_event_risk_level"}, "name": "risk_level", "tableSchema": {"span": 1, "type": "string", "title": "风险等级", "copyable": false}}, {"db": {"table": "t_profile_event", "column": "control_station", "mapping": "dept_code_to_dept_name", "jdbcType": "number"}, "name": "control_station", "tableSchema": {"span": 1, "type": "string", "title": "主管单位", "copyable": false}}, {"db": {"table": "t_profile_event", "column": "control_police", "mapping": "dept_code_to_dept_name", "jdbcType": "string"}, "name": "control_police", "tree": {"type": "dept"}, "tableSchema": {"span": 1, "type": "string", "title": "主责警种", "copyable": false}}, {"db": {"table": "t_profile_event", "column": "event_happened", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "profile_event_happened"}, "name": "event_happened", "tableSchema": {"span": 1, "type": "string", "title": "是否发生", "copyable": false}}, {"db": {"table": "t_profile_event", "column": "detail", "jdbcType": "string"}, "name": "detail", "tableSchema": {"span": 2, "type": "string", "title": "事件详情", "copyable": false}}, {"db": {"table": "t_profile_event", "column": "disposal_result", "jdbcType": "string"}, "name": "disposal_result", "tableSchema": {"span": 2, "type": "string", "title": "处置结果", "copyable": false}}, {"db": {"table": "t_profile_event", "column": "description", "jdbcType": "string"}, "name": "description", "tableSchema": {"span": 2, "type": "string", "title": "简要说明", "copyable": false}}], "moduleUi": {"column": 2, "bordered": true}}' WHERE id=208;
UPDATE t_profile_module SET form_schema = '{"name": "事件信息", "type": "FORM_SCHEMA", "table": "t_profile_event", "fields": [{"db": {"table": "t_profile_event", "column": "name", "jdbcType": "string"}, "name": "name", "formSchema": {"ui": {"ui:options": {"width": "1", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "事件名称"}}}, {"db": {"table": "t_profile_event", "column": "event_label", "mapping": "label_id_array_to_name", "jdbcType": "json_id_array"}, "name": "event_label", "tree": {"root": "event", "type": "label"}, "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "cascader", "multiple": true, "fieldNames": {"label": "name", "value": "id", "children": "children"}, "titleLocation": "left"}}, "schema": {"type": "array", "items": {"type": "array", "items": {"type": "number"}, "minItems": 1}, "title": "事件类别", "minItems": 1}}}, {"db": {"table": "t_profile_event", "column": "source", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "profile_event_source"}, "name": "source", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "select", "titleLocation": "left"}}, "schema": {"type": "number", "title": "事件来源"}}}, {"db": {"table": "t_profile_event", "column": "related_time", "mapping": "date_time_to_timestamp", "jdbcType": "timestamp"}, "name": "related_time", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "datePicker", "showTime": true, "titleLocation": "left"}}, "schema": {"type": "number", "title": "维权开始时间"}}}, {"db": {"table": "t_profile_event", "column": "related_end_time", "mapping": "date_time_to_timestamp", "jdbcType": "timestamp"}, "name": "related_end_time", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "datePicker", "showTime": true, "titleLocation": "left"}}, "schema": {"type": "number", "title": "维权结束时间"}}}, {"db": {"table": "t_profile_event", "column": "source_time", "mapping": "date_time_to_timestamp", "jdbcType": "timestamp"}, "name": "source_time", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "datePicker", "showTime": true, "titleLocation": "left"}}, "schema": {"type": "number", "title": "原发时间"}}}, {"db": {"table": "t_profile_event", "column": "level", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "profile_event_level"}, "name": "level", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "radio", "titleLocation": "left"}}, "schema": {"type": "number", "title": "事件级别"}}}, {"db": {"table": "t_profile_event", "column": "related_address", "jdbcType": "map"}, "name": "related_address", "formSchema": {"ui": {"ui:options": {"width": "1", "widget": "map", "titleLocation": "left"}}, "schema": {"type": "string", "title": "维权地址"}}}, {"db": {"table": "t_profile_event", "column": "belong_location", "mapping": "district_code_to_name", "jdbcType": "string"}, "name": "belong_location", "tree": {"root": "510600", "type": "district"}, "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "cascader", "multiple": false, "fieldNames": {"label": "name", "value": "code", "children": "children"}, "titleLocation": "left"}}, "schema": {"type": "string", "title": "归属地"}}}, {"db": {"table": "t_profile_event", "column": "person_estimation", "jdbcType": "number"}, "name": "person_estimation", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "number", "title": "估计人数"}}}, {"db": {"table": "t_profile_event", "column": "risk_level", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "profile_event_risk_level"}, "name": "risk_level", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "radio", "titleLocation": "left"}}, "schema": {"type": "number", "title": "风险等级"}}}, {"db": {"table": "t_profile_event", "column": "control_station", "mapping": "dept_code_to_dept_name", "jdbcType": "number"}, "name": "control_station", "tree": {"root": "510600000000", "type": "dept"}, "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "cascader", "multiple": false, "fieldNames": {"label": "shortName", "value": "deptCode", "children": "children"}, "titleLocation": "left", "notOnlyChildren": true}}, "schema": {"type": "string", "title": "主管单位"}}}, {"db": {"table": "t_profile_event", "column": "control_police", "jdbcType": "string"}, "name": "controlPolice", "tree": {"root": "510500000000", "type": "dept"}, "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "cascader", "multiple": false, "fieldNames": {"label": "shortName", "value": "deptCode", "children": "children"}, "titleLocation": "left", "notOnlyChildren": true}}, "schema": {"type": "string", "title": "主责警种"}}}, {"db": {"table": "t_profile_event", "column": "detail", "jdbcType": "string"}, "name": "detail", "formSchema": {"ui": {"ui:options": {"width": "1", "widget": "textarea", "titleLocation": "left"}}, "schema": {"type": "string", "title": "事件详情"}}}, {"db": {"table": "t_profile_event", "column": "disposal_result", "jdbcType": "string"}, "name": "disposal_result", "formSchema": {"ui": {"ui:options": {"width": "1", "widget": "textarea", "titleLocation": "left"}}, "schema": {"type": "string", "title": "处置结果"}}}, {"db": {"table": "t_profile_event", "column": "description", "jdbcType": "string"}, "name": "description", "formSchema": {"ui": {"ui:options": {"width": "1", "widget": "textarea", "titleLocation": "left"}}, "schema": {"type": "string", "title": "简要说明"}}}], "required": ["name", "event_label", "source", "related_time"]}' WHERE id=208;