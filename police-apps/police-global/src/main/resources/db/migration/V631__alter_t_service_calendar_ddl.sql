DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_service_calendar' AND column_name='mgr')
    THEN
ALTER TABLE t_service_calendar ADD mgr longtext NULL COMMENT '敏感日';
END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;