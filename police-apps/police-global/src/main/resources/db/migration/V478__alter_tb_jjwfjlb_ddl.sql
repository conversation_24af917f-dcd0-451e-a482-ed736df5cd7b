DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='tb_jjwfjlb' AND column_name='exported_inform')
    THEN
        ALTER TABLE tb_jjwfjlb ADD exported_inform TINYINT DEFAULT 0 NULL COMMENT '是否导出告知书，0：未导出，1：已导出';
    END IF;
    IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='tb_jjwfjlb' AND column_name='exported_confirm')
    THEN
        ALTER TABLE tb_jjwfjlb ADD exported_confirm TINYINT DEFAULT 0 NULL COMMENT '是否导出通知书跟确认书，0：未导出，1：已导出';
    END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;