INSERT IGNORE INTO t_schedule_job_config
(id, module, operation, cron, description, enable, `type`)
VALUES(1, '101', 'expire', '0 0 0 * * ?', NULL, 1, 'CRON_JOB');
INSERT IGNORE INTO t_schedule_job_config
(id, module, operation, cron, description, enable, `type`)
VALUES(2, '103', 'risk_sign', NULL, NULL, 1, 'DELAY_MESSAGE');
INSERT IGNORE INTO t_schedule_job_config
(id, module, operation, cron, description, enable, `type`)
VALUES(3, '103', 'risk_judge', NULL, NULL, 1, 'DELAY_MESSAGE');
INSERT IGNORE INTO t_schedule_job_config
(id, module, operation, cron, description, enable, `type`)
VALUES(4, '103', 'risk_process_sign', NULL, NULL, 1, 'DELAY_MESSAGE');
INSERT IGNORE INTO t_schedule_job_config
(id, module, operation, cron, description, enable, `type`)
VALUES(5, '103', 'risk_process_feedback', '0 0 0 * * ?', NULL, 1, 'CRON_JOB');
INSERT IGNORE INTO t_schedule_job_config
(id, module, operation, cron, description, enable, `type`)
VALUES(6, '5', 'expire', '0 0 0 * * ?', NULL, 1, 'CRON_JOB');
INSERT IGNORE INTO t_schedule_job_config
(id, module, operation, cron, description, enable, `type`)
VALUES(7, '5', 'synchronize', '0 0 0 * * ?', NULL, 1, 'CRON_JOB');
INSERT IGNORE INTO t_schedule_job_config
(id, module, operation, cron, description, enable, `type`)
VALUES(8, '101', 'synchronize', '0 0 0 * * ?', NULL, 1, 'CRON_JOB');
