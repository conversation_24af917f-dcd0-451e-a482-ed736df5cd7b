CREATE  OR REPLACE VIEW t_control_warning_source_view AS
SELECT s.id AS 'id',
s.update_time AS 'update_time',
s.create_time AS 'create_time',
s.code AS 'code',
(select d.dict_desc from t_dict d where d.type='control_warning_source_type' and d.code=s.type ) AS 'type',
if(s.category is null,null,concat('areaLabel_',s.category)) AS 'hitTag',
ST_X(s.point) AS 'latitude',
ST_Y(s.point) AS 'longitude',
s.name AS 'name',
s.address AS 'address',
s.district_name AS 'district'
FROM t_control_warning_source s;
