CREATE TABLE IF NOT EXISTS `t_schedule_job`
(
    `id`         bigint    NOT NULL AUTO_INCREMENT COMMENT '主键',
    `config_id`  bigint    NOT NULL COMMENT '配置id',
    `module`     varchar(100)   DEFAULT NULL COMMENT '模块id',
    `operation`  varchar(100)   DEFAULT NULL COMMENT '操作',
    `related_id` bigint         DEFAULT NULL COMMENT '关联对象id',
    `time_limit` timestamp NULL DEFAULT NULL COMMENT '到期时间',
    PRIMARY KEY (`id`)
) COMMENT '定时任务表';
CREATE INDEX t_schedule_job_config_id_IDX USING BTREE ON t_schedule_job (config_id);



CREATE TABLE IF NOT EXISTS `t_schedule_log`
(
    `id`           bigint    NOT NULL AUTO_INCREMENT COMMENT '主键',
    `execute_time` timestamp NOT NULL COMMENT '执行时间',
    `job_id`       bigint    NOT NULL COMMENT '任务id',
    `success`      tinyint   NOT NULL COMMENT '是否成功',
    PRIMARY KEY (`id`)
) COMMENT '定时任务执行记录表';
CREATE INDEX t_schedule_log_job_id_IDX USING BTREE ON t_schedule_log (job_id);
