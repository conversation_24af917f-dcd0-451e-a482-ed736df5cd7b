DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_profile_sthy' AND column_name='jd')
    THEN
ALTER TABLE t_profile_sthy ADD jd varchar(100) NULL COMMENT '经度';
END IF;
IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_profile_sthy' AND column_name='wd')
    THEN
ALTER TABLE t_profile_sthy ADD wd varchar(100) NULL COMMENT '纬度';
END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;