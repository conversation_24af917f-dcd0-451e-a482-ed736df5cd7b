update t_request_params set params = '{"filterParams": [{"key": "riskLevel", "type": "option", "value": ["%%risk_level%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "风险等级"}, {"key": "overdueStatus", "type": "option", "value": [{"code": "1", "name": "已逾期"}, {"code": "0", "name": "未逾期"}], "linkedKey": ["overdueType"], "fieldNames": {"label": "name", "value": "code"}, "displayName": "逾期状态"}, {"key": "overdueType", "type": "option", "value": [{"code": "1", "name": "签收逾期"}, {"code": "2", "name": "反馈逾期"}, {"code": "3", "name": "办结逾期"}], "enableInfo": {"key": "overdueStatus", "value": 1}, "fieldNames": {"label": "name", "value": "code"}, "displayName": "逾期类型"}, {"key": "resolveStatus", "type": "option", "value": [{"code": "1", "name": "已化解"}, {"code": "0", "name": "未化解"}], "fieldNames": {"label": "name", "value": "code"}, "displayName": "化解状态"}, {"key": "handOverStatus", "type": "option", "value": [{"code": "1", "name": "已移交"}, {"code": "0", "name": "未移交"}], "fieldNames": {"label": "name", "value": "code"}, "displayName": "移交党政"}, {"key": "riskStatus", "type": "select", "value": [{"code": "3", "name": "待签收"}, {"code": "6", "name": "待研判"}, {"code": "8", "name": "在办中"}, {"code": "7", "name": "待办结"}, {"code": "9", "name": "已办结"}, {"code": "10", "name": "不处置"}], "fieldNames": {"label": "name", "value": "code"}, "displayName": "风险状态"}, {"key": "riskType", "type": "select", "value": ["%%risk_type%%"], "fieldNames": {"label": "name", "value": "code"}, "displayName": "风险类别"}, {"key": "source", "type": "select", "value": ["%%risk_source%%"], "fieldNames": {"label": "name", "value": "code"}, "displayName": "风险来源"}, {"key": "responsibleDept", "type": "multiple-tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "id", "children": "children"}, "displayName": "主责单位"}, {"key": "handleDept", "type": "multiple-tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "id", "children": "children"}, "displayName": "处置单位"}, {"key": "timeLimit", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "反馈时限"}, {"key": "warningTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "预警时间"}], "searchFields": [{"key": "title", "name": "风险标题"}, {"key": "riskCode", "name": "风险编号"}, {"key": "content", "name": "风险概述"}]}'
where module = 'risk-list-standing-book-responsiblePerson';

update t_request_params set params = '{"filterParams": [{"key": "riskLevel", "type": "option", "value": ["%%risk_level%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "风险等级"}, {"key": "overdueStatus", "type": "option", "value": [{"code": "1", "name": "已逾期"}, {"code": "0", "name": "未逾期"}], "linkedKey": ["overdueType"], "fieldNames": {"label": "name", "value": "code"}, "displayName": "逾期状态"}, {"key": "overdueType", "type": "option", "value": [{"code": "1", "name": "签收逾期"}, {"code": "2", "name": "反馈逾期"}, {"code": "3", "name": "办结逾期"}], "enableInfo": {"key": "overdueStatus", "value": 1}, "fieldNames": {"label": "name", "value": "code"}, "displayName": "逾期类型"}, {"key": "resolveStatus", "type": "option", "value": [{"code": "1", "name": "已化解"}, {"code": "0", "name": "未化解"}], "fieldNames": {"label": "name", "value": "code"}, "displayName": "化解状态"}, {"key": "handOverStatus", "type": "option", "value": [{"code": "1", "name": "已移交"}, {"code": "0", "name": "未移交"}], "fieldNames": {"label": "name", "value": "code"}, "displayName": "移交党政"}, {"key": "riskStatus", "type": "select", "value": [{"code": "1", "name": "待分派"}, {"code": "2", "name": "已退回"}, {"code": "3", "name": "待签收"}, {"code": "6", "name": "待研判"}, {"code": "8", "name": "在办中"}, {"code": "7", "name": "待办结"}, {"code": "9", "name": "已办结"}, {"code": "10", "name": "不处置"}], "fieldNames": {"label": "name", "value": "code"}, "displayName": "风险状态"}, {"key": "riskType", "type": "select", "value": ["%%risk_type%%"], "fieldNames": {"label": "name", "value": "code"}, "displayName": "风险类别"}, {"key": "source", "type": "select", "value": ["%%risk_source%%"], "fieldNames": {"label": "name", "value": "code"}, "displayName": "风险来源"}, {"key": "responsibleDept", "type": "multiple-tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "id", "children": "children"}, "displayName": "主责单位"}, {"key": "handleDept", "type": "multiple-tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "id", "children": "children"}, "displayName": "处置单位"}, {"key": "timeLimit", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "反馈时限"}, {"key": "warningTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "预警时间"}], "searchFields": [{"key": "title", "name": "风险标题"}, {"key": "riskCode", "name": "风险编号"}, {"key": "content", "name": "风险概述"}]}'
where module = 'risk-list-standing-book-dispatcher';

update t_request_params set params = '{"filterParams": [{"key": "riskLevel", "type": "option", "value": ["%%risk_level%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "风险等级"}, {"key": "overdueStatus", "type": "option", "value": [{"code": "1", "name": "已逾期"}, {"code": "0", "name": "未逾期"}], "linkedKey": ["overdueType"], "fieldNames": {"label": "name", "value": "code"}, "displayName": "逾期状态"}, {"key": "overdueType", "type": "option", "value": [{"code": "1", "name": "签收逾期"}, {"code": "2", "name": "反馈逾期"}, {"code": "3", "name": "办结逾期"}], "enableInfo": {"key": "overdueStatus", "value": 1}, "fieldNames": {"label": "name", "value": "code"}, "displayName": "逾期类型"}, {"key": "resolveStatus", "type": "option", "value": [{"code": "1", "name": "已化解"}, {"code": "0", "name": "未化解"}], "fieldNames": {"label": "name", "value": "code"}, "displayName": "化解状态"}, {"key": "handOverStatus", "type": "option", "value": [{"code": "1", "name": "已移交"}, {"code": "0", "name": "未移交"}], "fieldNames": {"label": "name", "value": "code"}, "displayName": "移交党政"}, {"key": "riskStatus", "type": "select", "value": [{"code": "3", "name": "待签收"}, {"code": "4", "name": "已签收"}, {"code": "5", "name": "已反馈"}, {"code": "7", "name": "待办结"}, {"code": "9", "name": "已办结"}, {"code": "10", "name": "不处置"}], "fieldNames": {"label": "name", "value": "code"}, "displayName": "风险状态"}, {"key": "riskType", "type": "select", "value": ["%%risk_type%%"], "fieldNames": {"label": "name", "value": "code"}, "displayName": "风险类别"}, {"key": "source", "type": "select", "value": ["%%risk_source%%"], "fieldNames": {"label": "name", "value": "code"}, "displayName": "风险来源"}, {"key": "responsibleDept", "type": "multiple-tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "id", "children": "children"}, "displayName": "主责单位"}, {"key": "handleDept", "type": "multiple-tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "id", "children": "children"}, "displayName": "处置单位"}, {"key": "timeLimit", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "反馈时限"}, {"key": "warningTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "预警时间"}], "searchFields": [{"key": "title", "name": "风险标题"}, {"key": "riskCode", "name": "风险编号"}, {"key": "content", "name": "风险概述"}]}'
where module = 'risk-list-handle';

update t_request_params set params = '{"filterParams": [{"key": "riskLevel", "type": "option", "value": ["%%risk_level%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "风险等级"}, {"key": "overdueStatus", "type": "option", "value": [{"code": "1", "name": "已逾期"}, {"code": "0", "name": "未逾期"}], "linkedKey": ["overdueType"], "fieldNames": {"label": "name", "value": "code"}, "displayName": "逾期状态"}, {"key": "overdueType", "type": "option", "value": [{"code": "1", "name": "签收逾期"}, {"code": "2", "name": "反馈逾期"}, {"code": "3", "name": "办结逾期"}], "enableInfo": {"key": "overdueStatus", "value": 1}, "fieldNames": {"label": "name", "value": "code"}, "displayName": "逾期类型"}, {"key": "resolveStatus", "type": "option", "value": [{"code": "1", "name": "已化解"}, {"code": "0", "name": "未化解"}], "fieldNames": {"label": "name", "value": "code"}, "displayName": "化解状态"}, {"key": "handOverStatus", "type": "option", "value": [{"code": "1", "name": "已移交"}, {"code": "0", "name": "未移交"}], "fieldNames": {"label": "name", "value": "code"}, "displayName": "移交党政"}, {"key": "riskStatus", "type": "select", "value": [{"code": "1", "name": "待分派"}, {"code": "3", "name": "待签收"}, {"code": "6", "name": "待研判"}, {"code": "8", "name": "在办中"}, {"code": "7", "name": "待办结"}, {"code": "9", "name": "已办结"}, {"code": "2", "name": "已退回"}, {"code": "10", "name": "不处置"}], "fieldNames": {"label": "name", "value": "code"}, "displayName": "风险状态"}, {"key": "riskType", "type": "select", "value": ["%%risk_type%%"], "fieldNames": {"label": "name", "value": "code"}, "displayName": "风险类别"}, {"key": "source", "type": "select", "value": ["%%risk_source%%"], "fieldNames": {"label": "name", "value": "code"}, "displayName": "风险来源"}, {"key": "responsibleDept", "type": "multiple-tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "id", "children": "children"}, "displayName": "主责单位"}, {"key": "handleDept", "type": "multiple-tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "id", "children": "children"}, "displayName": "处置单位"}, {"key": "timeLimit", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "反馈时限"}, {"key": "warningTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "预警时间"}], "searchFields": [{"key": "title", "name": "风险标题"}, {"key": "riskCode", "name": "风险编号"}, {"key": "content", "name": "风险概述"}]}'
where module = 'risk-list-copy';

