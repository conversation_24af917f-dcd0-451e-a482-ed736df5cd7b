DROP TABLE IF EXISTS `tb_fx_action_excavate_dict`;
CREATE TABLE `tb_fx_action_excavate_dict`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '词典名字',
  `name_alias` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '名字别名',
  `enable` tinyint(4) NULL DEFAULT 1 COMMENT '逻辑删除标识',
  `parent_id` int(11) NULL DEFAULT 0 COMMENT '父节点id',
  `search_key` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '路径枚举',
  `tree_level` int(11) NULL DEFAULT NULL COMMENT '树深度',
  `node_type` int(11) NULL DEFAULT NULL COMMENT '节点类型',
  `sort` int(11) NOT NULL DEFAULT 10000 COMMENT '排序',
  `score` int(11) NULL DEFAULT NULL COMMENT '分值',
  `tag_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '标签类型',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `node_type`(`node_type`) USING BTREE,
  INDEX `enable`(`enable`) USING BTREE,
  INDEX `tree_level`(`tree_level`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 241 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;