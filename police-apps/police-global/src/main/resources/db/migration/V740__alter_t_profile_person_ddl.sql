DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS( SELECT * FROM information_schema.columns WHERE table_schema=(select database()) AND table_name='t_profile_person' AND column_name='zjhm')
    THEN
ALTER TABLE t_profile_person ADD zjhm varchar(255) NULL COMMENT '身份证';
END IF;
    IF NOT EXISTS( SELECT * FROM information_schema.columns WHERE table_schema=(select database()) AND table_name='t_profile_person' AND column_name='social_relation')
    THEN
ALTER TABLE t_profile_person ADD social_relation varchar(255) NULL COMMENT '社会关系';
END IF;
    IF NOT EXISTS( SELECT * FROM information_schema.columns WHERE table_schema=(select database()) AND table_name='t_profile_person' AND column_name='family_relation')
    THEN
ALTER TABLE t_profile_person ADD family_relation varchar(255) NULL COMMENT '家庭关系';
END IF;
    IF NOT EXISTS( SELECT * FROM information_schema.columns WHERE table_schema=(select database()) AND table_name='t_profile_person' AND column_name='mac')
    THEN
ALTER TABLE t_profile_person ADD mac varchar(255) NULL COMMENT 'mac完整性信息';
END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;