DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_warning' AND column_name='area_id')
    THEN
ALTER TABLE t_warning  ADD COLUMN `area_id` json  DEFAULT NULL COMMENT '命中的区域的id' ;
END IF;
END $$
DELIMITER ;
CALL add_column;

DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_warning' AND column_name='place_code')
    THEN
ALTER TABLE t_warning  ADD COLUMN `place_code` json  DEFAULT NULL COMMENT '命中的场所code' ;
END IF;
END $$
DELIMITER ;
CALL add_column;


DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_warning_track' AND column_name='source_type')
    THEN
ALTER TABLE t_warning_track  ADD COLUMN `source_type` bigint  DEFAULT NULL COMMENT '感知原类型' ;
END IF;
END $$
DELIMITER ;
CALL add_column;


update t_warning set area_id=(select JSON_ARRAYAGG(important_area_id) from t_control_monitor_warning_model where important_area_id is not null and important_area_id>0 and id member of(model_id));
update t_warning set place_code=(select JSON_ARRAYAGG(place_code) from t_control_monitor_warning_model where place_code is not null and id member of(model_id));
update t_warning_track set source_type=(select type from t_control_warning_source where unique_key=source_id);