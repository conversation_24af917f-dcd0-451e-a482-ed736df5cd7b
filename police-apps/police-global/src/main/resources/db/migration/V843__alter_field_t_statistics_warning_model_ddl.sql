DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    -- 给t_statistics_warning_model添加字段，判断第三方模型数据来源
    IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_statistics_warning_model' AND column_name='table_schema')
    THEN
        ALTER TABLE t_statistics_warning_model ADD table_schema json NULL COMMENT '数据能力第三方模型查表信息';
    END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;