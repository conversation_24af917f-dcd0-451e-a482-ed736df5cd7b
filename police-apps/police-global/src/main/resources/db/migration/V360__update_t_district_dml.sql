drop procedure if exists add_col;
-- 定义新的分隔符
DELIMITER //
-- 创建存储过程
CREATE PROCEDURE add_col()-- 新增一个存储过程
BEGIN

	IF EXISTS(SELECT 1
                  FROM t_district
                  WHERE `code`='000000')
    THEN
UPDATE `t_district` SET `contour`=ST_GeomFromText('GEOMETRYCOLLECTION(POLYGON((40.06865347 72.80498613, 42.41708939 77.60799891, 49.76268374 86.76566471, 46.62549398 93.01562206, 43.49950957 98.80844287, 42.95018439 108.71888678, 46.86974087 114.70072769, 50.66458126 115.85202066, 54.38625138 122.52512946,  48.13185068 136.78384845, 42.31026795 131.58512645, 39.28255235 124.94187864, 34.14369403 121.37969987, 27.22567109 124.40339946, 20.71804764 121.63783138, 3.43640892 112.26302743, 5.42855305 107.7509117, 17.1304473 109.2644763,  20.805068 100.6376429, 30.4278064 78.45798122, 39.89643428 72.65397861, 40.06865347 72.80498613)))'), `center`=ST_GeomFromText('POINT(30.659462 104.065735)') where `code`='000000';

END IF;

    IF not EXISTS(SELECT 1
                  FROM t_district
                  WHERE `code`='000000')
    THEN
		INSERT INTO `t_district` (`id`, `code`, `name`, `level`, `p_code`, `contour`, `center`, `path`) VALUES (211, '000000', '中华人民共和国', 0, '0', ST_GeomFromText('GEOMETRYCOLLECTION(POLYGON((40.06865347 72.80498613, 42.41708939 77.60799891, 49.76268374 86.76566471, 46.62549398 93.01562206, 43.49950957 98.80844287, 42.95018439 108.71888678, 46.86974087 114.70072769, 50.66458126 115.85202066, 54.38625138 122.52512946,  48.13185068 136.78384845, 42.31026795 131.58512645, 39.28255235 124.94187864, 34.14369403 121.37969987, 27.22567109 124.40339946, 20.71804764 121.63783138, 3.43640892 112.26302743, 5.42855305 107.7509117, 17.1304473 109.2644763,  20.805068 100.6376429, 30.4278064 78.45798122, 39.89643428 72.65397861, 40.06865347 72.80498613)))'), ST_GeomFromText('POINT(30.659462 104.065735)'), '-');

END IF;


END //
-- 还原默认分隔符
DELIMITER ;
-- 调用存储过程
call add_col();
-- 清除存储过程
drop procedure if exists add_col;