DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='tb_jjwfjlb' AND column_name='image_flag')
    THEN
        ALTER TABLE tb_jjwfjlb ADD image_flag varchar(50) NULL COMMENT '图片标识；Base64，url';
    END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;