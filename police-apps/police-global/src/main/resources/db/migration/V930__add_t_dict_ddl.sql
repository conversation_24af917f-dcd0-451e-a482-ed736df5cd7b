DELIMITER $$
DROP PROCEDURE IF EXISTS `insert_dict_data` $$
CREATE PROCEDURE insert_dict_data()
BEGIN
    DECLARE pid INT;
    DECLARE dsjid INT;
		DELETE FROM t_dict WHERE type like 'other_content_type%';
    INSERT INTO t_dict (`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        ('other_content_type_group',0,'其他内容类型',0,'otherContentType',0,NULL,NULL,NULL,1);
		SET pid = LAST_INSERT_ID();
		UPDATE t_dict SET p_id = pid WHERE type = 'other_content_type_group' and code = 0;

		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'other_content_type',1,'重要会议及勤务',0,'zyhyjqw',1,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'other_content_type',2,'指挥部建设',0,'zhbjs',2,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'other_content_type',3,'重大案件',0,'zdaj',3,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'other_content_type',4,'涉稳聚集',0,'swjj',4,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'other_content_type',5,'重要舆情',0,'zyyq',5,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'other_content_type',6,'灾害事故',0,'zhsg',6,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'other_content_type',7,'涉警警情',0,'sjjq',7,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'other_content_type',8,'非正常死亡',0,'fzcsw',8,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'other_content_type',9,'其他',0,'qt',9,NULL,null,NULL,1);
END $$
DELIMITER ;
CALL insert_dict_data;
DROP PROCEDURE IF EXISTS insert_dict_data;