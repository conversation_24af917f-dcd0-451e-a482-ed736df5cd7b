CREATE TABLE IF NOT EXISTS `tb_common_bigscreen_important_person` (
     `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
     `person_code` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '人员编号',
     `id_card` varchar(18) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '身份号码',
     `domicile_place_code` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '户籍地代码',
     `birthday` date DEFAULT NULL COMMENT '出生日期',
     `name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '姓名',
     `name_pinyin` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '姓名拼音',
     `nation_code` varchar(10) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '民族代码',
     `nation_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '民族名称',
     `gender_code` varchar(10) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '性别代码',
     `gender_name` varchar(10) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '性别名称',
     `mobile_phone` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '手机号',
     `domicile_place_address` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '户籍地址',
     `current_place_address` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '现居地址',
     `control_level_code` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '管控等级代码',
     `control_level_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '管控等级名称',
     `control_status_code` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '管控状态代码',
     `control_status_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '管控状态名称',
     `person_category_code` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '人员类别代码',
     `person_category_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '人员类别名称',
     `person_category_tree` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '人员类别代码树',
     `police_org_code` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '责任派出所代码',
     `police_org_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '责任派出所名称',
     `police_org_identifier` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '责任派出所标识符',
     `police_responsible_staff_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '责任民警ID',
     `police_responsible_staff_card_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '责任民警证件ID',
     `police_responsible_staff_position_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '责任民警职位名称',
     `police_responsible_staff_phone` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '责任民警电话',
     `gov_address_code` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '政府地址代码',
     `gov_responsible_staff_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '政府责任人姓名',
     `gov_responsible_staff_position` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '政府责任人职位',
     `gov_responsible_staff_phone` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '政府责任人电话',
     `personnel_tags_code` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '人员标签代码',
     `personnel_tags_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '人员标签名称',
     `create_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建用户ID',
     `create_user_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建用户名',
     `create_terminal_type` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建终端类型',
     `create_terminal_identify` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建终端标识',
     `create_user_org_code` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建用户组织代码',
     `create_user_org_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建用户组织名称',
     `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
     `modify_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改用户ID',
     `modify_user_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改用户名',
     `modify_terminal_type` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改终端类型',
     `modify_terminal_identify` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改终端标识',
     `modify_user_org_code` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改用户组织代码',
     `modify_user_org_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改用户组织名称',
     `modify_time` timestamp NULL DEFAULT NULL COMMENT '修改时间',
     `valid` int DEFAULT NULL COMMENT '是否有效',
     `police_responsible_staff_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '责任民警姓名',
     `id_type_code` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '证件类型代码',
     `id_type_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '证件类型名称',
     `domicile_place_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '户籍地名称',
     `gov_address_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '政府地址名称',
     `last_uncontrolled_time` timestamp NULL DEFAULT NULL COMMENT '最后一次失控时间',
     `police_category_code` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '警种代码',
     `police_category_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '警种名称',
     `license_plate_number` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '车牌号',
     `belong_area_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '所属区域名称',
     `belong_area_code` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '所属区域代码',
     `score` int DEFAULT NULL COMMENT '人员积分',
     `recent_occurrence_location` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最近发生地点',
     `foothold` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '根据地',
     `duty_county_code` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '责任区县代码',
     `duty_county_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '责任区县名称',
     `functionary` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '职能',
     `company_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '公司名称',
     `police_category_org_code` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '警种类别组织代码',
     `police_category_org_identifier` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '警种类别组织标识符',
     `police_category_org_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '警种类别组织名称',
     `notice_control` int DEFAULT NULL COMMENT '通知管控',
     `person_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '人员id',
     `photo` varchar(200) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '图片链接',
     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;