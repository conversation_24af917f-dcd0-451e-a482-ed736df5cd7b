delete from t_search_schema where module_name='主题库' AND en_name IN ('archive_person','archive_vehicle','archive_case','archive_phone');
INSERT INTO t_search_schema
(id, update_user_id, update_dept_id, update_time, `type`, sub_type, zh_name, en_name, last_synchronization_time, main_table_en_name, relate_field, is_main, module_name, data_total_count, last_data_total_count_time, related_archives, last_data_in_time, is_del, id_field, data_time_field, `order`)
VALUES(0, null, null, now(), '', '', '人员档案', 'archive_person', now(), null, null, 1, '主题库', 1, now(), 'person', now(), 0, 'zjhm', null, 1);
INSERT INTO t_search_schema
(id, update_user_id, update_dept_id, update_time, `type`, sub_type, zh_name, en_name, last_synchronization_time, main_table_en_name, relate_field, is_main, module_name, data_total_count, last_data_total_count_time, related_archives, last_data_in_time, is_del, id_field, data_time_field, `order`)
VALUES(0, null, null, now(), '', '', '车辆档案', 'archive_vehicle', now(), null, null, 1, '主题库', 1, now(), 'car', now(), 0, 'hphm', null, 2);
INSERT INTO t_search_schema
(id, update_user_id, update_dept_id, update_time, `type`, sub_type, zh_name, en_name, last_synchronization_time, main_table_en_name, relate_field, is_main, module_name, data_total_count, last_data_total_count_time, related_archives, last_data_in_time, is_del, id_field, data_time_field, `order`)
VALUES(0, null, null, now(), '', '', '案件档案', 'archive_case', now(), null, null, 1, '主题库', 1, now(), 'case', now(), 0, 'ajbh', null, 3);
INSERT INTO t_search_schema
(id, update_user_id, update_dept_id, update_time, `type`, sub_type, zh_name, en_name, last_synchronization_time, main_table_en_name, relate_field, is_main, module_name, data_total_count, last_data_total_count_time, related_archives, last_data_in_time, is_del, id_field, data_time_field, `order`)
VALUES(0, null, null, now(), '', '', '手机档案', 'archive_phone', now(), null, null, 1, '主题库', 1, now(), 'phone', now(), 0, 'sjhm', null, 4);