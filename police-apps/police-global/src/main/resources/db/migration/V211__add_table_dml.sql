CREATE TABLE if not exists`hk_cameras` (
                              `id` bigint NOT NULL AUTO_INCREMENT,
                              `name` varchar(255)  DEFAULT NULL,
                              `camera_index_code` varchar(255)  NOT NULL,
                              `longitude` double(10,6) DEFAULT NULL,
                              `latitude` double(10,6) DEFAULT NULL,
                              `status` varchar(255) DEFAULT NULL,
                              `status_name` varchar(255)  DEFAULT NULL,
                              `point` point NOT NULL,
                              PRIMARY KEY (`id`) USING BTREE,
                              UNIQUE KEY `camera_index_code_idx` (`camera_index_code`) USING BTREE,
                              SPATIAL KEY `point_idx` (`point`)
);

