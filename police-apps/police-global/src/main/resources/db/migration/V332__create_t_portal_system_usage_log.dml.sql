DROP TABLE IF EXISTS `t_portal_system_usage_log`;
CREATE TABLE `t_portal_system_usage_log`  (
                                              `id` int(11) NOT NULL,
                                              `app_id` int(11) NULL DEFAULT NULL COMMENT '应用id',
                                              `app_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '应用名',
                                              `id_entity_card` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '登录用户身份证',
                                              `user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '登录用户姓名',
                                              `org_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '登录用户所属部门code',
                                              `org_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '登录用户所属部门名称',
                                              `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                              PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- 2023-12-07 增加sql
-- ----------------------------

DROP TABLE IF EXISTS `t_portal_ext_user`;
CREATE TABLE `t_portal_ext_user` (
                                     `id` int(11) NOT NULL COMMENT '主键',
                                     `position_id` int(11) DEFAULT NULL COMMENT '岗位id',
                                     `position_name` varchar(255) DEFAULT NULL COMMENT '岗位名称',
                                     `is_virtual_user` varchar(20) DEFAULT NULL COMMENT '是否询用户（01-是 02-不是）',
                                     `login_name` varchar(255) DEFAULT NULL COMMENT '登录账号',
                                     `password` varchar(255) DEFAULT NULL COMMENT '密码',
                                     `user_name` varchar(255) DEFAULT NULL COMMENT '用户昵称',
                                     `user_type` varchar(255) DEFAULT NULL COMMENT '用户类型（01-系统用户，02-注册用户）',
                                     `email` varchar(255) DEFAULT NULL COMMENT '邮箱',
                                     `mobile` varchar(255) DEFAULT NULL COMMENT '移动电话',
                                     `tele_phone` varchar(255) DEFAULT NULL COMMENT '电话号码',
                                     `id_entity_card` varchar(255) DEFAULT NULL COMMENT '身份证号',
                                     `type_name` varchar(255) DEFAULT NULL COMMENT '行业类型（正式民警，失业编制人员）',
                                     `in_dust_rial_id` int(11) DEFAULT NULL COMMENT '行业号码（如：警号）',
                                     `sex` varchar(255) DEFAULT NULL COMMENT '性别(1-男 2-女 3-未知)',
                                     `nation` varchar(255) DEFAULT NULL COMMENT '民族',
                                     `politic` varchar(255) DEFAULT NULL COMMENT '政治面貌',
                                     `marital` varchar(255) DEFAULT NULL COMMENT '婚姻状态',
                                     `type` varchar(255) DEFAULT NULL COMMENT '人员类别（01-民警 02-辅警）',
                                     `whcd` varchar(255) DEFAULT NULL COMMENT '文化程度',
                                     `birthday` varchar(16) DEFAULT NULL COMMENT '出生日期',
                                     `begin_time` datetime DEFAULT NULL,
                                     `end_time` datetime DEFAULT NULL,
                                     `salt` varchar(20) DEFAULT NULL COMMENT '盐加密',
                                     `xt_zxbz` int(1) DEFAULT NULL,
                                     `xt_cjsj` datetime DEFAULT NULL COMMENT '采集时间',
                                     `xt_zhxgsj` date DEFAULT NULL,
                                     `xt_zhxgrxm` varchar(255) DEFAULT NULL,
                                     `xt_zhxgid` int(11) DEFAULT NULL,
                                     `xt_zhxgrbm` varchar(255) DEFAULT NULL,
                                     `xt_zhxgrbmid` int(11) DEFAULT NULL,
                                     `xt_zhxgrip` varchar(255) DEFAULT NULL,
                                     `avatar` varchar(255) DEFAULT NULL COMMENT '用户头像',
                                     `user_status` int(11) NOT NULL DEFAULT 0,
                                     `app_ids` varchar(255) DEFAULT null,
                                     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                     `create_user_id` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                     `create_dept_id` bigint(20) DEFAULT NULL,
                                     `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                     `update_user_id` bigint(20) DEFAULT NULL COMMENT '更新人id',
                                     `update_dept_id` bigint(20) DEFAULT NULL,
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

DROP TABLE IF EXISTS `t_portal_ext_dept`;
CREATE TABLE `t_portal_ext_dept` (
                                     `id` int(11) NOT NULL COMMENT '部门id',
                                     `org_code` varchar(30) DEFAULT NULL COMMENT '部门code',
                                     `org_name` varchar(30) DEFAULT NULL COMMENT '部门名称',
                                     `root_path` varchar(255) DEFAULT NULL COMMENT '根路径开始的id',
                                     `org_type` int(2) DEFAULT NULL COMMENT '部门类型',
                                     `org_type_name` varchar(255) DEFAULT NULL COMMENT '部门类型名称',
                                     `org_level` varchar(20) DEFAULT NULL COMMENT '部门等级',
                                     `org_biz_type` varchar(20) DEFAULT NULL COMMENT '部门业务类型',
                                     `org_no` varchar(30) DEFAULT NULL COMMENT '部门顺序号',
                                     `parent_id` int(11) DEFAULT NULL COMMENT '商机部门逐渐',
                                     `bmpyszm` varchar(255) DEFAULT NULL COMMENT '部门名字拼音首字母',
                                     `org_jc` varchar(255) DEFAULT NULL COMMENT '部门简称',
                                     `org_qc` varchar(255) DEFAULT NULL COMMENT '全称',
                                     `address` varchar(255) DEFAULT NULL COMMENT '地址',
                                     `link_tel` varchar(255) DEFAULT NULL COMMENT '电话',
                                     `link_man` varchar(255) DEFAULT NULL COMMENT '联系人',
                                     `link_man_tel` varchar(255) DEFAULT NULL COMMENT '联系人电话',
                                     `web_url` varchar(255) DEFAULT NULL COMMENT '主页',
                                     `email` varchar(255) DEFAULT NULL COMMENT '邮箱',
                                     `xzqh` varchar(255) DEFAULT NULL COMMENT '行政区别',
                                     `org_short_name` varchar(255) DEFAULT NULL COMMENT '部门简称',
                                     `sfgp` varchar(255) DEFAULT NULL COMMENT '是否挂牌',
                                     `gpdw` varchar(255) DEFAULT NULL COMMENT '是否单位',
                                     `xt_zxbz` int(2) DEFAULT NULL COMMENT '备注',
                                     `xt_cjsj` datetime DEFAULT NULL,
                                     `xt_lrsj` datetime DEFAULT NULL,
                                     `xt_lrrxm` varchar(255) DEFAULT NULL,
                                     `xt_lrrid` int(11) DEFAULT NULL,
                                     `xt_lrrbm` varchar(255) DEFAULT NULL,
                                     `xt_lrrbmid` int(11) DEFAULT NULL,
                                     `xt_lrip` varchar(255) DEFAULT NULL,
                                     `xt_zhxgsj` datetime DEFAULT NULL,
                                     `xt_zhxgrxm` varchar(255) DEFAULT NULL,
                                     `xt_zhxgid` int(11) DEFAULT NULL,
                                     `xt_zhxgbm` varchar(255) DEFAULT NULL,
                                     `xt_zhxgbmid` int(11) DEFAULT NULL,
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

DROP TABLE IF EXISTS `t_portal_ext_user_dept`;
CREATE TABLE `t_portal_ext_user_dept` (
                                          `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                          `user_id` int(11) DEFAULT NULL COMMENT '用户id',
                                          `dept_id` int(11) DEFAULT NULL COMMENT '部门id',
                                          PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

DROP TABLE IF EXISTS `t_portal_message`;
CREATE TABLE IF NOT EXISTS `t_portal_message`  (
    `id` bigint(0) NOT NULL AUTO_INCREMENT,
    `third_party_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '第三方系统名',
    `message_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息在第三方系统中的id',
    `user_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接收消息的用户id',
    `user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接收消息的用户姓名',
    `user_id_card` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接收消息的用户身份证号',
    `notice_time` datetime(0) NULL DEFAULT NULL COMMENT '通知时间',
    `source_organ_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息来源单位code',
    `source_organ_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息来源单位名称',
    `content` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息内容',
    `organ_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息接收者的单位code',
    `organ_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息接收者的单位名称',
    `read_status` int(1) NULL DEFAULT NULL COMMENT '阅读状态：0-未读，1-已读',
    `remark1` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '保留字段1',
    `remark2` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '保留字段2',
    `remark3` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '保留字段3',
    `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `create_user_id` bigint(20) NULL DEFAULT NULL COMMENT '创建人id',
    `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
    `update_user_id` bigint(20) NULL DEFAULT NULL COMMENT '更新人id',
    `create_dept_id` bigint(20) NULL DEFAULT NULL,
    `update_dept_id` bigint(20) NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
    )ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

DROP TABLE IF EXISTS `t_portal_statics_fn`;
CREATE TABLE `t_portal_statics_fn`  (
                                        `id` int(11) NOT NULL,
                                        `api_total_num` int(11) NULL DEFAULT NULL COMMENT '接口总数',
                                        `api_add_num` int(11) NULL DEFAULT NULL COMMENT '接口数-本周新增',
                                        `api_call_total_num` int(11) NULL DEFAULT NULL COMMENT '调用总次数',
                                        `api_call_add_num` int(11) NULL DEFAULT NULL COMMENT '接口调用数-本周新增',
                                        `record_call_total_num` int(11) NULL DEFAULT NULL COMMENT '档案调用总数',
                                        `record_call_add_num` int(11) NULL DEFAULT NULL COMMENT '档案调用数-本周新增',
                                        `person_total_num` int(11) NULL DEFAULT NULL COMMENT '人体总数',
                                        `person_add_num` int(11) NULL DEFAULT NULL COMMENT '人体总数-本周新增',
                                        `person_face_total_num` int(11) NULL DEFAULT NULL COMMENT '人脸总数',
                                        `person_face_add_num` int(11) NULL DEFAULT NULL COMMENT '人脸-本周新增',
                                        `person_car_total_num` int(11) NULL DEFAULT NULL COMMENT '人车总数',
                                        `person_car_add_num` int(11) NULL DEFAULT NULL COMMENT '人车-本周新增',
                                        `person_code_total_num` int(11) NULL DEFAULT NULL COMMENT '人码总数',
                                        `person_code_add_num` int(11) NULL DEFAULT NULL COMMENT '人码-本周新增',
                                        `model_call_total_num` int(11) NULL DEFAULT NULL COMMENT '模型调用总数',
                                        `model_call_add_num` int(11) NULL DEFAULT NULL COMMENT '模型调用数-本周新增',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;