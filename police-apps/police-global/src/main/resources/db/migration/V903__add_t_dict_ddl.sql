DELIMITER $$
DROP PROCEDURE IF EXISTS `insert_dict_data` $$
CREATE PROCEDURE insert_dict_data()
BEGIN
    DECLARE topid INT;
    DELETE FROM t_dict WHERE type LIKE 'dy_task_tracing_type%' and (`code` = 4 or `code` = 5);
    set topid = (SELECT id from t_dict where type = 'dy_task_tracing_type_group' and `code` = 0);
    INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color, status)
    VALUES (topid, 'dy_task_tracing_type', 4, '会议决策', 0, null, 1, NULL, NULL, NULL, 1),
           (topid, 'dy_task_tracing_type', 5, '重点工作', 0, null, 1, NULL, NULL, NULL, 1);
END $$
DELIMITER ;
CALL insert_dict_data;
DROP PROCEDURE IF EXISTS insert_dict_data;