DELIMITER $$
DROP PROCEDURE IF EXISTS `modify_column` $$
CREATE PROCEDURE modify_column()
BEGIN
    IF NOT EXISTS(
       SELECT * FROM  information_schema.columns
       WHERE table_schema=(select database()) AND table_name='t_plan_task_relation' AND column_name='lead_dept_info'
    )
    THEN
ALTER TABLE t_plan_task_relation ADD lead_dept_info json NULL COMMENT '牵头单位信息';
END IF;

END $$
DELIMITER ;
CALL modify_column;
DROP PROCEDURE IF EXISTS `modify_column`;