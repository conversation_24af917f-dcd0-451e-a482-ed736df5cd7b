-- 日志表增加DTO信息存储字段
DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS(
       SELECT * FROM  information_schema.columns
       WHERE table_schema=(select database()) AND table_name='tb_intelligence_action_log' AND column_name='dto_content'
    )
    THEN
    ALTER TABLE tb_intelligence_action_log ADD dto_content LONGTEXT NULL COMMENT 'DTO对象';
    END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;

