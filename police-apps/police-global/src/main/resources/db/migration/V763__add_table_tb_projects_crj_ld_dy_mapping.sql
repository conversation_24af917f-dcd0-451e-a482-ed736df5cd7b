CREATE TABLE IF NOT EXISTS `tb_projects_crj_ld_dy_mapping` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `cr_time` datetime NOT NULL COMMENT '创建时间',
  `is_del` int NOT NULL DEFAULT '0' COMMENT '是否删除\n0：否\n1：是',
  `ldmc` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '旅店名称',
  `ldbm` varchar(255) COLLATE utf8mb4_bin NOT NULL COMMENT '旅店编码',
  `dybm` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '地域编码',
  PRIMARY KEY (`id`),
  UNIQUE KEY `tb_projects_crj_ld_dy_mapping_unique` (`ldbm`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
