CREATE TABLE t_risk_score
(
    `id`        bigint       NOT NULL,
    `risk_id`   bigint       NOT NULL COMMENT '风险id',
    `bh`        varchar(255) NOT NULL COMMENT '编号',
    `type`      varchar(20)  NULL COMMENT 'jq/clue',
    `score`     json         NULL COMMENT '得分',
    `key_words` json         NULL COMMENT '关键词',
    PRIMARY KEY (`id`),
    INDEX `t_risk_score_risk_id_idx`(`risk_id`) USING BTREE COMMENT '编号索引',
    INDEX `t_risk_score_bh_idx`(`bh`) USING BTREE COMMENT '编号索引'
);

ALTER TABLE t_risk_jq_relation
    DROP COLUMN `score`;

ALTER TABLE t_risk_clue_relation
    DROP COLUMN `score`;

ALTER TABLE t_risk
    DROP COLUMN `score_key_word`;



