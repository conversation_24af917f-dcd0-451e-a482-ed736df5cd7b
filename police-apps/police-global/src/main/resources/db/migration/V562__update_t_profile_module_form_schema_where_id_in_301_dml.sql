UPDATE `t_profile_module` SET `list_schema` = '{\"name\": \"相关线索\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_clue\", \"fields\": [{\"db\": {\"table\": \"t_profile_clue\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"线索名称\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"code\", \"jdbcType\": \"string\"}, \"name\": \"code\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"线索编号\"}, \"properties\": {\"href\": \"/ys-app/archives/clue/details?id={value}\", \"isName\": true, \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"source\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_source_type\"}, \"name\": \"source\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"source\", \"type\": \"select\", \"value\": [\"%%profile_clue_source_type%%\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"code\", \"children\": \"children\"}, \"displayName\": \"线索来源\"}, \"schema\": {\"type\": \"string\", \"title\": \"线索来源\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"detail\", \"jdbcType\": \"string\"}, \"name\": \"detail\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": true}, \"schema\": {\"type\": \"string\", \"title\": \"线索内容\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"tags\", \"jdbcType\": \"string\"}, \"name\": \"tags\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"智能打标\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"target_location\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_location\"}, \"name\": \"target_location\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"target_location\", \"type\": \"select\", \"value\": [\"%%profile_clue_location%%\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"code\", \"children\": \"children\"}, \"displayName\": \"指向地点\"}, \"schema\": {\"type\": \"string\", \"title\": \"指向地点\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"action_type\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_action_type\"}, \"name\": \"action_type\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"行为方式\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"group_type\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_group_type\"}, \"name\": \"group_type\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"群体类型\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"report_dept\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"report_dept\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"filter\": {\"key\": \"report_dept\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"上报单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"上报单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"report_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"datetime\"}, \"name\": \"report_time\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"filter\": {\"key\": \"report_time\", \"type\": \"timeParams\", \"value\": [{\"id\": \"1\", \"name\": \"今天\"}, {\"id\": \"11\", \"name\": \"昨天\"}, {\"id\": \"2\", \"name\": \"本周\"}, {\"id\": \"12\", \"name\": \"上周\"}, {\"id\": \"3\", \"name\": \"本月\"}, {\"id\": \"13\", \"name\": \"上月\"}, {\"id\": \"4\", \"name\": \"本季\"}, {\"id\": \"14\", \"name\": \"上季\"}, {\"id\": \"99\", \"name\": \"自定义\"}], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\"}, \"displayName\": \"上报时间\"}, \"schema\": {\"type\": \"string\", \"title\": \"上报时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true, \"sortDefault\": \"descending\"}}}], \"selectable\": true, \"searchFields\": [{\"key\": \"detail\", \"name\": \"线索内容\"}, {\"key\": \"code\", \"name\": \"线索编号\"}]}' WHERE `id` = 301;