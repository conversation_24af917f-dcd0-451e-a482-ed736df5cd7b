UPDATE t_profile_module
SET cn_name='管控措施', en_name='gkcs', `type`='personV2', pid=1345, is_archive=1, show_order=1, table_schema=NULL, form_schema=NULL, list_schema='{"name": "管控措施", "type": "LIST_SCHEMA", "table": "t_profile_person_gkcs", "fields": [{"db": {"table": "t_profile_person_gkcs", "column": "detail", "jdbcType": "string"}, "name": "detail", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "管控措施"}, "properties": {"copyable": false, "editable": true, "required": true, "sortable": false}}}, {"db": {"table": "t_profile_person_gkcs", "column": "expiration_date", "jdbcType": "timestamp"}, "name": "expiration_date", "listSchema": {"style": {"align": "center", "format": "string"}, "schema": {"type": "datetime", "title": "管控到期日期"}, "properties": {"copyable": false, "editable": true, "required": true, "sortable": false}}}, {"db": {"table": "t_profile_person_gkcs", "column": "create_dept_id", "mapping": "dept_id_to_dept_name", "jdbcType": "number"}, "name": "create_dept_id", "listSchema": {"style": {"align": "center"}, "filter": {"key": "create_dept_id", "type": "tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "id", "children": "children"}, "displayName": "录入单位"}, "schema": {"type": "string", "title": "录入单位"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_person_gkcs", "column": "create_time", "mapping": "date_time_to_general_string", "jdbcType": "datetime"}, "name": "createTime", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "录入时间"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}], "selectable": false, "extendFields": [{"table": "t_profile_person_gkcs", "value": 5, "column": "police_kind", "jdbcType": "integer"}], "searchFields": [{"key": "name", "name": "管控措施"}]}', is_add=1, database_relation='{"type": "FOREIGN_KEY", "table": "t_profile_person_gkcs", "column": "person_id", "idColumn": "id", "primaryColumn": "id", "extendCondition": [{"value": 5, "column": "police_kind"}]}', show_schema_type='LIST_SCHEMA', add_schema_type='LIST_SCHEMA', is_operation_content=1, is_mobile_content=1, is_web_content=1, is_fk_content=0
WHERE id=1588;