UPDATE t_request_params
SET params='{"filterParams": [{"key": "level", "type": "option", "value": ["%%control_regular_monitor_level%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "常控级别"}, {"key": "hasWarning", "type": "option", "value": [{"id": 1, "name": "有预警"}, {"id": 0, "name": "无预警"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "是否有预警"}, {"key": "status", "type": "multiple-tree", "value": ["%%regular_monitor_status%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "常控状态"}, {"key": "personStatus", "type": "multiple-tree", "value": ["%%service_person_status%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "人员状态"}, {"key": "person_label", "type": "multiple-tree", "value": ["&&person_label&&"], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "人员标签"}, {"key": "regular_label", "type": "multiple-tree", "value": ["&&regular_label&&"], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "常控标签"}, {"key": "doneStatus", "type": "option", "value": [{"id": 0, "name": "待完成"}, {"id": 1, "name": "已完成"}, {"id": 2, "name": "无需完成"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "工作记录"}, {"key": "createTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "发起时间"}, {"key": "latestWarningTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "最近预警时间"}], "searchFields": [{"key": "targetName", "name": "姓名"}, {"key": "idNumber", "name": "证件号码"}, {"key": "content", "name": "预警信息"}]}'
WHERE module='regular-list-my';
UPDATE t_request_params
SET params='{"filterParams": [{"key": "level", "type": "option", "value": ["%%control_regular_monitor_level%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "常控级别"}, {"key": "hasWarning", "type": "option", "value": [{"id": 1, "name": "有预警"}, {"id": 0, "name": "无预警"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "是否有预警"}, {"key": "status", "type": "multiple-tree", "value": ["%%regular_monitor_status%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "常控状态"}, {"key": "personStatus", "type": "multiple-tree", "value": ["%%service_person_status%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "人员状态"}, {"key": "person_label", "type": "multiple-tree", "value": ["&&person_label&&"], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "人员标签"}, {"key": "regular_label", "type": "multiple-tree", "value": ["&&regular_label&&"], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "常控标签"}, {"key": "doneStatus", "type": "option", "value": [{"id": 0, "name": "待完成"}, {"id": 1, "name": "已完成"}, {"id": 2, "name": "无需完成"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "工作记录"}, {"key": "createDept", "type": "multiple-tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "id", "children": "children"}, "displayName": "发起单位"}, {"key": "createTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "发起时间"}, {"key": "latestWarningTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "最近预警时间"}], "searchFields": [{"key": "targetName", "name": "姓名"}, {"key": "idNumber", "name": "证件号码"}, {"key": "content", "name": "预警信息"}]}'
WHERE module='regular-list-all';
UPDATE t_request_params
SET params='{"filterParams": [{"key": "dept", "type": "tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "deptId", "children": "children"}, "displayName": "组织"}, {"key": "level", "type": "option", "value": ["%%control_regular_monitor_level%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "常控级别"}, {"key": "regular_label", "type": "multiple-tree", "value": ["&&regular_label&&"], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "常控标签"}, {"key": "countPeriod", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "统计周期", "defaultValue": {"range": "3"}}], "searchFields": [{"key": "deptName", "name": "单位名称"}]}'
WHERE module='statistic-regular';

INSERT IGNORE INTO t_request_params
(id, module, params)
VALUES(39, 'regular-export-params', '{"filterParams": [], "searchFields": [{"key": "name", "name": "姓名"}, {"key": "level", "name": "常控级别"}, {"key": "idNumber", "name": "证件号码"}, {"key": "labels", "name": "人员标签"}, {"key": "regularLabels", "name": "常控标签"}, {"key": "warningCount", "name": "预警信息"}, {"key": "latestWarningTime", "name": "最近预警时间"}, {"key": "workStatus", "name": "工作记录"}, {"key": "personStatus", "name": "人员状态"}, {"key": "createUser", "name": "发起人"}, {"key": "createDept", "name": "发起单位"}, {"key": "createTime", "name": "发起时间"}, {"key": "status", "name": "常控状态"}]}');

