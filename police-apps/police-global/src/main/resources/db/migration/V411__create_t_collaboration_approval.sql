DROP TABLE IF EXISTS `t_collaboration_approval`;
CREATE TABLE t_collaboration_approval (
    id BIGINT auto_increment NOT NULL COMMENT '主键',
    collaboration_id BIGINT NULL COMMENT '协作id',
    approval_result INT NULL COMMENT '审核结果，1：通过，2：驳回',
    reason varchar(500) NULL COMMENT '原因',
    attachments varchar(2000) NULL COMMENT '附件',
    create_time DATETIME NULL COMMENT '创建时间',
    create_user_id BIGINT NULL COMMENT '创建用户主键',
    create_dept_id BIGINT NULL COMMENT '操作人单位主键',
    update_time DATETIME NULL COMMENT '更新时间',
    update_user_id varchar(100) NULL COMMENT '更新用户主键',
    update_dept_id BIGINT NULL COMMENT '更新单位主键',
    CONSTRAINT t_collaboration_time_axis_pk PRIMARY KEY (id)
)
    ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_bin;