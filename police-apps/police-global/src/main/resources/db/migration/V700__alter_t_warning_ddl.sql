DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    DECLARE pid1 INT;
    DECLARE pid2 INT;
-- schema 模板更新
delete from t_profile_module where id = 606;
delete from t_profile_module where id = 608;
INSERT INTO t_profile_module (id,cn_name,en_name,`type`,pid,is_archive,show_order,table_schema,form_schema,list_schema,is_add,database_relation,show_schema_type,add_schema_type,is_operation_content,is_mobile_content,is_web_content,is_fk_content) VALUES
    (606,'档案管理','archive','goods',NULL,0,1,'{}','{}','{"name": "物品信息", "type": "LIST_SCHEMA", "table": "t_profile_goods", "fields": [{"db": {"table": "t_profile_goods", "column": "photo", "mapping": "json_to_image_array", "jdbcType": "json_object_array"}, "name": "photo", "listSchema": {"style": {"align": "center", "fixed": "left", "ellipsis": true}, "schema": {"type": "photo", "title": "照片"}, "properties": {"isPhoto": true, "copyable": false, "editable": false, "required": false, "sortable": false, "isRelatedShow": true}}}, {"db": {"table": "t_profile_goods", "column": "name", "jdbcType": "string"}, "name": "name", "listSchema": {"style": {"align": "center", "ellipsis": true}, "schema": {"type": "string", "title": "名称"}, "properties": {"isName": true, "copyable": false, "editable": false, "required": false, "sortable": false, "isRelatedShow": true}}}, {"db": {"table": "t_profile_goods", "column": "review_status", "mapping": "approval_statue_code_to_name", "jdbcType": "int"}, "name": "review_status", "listSchema": {"style": {"align": "center", "ellipsis": true}, "schema": {"type": "string", "title": "审批状态"}, "properties": {"isName": true, "copyable": false, "editable": false, "required": false, "sortable": false, "isRelatedShow": true}}}, {"db": {"table": "t_profile_goods", "column": "category", "mapping": "dict_code_array_to_tree_name", "jdbcType": "label_id_array"}, "dict": {"type": "goods_archives_category"}, "name": "category", "listSchema": {"style": {"align": "center"}, "filter": {"key": "category", "type": "select", "value": ["%%goods_archives_category%%"], "fieldNames": {"label": "name", "value": "code"}, "displayName": "类型"}, "schema": {"type": "array", "title": "类型"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_profile_goods", "column": "status", "mapping": "dict_code_to_name", "jdbcType": "string"}, "dict": {"type": "goods_status"}, "name": "status", "listSchema": {"style": {"align": "center"}, "filter": {"key": "status", "type": "select", "value": ["%%goods_status%%"], "fieldNames": {"label": "name", "value": "code"}, "displayName": "状态"}, "schema": {"type": "string", "title": "状态"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_profile_goods", "column": "focus_status", "mapping": "dict_code_to_name", "jdbcType": "string"}, "dict": {"type": "goods_focus_status"}, "name": "focus_status", "listSchema": {"style": {"align": "center"}, "filter": {"key": "focus_status", "type": "select", "value": ["%%goods_focus_status%%"], "fieldNames": {"label": "name", "value": "code"}, "displayName": "关注状态"}, "schema": {"type": "string", "title": "关注状态"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_profile_goods", "column": "create_dept_id", "mapping": "dept_id_to_dept_name", "jdbcType": "string"}, "name": "dept_name", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "录入单位"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_profile_goods", "column": "create_time", "mapping": "date_time_to_text", "jdbcType": "datetime"}, "name": "createTime", "listSchema": {"style": {"align": "center"}, "filter": {"key": "createTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "录入时间"}, "schema": {"type": "string", "title": "录入时间"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": true}}}, {"db": {"table": "t_profile_goods", "column": "update_time", "mapping": "date_time_to_text", "jdbcType": "datetime"}, "name": "updateTime", "listSchema": {"style": {"align": "center"}, "filter": {"key": "updateTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "更新时间"}, "schema": {"type": "string", "title": "更新时间"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": true, "sortDefault": "descending"}}}], "selectable": true, "searchFields": [{"key": "name", "name": "名称"}], "profileDataPermission": []}',0,'{}',NULL,NULL,1,1,1,0),
    (608,'物品信息','goods','goods',607,1,3,'{"name": "物品信息", "type": "TABLE_SCHEMA", "table": "t_profile_goods", "fields": [{"db": {"table": "t_profile_goods", "column": "name", "jdbcType": "string"}, "name": "name", "tableSchema": {"span": 1, "type": "string", "title": "名称", "copyable": false}}, {"db": {"table": "t_profile_goods", "column": "category", "mapping": "dict_code_array_to_tree_name", "jdbcType": "json_id_array"}, "dict": {"type": "goods_archives_category"}, "name": "category", "tableSchema": {"span": 1, "type": "label", "title": "类型", "copyable": false}}, {"db": {"table": "t_profile_goods", "column": "status", "mapping": "dict_code_to_name", "jdbcType": "string"}, "dict": {"type": "goods_status"}, "name": "status", "tableSchema": {"span": 1, "type": "string", "title": "状态", "copyable": false}}, {"db": {"table": "t_profile_goods", "column": "focus_status", "mapping": "dict_code_to_name", "jdbcType": "string"}, "dict": {"type": "goods_focus_status"}, "name": "focus_status", "tableSchema": {"span": 1, "type": "string", "title": "关注状态", "copyable": false}}, {"db": {"table": "t_profile_goods", "column": "location", "jdbcType": "string"}, "name": "location", "tableSchema": {"span": 2, "type": "string", "title": "位置", "copyable": false}}, {"db": {"table": "t_profile_goods", "column": "goods_describe", "jdbcType": "string"}, "name": "goods_describe", "tableSchema": {"span": 4, "type": "string", "title": "描述", "copyable": false}}], "moduleUi": {"column": 3, "bordered": true}}','{"name": "物品信息", "type": "FORM_SCHEMA", "table": "t_profile_goods", "fields": [{"db": {"table": "t_profile_goods", "column": "name", "jdbcType": "string"}, "name": "name", "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "名称"}}}, {"db": {"table": "t_profile_goods", "column": "photo", "jdbcType": "json_image_array"}, "name": "photo", "formSchema": {"ui": {"ui:options": {"style": {"top": 0, "right": 0, "bottom": 0.1, "position": "absolute"}, "width": "0.5", "action": "/upload/imgs", "widget": "upload", "isShowTitle": false, "titleLocation": "left"}}, "schema": {"type": "array", "items": {"type": "object"}, "title": "照片"}}}, {"db": {"table": "t_profile_goods", "column": "category", "jdbcType": "json_id_array"}, "name": "category", "tree": {"root": "goods_archives_category", "type": "dict"}, "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "0.5", "widget": "cascader", "multiple": true, "fieldNames": {"label": "name", "value": "code", "children": "children"}, "titleLocation": "left"}}, "schema": {"type": "array", "items": {"type": "array"}, "title": "类型"}}}, {"db": {"table": "t_profile_goods", "column": "status", "jdbcType": "integer"}, "dict": {"type": "goods_status"}, "name": "status", "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "0.5", "widget": "radio", "enumDisabled": [1, 2], "excludeField": "enumDisabled,style", "titleLocation": "left"}}, "schema": {"type": "number", "title": "状态"}}}, {"db": {"table": "t_profile_goods", "column": "focus_status", "jdbcType": "integer"}, "dict": {"type": "goods_focus_status"}, "name": "focusStatus", "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "0.5", "widget": "radio", "titleLocation": "left"}}, "schema": {"type": "number", "title": "关注状态"}}}, {"db": {"table": "t_profile_goods", "column": "location", "jdbcType": "string"}, "name": "location", "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "位置"}}}, {"db": {"table": "t_profile_goods", "column": "goods_describe", "jdbcType": "string"}, "name": "goodsDescribe", "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "1.0", "widget": "textarea", "titleLocation": "left"}}, "schema": {"type": "string", "title": "描述"}}}], "required": ["name", "status"]}','{}',1,'{"type": "PRIMARY_KEY", "table": "t_profile_goods", "column": "id"}','TABLE_SCHEMA','FORM_SCHEMA',1,1,1,0);

-- t_warning 增加字段
    IF NOT EXISTS( SELECT * FROM information_schema.columns WHERE table_schema=(select database()) AND table_name='t_warning' AND column_name='hit_subject')
    THEN
        ALTER TABLE t_warning ADD hit_subject BIGINT NULL COMMENT '命中专题';
END IF;
    IF NOT EXISTS( SELECT * FROM information_schema.columns WHERE table_schema=(select database()) AND table_name='t_warning' AND column_name='hit_subject_scene')
    THEN
        ALTER TABLE t_warning ADD hit_subject_scene BIGINT NULL COMMENT '命中场景';
END IF;
-- t_dict增加数据
delete from t_dict where type like 'hit_subject_scene%';
insert into t_dict(type,code,name,p_code, show_number) values('hit_subject_scene_group',0,'命中专题',0, 1);
set pid1 = (select t.id from t_dict t where type = 'hit_subject_scene_group');
update t_dict set p_id = pid1 where type = 'hit_subject_scene_group';
insert into t_dict(type,code,name,p_code, p_id, show_number) values('hit_subject_scene',1,'FX专题',0, pid1, 2);
set pid2 = (select t.id from t_dict t where type = 'hit_subject_scene' and code = 1);
insert into t_dict(type,code,name,p_code, p_id, show_number) values('hit_subject_scene',2,'区域预警场景',1, pid2, 3);
END $$
DELIMITER ;
CALL add_column;