CREATE TABLE IF NOT EXISTS `t_ga_ts_big_screen_mapinfo` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `parent_district` varchar(20) NOT NULL COMMENT '区域代码',
  `district_code` varchar(20) NOT NULL COMMENT '区域代码',
  `district_name` varchar(20) NOT NULL COMMENT '区域名称',
  `statistic_date` datetime NOT NULL COMMENT '统计数量的日期',
  `risk_type` tinyint default 1 comment '1:平稳，2：低风险，3：中风险，4：高峰险',
  `score_detail` text default null comment '评分详细',
  `score` float NOT NULL COMMENT '安稳分',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '状态，0正常，1删除',
  PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '广安态势大屏地图安稳分数信息表';
