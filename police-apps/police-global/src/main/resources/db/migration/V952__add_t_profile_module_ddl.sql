DELETE from t_profile_module WHERE `type` in ('personV2','groupV2','eventV2');
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1307, '经侦', 'jz', 'personV2', 1313, 1, 1, NULL, NULL, NULL, 1, NULL, NULL, NULL, 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1308, '政府管控信息', 'govControl', 'personV2', 1307, 1, 2, '{\"name\": \"政府管控信息\", \"table\": \"t_profile_person_government_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government\", \"jdbcType\": \"string\"}, \"name\": \"control_government\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"党政责任部门\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government_person\", \"jdbcType\": \"string\"}, \"name\": \"control_government_person\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"党政责任人\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government_person_duty\", \"jdbcType\": \"string\"}, \"name\": \"control_government_person_duty\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"党政责任人职务\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government_contact\", \"jdbcType\": \"string\"}, \"name\": \"control_government_contact\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"联系方式\", \"copyable\": true}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community\", \"jdbcType\": \"string\"}, \"name\": \"control_community\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任街道社区\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community_person\", \"jdbcType\": \"string\"}, \"name\": \"control_community_person\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"社区责任人\", \"config\": {\"fieldName\": \"control_community_person\", \"processType\": \"string\", \"processConfig\": null}, \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community_person_duty\", \"jdbcType\": \"string\"}, \"name\": \"control_community_person_duty\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"社区责任人职务\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community_contact\", \"jdbcType\": \"string\"}, \"name\": \"control_community_contact\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"联系方式\", \"copyable\": true}}], \"moduleUi\": {\"column\": 3, \"bordered\": true}}', '{\"name\": \"政府管控信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_person_government_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government\", \"jdbcType\": \"string\"}, \"name\": \"controlGovernment\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"党政责任部门\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government_person\", \"jdbcType\": \"string\"}, \"name\": \"controlGovernmentPerson\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"党政责任人\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government_person_duty\", \"jdbcType\": \"string\"}, \"name\": \"controlGovernmentPersonDuty\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"党政责任人职务\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government_contact\", \"jdbcType\": \"string\"}, \"name\": \"controlGovernmentContact\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"党政责任人联系方式\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community\", \"jdbcType\": \"string\"}, \"name\": \"controlCommunity\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"责任街道社区\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community_person\", \"jdbcType\": \"string\"}, \"name\": \"controlCommunityPerson\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"社区责任人\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community_person_duty\", \"jdbcType\": \"string\"}, \"name\": \"controlCommunityPersonDuty\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"社区责任人职务\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community_contact\", \"jdbcType\": \"string\"}, \"name\": \"controlCommunityContact\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"社区责任人联系方式\"}}}], \"required\": [], \"extendFields\": [{\"table\": \"t_profile_person_government_control\", \"value\": 3, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}]}', NULL, 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_person_government_control\", \"column\": \"person_id\", \"extendCondition\": [{\"value\": \"3\", \"column\": \"police_kind\"}]}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1309, '公安管控信息', 'policeControl', 'personV2', 1307, 1, 3, '{\"name\": \"公安管控信息\", \"type\": \"TABLE_SCHEMA\", \"table\": \"t_profile_person_police_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_bureau\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"control_bureau\", \"tree\": {\"type\": \"dept\"}, \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任分局\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_bureau_leader\", \"mapping\": \"user_id_array_to_single_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_bureau_leader\", \"tableSchema\": {\"span\": 1, \"type\": \"user\", \"title\": \"责任领导\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_police\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"control_police\", \"tree\": {\"type\": \"dept\"}, \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任警种\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_police_leader\", \"mapping\": \"user_id_array_to_single_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_police_leader\", \"tableSchema\": {\"span\": 1, \"type\": \"user\", \"title\": \"责任领导\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_station\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"control_station\", \"tree\": {\"type\": \"dept\"}, \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任派出所\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_station_leader\", \"mapping\": \"user_id_array_to_single_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_station_leader\", \"tableSchema\": {\"span\": 1, \"type\": \"user\", \"title\": \"责任领导\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_person\", \"mapping\": \"user_id_array_to_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_person\", \"tableSchema\": {\"span\": 2, \"type\": \"multiUser\", \"title\": \"责任民警\", \"copyable\": false}}], \"moduleUi\": {\"column\": 2, \"bordered\": true}}', '{\"name\": \"公安管控信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_person_police_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_bureau\", \"jdbcType\": \"string\"}, \"name\": \"controlBureau\", \"tree\": {\"root\": \"510500000000\", \"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"责任分局\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_bureau_leader\", \"jdbcType\": \"number\"}, \"name\": \"controlBureauLeader\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlBureau\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"责任分局领导\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_police\", \"jdbcType\": \"string\"}, \"name\": \"controlPolice\", \"tree\": {\"root\": \"510500000000\", \"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"责任警种\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_police_leader\", \"jdbcType\": \"number\"}, \"name\": \"controlPoliceLeader\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlPolice\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"责任警种领导\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_station\", \"jdbcType\": \"string\"}, \"name\": \"controlStation\", \"tree\": {\"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"责任派出所\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_station_leader\", \"jdbcType\": \"number\"}, \"name\": \"controlStationLeader\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlStation\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"责任派出所领导\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_person\", \"jdbcType\": \"json_id_array\"}, \"name\": \"controlPerson\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"1\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlStation\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"array\", \"items\": {\"type\": \"number\"}, \"title\": \"责任民警\", \"minItems\": 1}}}], \"required\": [\"controlPerson\", \"controlStationLeader\", \"controlStation\"], \"extendFields\": [{\"table\": \"t_profile_person_police_control\", \"value\": 3, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}]}', NULL, 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_person\", \"column\": \"person_id\", \"extendCondition\": [{\"value\": \"3\", \"column\": \"police_kind\"}]}', 'NO_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1310, '警种信息', 'policeKind', 'groupV2', NULL, 1, 22, NULL, NULL, NULL, 1, NULL, 'NO_SCHEMA', 'NO_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1311, '相关群体', 'relatedGroup', 'personV2', 1307, 1, 4, NULL, NULL, '{\"name\": \"相关群体\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_group\", \"fields\": [{\"db\": {\"table\": \"t_profile_group\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"群体名称\"}, \"properties\": {\"href\": \"/ys-app/archives/group/details?id={value}\", \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_group\", \"column\": \"group_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"label_id_array\"}, \"name\": \"groupLabel\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"groupLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&group_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"群体标签\"}, \"schema\": {\"type\": \"array\", \"title\": \"群体标签\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2}}}, {\"db\": {\"table\": \"t_profile_person_group_relation\", \"column\": \"group_location\", \"jdbcType\": \"string\", \"databaseRelation\": {\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_group_relation\", \"joinTo\": {\"table\": \"t_profile_group\", \"column\": \"id\", \"joinColumn\": \"group_id\"}, \"joinFrom\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}}}, \"name\": \"group_location\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"群体位置\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_group\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"createDept\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"createDept\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}], \"selectable\": false, \"extendFields\": [{\"table\": \"t_profile_person_group_relation\", \"value\": 3, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}], \"searchFields\": [{\"key\": \"name\", \"name\": \"群体名称\"}]}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_group_relation\", \"joinTo\": {\"table\": \"t_profile_group\", \"column\": \"id\", \"joinColumn\": \"group_id\"}, \"joinFrom\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}, \"extendCondition\": [{\"value\": \"3\", \"column\": \"police_kind\"}]}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1312, '基本信息', 'basicInfo', 'groupV2', NULL, 1, 16, NULL, NULL, NULL, 1, NULL, NULL, NULL, 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1313, '警种信息', 'policeKind', 'personV2', NULL, 1, 7, NULL, NULL, NULL, 1, NULL, NULL, NULL, 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1314, '群体信息', 'group', 'groupV2', 1312, 1, 17, '{\"name\": \"群体信息\", \"type\": \"TABLE_SCHEMA\", \"table\": \"t_profile_group\", \"fields\": [{\"db\": {\"table\": \"t_profile_group\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"群体名称\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group\", \"column\": \"group_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"json_id_array\"}, \"name\": \"group_label\", \"tableSchema\": {\"span\": 1, \"type\": \"label\", \"title\": \"群体类别\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group\", \"column\": \"control_level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"string\"}, \"dict\": {\"type\": \"profile_person_control_level\"}, \"name\": \"control_level\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"群体级别\", \"copyable\": false}}], \"moduleUi\": {\"column\": 4, \"bordered\": true}}', '{\"name\": \"群体信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_group\", \"fields\": [{\"db\": {\"table\": \"t_profile_group\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"群体名称\"}}}, {\"db\": {\"table\": \"t_profile_group\", \"column\": \"group_label\", \"jdbcType\": \"json_id_array\"}, \"name\": \"groupLabel\", \"tree\": {\"root\": \"group\", \"type\": \"label\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": true, \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"array\", \"items\": {\"type\": \"array\"}, \"title\": \"群体类别\"}}}, {\"db\": {\"table\": \"t_profile_group\", \"column\": \"control_level\", \"jdbcType\": \"string\"}, \"dict\": {\"type\": \"profile_person_control_level\"}, \"name\": \"control_level\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"radio\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"群体级别\"}}}], \"required\": [\"name\"]}', NULL, 1, '{\"type\": \"PRIMARY_KEY\", \"table\": \"t_profile_group\", \"column\": \"id\"}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1315, '风险点信息', 'riskInfo', 'groupV2', 1333, 1, 27, '{\"name\": \"风险点信息\", \"type\": \"TABLE_SCHEMA\", \"table\": \"t_profile_group_risk_other\", \"fields\": [{\"db\": {\"table\": \"t_profile_group_risk_other\", \"column\": \"main_demand\", \"jdbcType\": \"string\"}, \"name\": \"main_demand\", \"tableSchema\": {\"span\": 4, \"type\": \"string\", \"title\": \"风险背景\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_risk_other\", \"column\": \"petition_info\", \"jdbcType\": \"string\"}, \"name\": \"petition_info\", \"tableSchema\": {\"span\": 4, \"type\": \"string\", \"title\": \"化解难点\", \"copyable\": false}}], \"moduleUi\": {\"column\": 4, \"bordered\": true}}', '{\"name\": \"风险点信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_group_risk_other\", \"fields\": [{\"db\": {\"table\": \"t_profile_group_risk_other\", \"column\": \"main_demand\", \"jdbcType\": \"string\"}, \"name\": \"main_demand\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"textarea\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"风险背景\"}}}, {\"db\": {\"table\": \"t_profile_group_risk_other\", \"column\": \"petition_info\", \"jdbcType\": \"string\"}, \"name\": \"petition_info\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"textarea\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"化解难点\"}}}], \"required\": [], \"extendFields\": [{\"table\": \"t_profile_group_risk_other\", \"value\": 3, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}]}', NULL, 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_group_risk_other\", \"column\": \"group_id\", \"extendCondition\": [{\"value\": 3, \"column\": \"police_kind\"}]}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1316, '涉毒', 'sd', 'personV2', 1313, 1, 2, NULL, NULL, NULL, 1, NULL, NULL, NULL, 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1318, '政府管控信息', 'govControl', 'personV2', 1316, 1, 2, '{\"name\": \"政府管控信息\", \"table\": \"t_profile_person_government_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government\", \"jdbcType\": \"string\"}, \"name\": \"control_government\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"党政责任部门\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government_person\", \"jdbcType\": \"string\"}, \"name\": \"control_government_person\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"党政责任人\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government_person_duty\", \"jdbcType\": \"string\"}, \"name\": \"control_government_person_duty\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"党政责任人职务\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government_contact\", \"jdbcType\": \"string\"}, \"name\": \"control_government_contact\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"联系方式\", \"copyable\": true}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community\", \"jdbcType\": \"string\"}, \"name\": \"control_community\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任街道社区\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community_person\", \"jdbcType\": \"string\"}, \"name\": \"control_community_person\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"社区责任人\", \"config\": {\"fieldName\": \"control_community_person\", \"processType\": \"string\", \"processConfig\": null}, \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community_person_duty\", \"jdbcType\": \"string\"}, \"name\": \"control_community_person_duty\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"社区责任人职务\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community_contact\", \"jdbcType\": \"string\"}, \"name\": \"control_community_contact\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"联系方式\", \"copyable\": true}}], \"moduleUi\": {\"column\": 3, \"bordered\": true}}', '{\"name\": \"政府管控信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_person_government_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government\", \"jdbcType\": \"string\"}, \"name\": \"controlGovernment\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"党政责任部门\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government_person\", \"jdbcType\": \"string\"}, \"name\": \"controlGovernmentPerson\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"党政责任人\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government_person_duty\", \"jdbcType\": \"string\"}, \"name\": \"controlGovernmentPersonDuty\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"党政责任人职务\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government_contact\", \"jdbcType\": \"string\"}, \"name\": \"controlGovernmentContact\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"党政责任人联系方式\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community\", \"jdbcType\": \"string\"}, \"name\": \"controlCommunity\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"责任街道社区\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community_person\", \"jdbcType\": \"string\"}, \"name\": \"controlCommunityPerson\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"社区责任人\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community_person_duty\", \"jdbcType\": \"string\"}, \"name\": \"controlCommunityPersonDuty\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"社区责任人职务\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community_contact\", \"jdbcType\": \"string\"}, \"name\": \"controlCommunityContact\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"社区责任人联系方式\"}}}], \"required\": [], \"extendFields\": [{\"table\": \"t_profile_person_government_control\", \"value\": 10, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}]}', NULL, 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_person_government_control\", \"column\": \"person_id\", \"extendCondition\": [{\"value\": \"10\", \"column\": \"police_kind\"}]}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1319, '公安管控信息', 'policeControl', 'personV2', 1316, 1, 3, '{\"name\": \"公安管控信息\", \"type\": \"TABLE_SCHEMA\", \"table\": \"t_profile_person_police_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_bureau\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"control_bureau\", \"tree\": {\"type\": \"dept\"}, \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任分局\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_bureau_leader\", \"mapping\": \"user_id_array_to_single_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_bureau_leader\", \"tableSchema\": {\"span\": 1, \"type\": \"user\", \"title\": \"责任领导\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_police\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"control_police\", \"tree\": {\"type\": \"dept\"}, \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任警种\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_police_leader\", \"mapping\": \"user_id_array_to_single_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_police_leader\", \"tableSchema\": {\"span\": 1, \"type\": \"user\", \"title\": \"责任领导\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_station\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"control_station\", \"tree\": {\"type\": \"dept\"}, \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任派出所\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_station_leader\", \"mapping\": \"user_id_array_to_single_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_station_leader\", \"tableSchema\": {\"span\": 1, \"type\": \"user\", \"title\": \"责任领导\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_person\", \"mapping\": \"user_id_array_to_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_person\", \"tableSchema\": {\"span\": 2, \"type\": \"multiUser\", \"title\": \"责任民警\", \"copyable\": false}}], \"moduleUi\": {\"column\": 2, \"bordered\": true}}', '{\"name\": \"公安管控信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_person_police_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_bureau\", \"jdbcType\": \"string\"}, \"name\": \"controlBureau\", \"tree\": {\"root\": \"510500000000\", \"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"责任分局\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_bureau_leader\", \"jdbcType\": \"number\"}, \"name\": \"controlBureauLeader\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlBureau\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"责任分局领导\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_police\", \"jdbcType\": \"string\"}, \"name\": \"controlPolice\", \"tree\": {\"root\": \"510500000000\", \"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"责任警种\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_police_leader\", \"jdbcType\": \"number\"}, \"name\": \"controlPoliceLeader\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlPolice\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"责任警种领导\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_station\", \"jdbcType\": \"string\"}, \"name\": \"controlStation\", \"tree\": {\"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"责任派出所\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_station_leader\", \"jdbcType\": \"number\"}, \"name\": \"controlStationLeader\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlStation\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"责任派出所领导\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_person\", \"jdbcType\": \"json_id_array\"}, \"name\": \"controlPerson\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"1\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlStation\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"array\", \"items\": {\"type\": \"number\"}, \"title\": \"责任民警\", \"minItems\": 1}}}], \"required\": [\"controlPerson\", \"controlStationLeader\", \"controlStation\"], \"extendFields\": [{\"table\": \"t_profile_person_police_control\", \"value\": 10, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}]}', NULL, 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_person\", \"column\": \"person_id\", \"extendCondition\": [{\"value\": \"10\", \"column\": \"police_kind\"}]}', 'NO_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1321, '政府管控信息', 'govControl', 'groupV2', 1333, 1, 30, '{\"name\": \"政府管控信息\", \"table\": \"t_profile_group_government_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_government\", \"jdbcType\": \"string\"}, \"name\": \"control_government\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"党政责任部门\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_government_person\", \"jdbcType\": \"string\"}, \"name\": \"control_government_person\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"党政责任人\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_government_contact\", \"jdbcType\": \"string\"}, \"name\": \"control_government_contact\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"联系方式\", \"copyable\": true}}, {\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_community\", \"jdbcType\": \"string\"}, \"name\": \"control_community\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任街道社区\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_community_person\", \"jdbcType\": \"string\"}, \"name\": \"control_community_person\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"社区责任人\", \"config\": {\"fieldName\": \"control_community_person\", \"processType\": \"string\", \"processConfig\": null}, \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_community_contact\", \"jdbcType\": \"string\"}, \"name\": \"control_community_contact\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"联系方式\", \"copyable\": true}}], \"moduleUi\": {\"column\": 3, \"bordered\": true}}', '{\"name\": \"政府管控信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_group_government_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_government\", \"jdbcType\": \"string\"}, \"name\": \"controlGovernment\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"党政责任部门\"}}}, {\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_government_person\", \"jdbcType\": \"string\"}, \"name\": \"controlGovernmentPerson\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"党政责任人\"}}}, {\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_government_contact\", \"jdbcType\": \"string\"}, \"name\": \"controlGovernmentContact\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"联系方式\"}}}, {\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_community\", \"jdbcType\": \"string\"}, \"name\": \"controlCommunity\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"责任街道社区\"}}}, {\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_community_person\", \"jdbcType\": \"string\"}, \"name\": \"controlCommunityPerson\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"社区责任人\"}}}, {\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_community_contact\", \"jdbcType\": \"string\"}, \"name\": \"controlCommunityContact\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"联系方式\"}}}], \"required\": [], \"extendFields\": [{\"table\": \"t_profile_group_government_control\", \"value\": 3, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}]}', NULL, 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_group_government_control\", \"column\": \"group_id\", \"extendCondition\": [{\"value\": 3, \"column\": \"police_kind\"}]}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1322, '公安管控信息', 'policeControl', 'groupV2', 1333, 1, 32, '{\"name\": \"公安管控信息\", \"type\": \"TABLE_SCHEMA\", \"table\": \"t_profile_group_police_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_bureau\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"control_bureau\", \"tree\": {\"type\": \"dept\"}, \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任分局\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_bureau_leader\", \"mapping\": \"user_id_array_to_single_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_bureau_leader\", \"tableSchema\": {\"span\": 1, \"type\": \"user\", \"title\": \"责任领导\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_police\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"control_police\", \"tree\": {\"type\": \"dept\"}, \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任警种\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_police_leader\", \"mapping\": \"user_id_array_to_single_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_police_leader\", \"tableSchema\": {\"span\": 1, \"type\": \"user\", \"title\": \"责任领导\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_station\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"control_station\", \"tree\": {\"type\": \"dept\"}, \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任派出所\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_station_leader\", \"mapping\": \"user_id_array_to_single_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_station_leader\", \"tableSchema\": {\"span\": 1, \"type\": \"user\", \"title\": \"责任领导\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_person\", \"mapping\": \"user_id_array_to_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_person\", \"tableSchema\": {\"span\": 2, \"type\": \"multiUser\", \"title\": \"责任民警\", \"copyable\": false}}], \"moduleUi\": {\"column\": 2, \"bordered\": true}}', '{\"name\": \"公安管控信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_group_police_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_bureau\", \"jdbcType\": \"string\"}, \"name\": \"controlBureau\", \"tree\": {\"root\": \"510500000000\", \"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"责任分局\"}}}, {\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_bureau_leader\", \"jdbcType\": \"number\"}, \"name\": \"controlBureauLeader\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlBureau\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"责任分局领导\"}}}, {\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_police\", \"jdbcType\": \"string\"}, \"name\": \"controlPolice\", \"tree\": {\"root\": \"510500000000\", \"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"责任警种\"}}}, {\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_police_leader\", \"jdbcType\": \"number\"}, \"name\": \"controlPoliceLeader\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlPolice\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"责任警种领导\"}}}, {\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_station\", \"jdbcType\": \"string\"}, \"name\": \"controlStation\", \"tree\": {\"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"责任派出所\"}}}, {\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_station_leader\", \"jdbcType\": \"number\"}, \"name\": \"controlStationLeader\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlStation\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"责任派出所领导\"}}}, {\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_person\", \"jdbcType\": \"json_id_array\"}, \"name\": \"controlPerson\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"1\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlStation\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"array\", \"items\": {\"type\": \"number\"}, \"title\": \"责任民警\", \"minItems\": 1}}}], \"required\": [\"controlPerson\", \"controlStationLeader\", \"controlStation\"], \"extendFields\": [{\"table\": \"t_profile_group_police_control\", \"value\": 3, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}]}', NULL, 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_group_control\", \"column\": \"group_id\", \"extendCondition\": [{\"value\": 3, \"column\": \"police_kind\"}]}', 'NO_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1323, '敏感时间节点', 'sensitiveTime', 'groupV2', 1333, 1, 34, NULL, NULL, '{\"name\": \"敏感时间节点\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_sensitive_time\", \"fields\": [{\"db\": {\"table\": \"t_profile_sensitive_time\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"节点名称\"}, \"properties\": {\"copyable\": true, \"editable\": true, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_sensitive_time\", \"column\": \"start_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"timestamp\"}, \"name\": \"start_time\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"datetime\", \"title\": \"起始时间\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_sensitive_time\", \"column\": \"end_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"timestamp\"}, \"name\": \"end_time\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"datetime\", \"title\": \"结束时间\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_sensitive_time\", \"column\": \"remark\", \"jdbcType\": \"string\"}, \"name\": \"remark\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"备注\"}, \"properties\": {\"copyable\": true, \"editable\": true, \"required\": false, \"sortable\": false}}}], \"selectable\": false, \"extendFields\": [{\"table\": \"t_profile_sensitive_time\", \"value\": 3, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}], \"searchFields\": []}', 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_sensitive_time\", \"column\": \"group_id\", \"idColumn\": \"id\", \"primaryColumn\": \"id\", \"extendCondition\": [{\"value\": 3, \"column\": \"police_kind\"}]}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1324, '相关人员', 'relatedPerson', 'groupV2', 1333, 1, 36, NULL, NULL, '{\"name\": \"相关人员\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_person\", \"fields\": [{\"db\": {\"table\": \"t_profile_person\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"人员名称\"}, \"properties\": {\"href\": \"/ys-app/archives/person/details?id={value}\", \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"risk_score\", \"mapping\": \"number_half_adjust_to_string\", \"jdbcType\": \"number\"}, \"name\": \"riskScore\", \"listSchema\": {\"style\": {\"align\": \"left\", \"width\": 128}, \"schema\": {\"type\": \"string\", \"title\": \"风险分值\"}, \"properties\": {\"color\": \"#FF6C6C\", \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true, \"instrLength\": 1, \"sortDefault\": \"descending\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"risk_level\", \"jdbcType\": \"string\"}, \"name\": \"riskLevel\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 128}, \"schema\": {\"type\": \"string\", \"title\": \"风险等级\"}, \"properties\": {\"colorMap\": {\"关注\": \"#333333 \", \"中风险\": \"#FFCE60\", \"低风险\": \"#6088D6\", \"高风险\": \"#FA8C34\", \"重中之重\": \"#EC3939\"}, \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"person_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"label_id_array\"}, \"name\": \"personLabel\", \"listSchema\": {\"style\": {\"align\": \"left\"}, \"filter\": {\"key\": \"personLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&person_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"人员标签\"}, \"schema\": {\"type\": \"array\", \"title\": \"人员标签\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2}}}, {\"db\": {\"table\": \"t_profile_person_group_relation\", \"column\": \"activity_level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\", \"databaseRelation\": {\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_group_relation\", \"joinTo\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}, \"joinFrom\": {\"table\": \"t_profile_group\", \"column\": \"id\", \"joinColumn\": \"group_id\"}}}, \"dict\": {\"type\": \"profile_activity_level\"}, \"name\": \"activityLevel\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"select\", \"title\": \"活跃程度\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"createDept\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"createDept\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}], \"selectable\": false, \"extendFields\": [{\"table\": \"t_profile_person_group_relation\", \"value\": 3, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}], \"searchFields\": [{\"key\": \"name\", \"name\": \"人员名称\"}], \"defaultSortParams\": {\"sortField\": \"riskScore\", \"sortDirection\": \"descending\"}}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_group_relation\", \"joinTo\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}, \"joinFrom\": {\"table\": \"t_profile_group\", \"column\": \"id\", \"joinColumn\": \"group_id\"}, \"extendCondition\": [{\"value\": 3, \"column\": \"police_kind\"}]}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1325, '相关线索', 'relatedClue', 'groupV2', 1333, 1, 38, NULL, NULL, '{\"name\": \"相关线索\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_clue\", \"fields\": [{\"db\": {\"table\": \"t_profile_clue\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"线索名称\"}, \"properties\": {\"href\": \"/ys-app/archives/clue/details?id={value}\", \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"clue_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"label_id_array\"}, \"name\": \"clueLabel\", \"listSchema\": {\"style\": {\"align\": \"left\"}, \"filter\": {\"key\": \"clueLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&clue_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"线索标签\"}, \"schema\": {\"type\": \"array\", \"title\": \"线索标签\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"emergency_level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_emergency_level\"}, \"name\": \"emergency_level\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"紧急程度\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"source\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_source\"}, \"name\": \"source\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"线索来源\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"create_time\", \"mapping\": \"date_time_to_text\", \"jdbcType\": \"datetime\"}, \"name\": \"createTime\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"disposal_status\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_disposal_status\"}, \"name\": \"disposal_status\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"处置状态\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}], \"selectable\": false, \"extendFields\": [{\"table\": \"t_profile_group_clue_relation\", \"value\": 3, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}], \"searchFields\": [{\"key\": \"name\", \"name\": \"线索名称\"}]}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_group_clue_relation\", \"joinTo\": {\"table\": \"t_profile_clue\", \"column\": \"id\", \"joinColumn\": \"clue_id\"}, \"joinFrom\": {\"table\": \"t_profile_group\", \"column\": \"id\", \"joinColumn\": \"group_id\"}, \"extendCondition\": [{\"value\": 3, \"column\": \"police_kind\"}]}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1326, '相关事件', 'relatedEvent', 'groupV2', 1334, 1, 41, NULL, NULL, '{\"name\": \"相关事件\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_event\", \"fields\": [{\"db\": {\"table\": \"t_profile_event\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"事件名称\"}, \"properties\": {\"href\": \"/ys-app/archives/event/details?id={value}\", \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"create_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"datetime\"}, \"name\": \"createTime\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"create_dept_id\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"create_dept_id\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}], \"selectable\": false, \"searchFields\": [{\"key\": \"name\", \"name\": \"事件名称\"}]}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_group_event_relation\", \"joinTo\": {\"table\": \"t_profile_event\", \"column\": \"id\", \"joinColumn\": \"event_id\"}, \"joinFrom\": {\"table\": \"t_profile_group\", \"column\": \"id\", \"joinColumn\": \"group_id\"}}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1327, '人员信息', 'person', 'personV2', 1328, 1, 3, '{\"name\": \"人员信息\", \"type\": \"TABLE_SCHEMA\", \"table\": \"t_profile_person\", \"fields\": [{\"db\": {\"table\": \"t_profile_person\", \"column\": \"id_number\", \"jdbcType\": \"string\"}, \"name\": \"id_number\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"证件号码\", \"copyable\": true}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"姓名\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"id_type\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"string\"}, \"dict\": {\"type\": \"id_type\"}, \"name\": \"id_type\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"证件类型\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"gender\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"string\"}, \"dict\": {\"type\": \"gender\"}, \"name\": \"gender\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"性别\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"former_name\", \"jdbcType\": \"string\"}, \"name\": \"former_name\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"曾用名\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"nick_name\", \"jdbcType\": \"string\"}, \"name\": \"nick_name\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"绰号\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"nation\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"nation\"}, \"name\": \"nation\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"民族\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"political_status\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"profile_political_status\"}, \"name\": \"political_status\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"政治面貌\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"martial_status\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"profile_martial_status\"}, \"name\": \"martial_status\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"婚姻状况\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"current_job\", \"jdbcType\": \"string\"}, \"name\": \"current_job\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"现职业\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"current_position\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"profile_current_position\"}, \"name\": \"current_position\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"目前所在地\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"person_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"json_id_array\"}, \"name\": \"person_label\", \"tableSchema\": {\"span\": 1, \"type\": \"label\", \"title\": \"人员标签\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"tel\", \"jdbcType\": \"string\"}, \"name\": \"tel\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"电话号码\", \"copyable\": true}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"registered_residence\", \"mapping\": \"district_code_to_name\", \"jdbcType\": \"string\"}, \"name\": \"registered_residence\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"户籍地所在区\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"registered_residence_detail\", \"jdbcType\": \"string\"}, \"name\": \"registered_residence_detail\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"户籍地详细地址\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"current_residence\", \"mapping\": \"district_code_to_name\", \"jdbcType\": \"string\"}, \"name\": \"current_residence\", \"tree\": {\"root\": \"000000\", \"type\": \"district\"}, \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"现住地所在区\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"current_residence_detail\", \"jdbcType\": \"string\"}, \"name\": \"current_residence_detail\", \"tableSchema\": {\"span\": 4, \"type\": \"string\", \"title\": \"现住址详细地址\", \"copyable\": false}}], \"moduleUi\": {\"column\": 4, \"bordered\": true}}', '{\"name\": \"人员信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_person\", \"fields\": [{\"db\": {\"table\": \"t_profile_person\", \"column\": \"id_type\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"id_type\"}, \"name\": \"idType\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"证件类型\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"photo\", \"jdbcType\": \"json_image_array\"}, \"name\": \"photo\", \"formSchema\": {\"ui\": {\"ui:options\": {\"style\": {\"position\": \"absolute\"}, \"width\": \"0.5\", \"action\": \"/upload/imgs\", \"widget\": \"upload\", \"isShowTitle\": false, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"array\", \"items\": {\"type\": \"object\"}, \"title\": \"照片\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"id_number\", \"jdbcType\": \"string\"}, \"name\": \"idNumber\", \"formSchema\": {\"ui\": {\"ui:options\": {\"style\": {\"display\": \"block\"}, \"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"证件号码\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"formSchema\": {\"ui\": {\"ui:options\": {\"style\": {\"display\": \"block\"}, \"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"姓名\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"current_position\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"profile_current_position_zg\"}, \"name\": \"nowLocation\", \"formSchema\": {\"ui\": {\"ui:options\": {\"style\": {\"display\": \"block\"}, \"width\": \"0.5\", \"widget\": \"radio\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"目前所在地\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"person_label\", \"jdbcType\": \"json_id_array\"}, \"name\": \"personLabel\", \"tree\": {\"root\": \"person\", \"type\": \"label\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"style\": {\"display\": \"block\"}, \"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": true, \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"array\", \"items\": {\"type\": \"array\"}, \"title\": \"人员标签\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"tel\", \"jdbcType\": \"json_string_array\"}, \"name\": \"tel\", \"formSchema\": {\"ui\": {\"ui:options\": {\"style\": {\"display\": \"block\"}, \"width\": \"0.5\", \"widget\": \"select\", \"multiple\": true, \"inputable\": true, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}, \"title\": \"电话号码\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"gender\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"gender\"}, \"name\": \"gender\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"radio\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"性别\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"former_name\", \"jdbcType\": \"string\"}, \"name\": \"usedName\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"曾用名\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"nick_name\", \"jdbcType\": \"string\"}, \"name\": \"nickName\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"绰号\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"nation\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"nation\"}, \"name\": \"nation\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"民族\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"political_status\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"profile_political_status\"}, \"name\": \"politicalStatus\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"radio\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"政治面貌\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"martial_status\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"profile_martial_status\"}, \"name\": \"maritalStatus\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"radio\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"婚姻状态\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"current_job\", \"jdbcType\": \"string\"}, \"name\": \"currentJob\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"现职业\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"registered_residence\", \"jdbcType\": \"string\"}, \"name\": \"registerArea\", \"tree\": {\"root\": \"000000\", \"type\": \"district\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"name\", \"value\": \"code\", \"children\": \"children\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"户籍地所在区\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"registered_residence_detail\", \"jdbcType\": \"string\"}, \"name\": \"registerAreaInfo\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"户籍地详细地址\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"current_residence\", \"jdbcType\": \"string\"}, \"name\": \"address\", \"tree\": {\"root\": \"000000\", \"type\": \"district\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"name\", \"value\": \"code\", \"children\": \"children\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"现住地所在区\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"current_residence_detail\", \"jdbcType\": \"string\"}, \"name\": \"addressInfo\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"现住地详细地址\"}}}], \"required\": [\"idType\", \"idNumber\", \"name\", \"registerArea\", \"personLabel\"]}', '{}', 1, '{\"type\": \"PRIMARY_KEY\", \"table\": \"t_profile_person\", \"column\": \"id\"}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 0, 1, 1, 1);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1328, '基本信息', 'basicInfo', 'personV2', NULL, 1, 2, NULL, NULL, NULL, 1, NULL, NULL, NULL, 1, 1, 1, 1);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1329, '群体材料', 'groupFiles', 'groupV2', NULL, 1, 44, NULL, NULL, NULL, 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_group_file_relation\", \"column\": \"id\", \"joinTo\": {\"table\": \"t_file_info\", \"column\": \"id\", \"joinColumn\": \"file_id\"}, \"joinFrom\": {\"table\": \"t_profile_group\", \"column\": \"id\", \"joinColumn\": \"group_id\"}}', 'FILE_SCHEMA', 'FILE_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1330, '群组管理', 'groupInfo', 'groupV2', NULL, 1, 45, NULL, NULL, NULL, 0, NULL, 'NO_SCHEMA', 'NO_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1331, '群体架构', 'groupStructure', 'groupV2', NULL, 1, 46, NULL, NULL, NULL, 0, NULL, 'NO_SCHEMA', 'NO_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1332, '管控工作记录', 'groupWorkRecord', 'groupV2', NULL, 1, 47, NULL, NULL, NULL, 0, NULL, 'NO_SCHEMA', 'NO_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1333, '经侦', 'jz', 'groupV2', 1310, 1, 23, NULL, NULL, NULL, 1, NULL, 'NO_SCHEMA', 'NO_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1334, '治安', 'za', 'groupV2', 1310, 1, 24, NULL, NULL, NULL, 1, NULL, 'NO_SCHEMA', 'NO_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1335, '车辆信息', 'vehicle', 'personV2', 1328, 1, 4, NULL, NULL, '{\"name\": \"车辆信息\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_vehicle\", \"fields\": [{\"db\": {\"table\": \"t_profile_vehicle\", \"column\": \"car_number\", \"jdbcType\": \"string\"}, \"name\": \"carNumber\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"车牌号\"}, \"properties\": {\"copyable\": true, \"editable\": true, \"required\": true, \"sortable\": false, \"validate\": [{\"message\": \"格式错误\", \"pattern\": \"^([京津晋冀蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼渝川贵云藏陕甘青宁新][ABCDEFGHJKLMNPQRSTUVWXYZ][1-9DF][1-9ABCDEFGHJKLMNPQRSTUVWXYZ]\\\\d{3}[1-9DF]|[京津晋冀蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼渝川贵云藏陕甘青宁新][ABCDEFGHJKLMNPQRSTUVWXYZ][\\\\dABCDEFGHJKLNMxPQRSTUVWXYZ]{5})$\"}]}}}, {\"db\": {\"table\": \"t_profile_vehicle\", \"column\": \"owner\", \"jdbcType\": \"string\"}, \"name\": \"owner\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"车辆所有人\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_vehicle\", \"column\": \"type\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"json_object_array\"}, \"dict\": {\"type\": \"profile_vehicle_type\"}, \"name\": \"type\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"select\", \"title\": \"类型\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": true, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_vehicle\", \"column\": \"source\", \"mapping\": \"source_to_name\", \"jdbcType\": \"string\"}, \"name\": \"source\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"来源\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"modifyDefaultValue\": \"{\\\"type\\\": 1}\", \"displayDefaultValue\": \"手动录入\"}}}], \"selectable\": false, \"searchFields\": []}', 1, '{\"type\": \"JSON_ID_ARRAY\", \"table\": \"t_profile_person\", \"column\": \"vehicle_ids\"}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1336, '虚拟身份', 'platform', 'personV2', 1328, 1, 5, NULL, NULL, '{\"name\": \"虚拟身份\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_platform_account\", \"fields\": [{\"db\": {\"table\": \"t_profile_platform_account\", \"column\": \"platform\", \"jdbcType\": \"string\"}, \"name\": \"platform\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"社交平台\"}, \"properties\": {\"default\": 1, \"copyable\": false, \"editable\": true, \"required\": true, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_platform_account\", \"column\": \"account\", \"jdbcType\": \"string\"}, \"name\": \"account\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"社交平台昵称\"}, \"properties\": {\"copyable\": true, \"editable\": true, \"required\": true, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_platform_account\", \"column\": \"source\", \"mapping\": \"source_to_name\", \"jdbcType\": \"string\"}, \"name\": \"source\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"来源\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"modifyDefaultValue\": \"{\\\"type\\\": 1}\", \"displayDefaultValue\": \"手动录入\"}}}], \"selectable\": false, \"searchFields\": []}', 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_platform_account\", \"column\": \"person_id\", \"idColumn\": \"id\", \"primaryColumn\": \"id\"}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1337, '风险点信息', 'riskInfo', 'groupV2', 1334, 1, 28, '{\"name\": \"风险点信息\", \"type\": \"TABLE_SCHEMA\", \"table\": \"t_profile_group_risk_other\", \"fields\": [{\"db\": {\"table\": \"t_profile_group_risk_other\", \"column\": \"main_demand\", \"jdbcType\": \"string\"}, \"name\": \"main_demand\", \"tableSchema\": {\"span\": 4, \"type\": \"string\", \"title\": \"诉求情况\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_risk_other\", \"column\": \"petition_info\", \"jdbcType\": \"string\"}, \"name\": \"petition_info\", \"tableSchema\": {\"span\": 4, \"type\": \"string\", \"title\": \"工作难点\", \"copyable\": false}}], \"moduleUi\": {\"column\": 4, \"bordered\": true}}', '{\"name\": \"风险点信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_group_risk_other\", \"fields\": [{\"db\": {\"table\": \"t_profile_group_risk_other\", \"column\": \"main_demand\", \"jdbcType\": \"string\"}, \"name\": \"main_demand\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"textarea\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"诉求情况\"}}}, {\"db\": {\"table\": \"t_profile_group_risk_other\", \"column\": \"petition_info\", \"jdbcType\": \"string\"}, \"name\": \"petition_info\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"textarea\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"工作难点\"}}}], \"required\": [], \"extendFields\": [{\"table\": \"t_profile_group_risk_other\", \"value\": 4, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}]}', NULL, 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_group_risk_other\", \"column\": \"group_id\", \"extendCondition\": [{\"value\": 4, \"column\": \"police_kind\"}]}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1338, '党政管控信息', 'govControl', 'groupV2', 1334, 1, 31, '{\"name\": \"党政管控信息\", \"table\": \"t_profile_group_dz_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_group_dz_control\", \"column\": \"defuse_government\", \"jdbcType\": \"string\"}, \"name\": \"defuse_government\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"化解责任单位\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_dz_control\", \"column\": \"defuse_government_person\", \"jdbcType\": \"string\"}, \"name\": \"defaluse_government_person\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"化解责任人\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_dz_control\", \"column\": \"defuse_government_contact\", \"jdbcType\": \"string\"}, \"name\": \"defuse_government_contact\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"联系方式\", \"copyable\": true}}, {\"db\": {\"table\": \"t_profile_group_dz_control\", \"column\": \"control_government\", \"jdbcType\": \"string\"}, \"name\": \"control_government\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"主管监管部门\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_dz_control\", \"column\": \"control_government_person\", \"jdbcType\": \"string\"}, \"name\": \"control_government_person\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"主管监管部门责任人\", \"config\": {\"fieldName\": \"control_government_person\", \"processType\": \"string\", \"processConfig\": null}, \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_dz_control\", \"column\": \"control_government_contact\", \"jdbcType\": \"string\"}, \"name\": \"control_government_contact\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"联系方式\", \"copyable\": true}}], \"moduleUi\": {\"column\": 3, \"bordered\": true}}', '{\"name\": \"党政管控信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_group_dz_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_group_dz_control\", \"column\": \"defuse_government\", \"jdbcType\": \"string\"}, \"name\": \"defuseGovernment\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"化解责任单位\"}}}, {\"db\": {\"table\": \"t_profile_group_dz_control\", \"column\": \"defuse_government_person\", \"jdbcType\": \"string\"}, \"name\": \"defuseGovernmentPerson\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"化解责任人\"}}}, {\"db\": {\"table\": \"t_profile_group_dz_control\", \"column\": \"defuse_government_contact\", \"jdbcType\": \"string\"}, \"name\": \"defuseGovernmentContact\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"联系方式\"}}}, {\"db\": {\"table\": \"t_profile_group_dz_control\", \"column\": \"control_government\", \"jdbcType\": \"string\"}, \"name\": \"controlGovernment\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"主管监管部门\"}}}, {\"db\": {\"table\": \"t_profile_group_dz_control\", \"column\": \"control_government_person\", \"jdbcType\": \"string\"}, \"name\": \"controlGovernmentPerson\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"主管监管部门责任人\"}}}, {\"db\": {\"table\": \"t_profile_group_dz_control\", \"column\": \"control_government_contact\", \"jdbcType\": \"string\"}, \"name\": \"controlGovernmentContact\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"联系方式\"}}}], \"required\": []}', NULL, 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_group_dz_control\", \"column\": \"group_id\"}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1339, '公安管控信息', 'policeControl', 'groupV2', 1334, 1, 40, '{\"name\": \"公安管控信息\", \"type\": \"TABLE_SCHEMA\", \"table\": \"t_profile_group_za_ga_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_group_za_ga_control\", \"column\": \"control_bureau\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"control_bureau\", \"tree\": {\"type\": \"dept\"}, \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"管控分县局\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_za_ga_control\", \"column\": \"control_bureau_person\", \"mapping\": \"user_id_array_to_single_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_bureau_person\", \"tableSchema\": {\"span\": 1, \"type\": \"user\", \"title\": \"负责人\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_za_ga_control\", \"column\": \"control_bureau_contact\", \"jdbcType\": \"string\"}, \"name\": \"control_bureau_contact\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"联系方式\", \"copyable\": true}}, {\"db\": {\"table\": \"t_profile_group_za_ga_control\", \"column\": \"control_police\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"control_police\", \"tree\": {\"type\": \"dept\"}, \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"管控责任警种\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_za_ga_control\", \"column\": \"control_police_person\", \"mapping\": \"user_id_array_to_single_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_police_person\", \"tableSchema\": {\"span\": 1, \"type\": \"user\", \"title\": \"负责人\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_za_ga_control\", \"column\": \"control_police_contact\", \"jdbcType\": \"string\"}, \"name\": \"control_police_contact\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"联系方式\", \"copyable\": true}}], \"moduleUi\": {\"column\": 2, \"bordered\": true}}', '{\"name\": \"公安管控信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_group_za_ga_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_group_za_ga_control\", \"column\": \"control_bureau\", \"jdbcType\": \"string\"}, \"name\": \"controlBureau\", \"tree\": {\"root\": \"510500000000\", \"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"管控分县局\"}}}, {\"db\": {\"table\": \"t_profile_group_za_ga_control\", \"column\": \"control_bureau_person\", \"jdbcType\": \"number\"}, \"name\": \"controlBureauPerson\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlBureau\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"负责人\"}}}, {\"db\": {\"table\": \"t_profile_group_za_ga_control\", \"column\": \"control_bureau_contact\", \"jdbcType\": \"string\"}, \"name\": \"controlBureauContact\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"联系方式\"}}}, {\"db\": {\"table\": \"t_profile_group_za_ga_control\", \"column\": \"control_police\", \"jdbcType\": \"string\"}, \"name\": \"controlPolice\", \"tree\": {\"root\": \"510500000000\", \"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"管控责任警种\"}}}, {\"db\": {\"table\": \"t_profile_group_za_ga_control\", \"column\": \"control_police_person\", \"jdbcType\": \"number\"}, \"name\": \"controlPolicePerson\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlPolice\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"负责人\"}}}, {\"db\": {\"table\": \"t_profile_group_za_ga_control\", \"column\": \"control_police_contact\", \"jdbcType\": \"string\"}, \"name\": \"controlPoliceContact\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"联系方式\"}}}], \"required\": []}', NULL, 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_group_za_ga_control\", \"column\": \"group_id\"}', 'NO_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1341, '相关人员', 'relatedPerson', 'groupV2', 1334, 1, 42, NULL, NULL, '{\"name\": \"相关人员\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_person\", \"fields\": [{\"db\": {\"table\": \"t_profile_person\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"人员名称\"}, \"properties\": {\"href\": \"/ys-app/archives/person/details?id={value}\", \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"risk_score\", \"mapping\": \"number_half_adjust_to_string\", \"jdbcType\": \"number\"}, \"name\": \"riskScore\", \"listSchema\": {\"style\": {\"align\": \"left\", \"width\": 128}, \"schema\": {\"type\": \"string\", \"title\": \"风险分值\"}, \"properties\": {\"color\": \"#FF6C6C\", \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true, \"instrLength\": 1, \"sortDefault\": \"descending\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"risk_level\", \"jdbcType\": \"string\"}, \"name\": \"riskLevel\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 128}, \"schema\": {\"type\": \"string\", \"title\": \"风险等级\"}, \"properties\": {\"colorMap\": {\"关注\": \"#333333 \", \"中风险\": \"#FFCE60\", \"低风险\": \"#6088D6\", \"高风险\": \"#FA8C34\", \"重中之重\": \"#EC3939\"}, \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"person_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"label_id_array\"}, \"name\": \"personLabel\", \"listSchema\": {\"style\": {\"align\": \"left\"}, \"filter\": {\"key\": \"personLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&person_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"人员标签\"}, \"schema\": {\"type\": \"array\", \"title\": \"人员标签\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2}}}, {\"db\": {\"table\": \"t_profile_person_group_relation\", \"column\": \"activity_level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\", \"databaseRelation\": {\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_group_relation\", \"joinTo\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}, \"joinFrom\": {\"table\": \"t_profile_group\", \"column\": \"id\", \"joinColumn\": \"group_id\"}}}, \"dict\": {\"type\": \"profile_activity_level\"}, \"name\": \"activityLevel\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"select\", \"title\": \"活跃程度\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"createDept\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"createDept\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}], \"selectable\": false, \"extendFields\": [{\"table\": \"t_profile_person_group_relation\", \"value\": 4, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}], \"searchFields\": [{\"key\": \"name\", \"name\": \"人员名称\"}], \"defaultSortParams\": {\"sortField\": \"riskScore\", \"sortDirection\": \"descending\"}}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_group_relation\", \"joinTo\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}, \"joinFrom\": {\"table\": \"t_profile_group\", \"column\": \"id\", \"joinColumn\": \"group_id\"}, \"extendCondition\": [{\"value\": 4, \"column\": \"police_kind\"}]}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1342, '相关线索', 'relatedClue', 'groupV2', 1334, 1, 43, NULL, NULL, '{\"name\": \"相关线索\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_clue\", \"fields\": [{\"db\": {\"table\": \"t_profile_clue\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"线索名称\"}, \"properties\": {\"href\": \"/ys-app/archives/clue/details?id={value}\", \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"clue_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"label_id_array\"}, \"name\": \"clueLabel\", \"listSchema\": {\"style\": {\"align\": \"left\"}, \"filter\": {\"key\": \"clueLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&clue_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"线索标签\"}, \"schema\": {\"type\": \"array\", \"title\": \"线索标签\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"emergency_level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_emergency_level\"}, \"name\": \"emergency_level\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"紧急程度\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"source\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_source\"}, \"name\": \"source\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"线索来源\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"create_time\", \"mapping\": \"date_time_to_text\", \"jdbcType\": \"datetime\"}, \"name\": \"createTime\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"disposal_status\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_disposal_status\"}, \"name\": \"disposal_status\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"处置状态\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}], \"selectable\": false, \"extendFields\": [{\"table\": \"t_profile_group_clue_relation\", \"value\": 4, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}], \"searchFields\": [{\"key\": \"name\", \"name\": \"线索名称\"}]}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_group_clue_relation\", \"joinTo\": {\"table\": \"t_profile_clue\", \"column\": \"id\", \"joinColumn\": \"clue_id\"}, \"joinFrom\": {\"table\": \"t_profile_group\", \"column\": \"id\", \"joinColumn\": \"group_id\"}, \"extendCondition\": [{\"value\": 4, \"column\": \"police_kind\"}]}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1343, '相关事件', 'relatedEvent', 'personV2', 1316, 1, 4, NULL, NULL, '{\"name\": \"相关事件\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_event\", \"fields\": [{\"db\": {\"table\": \"t_profile_event\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"事件名称\"}, \"properties\": {\"href\": \"/ys-app/archives/event/details?id={value}\", \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"create_dept_id\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"create_dept_id\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"create_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"datetime\"}, \"name\": \"createTime\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}], \"selectable\": false, \"extendFields\": [{\"table\": \"t_profile_person_event_relation\", \"value\": 10, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}], \"searchFields\": [{\"key\": \"name\", \"name\": \"事件名称\"}]}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_event_relation\", \"joinTo\": {\"table\": \"t_profile_event\", \"column\": \"id\", \"joinColumn\": \"event_id\"}, \"joinFrom\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}, \"extendCondition\": [{\"value\": \"10\", \"column\": \"police_kind\"}]}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1345, '刑侦', 'xz', 'personV2', 1313, 1, 3, NULL, NULL, NULL, 1, NULL, NULL, NULL, 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1346, '政府管控信息', 'govControl', 'personV2', 1345, 1, 4, '{\"name\": \"政府管控信息\", \"table\": \"t_profile_person_government_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government\", \"jdbcType\": \"string\"}, \"name\": \"control_government\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"党政责任部门\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government_person\", \"jdbcType\": \"string\"}, \"name\": \"control_government_person\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"党政责任人\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government_person_duty\", \"jdbcType\": \"string\"}, \"name\": \"control_government_person_duty\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"党政责任人职务\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government_contact\", \"jdbcType\": \"string\"}, \"name\": \"control_government_contact\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"联系方式\", \"copyable\": true}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community\", \"jdbcType\": \"string\"}, \"name\": \"control_community\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任街道社区\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community_person\", \"jdbcType\": \"string\"}, \"name\": \"control_community_person\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"社区责任人\", \"config\": {\"fieldName\": \"control_community_person\", \"processType\": \"string\", \"processConfig\": null}, \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community_person_duty\", \"jdbcType\": \"string\"}, \"name\": \"control_community_person_duty\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"社区责任人职务\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community_contact\", \"jdbcType\": \"string\"}, \"name\": \"control_community_contact\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"联系方式\", \"copyable\": true}}], \"moduleUi\": {\"column\": 3, \"bordered\": true}}', '{\"name\": \"政府管控信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_person_government_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government\", \"jdbcType\": \"string\"}, \"name\": \"controlGovernment\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"党政责任部门\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government_person\", \"jdbcType\": \"string\"}, \"name\": \"controlGovernmentPerson\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"党政责任人\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government_person_duty\", \"jdbcType\": \"string\"}, \"name\": \"controlGovernmentPersonDuty\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"党政责任人职务\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government_contact\", \"jdbcType\": \"string\"}, \"name\": \"controlGovernmentContact\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"党政责任人联系方式\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community\", \"jdbcType\": \"string\"}, \"name\": \"controlCommunity\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"责任街道社区\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community_person\", \"jdbcType\": \"string\"}, \"name\": \"controlCommunityPerson\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"社区责任人\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community_person_duty\", \"jdbcType\": \"string\"}, \"name\": \"controlCommunityPersonDuty\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"社区责任人职务\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community_contact\", \"jdbcType\": \"string\"}, \"name\": \"controlCommunityContact\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"社区责任人联系方式\"}}}], \"required\": [], \"extendFields\": [{\"table\": \"t_profile_person_government_control\", \"value\": 5, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}]}', NULL, 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_person_government_control\", \"column\": \"person_id\", \"extendCondition\": [{\"value\": 5, \"column\": \"police_kind\"}]}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1347, '公安管控信息', 'policeControl', 'personV2', 1345, 1, 5, '{\"name\": \"公安管控信息\", \"type\": \"TABLE_SCHEMA\", \"table\": \"t_profile_person_police_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_bureau\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"control_bureau\", \"tree\": {\"type\": \"dept\"}, \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任分局\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_bureau_leader\", \"mapping\": \"user_id_array_to_single_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_bureau_leader\", \"tableSchema\": {\"span\": 1, \"type\": \"user\", \"title\": \"责任领导\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_police\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"control_police\", \"tree\": {\"type\": \"dept\"}, \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任警种\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_police_leader\", \"mapping\": \"user_id_array_to_single_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_police_leader\", \"tableSchema\": {\"span\": 1, \"type\": \"user\", \"title\": \"责任领导\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_station\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"control_station\", \"tree\": {\"type\": \"dept\"}, \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任派出所\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_station_leader\", \"mapping\": \"user_id_array_to_single_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_station_leader\", \"tableSchema\": {\"span\": 1, \"type\": \"user\", \"title\": \"责任领导\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_person\", \"mapping\": \"user_id_array_to_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_person\", \"tableSchema\": {\"span\": 2, \"type\": \"multiUser\", \"title\": \"责任民警\", \"copyable\": false}}], \"moduleUi\": {\"column\": 2, \"bordered\": true}}', '{\"name\": \"公安管控信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_person_police_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_bureau\", \"jdbcType\": \"string\"}, \"name\": \"controlBureau\", \"tree\": {\"root\": \"510500000000\", \"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"责任分局\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_bureau_leader\", \"jdbcType\": \"number\"}, \"name\": \"controlBureauLeader\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlBureau\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"责任分局领导\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_police\", \"jdbcType\": \"string\"}, \"name\": \"controlPolice\", \"tree\": {\"root\": \"510500000000\", \"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"责任警种\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_police_leader\", \"jdbcType\": \"number\"}, \"name\": \"controlPoliceLeader\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlPolice\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"责任警种领导\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_station\", \"jdbcType\": \"string\"}, \"name\": \"controlStation\", \"tree\": {\"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"责任派出所\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_station_leader\", \"jdbcType\": \"number\"}, \"name\": \"controlStationLeader\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlStation\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"责任派出所领导\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_person\", \"jdbcType\": \"json_id_array\"}, \"name\": \"controlPerson\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"1\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlStation\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"array\", \"items\": {\"type\": \"number\"}, \"title\": \"责任民警\", \"minItems\": 1}}}], \"required\": [\"controlPerson\", \"controlStationLeader\", \"controlStation\"], \"extendFields\": [{\"table\": \"t_profile_person_police_control\", \"value\": 5, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}]}', NULL, 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_person\", \"column\": \"person_id\", \"extendCondition\": [{\"value\": 5, \"column\": \"police_kind\"}]}', 'NO_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1348, '相关线索', 'relatedClue', 'personV2', 1345, 1, 6, NULL, NULL, '{\"name\": \"相关线索\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_clue\", \"fields\": [{\"db\": {\"table\": \"t_profile_clue\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"线索名称\"}, \"properties\": {\"href\": \"/ys-app/archives/clue/details?id={value}\", \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"clue_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"label_id_array\"}, \"name\": \"clueLabel\", \"listSchema\": {\"style\": {\"align\": \"left\"}, \"filter\": {\"key\": \"clueLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&clue_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"线索标签\"}, \"schema\": {\"type\": \"array\", \"title\": \"线索标签\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"emergency_level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_emergency_level\"}, \"name\": \"emergency_level\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"紧急程度\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"source\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_source_type\"}, \"name\": \"source\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"线索来源\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"create_time\", \"mapping\": \"date_time_to_text\", \"jdbcType\": \"datetime\"}, \"name\": \"createTime\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"disposal_status\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_disposal_status\"}, \"name\": \"disposal_status\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"处置状态\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}], \"selectable\": false, \"extendFields\": [{\"table\": \"t_profile_person_clue_relation\", \"value\": 5, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}], \"searchFields\": [{\"key\": \"name\", \"name\": \"线索名称\"}]}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_clue_relation\", \"joinTo\": {\"table\": \"t_profile_clue\", \"column\": \"id\", \"joinColumn\": \"clue_id\"}, \"joinFrom\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}, \"extendCondition\": [{\"value\": 5, \"column\": \"police_kind\"}]}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1349, '相关案件', 'relatedCase', 'personV2', 1345, 1, 7, NULL, NULL, '{\"name\": \"相关案件\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_case\", \"fields\": [{\"db\": {\"table\": \"t_profile_case\", \"column\": \"ajmc\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"案件名称\"}, \"properties\": {\"href\": \"/ys-app/archives/case/details?id={value}\", \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_case\", \"column\": \"update_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"update_dept_id\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"update_dept_id\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_case\", \"column\": \"update_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"datetime\"}, \"name\": \"update_time\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}], \"selectable\": false, \"extendFields\": [{\"table\": \"t_profile_person_case_relation\", \"value\": 5, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}], \"searchFields\": [{\"key\": \"name\", \"name\": \"案件名称\"}]}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_case_related_person\", \"joinTo\": {\"table\": \"t_profile_case\", \"column\": \"asjbh\", \"joinColumn\": \"asjbh\"}, \"joinFrom\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}, \"extendCondition\": [{\"value\": 5, \"column\": \"police_kind\"}]}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1350, '基本情况', 'baseInfo', 'groupV2', 1312, 1, 18, '{\"name\": \"基本情况\", \"type\": \"TABLE_SCHEMA\", \"table\": \"t_profile_group_base_info_relation\", \"fields\": [{\"db\": {\"table\": \"t_profile_group_base_info_relation\", \"column\": \"basic_info\", \"jdbcType\": \"string\"}, \"name\": \"basic_info\", \"tableSchema\": {\"span\": 4, \"type\": \"string\", \"title\": \"基本情况\", \"copyable\": false}}], \"moduleUi\": {\"column\": 4, \"bordered\": true}}', '{\"name\": \"基本情况\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_group_base_info_relation\", \"fields\": [{\"db\": {\"table\": \"t_profile_group_base_info_relation\", \"column\": \"basic_info\", \"jdbcType\": \"string\"}, \"name\": \"basic_info\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"textarea\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"基本情况\"}}}], \"required\": []}', NULL, 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_group_base_info_relation\", \"column\": \"group_id\", \"idColumn\": \"id\", \"primaryColumn\": \"id\"}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1351, '工作措施', 'workMeasures', 'groupV2', 1312, 1, 19, '{\"name\": \"工作措施\", \"type\": \"TABLE_SCHEMA\", \"table\": \"t_profile_group_work_measures_relation\", \"fields\": [{\"db\": {\"table\": \"t_profile_group_work_measures_relation\", \"column\": \"work_measures\", \"jdbcType\": \"string\"}, \"name\": \"work_measures\", \"tableSchema\": {\"span\": 4, \"type\": \"string\", \"title\": \"工作措施\", \"copyable\": false}}], \"moduleUi\": {\"column\": 4, \"bordered\": true}}', '{\"name\": \"工作措施\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_group_work_measures_relation\", \"fields\": [{\"db\": {\"table\": \"t_profile_group_work_measures_relation\", \"column\": \"work_measures\", \"jdbcType\": \"string\"}, \"name\": \"work_measures\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"textarea\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"工作措施\"}}}], \"required\": []}', NULL, 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_group_work_measures_relation\", \"column\": \"group_id\", \"idColumn\": \"id\", \"primaryColumn\": \"id\"}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1352, '现实动向', 'realtimeTrend', 'groupV2', 1312, 1, 20, '{\"name\": \"现实动向\", \"type\": \"TABLE_SCHEMA\", \"table\": \"t_profile_group_realtime_trend_relation\", \"fields\": [{\"db\": {\"table\": \"t_profile_group_realtime_trend_relation\", \"column\": \"realtime_trend\", \"jdbcType\": \"string\"}, \"name\": \"realtime_trend\", \"tableSchema\": {\"span\": 4, \"type\": \"string\", \"title\": \"现实动向\", \"copyable\": false}}], \"moduleUi\": {\"column\": 4, \"bordered\": true}}', '{\"name\": \"现实动向\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_group_realtime_trend_relation\", \"fields\": [{\"db\": {\"table\": \"t_profile_group_realtime_trend_relation\", \"column\": \"realtime_trend\", \"jdbcType\": \"string\"}, \"name\": \"realtime_trend\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"textarea\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"现实动向\"}}}], \"required\": []}', NULL, 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_group_realtime_trend_relation\", \"column\": \"group_id\", \"idColumn\": \"id\", \"primaryColumn\": \"id\"}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1353, '打处情况', 'punishInfo', 'groupV2', 1312, 1, 21, '{\"name\": \"打处情况\", \"type\": \"TABLE_SCHEMA\", \"table\": \"t_profile_group_punish_info_relation\", \"fields\": [{\"db\": {\"table\": \"t_profile_group_punish_info_relation\", \"column\": \"punish_info\", \"jdbcType\": \"string\"}, \"name\": \"punish_info\", \"tableSchema\": {\"span\": 4, \"type\": \"string\", \"title\": \"打处情况\", \"copyable\": false}}], \"moduleUi\": {\"column\": 4, \"bordered\": true}}', '{\"name\": \"打处情况\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_group_punish_info_relation\", \"fields\": [{\"db\": {\"table\": \"t_profile_group_punish_info_relation\", \"column\": \"punish_info\", \"jdbcType\": \"string\"}, \"name\": \"punish_info\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"textarea\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"打处情况\"}}}], \"required\": []}', NULL, 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_group_punish_info_relation\", \"column\": \"group_id\", \"idColumn\": \"id\", \"primaryColumn\": \"id\"}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1373, '风险点信息', 'riskPoint', 'personV2', 1307, 1, 1, '{\"name\": \"风险点信息\", \"type\": \"TABLE_SCHEMA\", \"table\": \"t_profile_person_risk_jz\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_risk_jz\", \"column\": \"risk_level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"string\"}, \"dict\": {\"type\": \"profile_person_risk_level\"}, \"name\": \"risk_level\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"风险级别\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_risk_jz\", \"column\": \"current_location\", \"mapping\": \"district_code_to_name\", \"jdbcType\": \"string\"}, \"name\": \"current_location\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"现位置\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_risk_jz\", \"column\": \"work_unit\", \"jdbcType\": \"string\"}, \"name\": \"work_unit\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"就职单位\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_risk_jz\", \"column\": \"work_duty\", \"jdbcType\": \"string\"}, \"name\": \"work_duty\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"职务\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_risk_jz\", \"column\": \"retire_time\", \"mapping\": \"date_to_text\", \"jdbcType\": \"string\"}, \"name\": \"retire_time\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"离退休时间\", \"copyable\": false}}], \"moduleUi\": {\"column\": 3, \"bordered\": true}}', '{\"name\": \"风险点信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_person_risk_jz\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_risk_jz\", \"column\": \"risk_level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"profile_person_risk_level\"}, \"name\": \"risk_level\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"radio\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"风险等级\"}}}, {\"db\": {\"table\": \"t_profile_person_risk_jz\", \"column\": \"current_location\", \"mapping\": \"district_code_to_name\", \"jdbcType\": \"string\"}, \"name\": \"current_location\", \"tree\": {\"root\": \"510600\", \"type\": \"district\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"name\", \"value\": \"code\", \"children\": \"children\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"现位置\"}}}, {\"db\": {\"table\": \"t_profile_person_risk_jz\", \"column\": \"work_unit\", \"jdbcType\": \"string\"}, \"name\": \"work_unit\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"就职单位\"}}}, {\"db\": {\"table\": \"t_profile_person_risk_jz\", \"column\": \"work_duty\", \"jdbcType\": \"string\"}, \"name\": \"work_duty\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"职务\"}}}, {\"db\": {\"table\": \"t_profile_person_risk_jz\", \"column\": \"retire_time\", \"jdbcType\": \"string\"}, \"name\": \"retire_time\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"datePicker\", \"showTime\": false, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"离退休时间\"}}}], \"required\": []}', NULL, 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_person\", \"column\": \"person_id\"}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1374, '其他', 'qt', 'groupV2', 1310, 1, 25, NULL, NULL, NULL, 1, NULL, 'NO_SCHEMA', 'NO_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1375, '风险点信息', 'riskInfo', 'groupV2', 1374, 1, 26, '{\"name\": \"风险点信息\", \"type\": \"TABLE_SCHEMA\", \"table\": \"t_profile_group_risk_other\", \"fields\": [{\"db\": {\"table\": \"t_profile_group_risk_other\", \"column\": \"main_demand\", \"jdbcType\": \"string\"}, \"name\": \"main_demand\", \"tableSchema\": {\"span\": 4, \"type\": \"string\", \"title\": \"风险背景\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_risk_other\", \"column\": \"petition_info\", \"jdbcType\": \"string\"}, \"name\": \"petition_info\", \"tableSchema\": {\"span\": 4, \"type\": \"string\", \"title\": \"化解难点\", \"copyable\": false}}], \"moduleUi\": {\"column\": 4, \"bordered\": true}}', '{\"name\": \"风险点信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_group_risk_other\", \"fields\": [{\"db\": {\"table\": \"t_profile_group_risk_other\", \"column\": \"main_demand\", \"jdbcType\": \"string\"}, \"name\": \"main_demand\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"textarea\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"风险背景\"}}}, {\"db\": {\"table\": \"t_profile_group_risk_other\", \"column\": \"petition_info\", \"jdbcType\": \"string\"}, \"name\": \"petition_info\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"textarea\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"化解难点\"}}}], \"required\": [], \"extendFields\": [{\"table\": \"t_profile_group_risk_other\", \"value\": 99, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}]}', NULL, 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_group_risk_other\", \"column\": \"group_id\", \"extendCondition\": [{\"value\": 99, \"column\": \"police_kind\"}]}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1376, '政府管控信息', 'govControl', 'groupV2', 1374, 1, 29, '{\"name\": \"政府管控信息\", \"table\": \"t_profile_group_government_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_government\", \"jdbcType\": \"string\"}, \"name\": \"control_government\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"党政责任部门\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_government_person\", \"jdbcType\": \"string\"}, \"name\": \"control_government_person\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"党政责任人\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_government_person_duty\", \"jdbcType\": \"string\"}, \"name\": \"control_government_person_duty\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"党政责任人职务\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_government_contact\", \"jdbcType\": \"string\"}, \"name\": \"control_government_contact\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"联系方式\", \"copyable\": true}}, {\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_community\", \"jdbcType\": \"string\"}, \"name\": \"control_community\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任街道社区\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_community_person\", \"jdbcType\": \"string\"}, \"name\": \"control_community_person\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"社区责任人\", \"config\": {\"fieldName\": \"control_community_person\", \"processType\": \"string\", \"processConfig\": null}, \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_community_person_duty\", \"jdbcType\": \"string\"}, \"name\": \"control_community_person_duty\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"社区责任人职务\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_community_contact\", \"jdbcType\": \"string\"}, \"name\": \"control_community_contact\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"联系方式\", \"copyable\": true}}], \"moduleUi\": {\"column\": 3, \"bordered\": true}}', '{\"name\": \"政府管控信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_group_government_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_government\", \"jdbcType\": \"string\"}, \"name\": \"controlGovernment\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"党政责任部门\"}}}, {\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_government_person\", \"jdbcType\": \"string\"}, \"name\": \"controlGovernmentPerson\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"党政责任人\"}}}, {\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_government_contact\", \"jdbcType\": \"string\"}, \"name\": \"controlGovernmentContact\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"联系方式\"}}}, {\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_community\", \"jdbcType\": \"string\"}, \"name\": \"controlCommunity\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"责任街道社区\"}}}, {\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_community_person\", \"jdbcType\": \"string\"}, \"name\": \"controlCommunityPerson\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"社区责任人\"}}}, {\"db\": {\"table\": \"t_profile_group_government_control\", \"column\": \"control_community_contact\", \"jdbcType\": \"string\"}, \"name\": \"controlCommunityContact\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"联系方式\"}}}], \"required\": [], \"extendFields\": [{\"table\": \"t_profile_group_government_control\", \"value\": 99, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}]}', NULL, 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_group_government_control\", \"column\": \"group_id\", \"extendCondition\": [{\"value\": 99, \"column\": \"police_kind\"}]}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1377, '公安管控信息', 'policeControl', 'groupV2', 1374, 1, 33, '{\"name\": \"公安管控信息\", \"type\": \"TABLE_SCHEMA\", \"table\": \"t_profile_group_police_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_bureau\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"control_bureau\", \"tree\": {\"type\": \"dept\"}, \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任分局\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_bureau_leader\", \"mapping\": \"user_id_array_to_single_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_bureau_leader\", \"tableSchema\": {\"span\": 1, \"type\": \"user\", \"title\": \"责任领导\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_police\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"control_police\", \"tree\": {\"type\": \"dept\"}, \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任警种\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_police_leader\", \"mapping\": \"user_id_array_to_single_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_police_leader\", \"tableSchema\": {\"span\": 1, \"type\": \"user\", \"title\": \"责任领导\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_station\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"control_station\", \"tree\": {\"type\": \"dept\"}, \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任派出所\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_station_leader\", \"mapping\": \"user_id_array_to_single_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_station_leader\", \"tableSchema\": {\"span\": 1, \"type\": \"user\", \"title\": \"责任领导\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_person\", \"mapping\": \"user_id_array_to_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_person\", \"tableSchema\": {\"span\": 2, \"type\": \"multiUser\", \"title\": \"责任民警\", \"copyable\": false}}], \"moduleUi\": {\"column\": 2, \"bordered\": true}}', '{\"name\": \"公安管控信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_group_police_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_bureau\", \"jdbcType\": \"string\"}, \"name\": \"controlBureau\", \"tree\": {\"root\": \"510500000000\", \"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"责任分局\"}}}, {\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_bureau_leader\", \"jdbcType\": \"number\"}, \"name\": \"controlBureauLeader\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlBureau\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"责任分局领导\"}}}, {\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_police\", \"jdbcType\": \"string\"}, \"name\": \"controlPolice\", \"tree\": {\"root\": \"510500000000\", \"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"责任警种\"}}}, {\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_police_leader\", \"jdbcType\": \"number\"}, \"name\": \"controlPoliceLeader\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlPolice\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"责任警种领导\"}}}, {\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_station\", \"jdbcType\": \"string\"}, \"name\": \"controlStation\", \"tree\": {\"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"责任派出所\"}}}, {\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_station_leader\", \"jdbcType\": \"number\"}, \"name\": \"controlStationLeader\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlStation\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"责任派出所领导\"}}}, {\"db\": {\"table\": \"t_profile_group_police_control\", \"column\": \"control_person\", \"jdbcType\": \"json_id_array\"}, \"name\": \"controlPerson\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"1\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlStation\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"array\", \"items\": {\"type\": \"number\"}, \"title\": \"责任民警\", \"minItems\": 1}}}], \"required\": [\"controlPerson\", \"controlStationLeader\", \"controlStation\"], \"extendFields\": [{\"table\": \"t_profile_group_police_control\", \"value\": 99, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}]}', NULL, 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_group_control\", \"column\": \"group_id\", \"extendCondition\": [{\"value\": 99, \"column\": \"police_kind\"}]}', 'NO_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1378, '敏感时间节点', 'sensitiveTime', 'groupV2', 1374, 1, 35, NULL, NULL, '{\"name\": \"敏感时间节点\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_sensitive_time\", \"fields\": [{\"db\": {\"table\": \"t_profile_sensitive_time\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"节点名称\"}, \"properties\": {\"copyable\": true, \"editable\": true, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_sensitive_time\", \"column\": \"start_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"timestamp\"}, \"name\": \"start_time\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"datetime\", \"title\": \"起始时间\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_sensitive_time\", \"column\": \"end_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"timestamp\"}, \"name\": \"end_time\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"datetime\", \"title\": \"结束时间\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_sensitive_time\", \"column\": \"remark\", \"jdbcType\": \"string\"}, \"name\": \"remark\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"备注\"}, \"properties\": {\"copyable\": true, \"editable\": true, \"required\": false, \"sortable\": false}}}], \"selectable\": false, \"extendFields\": [{\"table\": \"t_profile_sensitive_time\", \"value\": 99, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}], \"searchFields\": []}', 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_sensitive_time\", \"column\": \"group_id\", \"idColumn\": \"id\", \"primaryColumn\": \"id\", \"extendCondition\": [{\"value\": 99, \"column\": \"police_kind\"}]}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1379, '相关人员', 'relatedPerson', 'groupV2', 1374, 1, 37, NULL, NULL, '{\"name\": \"相关人员\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_person\", \"fields\": [{\"db\": {\"table\": \"t_profile_person\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"人员名称\"}, \"properties\": {\"href\": \"/ys-app/archives/person/details?id={value}\", \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"risk_score\", \"mapping\": \"number_half_adjust_to_string\", \"jdbcType\": \"number\"}, \"name\": \"riskScore\", \"listSchema\": {\"style\": {\"align\": \"left\", \"width\": 128}, \"schema\": {\"type\": \"string\", \"title\": \"风险分值\"}, \"properties\": {\"color\": \"#FF6C6C\", \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true, \"instrLength\": 1, \"sortDefault\": \"descending\"}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"risk_level\", \"jdbcType\": \"string\"}, \"name\": \"riskLevel\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 128}, \"schema\": {\"type\": \"string\", \"title\": \"风险等级\"}, \"properties\": {\"colorMap\": {\"关注\": \"#333333 \", \"中风险\": \"#FFCE60\", \"低风险\": \"#6088D6\", \"高风险\": \"#FA8C34\", \"重中之重\": \"#EC3939\"}, \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"person_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"label_id_array\"}, \"name\": \"personLabel\", \"listSchema\": {\"style\": {\"align\": \"left\"}, \"filter\": {\"key\": \"personLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&person_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"人员标签\"}, \"schema\": {\"type\": \"array\", \"title\": \"人员标签\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2}}}, {\"db\": {\"table\": \"t_profile_person_group_relation\", \"column\": \"activity_level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\", \"databaseRelation\": {\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_group_relation\", \"joinTo\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}, \"joinFrom\": {\"table\": \"t_profile_group\", \"column\": \"id\", \"joinColumn\": \"group_id\"}}}, \"dict\": {\"type\": \"profile_activity_level\"}, \"name\": \"activityLevel\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"select\", \"title\": \"活跃程度\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"createDept\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"createDept\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}], \"selectable\": false, \"extendFields\": [{\"table\": \"t_profile_person_group_relation\", \"value\": 99, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}], \"searchFields\": [{\"key\": \"name\", \"name\": \"人员名称\"}], \"defaultSortParams\": {\"sortField\": \"riskScore\", \"sortDirection\": \"descending\"}}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_group_relation\", \"joinTo\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}, \"joinFrom\": {\"table\": \"t_profile_group\", \"column\": \"id\", \"joinColumn\": \"group_id\"}, \"extendCondition\": [{\"value\": 99, \"column\": \"police_kind\"}]}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1380, '相关线索', 'relatedClue', 'groupV2', 1374, 1, 39, NULL, NULL, '{\"name\": \"相关线索\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_clue\", \"fields\": [{\"db\": {\"table\": \"t_profile_clue\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"线索名称\"}, \"properties\": {\"href\": \"/ys-app/archives/clue/details?id={value}\", \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"clue_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"label_id_array\"}, \"name\": \"clueLabel\", \"listSchema\": {\"style\": {\"align\": \"left\"}, \"filter\": {\"key\": \"clueLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&clue_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"线索标签\"}, \"schema\": {\"type\": \"array\", \"title\": \"线索标签\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"emergency_level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_emergency_level\"}, \"name\": \"emergency_level\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"紧急程度\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"source\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_source\"}, \"name\": \"source\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"线索来源\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"create_time\", \"mapping\": \"date_time_to_text\", \"jdbcType\": \"datetime\"}, \"name\": \"createTime\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"disposal_status\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_disposal_status\"}, \"name\": \"disposal_status\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"处置状态\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}], \"selectable\": false, \"extendFields\": [{\"table\": \"t_profile_group_clue_relation\", \"value\": 99, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}], \"searchFields\": [{\"key\": \"name\", \"name\": \"线索名称\"}]}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_group_clue_relation\", \"joinTo\": {\"table\": \"t_profile_clue\", \"column\": \"id\", \"joinColumn\": \"clue_id\"}, \"joinFrom\": {\"table\": \"t_profile_group\", \"column\": \"id\", \"joinColumn\": \"group_id\"}, \"extendCondition\": [{\"value\": 99, \"column\": \"police_kind\"}]}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1381, '社会关系', 'society', 'personV2', 1328, 1, 19, NULL, NULL, '{\"name\": \"社会关系\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_social_relation\", \"fields\": [{\"db\": {\"table\": \"t_profile_social_relation\", \"column\": \"relation\", \"jdbcType\": \"string\"}, \"name\": \"relation\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"关系\"}, \"properties\": {\"copyable\": false, \"required\": true, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_social_relation\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"姓名\"}, \"properties\": {\"copyable\": false, \"required\": true, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_social_relation\", \"column\": \"id_number\", \"jdbcType\": \"string\"}, \"name\": \"id_number\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"身份证号\"}, \"properties\": {\"copyable\": true, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_social_relation\", \"column\": \"current_location\", \"jdbcType\": \"string\"}, \"name\": \"current_location\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"现住址\"}, \"properties\": {\"copyable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_social_relation\", \"column\": \"phone_number\", \"jdbcType\": \"string\"}, \"name\": \"phone_number\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"联系方式\"}, \"properties\": {\"copyable\": false, \"required\": false, \"sortable\": false}}}], \"selectable\": false, \"searchFields\": []}', 1, '{\"type\": \"JSON_ID_ARRAY\", \"table\": \"t_profile_person\", \"column\": \"social_relation_ids\"}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1382, '家庭关系', 'family', 'personV2', 1328, 1, 18, NULL, NULL, '{\"name\": \"家庭关系\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_family_relation\", \"fields\": [{\"db\": {\"table\": \"t_profile_family_relation\", \"column\": \"relation\", \"jdbcType\": \"string\"}, \"name\": \"relation\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"关系\"}, \"properties\": {\"copyable\": false, \"required\": true, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_family_relation\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"姓名\"}, \"properties\": {\"copyable\": false, \"required\": true, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_family_relation\", \"column\": \"id_number\", \"jdbcType\": \"string\"}, \"name\": \"id_number\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"身份证号\"}, \"properties\": {\"copyable\": true, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_family_relation\", \"column\": \"political_status\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"profile_political_status\"}, \"name\": \"political_status\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"political_status\", \"type\": \"select\", \"value\": [\"%%political_status%%\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"code\"}, \"displayName\": \"政治面貌2\"}, \"schema\": {\"type\": \"select\", \"title\": \"政治面貌\", \"fieldNames\": {\"label\": \"name\", \"value\": \"code\"}}, \"properties\": {\"copyable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_family_relation\", \"column\": \"work_unit\", \"jdbcType\": \"string\"}, \"name\": \"work_unit\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"工作单位\"}, \"properties\": {\"copyable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_family_relation\", \"column\": \"work_duty\", \"jdbcType\": \"string\"}, \"name\": \"work_duty\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"职务\"}, \"properties\": {\"copyable\": false, \"required\": false, \"sortable\": false}}}], \"selectable\": false, \"searchFields\": []}', 1, '{\"type\": \"JSON_ID_ARRAY\", \"table\": \"t_profile_person\", \"column\": \"family_relation_ids\"}', 'LIST_SCHEMA', 'LIST_SCHEMA', 0, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1383, '背景关系', 'background', 'personV2', NULL, 1, 17, NULL, NULL, NULL, 0, NULL, NULL, NULL, 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1384, '社会关系', 'society', 'personV2', 1383, 1, 19, NULL, NULL, '{\"name\": \"社会关系\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_social_relation\", \"fields\": [{\"db\": {\"table\": \"t_profile_social_relation\", \"column\": \"relation\", \"jdbcType\": \"string\"}, \"name\": \"relation\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"关系\"}, \"properties\": {\"copyable\": false, \"required\": true, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_social_relation\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"姓名\"}, \"properties\": {\"copyable\": false, \"required\": true, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_social_relation\", \"column\": \"id_number\", \"jdbcType\": \"string\"}, \"name\": \"id_number\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"身份证号\"}, \"properties\": {\"copyable\": true, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_social_relation\", \"column\": \"current_location\", \"jdbcType\": \"string\"}, \"name\": \"current_location\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"现住址\"}, \"properties\": {\"copyable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_social_relation\", \"column\": \"phone_number\", \"jdbcType\": \"string\"}, \"name\": \"phone_number\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"联系方式\"}, \"properties\": {\"copyable\": false, \"required\": false, \"sortable\": false}}}], \"selectable\": false, \"searchFields\": []}', 0, '{\"type\": \"JSON_ID_ARRAY\", \"table\": \"t_profile_person\", \"column\": \"social_relation_ids\"}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1385, '家庭关系', 'family', 'personV2', 1383, 1, 18, NULL, NULL, '{\"name\": \"家庭关系\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_family_relation\", \"fields\": [{\"db\": {\"table\": \"t_profile_family_relation\", \"column\": \"relation\", \"jdbcType\": \"string\"}, \"name\": \"relation\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"关系\"}, \"properties\": {\"copyable\": false, \"required\": true, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_family_relation\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"姓名\"}, \"properties\": {\"copyable\": false, \"required\": true, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_family_relation\", \"column\": \"id_number\", \"jdbcType\": \"string\"}, \"name\": \"id_number\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"身份证号\"}, \"properties\": {\"copyable\": true, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_family_relation\", \"column\": \"political_status\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"profile_political_status\"}, \"name\": \"political_status\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"select\", \"title\": \"政治面貌\"}, \"properties\": {\"copyable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_family_relation\", \"column\": \"work_unit\", \"jdbcType\": \"string\"}, \"name\": \"work_unit\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"工作单位\"}, \"properties\": {\"copyable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_family_relation\", \"column\": \"work_duty\", \"jdbcType\": \"string\"}, \"name\": \"work_duty\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"职务\"}, \"properties\": {\"copyable\": false, \"required\": false, \"sortable\": false}}}], \"selectable\": false, \"searchFields\": []}', 0, '{\"type\": \"JSON_ID_ARRAY\", \"table\": \"t_profile_person\", \"column\": \"family_relation_ids\"}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1386, '手机三码', 'virtualIdentity', 'personV2', 1328, 1, 5, NULL, NULL, '{\"name\": \"虚拟身份\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_virtual_identity\", \"fields\": [{\"db\": {\"table\": \"t_profile_virtual_identity\", \"column\": \"virtual_number\", \"jdbcType\": \"string\"}, \"name\": \"virtual_number\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"虚拟号码\"}, \"properties\": {\"copyable\": true, \"editable\": true, \"required\": true, \"sortable\": false, \"validate\": [{\"message\": \"手机号错误\", \"pattern\": \"^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\\\\d{8}$\", \"conditions\": \"{type}=4\"}, {\"message\": \"qq号错误\", \"pattern\": \"[1-9][0-9]{4,14}\", \"conditions\": \"{type}=5\"}, {\"message\": \"MAC地址错误\", \"pattern\": \"^([0-9a-fA-F]{2})(([/\\\\s:-][0-9a-fA-F]{2}){5})$\", \"conditions\": \"{type}=1\"}, {\"message\": \"IMEI地址错误\", \"pattern\": \"^[\\\\d]{15}(?:[\\\\d]{2})?$\", \"conditions\": \"{type}=3\"}, {\"message\": \"IMSI地址错误\", \"pattern\": \" ^[\\\\d]{15}(?:[\\\\d]{2})?$\", \"conditions\": \"{type}=2\"}]}}}, {\"db\": {\"table\": \"t_profile_virtual_identity\", \"column\": \"type\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"virtual_identity_type\"}, \"name\": \"type\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"select\", \"title\": \"类型\"}, \"properties\": {\"default\": 1, \"copyable\": false, \"editable\": true, \"required\": true, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_virtual_identity\", \"column\": \"source\", \"mapping\": \"source_to_name\", \"jdbcType\": \"string\"}, \"name\": \"source\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"来源\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"modifyDefaultValue\": \"{\\\"type\\\": 1}\", \"displayDefaultValue\": \"手动录入\"}}}], \"selectable\": false, \"searchFields\": []}', 1, '{\"type\": \"JSON_ID_ARRAY\", \"table\": \"t_profile_person\", \"column\": \"virtual_identity_ids\"}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1388, '风险点信息', 'riskPoint', 'personV2', 1316, 1, 1, '{\"name\": \"风险点信息\", \"type\": \"TABLE_SCHEMA\", \"table\": \"t_profile_person_risk_sd\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_risk_sd\", \"column\": \"secondary_control_district\", \"jdbcType\": \"string\"}, \"name\": \"secondary_control_district\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"二级管控地区\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_risk_sd\", \"column\": \"first_seizure_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"string\"}, \"name\": \"first_seizure_time\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"初次查获时间\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_risk_sd\", \"column\": \"last_seizure_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"string\"}, \"name\": \"last_seizure_time\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"末次查获时间\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_risk_sd\", \"column\": \"durgs_kind\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"string\"}, \"dict\": {\"type\": \"profile_person_control_level\"}, \"name\": \"durgs_kind\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"吸食毒品种类\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_risk_sd\", \"column\": \"person_level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"string\"}, \"dict\": {\"type\": \"profile_person_person_level_sd\"}, \"name\": \"person_level\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"人员等级\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_risk_sd\", \"column\": \"secondary_control_situation\", \"jdbcType\": \"string\"}, \"name\": \"secondary_control_situation\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"二级管控现状\", \"copyable\": false}}], \"moduleUi\": {\"column\": 3, \"bordered\": true}}', '{\"name\": \"风险点信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_person_risk_sd\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_risk_sd\", \"column\": \"secondary_control_district\", \"jdbcType\": \"string\"}, \"name\": \"secondary_control_district\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"二级管控地区\"}}}, {\"db\": {\"table\": \"t_profile_person_risk_sd\", \"column\": \"first_seizure_time\", \"jdbcType\": \"string\"}, \"name\": \"first_seizure_time\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"datePicker\", \"showTime\": true, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"初次查获时间\"}}}, {\"db\": {\"table\": \"t_profile_person_risk_sd\", \"column\": \"last_seizure_time\", \"jdbcType\": \"string\"}, \"name\": \"last_seizure_time\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"datePicker\", \"showTime\": true, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"末次查获时间\"}}}, {\"db\": {\"table\": \"t_profile_person_risk_sd\", \"column\": \"durgs_kind\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"profile_person_durgs_kind\"}, \"name\": \"durgs_kind\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"吸食毒品种类\"}}}, {\"db\": {\"table\": \"t_profile_person_risk_sd\", \"column\": \"person_level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"profile_person_person_level_sd\"}, \"name\": \"person_level\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"radio\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"人员等级\"}}}, {\"db\": {\"table\": \"t_profile_person_risk_sd\", \"column\": \"secondary_control_situation\", \"jdbcType\": \"string\"}, \"name\": \"secondary_control_situation\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"二级管控现状\"}}}], \"required\": []}', NULL, 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_person\", \"column\": \"person_id\"}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1389, '政保', 'zb', 'personV2', 1313, 1, 1, NULL, NULL, NULL, 1, NULL, NULL, NULL, 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1390, '治安', 'za', 'personV2', 1313, 1, 1, NULL, NULL, NULL, 1, NULL, NULL, NULL, 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1391, '其他', 'qt', 'personV2', 1313, 1, 1, NULL, NULL, NULL, 1, NULL, NULL, NULL, 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1392, '风险点信息', 'riskPoint', 'personV2', 1345, 1, 1, '{\"name\": \"风险点信息\", \"type\": \"TABLE_SCHEMA\", \"table\": \"t_profile_person_risk_other\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_risk_other\", \"column\": \"person_level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"string\"}, \"dict\": {\"type\": \"profile_person_control_level\"}, \"name\": \"person_level\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"人员级别\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_risk_other\", \"column\": \"main_demand\", \"jdbcType\": \"string\"}, \"name\": \"main_demand\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"风险背景\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_risk_other\", \"column\": \"petition_info\", \"jdbcType\": \"string\"}, \"name\": \"petition_info\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"化解难点\", \"copyable\": false}}], \"moduleUi\": {\"column\": 3, \"bordered\": true}}', '{\"name\": \"风险点信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_person_risk_other\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_risk_other\", \"column\": \"person_level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"profile_person_control_level\"}, \"name\": \"person_level\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"radio\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"人员级别\"}}}, {\"db\": {\"table\": \"t_profile_person_risk_other\", \"column\": \"main_demand\", \"jdbcType\": \"string\"}, \"name\": \"main_demand\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"风险背景\"}}}, {\"db\": {\"table\": \"t_profile_person_risk_other\", \"column\": \"petition_info\", \"jdbcType\": \"string\"}, \"name\": \"petition_info\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"化解难点\"}}}], \"required\": [], \"extendFields\": [{\"table\": \"t_profile_person_risk_other\", \"value\": 5, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}]}', NULL, 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_person\", \"column\": \"person_id\", \"extendCondition\": [{\"value\": 5, \"column\": \"police_kind\"}]}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1393, '工作措施', 'gzcs', 'personV2', 1345, 1, 2, NULL, NULL, '{\"name\": \"工作措施\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_person_gzcs\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_gzcs\", \"column\": \"detail\", \"jdbcType\": \"string\"}, \"name\": \"detail\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"工作措施\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": true, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_person_gzcs\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"create_dept_id\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"create_dept_id\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_person_gzcs\", \"column\": \"create_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"datetime\"}, \"name\": \"createTime\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}], \"selectable\": false, \"extendFields\": [{\"table\": \"t_profile_person_gzcs\", \"value\": 5, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}], \"searchFields\": [{\"key\": \"name\", \"name\": \"工作措施\"}]}', 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_person_gzcs\", \"column\": \"person_id\", \"idColumn\": \"id\", \"primaryColumn\": \"id\", \"extendCondition\": [{\"value\": 5, \"column\": \"police_kind\"}]}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1394, '打处情况', 'dcqk', 'personV2', 1345, 1, 3, NULL, NULL, '{\"name\": \"打处情况\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_person_dcqk\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_dcqk\", \"column\": \"detail\", \"jdbcType\": \"string\"}, \"name\": \"detail\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"打处情况\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_person_dcqk\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"create_dept_id\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"create_dept_id\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_person_dcqk\", \"column\": \"create_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"datetime\"}, \"name\": \"createTime\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}], \"selectable\": false, \"extendFields\": [{\"table\": \"t_profile_person_dcqk\", \"value\": 5, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}], \"searchFields\": [{\"key\": \"name\", \"name\": \"打处情况\"}]}', 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_person_dcqk\", \"column\": \"person_id\", \"idColumn\": \"id\", \"primaryColumn\": \"id\", \"extendCondition\": [{\"value\": 5, \"column\": \"police_kind\"}]}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1396, '风险点信息', 'riskPoint', 'personV2', 1389, 1, 1, '{\"name\": \"风险点信息\", \"type\": \"TABLE_SCHEMA\", \"table\": \"t_profile_person_risk_other\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_risk_other\", \"column\": \"person_level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"string\"}, \"dict\": {\"type\": \"profile_person_person_level_zb\"}, \"name\": \"person_level\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"人员级别\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_risk_other\", \"column\": \"main_demand\", \"jdbcType\": \"string\"}, \"name\": \"main_demand\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"风险背景\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_risk_other\", \"column\": \"qkbj\", \"jdbcType\": \"string\"}, \"name\": \"qkbj\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"前科背景\", \"copyable\": false}}], \"moduleUi\": {\"column\": 3, \"bordered\": true}}', '{\"name\": \"风险点信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_person_risk_other\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_risk_other\", \"column\": \"person_level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"profile_person_person_level_zb\"}, \"name\": \"person_level\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"radio\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"人员级别\"}}}, {\"db\": {\"table\": \"t_profile_person_risk_other\", \"column\": \"main_demand\", \"jdbcType\": \"string\"}, \"name\": \"main_demand\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"风险背景\"}}}, {\"db\": {\"table\": \"t_profile_person_risk_other\", \"column\": \"qkbj\", \"jdbcType\": \"string\"}, \"name\": \"qkbj\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"前科背景\"}}}], \"required\": [], \"extendFields\": [{\"table\": \"t_profile_person_risk_other\", \"value\": 2, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}]}', NULL, 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_person\", \"column\": \"person_id\", \"extendCondition\": [{\"value\": \"2\", \"column\": \"police_kind\"}]}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1397, '工作措施', 'gzcs', 'personV2', 1389, 1, 2, NULL, NULL, '{\"name\": \"工作措施\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_person_gzcs\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_gzcs\", \"column\": \"detail\", \"jdbcType\": \"string\"}, \"name\": \"detail\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"工作措施\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_person_gzcs\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"create_dept_id\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"create_dept_id\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_person_gzcs\", \"column\": \"create_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"datetime\"}, \"name\": \"createTime\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}], \"selectable\": false, \"extendFields\": [{\"table\": \"t_profile_person_gzcs\", \"value\": 2, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}], \"searchFields\": [{\"key\": \"name\", \"name\": \"工作措施\"}]}', 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_person_gzcs\", \"column\": \"person_id\", \"idColumn\": \"id\", \"primaryColumn\": \"id\", \"extendCondition\": [{\"value\": 2, \"column\": \"police_kind\"}]}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1398, '现实危害', 'xswh', 'personV2', 1389, 1, 3, NULL, NULL, '{\"name\": \"现实危害\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_person_xswh\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_xswh\", \"column\": \"detail\", \"jdbcType\": \"string\"}, \"name\": \"detail\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"现实危害\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_person_xswh\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"create_dept_id\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"create_dept_id\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_person_xswh\", \"column\": \"create_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"datetime\"}, \"name\": \"createTime\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}], \"selectable\": false, \"extendFields\": [{\"table\": \"t_profile_person_xswh\", \"value\": 2, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}], \"searchFields\": [{\"key\": \"name\", \"name\": \"现实危害\"}]}', 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_person_xswh\", \"column\": \"person_id\", \"idColumn\": \"id\", \"primaryColumn\": \"id\", \"extendCondition\": [{\"value\": 2, \"column\": \"police_kind\"}]}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1399, '政府管控信息', 'govControl', 'personV2', 1389, 1, 4, '{\"name\": \"政府管控信息\", \"table\": \"t_profile_person_government_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government\", \"jdbcType\": \"string\"}, \"name\": \"control_government\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"党政责任部门\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government_person\", \"jdbcType\": \"string\"}, \"name\": \"control_government_person\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"党政责任人\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government_person_duty\", \"jdbcType\": \"string\"}, \"name\": \"control_government_person_duty\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"党政责任人职务\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government_contact\", \"jdbcType\": \"string\"}, \"name\": \"control_government_contact\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"联系方式\", \"copyable\": true}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community\", \"jdbcType\": \"string\"}, \"name\": \"control_community\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任街道社区\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community_person\", \"jdbcType\": \"string\"}, \"name\": \"control_community_person\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"社区责任人\", \"config\": {\"fieldName\": \"control_community_person\", \"processType\": \"string\", \"processConfig\": null}, \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community_person_duty\", \"jdbcType\": \"string\"}, \"name\": \"control_community_person_duty\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"社区责任人职务\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community_contact\", \"jdbcType\": \"string\"}, \"name\": \"control_community_contact\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"联系方式\", \"copyable\": true}}], \"moduleUi\": {\"column\": 3, \"bordered\": true}}', '{\"name\": \"政府管控信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_person_government_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government\", \"jdbcType\": \"string\"}, \"name\": \"controlGovernment\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"党政责任部门\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government_person\", \"jdbcType\": \"string\"}, \"name\": \"controlGovernmentPerson\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"党政责任人\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government_person_duty\", \"jdbcType\": \"string\"}, \"name\": \"controlGovernmentPersonDuty\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"党政责任人职务\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government_contact\", \"jdbcType\": \"string\"}, \"name\": \"controlGovernmentContact\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"党政责任人联系方式\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community\", \"jdbcType\": \"string\"}, \"name\": \"controlCommunity\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"责任街道社区\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community_person\", \"jdbcType\": \"string\"}, \"name\": \"controlCommunityPerson\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"社区责任人\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community_person_duty\", \"jdbcType\": \"string\"}, \"name\": \"controlCommunityPersonDuty\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"社区责任人职务\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community_contact\", \"jdbcType\": \"string\"}, \"name\": \"controlCommunityContact\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"社区责任人联系方式\"}}}], \"required\": [], \"extendFields\": [{\"table\": \"t_profile_person_government_control\", \"value\": 2, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}]}', NULL, 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_person_government_control\", \"column\": \"person_id\", \"extendCondition\": [{\"value\": 2, \"column\": \"police_kind\"}]}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1400, '公安管控信息', 'policeControl', 'personV2', 1389, 1, 5, '{\"name\": \"公安管控信息\", \"type\": \"TABLE_SCHEMA\", \"table\": \"t_profile_person_police_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_bureau\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"control_bureau\", \"tree\": {\"type\": \"dept\"}, \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任分局\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_bureau_leader\", \"mapping\": \"user_id_array_to_single_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_bureau_leader\", \"tableSchema\": {\"span\": 1, \"type\": \"user\", \"title\": \"责任领导\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_police\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"control_police\", \"tree\": {\"type\": \"dept\"}, \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任警种\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_police_leader\", \"mapping\": \"user_id_array_to_single_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_police_leader\", \"tableSchema\": {\"span\": 1, \"type\": \"user\", \"title\": \"责任领导\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_station\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"control_station\", \"tree\": {\"type\": \"dept\"}, \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任派出所\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_station_leader\", \"mapping\": \"user_id_array_to_single_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_station_leader\", \"tableSchema\": {\"span\": 1, \"type\": \"user\", \"title\": \"责任领导\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_person\", \"mapping\": \"user_id_array_to_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_person\", \"tableSchema\": {\"span\": 2, \"type\": \"multiUser\", \"title\": \"责任民警\", \"copyable\": false}}], \"moduleUi\": {\"column\": 2, \"bordered\": true}}', '{\"name\": \"公安管控信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_person_police_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_bureau\", \"jdbcType\": \"string\"}, \"name\": \"controlBureau\", \"tree\": {\"root\": \"510500000000\", \"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"责任分局\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_bureau_leader\", \"jdbcType\": \"number\"}, \"name\": \"controlBureauLeader\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlBureau\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"责任分局领导\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_police\", \"jdbcType\": \"string\"}, \"name\": \"controlPolice\", \"tree\": {\"root\": \"510500000000\", \"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"责任警种\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_police_leader\", \"jdbcType\": \"number\"}, \"name\": \"controlPoliceLeader\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlPolice\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"责任警种领导\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_station\", \"jdbcType\": \"string\"}, \"name\": \"controlStation\", \"tree\": {\"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"责任派出所\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_station_leader\", \"jdbcType\": \"number\"}, \"name\": \"controlStationLeader\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlStation\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"责任派出所领导\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_person\", \"jdbcType\": \"json_id_array\"}, \"name\": \"controlPerson\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"1\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlStation\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"array\", \"items\": {\"type\": \"number\"}, \"title\": \"责任民警\", \"minItems\": 1}}}], \"required\": [\"controlPerson\", \"controlStationLeader\", \"controlStation\"], \"extendFields\": [{\"table\": \"t_profile_person_police_control\", \"value\": 2, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}]}', NULL, 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_person\", \"column\": \"person_id\", \"extendCondition\": [{\"value\": 2, \"column\": \"police_kind\"}]}', 'NO_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1401, '相关群体', 'relatedGroup', 'personV2', 1389, 1, 6, NULL, NULL, '{\"name\": \"相关群体\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_group\", \"fields\": [{\"db\": {\"table\": \"t_profile_group\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"群体名称\"}, \"properties\": {\"href\": \"/ys-app/archives/group/details?id={value}\", \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_group\", \"column\": \"group_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"label_id_array\"}, \"name\": \"groupLabel\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"groupLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&group_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"群体标签\"}, \"schema\": {\"type\": \"array\", \"title\": \"群体标签\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2}}}, {\"db\": {\"table\": \"t_profile_person_group_relation\", \"column\": \"activity_level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\", \"databaseRelation\": {\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_group_relation\", \"joinTo\": {\"table\": \"t_profile_group\", \"column\": \"id\", \"joinColumn\": \"group_id\"}, \"joinFrom\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}}}, \"dict\": {\"type\": \"profile_activity_level\"}, \"name\": \"activityLevel\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"select\", \"title\": \"活跃程度\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_group\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"createDept\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"createDept\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}], \"selectable\": false, \"extendFields\": [{\"table\": \"t_profile_person_group_relation\", \"value\": 2, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}], \"searchFields\": [{\"key\": \"name\", \"name\": \"群体名称\"}]}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_group_relation\", \"joinTo\": {\"table\": \"t_profile_group\", \"column\": \"id\", \"joinColumn\": \"group_id\"}, \"joinFrom\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}, \"extendCondition\": [{\"value\": 2, \"column\": \"police_kind\"}]}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1402, '相关事件', 'relatedEvent', 'personV2', 1389, 1, 7, NULL, NULL, '{\"name\": \"相关事件\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_event\", \"fields\": [{\"db\": {\"table\": \"t_profile_event\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"事件名称\"}, \"properties\": {\"href\": \"/ys-app/archives/event/details?id={value}\", \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"create_dept_id\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"create_dept_id\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"create_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"datetime\"}, \"name\": \"createTime\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}], \"selectable\": false, \"extendFields\": [{\"table\": \"t_profile_person_event_relation\", \"value\": 2, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}], \"searchFields\": [{\"key\": \"name\", \"name\": \"事件名称\"}]}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_event_relation\", \"joinTo\": {\"table\": \"t_profile_event\", \"column\": \"id\", \"joinColumn\": \"event_id\"}, \"joinFrom\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}, \"extendCondition\": [{\"value\": 2, \"column\": \"police_kind\"}]}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1403, '相关线索', 'relatedClue', 'personV2', 1389, 1, 8, NULL, NULL, '{\"name\": \"相关线索\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_clue\", \"fields\": [{\"db\": {\"table\": \"t_profile_clue\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"线索名称\"}, \"properties\": {\"href\": \"/ys-app/archives/clue/details?id={value}\", \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"clue_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"label_id_array\"}, \"name\": \"clueLabel\", \"listSchema\": {\"style\": {\"align\": \"left\"}, \"filter\": {\"key\": \"clueLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&clue_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"线索标签\"}, \"schema\": {\"type\": \"array\", \"title\": \"线索标签\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"emergency_level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_emergency_level\"}, \"name\": \"emergency_level\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"紧急程度\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"source\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_source_type\"}, \"name\": \"source\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"线索来源\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"create_time\", \"mapping\": \"date_time_to_text\", \"jdbcType\": \"datetime\"}, \"name\": \"createTime\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"disposal_status\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_disposal_status\"}, \"name\": \"disposal_status\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"处置状态\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}], \"selectable\": false, \"extendFields\": [{\"table\": \"t_profile_person_clue_relation\", \"value\": 2, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}], \"searchFields\": [{\"key\": \"name\", \"name\": \"线索名称\"}]}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_clue_relation\", \"joinTo\": {\"table\": \"t_profile_clue\", \"column\": \"id\", \"joinColumn\": \"clue_id\"}, \"joinFrom\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}, \"extendCondition\": [{\"value\": 2, \"column\": \"police_kind\"}]}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1404, '风险点信息', 'riskPoint', 'personV2', 1390, 1, 1, '{\"name\": \"风险点信息\", \"type\": \"TABLE_SCHEMA\", \"table\": \"t_profile_person_risk_za\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_risk_za\", \"column\": \"control_level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"string\"}, \"dict\": {\"type\": \"profile_person_control_level_za\"}, \"name\": \"control_level\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"风险级别\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_risk_za\", \"column\": \"work_unit\", \"jdbcType\": \"string\"}, \"name\": \"work_unit\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"工作单位\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_risk_za\", \"column\": \"work_duty\", \"jdbcType\": \"string\"}, \"name\": \"work_duty\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"职务\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_risk_za\", \"column\": \"work_duty_type\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"string\"}, \"dict\": {\"type\": \"profile_person_duty_type\"}, \"name\": \"work_duty_type\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"职务类型\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_risk_za\", \"column\": \"event_place\", \"jdbcType\": \"string\"}, \"name\": \"event_place\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"事发地\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_risk_za\", \"column\": \"sksflry\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"string\"}, \"dict\": {\"type\": \"yes_or_not_group\"}, \"name\": \"sksflry\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"是否三跨三分离人员\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_risk_za\", \"column\": \"demand_situation\", \"jdbcType\": \"string\"}, \"name\": \"demand_situation\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"诉求情况\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_risk_za\", \"column\": \"work_difficulty\", \"jdbcType\": \"string\"}, \"name\": \"work_difficulty\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"工作难点\", \"copyable\": false}}], \"moduleUi\": {\"column\": 3, \"bordered\": true}}', '{\"name\": \"风险点信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_person_risk_za\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_risk_za\", \"column\": \"control_level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"profile_person_control_level_za\"}, \"name\": \"control_level\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"radio\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"管控级别\"}}}, {\"db\": {\"table\": \"t_profile_person_risk_za\", \"column\": \"work_unit\", \"jdbcType\": \"string\"}, \"name\": \"work_unit\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"工作单位\"}}}, {\"db\": {\"table\": \"t_profile_person_risk_za\", \"column\": \"work_duty\", \"jdbcType\": \"string\"}, \"name\": \"work_duty\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"职务\"}}}, {\"db\": {\"table\": \"t_profile_person_risk_za\", \"column\": \"work_duty_type\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"profile_person_duty_type\"}, \"name\": \"work_duty_type\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"radio\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"职务类型\"}}}, {\"db\": {\"table\": \"t_profile_person_risk_za\", \"column\": \"event_place\", \"jdbcType\": \"string\"}, \"name\": \"event_place\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"事发地\"}}}, {\"db\": {\"table\": \"t_profile_person_risk_za\", \"column\": \"sksflry\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"yes_or_not_group\"}, \"name\": \"sksflry\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"radio\", \"fieldNames\": {\"label\": \"name\", \"value\": \"code\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"是否三跨三分离人员\"}}}, {\"db\": {\"table\": \"t_profile_person_risk_za\", \"column\": \"demand_situation\", \"jdbcType\": \"string\"}, \"name\": \"demand_situation\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"诉求情况\"}}}, {\"db\": {\"table\": \"t_profile_person_risk_za\", \"column\": \"work_difficulty\", \"jdbcType\": \"string\"}, \"name\": \"work_difficulty\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"工作难点\"}}}], \"required\": []}', NULL, 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_person\", \"column\": \"person_id\"}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1405, '工作措施', 'gzcs', 'personV2', 1390, 1, 2, NULL, NULL, '{\"name\": \"工作措施\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_person_gzcs\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_gzcs\", \"column\": \"detail\", \"jdbcType\": \"string\"}, \"name\": \"detail\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"工作措施\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_person_gzcs\", \"column\": \"data_source\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"profile_person_gzcs_data_source\"}, \"name\": \"data_source\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"select\", \"title\": \"数据来源\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_person_gzcs\", \"column\": \"data_source_remark\", \"jdbcType\": \"string\"}, \"name\": \"data_source_remark\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"数据来源描述\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_person_gzcs\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"create_dept_id\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"create_dept_id\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_person_gzcs\", \"column\": \"create_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"datetime\"}, \"name\": \"createTime\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}], \"selectable\": false, \"extendFields\": [{\"table\": \"t_profile_person_gzcs\", \"value\": 4, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}], \"searchFields\": [{\"key\": \"name\", \"name\": \"工作措施\"}]}', 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_person_gzcs\", \"column\": \"person_id\", \"idColumn\": \"id\", \"primaryColumn\": \"id\", \"extendCondition\": [{\"value\": 4, \"column\": \"police_kind\"}]}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1406, '现实表现', 'xsbx', 'personV2', 1390, 1, 3, NULL, NULL, '{\"name\": \"现实表现\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_person_xsbx\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_xsbx\", \"column\": \"detail\", \"jdbcType\": \"string\"}, \"name\": \"detail\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"现实表现\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_person_xsbx\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"create_dept_id\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"create_dept_id\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_person_xsbx\", \"column\": \"create_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"datetime\"}, \"name\": \"createTime\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}], \"selectable\": false, \"extendFields\": [{\"table\": \"t_profile_person_xsbx\", \"value\": 4, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}], \"searchFields\": [{\"key\": \"name\", \"name\": \"现实表现\"}]}', 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_person_xsbx\", \"column\": \"person_id\", \"idColumn\": \"id\", \"primaryColumn\": \"id\", \"extendCondition\": [{\"value\": 4, \"column\": \"police_kind\"}]}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1407, '打处情况', 'dcqk', 'personV2', 1390, 1, 4, NULL, NULL, '{\"name\": \"打处情况\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_person_dcqk\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_dcqk\", \"column\": \"detail\", \"jdbcType\": \"string\"}, \"name\": \"detail\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"打处情况\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_person_dcqk\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"create_dept_id\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"create_dept_id\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_person_dcqk\", \"column\": \"create_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"datetime\"}, \"name\": \"createTime\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}], \"selectable\": false, \"extendFields\": [{\"table\": \"t_profile_person_dcqk\", \"value\": 4, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}], \"searchFields\": [{\"key\": \"name\", \"name\": \"打处情况\"}]}', 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_person_dcqk\", \"column\": \"person_id\", \"idColumn\": \"id\", \"primaryColumn\": \"id\", \"extendCondition\": [{\"value\": 4, \"column\": \"police_kind\"}]}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1409, '党政管控信息', 'dzControl', 'personV2', 1390, 1, 6, '{\"name\": \"党政管控信息\", \"table\": \"t_profile_person_dz_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_dz_control\", \"column\": \"defuse_government\", \"jdbcType\": \"string\"}, \"name\": \"defuse_government\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"化解责任单位\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_dz_control\", \"column\": \"defuse_government_person\", \"jdbcType\": \"string\"}, \"name\": \"defuse_government_person\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"化解责任人\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_dz_control\", \"column\": \"defuse_government_contact\", \"jdbcType\": \"string\"}, \"name\": \"defuse_government_contact\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"化解责任人联系方式\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_dz_control\", \"column\": \"control_government\", \"jdbcType\": \"string\"}, \"name\": \"control_government\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"稳控责任单位\", \"copyable\": true}}, {\"db\": {\"table\": \"t_profile_person_dz_control\", \"column\": \"control_government_person\", \"jdbcType\": \"string\"}, \"name\": \"control_government_person\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"稳控责任人\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_dz_control\", \"column\": \"control_government_contact\", \"jdbcType\": \"string\"}, \"name\": \"control_government_contact\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"稳控责任人联系方式\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_dz_control\", \"column\": \"control_leader\", \"jdbcType\": \"string\"}, \"name\": \"control_leader\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"包案县级领导姓名\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_dz_control\", \"column\": \"control_leader_duty\", \"jdbcType\": \"string\"}, \"name\": \"control_leader_duty\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"包案县级领导职务\", \"copyable\": true}}, {\"db\": {\"table\": \"t_profile_person_dz_control\", \"column\": \"control_leader_contact\", \"jdbcType\": \"string\"}, \"name\": \"control_leader_contact\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"包案县级领导联系方式\", \"copyable\": true}}], \"moduleUi\": {\"column\": 3, \"bordered\": true}}', '{\"name\": \"党政管控信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_person_dz_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_dz_control\", \"column\": \"defuse_government\", \"jdbcType\": \"string\"}, \"name\": \"defuse_government\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"化解责任单位\"}}}, {\"db\": {\"table\": \"t_profile_person_dz_control\", \"column\": \"defuse_government_person\", \"jdbcType\": \"string\"}, \"name\": \"defuse_government_person\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"化解责任人\"}}}, {\"db\": {\"table\": \"t_profile_person_dz_control\", \"column\": \"defuse_government_contact\", \"jdbcType\": \"string\"}, \"name\": \"defuse_government_contact\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"化解责任人联系方式\"}}}, {\"db\": {\"table\": \"t_profile_person_dz_control\", \"column\": \"control_government\", \"jdbcType\": \"string\"}, \"name\": \"control_government\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"稳控责任单位\"}}}, {\"db\": {\"table\": \"t_profile_person_dz_control\", \"column\": \"control_government_person\", \"jdbcType\": \"string\"}, \"name\": \"control_government_person\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"稳控责任人\"}}}, {\"db\": {\"table\": \"t_profile_person_dz_control\", \"column\": \"control_government_contact\", \"jdbcType\": \"string\"}, \"name\": \"control_government_contact\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"稳控责任人联系方式\"}}}, {\"db\": {\"table\": \"t_profile_person_dz_control\", \"column\": \"control_leader\", \"jdbcType\": \"string\"}, \"name\": \"control_leader\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"包案县级领导姓名\"}}}, {\"db\": {\"table\": \"t_profile_person_dz_control\", \"column\": \"control_leader_duty\", \"jdbcType\": \"string\"}, \"name\": \"control_leader_duty\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"包案县级领导职务\"}}}, {\"db\": {\"table\": \"t_profile_person_dz_control\", \"column\": \"control_leader_contact\", \"jdbcType\": \"string\"}, \"name\": \"control_leader_contact\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"包案县级领导联系方式\"}}}], \"required\": []}', NULL, 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_person_government_control\", \"column\": \"person_id\"}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1410, '公安管控信息', 'policeControl', 'personV2', 1390, 1, 7, '{\"name\": \"公安管控信息\", \"type\": \"TABLE_SCHEMA\", \"table\": \"t_profile_person_police_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_bureau\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"control_bureau\", \"tree\": {\"type\": \"dept\"}, \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任分局\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_bureau_leader\", \"mapping\": \"user_id_array_to_single_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_bureau_leader\", \"tableSchema\": {\"span\": 1, \"type\": \"user\", \"title\": \"责任领导\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_police\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"control_police\", \"tree\": {\"type\": \"dept\"}, \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任警种\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_police_leader\", \"mapping\": \"user_id_array_to_single_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_police_leader\", \"tableSchema\": {\"span\": 1, \"type\": \"user\", \"title\": \"责任领导\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_station\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"control_station\", \"tree\": {\"type\": \"dept\"}, \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任派出所\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_station_leader\", \"mapping\": \"user_id_array_to_single_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_station_leader\", \"tableSchema\": {\"span\": 1, \"type\": \"user\", \"title\": \"责任领导\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_person\", \"mapping\": \"user_id_array_to_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_person\", \"tableSchema\": {\"span\": 1, \"type\": \"multiUser\", \"title\": \"责任民警\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"technology_control\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"string\"}, \"dict\": {\"type\": \"profile_person_technology_control\"}, \"name\": \"technology_control\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"技术布控情况\", \"copyable\": false}}], \"moduleUi\": {\"column\": 2, \"bordered\": true}}', '{\"name\": \"公安管控信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_person_police_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_bureau\", \"jdbcType\": \"string\"}, \"name\": \"controlBureau\", \"tree\": {\"root\": \"510500000000\", \"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"责任分局\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_bureau_leader\", \"jdbcType\": \"number\"}, \"name\": \"controlBureauLeader\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlBureau\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"责任分局领导\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_police\", \"jdbcType\": \"string\"}, \"name\": \"controlPolice\", \"tree\": {\"root\": \"510500000000\", \"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"责任警种\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_police_leader\", \"jdbcType\": \"number\"}, \"name\": \"controlPoliceLeader\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlPolice\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"责任警种领导\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_station\", \"jdbcType\": \"string\"}, \"name\": \"controlStation\", \"tree\": {\"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"责任派出所\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_station_leader\", \"jdbcType\": \"number\"}, \"name\": \"controlStationLeader\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlStation\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"责任派出所领导\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_person\", \"jdbcType\": \"json_id_array\"}, \"name\": \"controlPerson\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlStation\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"array\", \"items\": {\"type\": \"number\"}, \"title\": \"责任民警\", \"minItems\": 1}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"technology_control\", \"jdbcType\": \"json_id_array\"}, \"name\": \"technologyControl\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"options\": \"/global/dict?type=profile_person_technology_control\", \"fieldNames\": {\"label\": \"name\", \"value\": \"code\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"array\", \"items\": {\"type\": \"number\"}, \"title\": \"技术布控情况\", \"minItems\": 1}}}], \"required\": [\"controlPerson\", \"controlStationLeader\", \"controlStation\"], \"extendFields\": [{\"table\": \"t_profile_person_police_control\", \"value\": 4, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}]}', NULL, 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_person\", \"column\": \"person_id\", \"extendCondition\": [{\"value\": 4, \"column\": \"police_kind\"}]}', 'NO_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1411, '相关群体', 'relatedGroup', 'personV2', 1390, 1, 8, NULL, NULL, '{\"name\": \"相关群体\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_group\", \"fields\": [{\"db\": {\"table\": \"t_profile_group\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"群体名称\"}, \"properties\": {\"href\": \"/ys-app/archives/group/details?id={value}\", \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_person_group_relation\", \"column\": \"group_work\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\", \"databaseRelation\": {\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_group_relation\", \"joinTo\": {\"table\": \"t_profile_group\", \"column\": \"id\", \"joinColumn\": \"group_id\"}, \"joinFrom\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}}}, \"dict\": {\"type\": \"profile_person_group_work\"}, \"name\": \"group_work\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"select\", \"title\": \"群体分工\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_group\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"createDept\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"createDept\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}], \"selectable\": false, \"extendFields\": [{\"table\": \"t_profile_person_group_relation\", \"value\": 4, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}], \"searchFields\": [{\"key\": \"name\", \"name\": \"群体名称\"}]}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_group_relation\", \"joinTo\": {\"table\": \"t_profile_group\", \"column\": \"id\", \"joinColumn\": \"group_id\"}, \"joinFrom\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}, \"extendCondition\": [{\"value\": 4, \"column\": \"police_kind\"}]}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1412, '相关事件', 'relatedEvent', 'personV2', 1390, 1, 9, NULL, NULL, '{\"name\": \"相关事件\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_event\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_event_relation\", \"column\": \"event_type\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\", \"databaseRelation\": {\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_group_relation\", \"joinTo\": {\"table\": \"t_profile_event\", \"column\": \"id\", \"joinColumn\": \"event_id\"}, \"joinFrom\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}}}, \"dict\": {\"type\": \"profile_event_type\"}, \"name\": \"event_type\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"select\", \"title\": \"事件类别\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"detail\", \"jdbcType\": \"string\"}, \"name\": \"detail\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"事件详情\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"create_dept_id\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"create_dept_id\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"create_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"datetime\"}, \"name\": \"createTime\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}], \"selectable\": false, \"extendFields\": [{\"table\": \"t_profile_person_event_relation\", \"value\": 4, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}], \"searchFields\": [{\"key\": \"name\", \"name\": \"事件名称\"}]}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_event_relation\", \"joinTo\": {\"table\": \"t_profile_event\", \"column\": \"id\", \"joinColumn\": \"event_id\"}, \"joinFrom\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}, \"extendCondition\": [{\"value\": 4, \"column\": \"police_kind\"}]}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1413, '相关线索', 'relatedClue', 'personV2', 1390, 1, 10, NULL, NULL, '{\"name\": \"相关线索\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_clue\", \"fields\": [{\"db\": {\"table\": \"t_profile_clue\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"线索名称\"}, \"properties\": {\"href\": \"/ys-app/archives/clue/details?id={value}\", \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"clue_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"label_id_array\"}, \"name\": \"clueLabel\", \"listSchema\": {\"style\": {\"align\": \"left\"}, \"filter\": {\"key\": \"clueLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&clue_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"线索标签\"}, \"schema\": {\"type\": \"array\", \"title\": \"线索标签\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"emergency_level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_emergency_level\"}, \"name\": \"emergency_level\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"紧急程度\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"source\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_source_type\"}, \"name\": \"source\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"线索来源\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"create_time\", \"mapping\": \"date_time_to_text\", \"jdbcType\": \"datetime\"}, \"name\": \"createTime\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"disposal_status\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_disposal_status\"}, \"name\": \"disposal_status\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"处置状态\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}], \"selectable\": false, \"extendFields\": [{\"table\": \"t_profile_person_clue_relation\", \"value\": 4, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}], \"searchFields\": [{\"key\": \"name\", \"name\": \"线索名称\"}]}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_clue_relation\", \"joinTo\": {\"table\": \"t_profile_clue\", \"column\": \"id\", \"joinColumn\": \"clue_id\"}, \"joinFrom\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}, \"extendCondition\": [{\"value\": 4, \"column\": \"police_kind\"}]}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1414, '风险点信息', 'riskPoint', 'personV2', 1391, 1, 1, '{\"name\": \"风险点信息\", \"type\": \"TABLE_SCHEMA\", \"table\": \"t_profile_person_risk_other\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_risk_other\", \"column\": \"person_level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"string\"}, \"dict\": {\"type\": \"profile_person_control_level\"}, \"name\": \"person_level\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"管控级别\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_risk_other\", \"column\": \"main_demand\", \"jdbcType\": \"string\"}, \"name\": \"main_demand\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"风险背景\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_risk_other\", \"column\": \"petition_info\", \"jdbcType\": \"string\"}, \"name\": \"petition_info\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"化解难点\", \"copyable\": false}}], \"moduleUi\": {\"column\": 3, \"bordered\": true}}', '{\"name\": \"风险点信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_person_risk_other\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_risk_other\", \"column\": \"person_level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"profile_person_control_level\"}, \"name\": \"person_level\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"radio\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"人员级别\"}}}, {\"db\": {\"table\": \"t_profile_person_risk_other\", \"column\": \"main_demand\", \"jdbcType\": \"string\"}, \"name\": \"main_demand\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"风险背景\"}}}, {\"db\": {\"table\": \"t_profile_person_risk_other\", \"column\": \"petition_info\", \"jdbcType\": \"string\"}, \"name\": \"petition_info\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"化解难点\"}}}], \"required\": [], \"extendFields\": [{\"table\": \"t_profile_person_risk_other\", \"value\": 99, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}]}', NULL, 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_person\", \"column\": \"person_id\"}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1415, '工作措施', 'gzcs', 'personV2', 1391, 1, 2, NULL, NULL, '{\"name\": \"工作措施\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_person_gzcs\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_gzcs\", \"column\": \"detail\", \"jdbcType\": \"string\"}, \"name\": \"detail\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"工作措施\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_person_gzcs\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"create_dept_id\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"create_dept_id\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_person_gzcs\", \"column\": \"create_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"datetime\"}, \"name\": \"createTime\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}], \"selectable\": false, \"extendFields\": [{\"table\": \"t_profile_person_gzcs\", \"value\": 99, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}], \"searchFields\": [{\"key\": \"name\", \"name\": \"工作措施\"}]}', 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_person_gzcs\", \"column\": \"person_id\", \"idColumn\": \"id\", \"primaryColumn\": \"id\", \"extendCondition\": [{\"value\": 99, \"column\": \"police_kind\"}]}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1416, '打处情况', 'dcqk', 'personV2', 1391, 1, 3, NULL, NULL, '{\"name\": \"打处情况\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_person_dcqk\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_dcqk\", \"column\": \"detail\", \"jdbcType\": \"string\"}, \"name\": \"detail\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"打处情况\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_person_dcqk\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"create_dept_id\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"create_dept_id\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_person_dcqk\", \"column\": \"create_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"datetime\"}, \"name\": \"createTime\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}], \"selectable\": false, \"extendFields\": [{\"table\": \"t_profile_person_dcqk\", \"value\": 99, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}], \"searchFields\": [{\"key\": \"name\", \"name\": \"打处情况\"}]}', 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_person_dcqk\", \"column\": \"person_id\", \"idColumn\": \"id\", \"primaryColumn\": \"id\", \"extendCondition\": [{\"value\": 99, \"column\": \"police_kind\"}]}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1417, '政府管控信息', 'govControl', 'personV2', 1391, 1, 4, '{\"name\": \"政府管控信息\", \"table\": \"t_profile_person_government_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government\", \"jdbcType\": \"string\"}, \"name\": \"control_government\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"党政责任部门\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government_person\", \"jdbcType\": \"string\"}, \"name\": \"control_government_person\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"党政责任人\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government_person_duty\", \"jdbcType\": \"string\"}, \"name\": \"control_government_person_duty\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"党政责任人职务\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government_contact\", \"jdbcType\": \"string\"}, \"name\": \"control_government_contact\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"联系方式\", \"copyable\": true}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community\", \"jdbcType\": \"string\"}, \"name\": \"control_community\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任街道社区\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community_person\", \"jdbcType\": \"string\"}, \"name\": \"control_community_person\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"社区责任人\", \"config\": {\"fieldName\": \"control_community_person\", \"processType\": \"string\", \"processConfig\": null}, \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community_person_duty\", \"jdbcType\": \"string\"}, \"name\": \"control_community_person_duty\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"社区责任人职务\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community_contact\", \"jdbcType\": \"string\"}, \"name\": \"control_community_contact\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"联系方式\", \"copyable\": true}}], \"moduleUi\": {\"column\": 3, \"bordered\": true}}', '{\"name\": \"政府管控信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_person_government_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government\", \"jdbcType\": \"string\"}, \"name\": \"controlGovernment\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"党政责任部门\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government_person\", \"jdbcType\": \"string\"}, \"name\": \"controlGovernmentPerson\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"党政责任人\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government_person_duty\", \"jdbcType\": \"string\"}, \"name\": \"controlGovernmentPersonDuty\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"党政责任人职务\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_government_contact\", \"jdbcType\": \"string\"}, \"name\": \"controlGovernmentContact\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"党政责任人联系方式\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community\", \"jdbcType\": \"string\"}, \"name\": \"controlCommunity\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"责任街道社区\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community_person\", \"jdbcType\": \"string\"}, \"name\": \"controlCommunityPerson\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"社区责任人\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community_person_duty\", \"jdbcType\": \"string\"}, \"name\": \"controlCommunityPersonDuty\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"社区责任人职务\"}}}, {\"db\": {\"table\": \"t_profile_person_government_control\", \"column\": \"control_community_contact\", \"jdbcType\": \"string\"}, \"name\": \"controlCommunityContact\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"社区责任人联系方式\"}}}], \"required\": [], \"extendFields\": [{\"table\": \"t_profile_person_government_control\", \"value\": 99, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}]}', NULL, 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_person_government_control\", \"column\": \"person_id\", \"extendCondition\": [{\"value\": 99, \"column\": \"police_kind\"}]}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1418, '公安管控信息', 'policeControl', 'personV2', 1391, 1, 5, '{\"name\": \"公安管控信息\", \"type\": \"TABLE_SCHEMA\", \"table\": \"t_profile_person_police_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_bureau\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"control_bureau\", \"tree\": {\"type\": \"dept\"}, \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任分局\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_bureau_leader\", \"mapping\": \"user_id_array_to_single_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_bureau_leader\", \"tableSchema\": {\"span\": 1, \"type\": \"user\", \"title\": \"责任领导\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_police\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"control_police\", \"tree\": {\"type\": \"dept\"}, \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任警种\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_police_leader\", \"mapping\": \"user_id_array_to_single_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_police_leader\", \"tableSchema\": {\"span\": 1, \"type\": \"user\", \"title\": \"责任领导\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_station\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"control_station\", \"tree\": {\"type\": \"dept\"}, \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任派出所\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_station_leader\", \"mapping\": \"user_id_array_to_single_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_station_leader\", \"tableSchema\": {\"span\": 1, \"type\": \"user\", \"title\": \"责任领导\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_person\", \"mapping\": \"user_id_array_to_user_card\", \"jdbcType\": \"string\"}, \"name\": \"control_person\", \"tableSchema\": {\"span\": 2, \"type\": \"multiUser\", \"title\": \"责任民警\", \"copyable\": false}}], \"moduleUi\": {\"column\": 2, \"bordered\": true}}', '{\"name\": \"公安管控信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_person_police_control\", \"fields\": [{\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_bureau\", \"jdbcType\": \"string\"}, \"name\": \"controlBureau\", \"tree\": {\"root\": \"510500000000\", \"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"责任分局\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_bureau_leader\", \"jdbcType\": \"number\"}, \"name\": \"controlBureauLeader\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlBureau\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"责任分局领导\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_police\", \"jdbcType\": \"string\"}, \"name\": \"controlPolice\", \"tree\": {\"root\": \"510500000000\", \"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"责任警种\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_police_leader\", \"jdbcType\": \"number\"}, \"name\": \"controlPoliceLeader\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlPolice\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"责任警种领导\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_station\", \"jdbcType\": \"string\"}, \"name\": \"controlStation\", \"tree\": {\"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"责任派出所\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_station_leader\", \"jdbcType\": \"number\"}, \"name\": \"controlStationLeader\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlStation\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"责任派出所领导\"}}}, {\"db\": {\"table\": \"t_profile_person_police_control\", \"column\": \"control_person\", \"jdbcType\": \"json_id_array\"}, \"name\": \"controlPerson\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"1\", \"widget\": \"select\", \"options\": \"/permission/user/user-dept/list/no-page?deptId=$.controlStation\", \"fieldNames\": {\"label\": \"name\", \"value\": \"userId\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"array\", \"items\": {\"type\": \"number\"}, \"title\": \"责任民警\", \"minItems\": 1}}}], \"required\": [\"controlPerson\", \"controlStationLeader\", \"controlStation\"], \"extendFields\": [{\"table\": \"t_profile_person_police_control\", \"value\": 99, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}]}', NULL, 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_person\", \"column\": \"person_id\", \"extendCondition\": [{\"value\": 99, \"column\": \"police_kind\"}]}', 'NO_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1419, '相关群体', 'relatedGroup', 'personV2', 1391, 1, 6, NULL, NULL, '{\"name\": \"相关群体\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_group\", \"fields\": [{\"db\": {\"table\": \"t_profile_group\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"群体名称\"}, \"properties\": {\"href\": \"/ys-app/archives/group/details?id={value}\", \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_group\", \"column\": \"group_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"label_id_array\"}, \"name\": \"groupLabel\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"groupLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&group_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"群体标签\"}, \"schema\": {\"type\": \"array\", \"title\": \"群体标签\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_person_group_relation\", \"column\": \"activity_level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\", \"databaseRelation\": {\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_group_relation\", \"joinTo\": {\"table\": \"t_profile_group\", \"column\": \"id\", \"joinColumn\": \"group_id\"}, \"joinFrom\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}}}, \"dict\": {\"type\": \"profile_activity_level\"}, \"name\": \"activityLevel\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"select\", \"title\": \"活跃程度\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_group\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"createDept\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"createDept\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}], \"selectable\": false, \"extendFields\": [{\"table\": \"t_profile_person_group_relation\", \"value\": 99, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}], \"searchFields\": [{\"key\": \"name\", \"name\": \"群体名称\"}]}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_group_relation\", \"joinTo\": {\"table\": \"t_profile_group\", \"column\": \"id\", \"joinColumn\": \"group_id\"}, \"joinFrom\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}, \"extendCondition\": [{\"value\": 99, \"column\": \"police_kind\"}]}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1420, '相关事件', 'relatedEvent', 'personV2', 1391, 1, 7, NULL, NULL, '{\"name\": \"相关事件\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_event\", \"fields\": [{\"db\": {\"table\": \"t_profile_event\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"事件名称\"}, \"properties\": {\"href\": \"/ys-app/archives/event/details?id={value}\", \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"create_dept_id\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"create_dept_id\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"create_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"datetime\"}, \"name\": \"createTime\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}], \"selectable\": false, \"extendFields\": [{\"table\": \"t_profile_person_event_relation\", \"value\": 99, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}], \"searchFields\": [{\"key\": \"name\", \"name\": \"事件名称\"}]}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_event_relation\", \"joinTo\": {\"table\": \"t_profile_event\", \"column\": \"id\", \"joinColumn\": \"event_id\"}, \"joinFrom\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}, \"extendCondition\": [{\"value\": 99, \"column\": \"police_kind\"}]}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1421, '相关线索', 'relatedClue', 'personV2', 1391, 1, 8, NULL, NULL, '{\"name\": \"相关线索\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_clue\", \"fields\": [{\"db\": {\"table\": \"t_profile_clue\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"线索名称\"}, \"properties\": {\"href\": \"/ys-app/archives/clue/details?id={value}\", \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"clue_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"label_id_array\"}, \"name\": \"clueLabel\", \"listSchema\": {\"style\": {\"align\": \"left\"}, \"filter\": {\"key\": \"clueLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&clue_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"线索标签\"}, \"schema\": {\"type\": \"array\", \"title\": \"线索标签\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"emergency_level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_emergency_level\"}, \"name\": \"emergency_level\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"紧急程度\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"source\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_source_type\"}, \"name\": \"source\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"线索来源\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"create_time\", \"mapping\": \"date_time_to_text\", \"jdbcType\": \"datetime\"}, \"name\": \"createTime\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"disposal_status\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_disposal_status\"}, \"name\": \"disposal_status\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"处置状态\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}], \"selectable\": false, \"extendFields\": [{\"table\": \"t_profile_person_clue_relation\", \"value\": 99, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}], \"searchFields\": [{\"key\": \"name\", \"name\": \"线索名称\"}]}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_clue_relation\", \"joinTo\": {\"table\": \"t_profile_clue\", \"column\": \"id\", \"joinColumn\": \"clue_id\"}, \"joinFrom\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}, \"extendCondition\": [{\"value\": 99, \"column\": \"police_kind\"}]}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1422, '相关案件', 'relatedCase', 'personV2', 1316, 1, 5, NULL, NULL, '{\"name\": \"相关案件\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_case\", \"fields\": [{\"db\": {\"table\": \"t_profile_case\", \"column\": \"ajmc\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"案件名称\"}, \"properties\": {\"href\": \"/ys-app/archives/case/details?id={value}\", \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_case\", \"column\": \"update_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"update_dept_id\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"update_dept_id\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_case\", \"column\": \"update_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"datetime\"}, \"name\": \"update_time\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}], \"selectable\": false, \"extendFields\": [{\"table\": \"t_profile_person_case_relation\", \"value\": 10, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}], \"searchFields\": [{\"key\": \"name\", \"name\": \"案件名称\"}]}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_case_related_person\", \"joinTo\": {\"table\": \"t_profile_case\", \"column\": \"asjbh\", \"joinColumn\": \"asjbh\"}, \"joinFrom\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}, \"extendCondition\": [{\"value\": \"10\", \"column\": \"police_kind\"}]}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1426, '相关业务', 'relatedService', 'personV2', NULL, 1, 10, NULL, NULL, NULL, 0, NULL, NULL, NULL, 1, 1, 1, 1);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1427, '相关常控', 'relatedRegular', 'personV2', 1426, 1, 19, NULL, NULL, '{}', 0, '{}', 'NO_SCHEMA', 'NO_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1428, '相关布控', 'relatedMonitor', 'personV2', 1426, 1, 12, NULL, NULL, NULL, 0, NULL, 'NO_SCHEMA', 'NO_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1429, '相关预警', 'relatedWarning', 'personV2', 1426, 1, 11, NULL, NULL, NULL, 0, NULL, 'NO_SCHEMA', NULL, 1, 1, 1, 1);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1430, '风险研判', 'personWorkRecord', 'personV2', NULL, 1, 22, NULL, NULL, NULL, 0, NULL, 'NO_SCHEMA', 'NO_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1431, '督查清单', 'inspector', 'personV2', NULL, 1, 21, NULL, NULL, '{}', 0, '{}', 'NO_SCHEMA', 'NO_SCHEMA', 0, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1432, '人员轨迹', 'track', 'personV2', NULL, 1, 20, NULL, NULL, NULL, 0, NULL, 'NO_SCHEMA', 'NO_SCHEMA', 0, 1, 1, 1);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1433, '重点群体人员', 'riskPersonInfo', 'groupV2', 1333, 1, 28, NULL, NULL, '{\"name\": \"重点群体人员\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_group_risk_person_relation\", \"fields\": [{\"db\": {\"table\": \"t_profile_group_risk_person_relation\", \"column\": \"role_name\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"integer\"}, \"dict\": {\"type\": \"important_group_person_type\"}, \"name\": \"roleName\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"select\", \"title\": \"身份\"}, \"properties\": {\"copyable\": true, \"editable\": true, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_group_risk_person_relation\", \"column\": \"person_name\", \"jdbcType\": \"string\"}, \"name\": \"personName\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"姓名\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": true, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_group_risk_person_relation\", \"column\": \"id_card\", \"jdbcType\": \"string\"}, \"name\": \"idCard\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"身份证号\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": true, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_group_risk_person_relation\", \"column\": \"present_address\", \"jdbcType\": \"string\"}, \"name\": \"presentAddress\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"现住址\"}, \"properties\": {\"copyable\": true, \"editable\": true, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_group_risk_person_relation\", \"column\": \"tel\", \"jdbcType\": \"string\"}, \"name\": \"tel\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"联系方式\"}, \"properties\": {\"copyable\": true, \"editable\": true, \"required\": false, \"sortable\": false}}}], \"selectable\": false}', 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_group_risk_person_relation\", \"column\": \"group_id\", \"idColumn\": \"id\", \"primaryColumn\": \"id\"}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1464, '档案管理', 'archive', 'eventV2', NULL, 0, 1, NULL, NULL, '{\"name\": \"事件档案\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_event\", \"fields\": [{\"db\": {\"table\": \"t_profile_event\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"事件名称\"}, \"properties\": {\"href\": \"/ys-app/archives/event/details?id={value}\", \"isName\": true, \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"event_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"label_id_array\"}, \"name\": \"eventLabel\", \"listSchema\": {\"style\": {\"align\": \"left\"}, \"filter\": {\"key\": \"eventLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&event_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"事件类别\"}, \"schema\": {\"type\": \"array\", \"title\": \"事件类别\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"create_dept_id\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"filter\": {\"key\": \"create_dept_id\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_event_level\"}, \"name\": \"level\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"level\", \"type\": \"select\", \"value\": [\"%%profile_event_level%%\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"code\"}, \"displayName\": \"事件级别\"}, \"schema\": {\"type\": \"string\", \"title\": \"事件级别\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"related_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"datetime\"}, \"name\": \"relatedTime\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"filter\": {\"key\": \"relatedTime\", \"type\": \"timeParams\", \"value\": [{\"id\": \"99\", \"name\": \"自定义\"}], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\"}, \"displayName\": \"维权开始时间\"}, \"schema\": {\"type\": \"string\", \"title\": \"维权开始时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"related_end_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"datetime\"}, \"name\": \"relatedEndTime\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"filter\": {\"key\": \"relatedEndTime\", \"type\": \"timeParams\", \"value\": [{\"id\": \"99\", \"name\": \"自定义\"}], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\"}, \"displayName\": \"维权结束时间\"}, \"schema\": {\"type\": \"string\", \"title\": \"维权结束时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"related_address\", \"mapping\": \"map_location_string\", \"jdbcType\": \"string\"}, \"name\": \"relatedAddress\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"维权地址\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"event_happened\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_event_happened\"}, \"name\": \"event_happened\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"event_happened\", \"type\": \"option\", \"value\": [\"%%profile_event_happened%%\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"code\", \"children\": \"children\"}, \"displayName\": \"是否发生\"}, \"schema\": {\"type\": \"string\", \"title\": \"是否发生\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"source\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_event_source\"}, \"name\": \"source\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"事件来源\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"status\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_event_status\"}, \"name\": \"status\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"事件状态\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"create_time\", \"mapping\": \"date_time_to_text\", \"jdbcType\": \"datetime\"}, \"name\": \"createTime\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true, \"sortDefault\": \"descending\"}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"control_station\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"control_station\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"filter\": {\"key\": \"control_station\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"displayName\": \"主管单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"主管单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"control_police\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"control_police\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"filter\": {\"key\": \"control_police\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"displayName\": \"主责警种\"}, \"schema\": {\"type\": \"string\", \"title\": \"主责警种\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_dept\", \"column\": \"police_kind\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"array\", \"databaseRelation\": {\"type\": \"IMPORT_TABLE\", \"column\": \"code\", \"joinFrom\": {\"joinColumn\": \"control_police\"}}}, \"dict\": {\"type\": \"police_kind\"}, \"name\": \"police_kind\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"filter\": {\"key\": \"police_kind\", \"type\": \"multiple-select\", \"value\": [\"%%police_kind%%\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"code\"}, \"displayName\": \"管控警种\"}, \"schema\": {\"type\": \"string\", \"title\": \"管控警种\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_group_event_relation r left join t_profile_group g on r.group_id = g.id\", \"column\": \"group_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"eventRelatedGroup\", \"databaseRelation\": {\"type\": \"EVENT_RELATED_GROUP\", \"table\": \"t_profile_group_event_relation r left join t_profile_group g on r.event_id = g.id\", \"column\": \"event_id\"}}, \"name\": \"groupLabel\", \"listSchema\": {\"style\": {\"align\": \"left\", \"width\": 120, \"ellipsis\": true}, \"filter\": {\"key\": \"groupLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&group_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"关联群体类别\"}, \"schema\": {\"type\": \"array\", \"title\": \"关联群体类别\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2, \"isRelatedShow\": true}}}], \"selectable\": true, \"searchFields\": [{\"key\": \"name\", \"name\": \"事件名称\"}, {\"key\": \"relatedAddress\", \"name\": \"事发地点\"}]}', 0, '{}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1465, '基本信息', 'basicInfo', 'eventV2', NULL, 1, 2, '{}', '{}', '{}', 1, '{}', '', '', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1466, '事件信息', 'eventInfo', 'eventV2', 1465, 1, 3, '{\"name\": \"事件信息\", \"type\": \"TABLE_SCHEMA\", \"table\": \"t_profile_event\", \"fields\": [{\"db\": {\"table\": \"t_profile_event\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"tableSchema\": {\"span\": 2, \"type\": \"string\", \"title\": \"事件名称\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"belong_location\", \"mapping\": \"district_code_to_name\", \"jdbcType\": \"string\"}, \"name\": \"belong_location\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"归属地\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"person_estimation\", \"jdbcType\": \"number\"}, \"name\": \"person_estimation\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"估计人数\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"risk_level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_event_risk_level\"}, \"name\": \"risk_level\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"风险等级\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"detail\", \"jdbcType\": \"string\"}, \"name\": \"detail\", \"tableSchema\": {\"span\": 2, \"type\": \"string\", \"title\": \"事件详情\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"disposal_result\", \"jdbcType\": \"string\"}, \"name\": \"disposal_result\", \"tableSchema\": {\"span\": 2, \"type\": \"string\", \"title\": \"处置结果\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"description\", \"jdbcType\": \"string\"}, \"name\": \"description\", \"tableSchema\": {\"span\": 2, \"type\": \"string\", \"title\": \"简要说明\", \"copyable\": false}}], \"moduleUi\": {\"column\": 2, \"bordered\": true}}', '{\"name\": \"事件信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_event\", \"fields\": [{\"db\": {\"table\": \"t_profile_event\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"1\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"事件名称\"}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"belong_location\", \"mapping\": \"district_code_to_name\", \"jdbcType\": \"string\"}, \"name\": \"belong_location\", \"tree\": {\"root\": \"510600\", \"type\": \"district\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"name\", \"value\": \"code\", \"children\": \"children\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"归属地\"}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"person_estimation\", \"jdbcType\": \"number\"}, \"name\": \"person_estimation\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"input\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"估计人数\"}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"risk_level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_event_risk_level\"}, \"name\": \"risk_level\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"radio\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"风险等级\"}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"detail\", \"jdbcType\": \"string\"}, \"name\": \"detail\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"1\", \"widget\": \"textarea\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"事件详情\"}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"disposal_result\", \"jdbcType\": \"string\"}, \"name\": \"disposal_result\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"1\", \"widget\": \"textarea\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"处置结果\"}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"description\", \"jdbcType\": \"string\"}, \"name\": \"description\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"1\", \"widget\": \"textarea\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"简要说明\"}}}], \"required\": [\"name\"]}', '{}', 1, '{\"type\": \"PRIMARY_KEY\", \"table\": \"t_profile_event\", \"column\": \"id\"}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1467, '相关档案', 'relatedProfile', 'eventV2', NULL, 1, 4, '{}', '{}', '{}', 1, '{}', '', '', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1468, '涉及人员', 'relatedPerson', 'eventV2', 1467, 1, 5, NULL, NULL, '{\"name\": \"相关人员\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_person\", \"fields\": [{\"db\": {\"table\": \"t_profile_person\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 104}, \"schema\": {\"type\": \"string\", \"title\": \"人员名称\"}, \"properties\": {\"href\": \"/ys-app/archives/person/details?id={value}\", \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"risk_level\", \"jdbcType\": \"string\"}, \"name\": \"riskLevel\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 128}, \"schema\": {\"type\": \"string\", \"title\": \"风险等级\"}, \"properties\": {\"colorMap\": {\"关注\": \"#333333 \", \"中风险\": \"#FFCE60\", \"低风险\": \"#6088D6\", \"高风险\": \"#FA8C34\", \"重中之重\": \"#EC3939\"}, \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"id_number\", \"jdbcType\": \"number\"}, \"name\": \"idNumber\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 224}, \"schema\": {\"type\": \"string\", \"title\": \"身份证号\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"exist\": false, \"table\": \"t_profile_person_event_relation\", \"column\": \"risk_label_ids\", \"mapping\": \"risk_label_ids_sum_score\", \"jdbcType\": \"string\", \"databaseRelation\": {\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_event_relation\", \"joinTo\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}, \"joinFrom\": {\"table\": \"t_profile_event\", \"column\": \"id\", \"joinColumn\": \"event_id\"}}}, \"name\": \"riskScore\", \"listSchema\": {\"style\": {\"align\": \"left\", \"width\": 128}, \"schema\": {\"type\": \"string\", \"title\": \"风险分值\"}, \"properties\": {\"color\": \"#FF6C6C\", \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"person_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"label_id_array\"}, \"name\": \"personLabel\", \"listSchema\": {\"style\": {\"align\": \"left\", \"width\": 237}, \"filter\": {\"key\": \"personLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&person_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"人员标签\"}, \"schema\": {\"type\": \"array\", \"title\": \"人员标签\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2}}}, {\"db\": {\"table\": \"t_profile_person_event_relation\", \"column\": \"risk_label_ids\", \"mapping\": \"risk_label_id_array_to_name\", \"jdbcType\": \"string\", \"databaseRelation\": {\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_event_relation\", \"joinTo\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}, \"joinFrom\": {\"table\": \"t_profile_event\", \"column\": \"id\", \"joinColumn\": \"event_id\"}}}, \"name\": \"riskLabels\", \"listSchema\": {\"style\": {\"align\": \"left\"}, \"schema\": {\"type\": \"risk_label_array\", \"title\": \"风险标识\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": false, \"sortable\": false, \"instrLength\": 3}}}], \"selectable\": false, \"searchFields\": [{\"key\": \"name\", \"name\": \"人员名称\"}]}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_event_relation\", \"joinTo\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}, \"joinFrom\": {\"table\": \"t_profile_event\", \"column\": \"id\", \"joinColumn\": \"event_id\"}}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1469, '涉及群体', 'relatedGroup', 'eventV2', 1467, 1, 6, NULL, NULL, '{\"name\": \"相关群体\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_group\", \"fields\": [{\"db\": {\"table\": \"t_profile_group\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"群体名称\"}, \"properties\": {\"href\": \"/ys-app/archives/group/details?id={value}\", \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_group\", \"column\": \"group_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"label_id_array\"}, \"name\": \"groupLabel\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"groupLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&group_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"群体标签\"}, \"schema\": {\"type\": \"array\", \"title\": \"群体标签\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2}}}, {\"db\": {\"table\": \"t_profile_group\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"createDept\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"createDept\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}], \"selectable\": false, \"searchFields\": [{\"key\": \"name\", \"name\": \"群体名称\"}]}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_group_event_relation\", \"joinTo\": {\"table\": \"t_profile_group\", \"column\": \"id\", \"joinColumn\": \"group_id\"}, \"joinFrom\": {\"table\": \"t_profile_event\", \"column\": \"id\", \"joinColumn\": \"event_id\"}}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1470, '事件材料', 'eventFiles', 'eventV2', 1467, 1, 7, NULL, NULL, '{}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_event_file_relation\", \"column\": \"id\", \"joinTo\": {\"table\": \"t_file_info\", \"column\": \"id\", \"joinColumn\": \"file_id\"}, \"joinFrom\": {\"table\": \"t_profile_event\", \"column\": \"id\", \"joinColumn\": \"event_id\"}}', 'FILE_SCHEMA', 'FILE_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1471, '相关线索', 'relatedClue', 'eventV2', 1467, 1, 8, NULL, NULL, '{\"name\": \"相关线索\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_clue\", \"fields\": [{\"db\": {\"table\": \"t_profile_clue\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"线索名称\"}, \"properties\": {\"href\": \"/ys-app/archives/clue/details?id={value}\", \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"clue_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"label_id_array\"}, \"name\": \"clueLabel\", \"listSchema\": {\"style\": {\"align\": \"left\"}, \"filter\": {\"key\": \"clueLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&clue_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"线索标签\"}, \"schema\": {\"type\": \"array\", \"title\": \"线索标签\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"source\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_source\"}, \"name\": \"source\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"线索来源\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"disposal_status\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_disposal_status\"}, \"name\": \"disposal_status\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"处置状态\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"emergency_level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_clue_emergency_level\"}, \"name\": \"emergency_level\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"紧急程度\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_clue\", \"column\": \"create_time\", \"mapping\": \"date_time_to_text\", \"jdbcType\": \"datetime\"}, \"name\": \"createTime\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true}}}], \"selectable\": false, \"searchFields\": [{\"key\": \"name\", \"name\": \"线索名称\"}]}', 1, '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_event_clue_relation\", \"joinTo\": {\"table\": \"t_profile_clue\", \"column\": \"id\", \"joinColumn\": \"clue_id\"}, \"joinFrom\": {\"table\": \"t_profile_event\", \"column\": \"id\", \"joinColumn\": \"event_id\"}}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1472, '警种信息', 'policeKind', 'eventV2', NULL, 1, 3, '{}', '{}', '{}', 1, '{}', '', '', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1473, '经侦', 'jz', 'eventV2', 1472, 1, 4, '{}', '{}', '{}', 1, '{}', NULL, NULL, 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1474, '风险点信息', 'riskPoint', 'eventV2', 1473, 1, 1, '{\"name\": \"事件信息\", \"type\": \"TABLE_SCHEMA\", \"table\": \"t_profile_event_risk_point_info\", \"fields\": [{\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"event_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"json_id_array\"}, \"name\": \"event_label\", \"tableSchema\": {\"span\": 1, \"type\": \"label\", \"title\": \"事件类别\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"source\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_event_source\"}, \"name\": \"source\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"事件来源\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"event_target_address\", \"mapping\": \"dict_code_array_to_name\", \"jdbcType\": \"json_id_array\"}, \"dict\": {\"type\": \"profile_event_zxdd\"}, \"name\": \"event_target_address\", \"tableSchema\": {\"span\": 1, \"type\": \"dict\", \"title\": \"事件指向地点\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"related_address_mgd\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_event_mgd\"}, \"name\": \"related_address_mgd\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"事件指向地点敏感度\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"related_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"timestamp\"}, \"name\": \"related_time\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"事件指向时间\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"related_end_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"timestamp\"}, \"name\": \"related_end_time\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"结束时间\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"source_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"timestamp\"}, \"name\": \"source_time\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"发生时间\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"related_address\", \"mapping\": \"map_location\", \"jdbcType\": \"string\"}, \"name\": \"related_address\", \"tableSchema\": {\"span\": 2, \"type\": \"mapLocation\", \"title\": \"发生地点\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"control_station\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"control_station\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"主管单位\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"control_police\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"control_police\", \"tree\": {\"type\": \"dept\"}, \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"主责警种\", \"copyable\": false}}], \"moduleUi\": {\"column\": 2, \"bordered\": true}}', '{\"name\": \"事件信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_event_risk_point_info\", \"fields\": [{\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"event_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"json_id_array\"}, \"name\": \"event_label\", \"tree\": {\"root\": \"event\", \"type\": \"label\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": true, \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"array\", \"items\": {\"type\": \"array\", \"items\": {\"type\": \"number\"}, \"minItems\": 1}, \"title\": \"事件类别\", \"minItems\": 1}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"source\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_event_source\"}, \"name\": \"source\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"事件来源\"}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"event_target_address\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_event_zxdd\"}, \"name\": \"event_target_address\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"事件指向地点\"}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"related_address_mgd\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_event_mgd\"}, \"name\": \"related_address_mgd\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"radio\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"事件指向地点敏感度\"}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"related_time\", \"mapping\": \"date_time_to_timestamp\", \"jdbcType\": \"timestamp\"}, \"name\": \"related_time\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"datePicker\", \"showTime\": true, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"事件指向时间\"}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"related_end_time\", \"mapping\": \"date_time_to_timestamp\", \"jdbcType\": \"timestamp\"}, \"name\": \"related_end_time\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"datePicker\", \"showTime\": true, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"结束时间\"}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"source_time\", \"mapping\": \"date_time_to_timestamp\", \"jdbcType\": \"timestamp\"}, \"name\": \"source_time\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"datePicker\", \"showTime\": true, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"发生时间\"}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"related_address\", \"jdbcType\": \"map\"}, \"name\": \"related_address\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"1\", \"widget\": \"map\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"发生地点\"}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"control_station\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"control_station\", \"tree\": {\"root\": \"510600000000\", \"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"主管单位\"}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"control_police\", \"jdbcType\": \"string\"}, \"name\": \"controlPolice\", \"tree\": {\"root\": \"510500000000\", \"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"主责警种\"}}}], \"required\": [\"event_label\", \"source\", \"related_time\"], \"extendFields\": [{\"table\": \"t_profile_event_risk_point_info\", \"value\": 3, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}]}', '{}', 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_event_risk_point_info\", \"column\": \"event_id\", \"extendCondition\": [{\"value\": \"3\", \"column\": \"police_kind\"}]}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1475, '治安', 'za', 'eventV2', 1472, 1, 4, '{}', '{}', '{}', 1, '{}', NULL, NULL, 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1476, '风险点信息', 'riskPoint', 'eventV2', 1475, 1, 1, '{\"name\": \"事件信息\", \"type\": \"TABLE_SCHEMA\", \"table\": \"t_profile_event_risk_point_info\", \"fields\": [{\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"event_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"json_id_array\"}, \"name\": \"event_label\", \"tableSchema\": {\"span\": 1, \"type\": \"label\", \"title\": \"事件类别\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"source\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_event_source_za\"}, \"name\": \"source\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"事件来源\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"control_station\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"control_station\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任分县局(牵头)\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"control_station_cooperate\", \"mapping\": \"dept_path_id_array_to_dept_name\", \"jdbcType\": \"json_id_array\"}, \"name\": \"control_station_cooperate\", \"tableSchema\": {\"span\": 1, \"type\": \"dept\", \"title\": \"责任分县局(配合)\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"control_police\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"control_police\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"责任警种(牵头)\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"control_police_cooperate\", \"mapping\": \"dept_path_id_array_to_dept_name\", \"jdbcType\": \"json_id_array\"}, \"name\": \"control_police_cooperate\", \"tableSchema\": {\"span\": 1, \"type\": \"dept\", \"title\": \"责任警种(配合)\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"related_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"timestamp\"}, \"name\": \"related_time\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"事件开始时间\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"related_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"timestamp\"}, \"name\": \"related_time\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"事件结束时间\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"related_address\", \"mapping\": \"map_location\", \"jdbcType\": \"string\"}, \"name\": \"related_address\", \"tableSchema\": {\"span\": 2, \"type\": \"mapLocation\", \"title\": \"事件发生地址\", \"copyable\": false}}], \"moduleUi\": {\"column\": 2, \"bordered\": true}}', '{\"name\": \"事件信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_event_risk_point_info\", \"fields\": [{\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"event_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"json_id_array\"}, \"name\": \"event_label\", \"tree\": {\"root\": \"event\", \"type\": \"label\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": true, \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"array\", \"items\": {\"type\": \"array\", \"items\": {\"type\": \"number\"}, \"minItems\": 1}, \"title\": \"事件类别\", \"minItems\": 1}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"source\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_event_source_za\"}, \"name\": \"source\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"事件来源\"}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"control_station\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"control_station\", \"tree\": {\"root\": \"510600000000\", \"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"责任分县局（牵头）\"}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"control_station_cooperate\", \"mapping\": \"dept_id_array_to_dept_name\", \"jdbcType\": \"json_id_array\"}, \"name\": \"control_station_cooperate\", \"tree\": {\"root\": 2, \"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": true, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"array\", \"items\": {\"type\": \"array\", \"items\": {\"type\": \"number\"}, \"minItems\": 1}, \"title\": \"责任分县局（配合）\", \"minItems\": 1}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"control_police\", \"jdbcType\": \"string\"}, \"name\": \"controlPolice\", \"tree\": {\"root\": \"510500000000\", \"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"责任警种（牵头）\"}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"control_police_cooperate\", \"mapping\": \"dept_id_array_to_dept_name\", \"jdbcType\": \"json_id_array\"}, \"name\": \"control_police_cooperate\", \"tree\": {\"root\": 2, \"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": true, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"array\", \"items\": {\"type\": \"array\", \"items\": {\"type\": \"number\"}, \"minItems\": 1}, \"title\": \"责任警种（配合）\", \"minItems\": 1}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"related_time\", \"mapping\": \"date_time_to_timestamp\", \"jdbcType\": \"timestamp\"}, \"name\": \"related_time\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"datePicker\", \"showTime\": true, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"事件开始时间\"}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"related_end_time\", \"mapping\": \"date_time_to_timestamp\", \"jdbcType\": \"timestamp\"}, \"name\": \"related_end_time\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"datePicker\", \"showTime\": true, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"事件结束时间\"}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"related_address\", \"jdbcType\": \"map\"}, \"name\": \"related_address\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"1\", \"widget\": \"map\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"事件发生地点\"}}}], \"required\": [\"event_label\", \"source\", \"related_time\"], \"extendFields\": [{\"table\": \"t_profile_event_risk_point_info\", \"value\": 4, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}]}', '{}', 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_event_risk_point_info\", \"column\": \"event_id\", \"extendCondition\": [{\"value\": \"4\", \"column\": \"police_kind\"}]}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1477, '其他', 'qt', 'eventV2', 1472, 1, 4, '{}', '{}', '{}', 1, '{}', NULL, NULL, 1, 1, 1, 0);
INSERT INTO `t_profile_module`(`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1478, '风险点信息', 'riskPoint', 'eventV2', 1477, 1, 1, '{\"name\": \"事件信息\", \"type\": \"TABLE_SCHEMA\", \"table\": \"t_profile_event_risk_point_info\", \"fields\": [{\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"event_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"json_id_array\"}, \"name\": \"event_label\", \"tableSchema\": {\"span\": 1, \"type\": \"label\", \"title\": \"事件类别\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"source\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_event_source\"}, \"name\": \"source\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"事件来源\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"source_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"timestamp\"}, \"name\": \"source_time\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"原发时间\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_event_level\"}, \"name\": \"level\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"事件级别\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"control_police\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"control_police\", \"tree\": {\"type\": \"dept\"}, \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"主责警种\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"control_station\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"control_station\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"主管单位\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"related_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"timestamp\"}, \"name\": \"related_time\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"维权开始时间\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"related_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"timestamp\"}, \"name\": \"related_time\", \"tableSchema\": {\"span\": 1, \"type\": \"string\", \"title\": \"维权结束时间\", \"copyable\": false}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"related_address\", \"mapping\": \"map_location\", \"jdbcType\": \"string\"}, \"name\": \"related_address\", \"tableSchema\": {\"span\": 2, \"type\": \"mapLocation\", \"title\": \"维权地址\", \"copyable\": false}}], \"moduleUi\": {\"column\": 2, \"bordered\": true}}', '{\"name\": \"事件信息\", \"type\": \"FORM_SCHEMA\", \"table\": \"t_profile_event_risk_point_info\", \"fields\": [{\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"event_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"json_id_array\"}, \"name\": \"event_label\", \"tree\": {\"root\": \"event\", \"type\": \"label\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": true, \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"array\", \"items\": {\"type\": \"array\", \"items\": {\"type\": \"number\"}, \"minItems\": 1}, \"title\": \"事件类别\", \"minItems\": 1}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"source\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_event_source\"}, \"name\": \"source\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"select\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"事件来源\"}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"source_time\", \"mapping\": \"date_time_to_timestamp\", \"jdbcType\": \"timestamp\"}, \"name\": \"source_time\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"datePicker\", \"showTime\": true, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"原发时间\"}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_event_level\"}, \"name\": \"level\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"radio\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"事件级别\"}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"control_police\", \"jdbcType\": \"string\"}, \"name\": \"controlPolice\", \"tree\": {\"root\": \"510500000000\", \"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"主责警种\"}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"control_station\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"control_station\", \"tree\": {\"root\": \"510600000000\", \"type\": \"dept\"}, \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"cascader\", \"multiple\": false, \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"titleLocation\": \"left\", \"notOnlyChildren\": true}}, \"schema\": {\"type\": \"string\", \"title\": \"主管单位\"}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"related_time\", \"mapping\": \"date_time_to_timestamp\", \"jdbcType\": \"timestamp\"}, \"name\": \"related_time\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"datePicker\", \"showTime\": true, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"维权开始时间\"}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"related_end_time\", \"mapping\": \"date_time_to_timestamp\", \"jdbcType\": \"timestamp\"}, \"name\": \"related_end_time\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"0.5\", \"widget\": \"datePicker\", \"showTime\": true, \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"number\", \"title\": \"维权结束时间\"}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"related_address\", \"jdbcType\": \"map\"}, \"name\": \"related_address\", \"formSchema\": {\"ui\": {\"ui:options\": {\"width\": \"1\", \"widget\": \"map\", \"titleLocation\": \"left\"}}, \"schema\": {\"type\": \"string\", \"title\": \"维权地址\"}}}], \"required\": [\"event_label\", \"source\", \"related_time\"], \"extendFields\": [{\"table\": \"t_profile_event_risk_point_info\", \"value\": 99, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}]}', '{}', 1, '{\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_event_risk_point_info\", \"column\": \"event_id\", \"extendCondition\": [{\"value\": \"99\", \"column\": \"police_kind\"}]}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
