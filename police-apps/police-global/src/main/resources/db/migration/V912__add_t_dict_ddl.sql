DELIMITER $$
DROP PROCEDURE IF EXISTS `insert_dict_data` $$
CREATE PROCEDURE insert_dict_data()
BEGIN
    DECLARE pid INT;
    DECLARE dsjid INT;
		DELETE FROM t_dict WHERE type like 'writing_log_type%';
    INSERT INTO t_dict (`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        ('writing_log_type_group',0,'写日志类型',0,'writingLogType',0,NULL,NULL,NULL,1);
		SET pid = LAST_INSERT_ID();
		UPDATE t_dict SET p_id = pid WHERE type = 'writing_log_type_group' and code = 0;

		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'writing_log_type',1,'值班人员',0,'dutyPerson',1,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'writing_log_type',2,'工作情况',0,'workCondition',2,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'writing_log_type',3,'值班要、舆情',0,'dutyYyq',3,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'writing_log_type',4,'值班收发文',0,'dutyDispatchDoc',4,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'writing_log_type',5,'短信',0,'shortMessage',5,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'writing_log_type',6,'指令',0,'instruction',6,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'writing_log_type',7,'接交班登记表',0,'shiftSignForm',7,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'writing_log_type',8,'大事记',0,'bigEventRecord',8,NULL,null,NULL,1);
		SET dsjid = LAST_INSERT_ID();
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'writing_log_type',9,'交班物品',0,'shiftItems',9,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (dsjid,'writing_log_type',10,'领导批示',8,'leaderApproval',1,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (dsjid,'writing_log_type',11,'其他内容',8,'otherContent',2,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (dsjid,'writing_log_type',12,'查询',8,'search',3,NULL,null,NULL,1);
END $$
DELIMITER ;
CALL insert_dict_data;
DROP PROCEDURE IF EXISTS insert_dict_data;