UPDATE t_profile_module
SET table_schema = '{"name": "事件信息", "type": "TABLE_SCHEMA", "table": "t_profile_event", "fields": [{"db": {"table": "t_profile_event", "column": "name", "jdbcType": "string"}, "name": "name", "tableSchema": {"span": 2, "type": "string", "title": "事件名称", "copyable": false}}, {"db": {"table": "t_profile_event", "column": "event_label", "mapping": "label_id_array_to_name", "jdbcType": "json_id_array"}, "name": "event_label", "tableSchema": {"span": 1, "type": "label", "title": "事件类别", "copyable": false}}, {"db": {"table": "t_profile_event", "column": "source", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "profile_event_source"}, "name": "source", "tableSchema": {"span": 1, "type": "string", "title": "事件来源", "copyable": false}}, {"db": {"table": "t_profile_event", "column": "related_time", "mapping": "date_time_to_general_string", "jdbcType": "timestamp"}, "name": "related_time", "tableSchema": {"span": 1, "type": "string", "title": "指向时间", "copyable": false}}, {"db": {"table": "t_profile_event", "column": "related_address", "mapping": "map_location", "jdbcType": "string"}, "name": "related_address", "tableSchema": {"span": 2, "type": "mapLocation", "title": "指向地址", "copyable": false}}, {"db": {"table": "t_profile_event", "column": "level", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "profile_event_level"}, "name": "level", "tableSchema": {"span": 1, "type": "string", "title": "事件级别", "copyable": false}}, {"db": {"table": "t_profile_event", "column": "belong_location", "mapping": "district_code_to_name", "jdbcType": "string"}, "name": "belong_location", "tableSchema": {"span": 1, "type": "string", "title": "归属地", "copyable": false}}, {"db": {"table": "t_profile_event", "column": "person_estimation", "jdbcType": "number"}, "name": "person_estimation", "tableSchema": {"span": 1, "type": "string", "title": "估计人数", "copyable": false}}, {"db": {"table": "t_profile_event", "column": "risk_level", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "profile_event_risk_level"}, "name": "risk_level", "tableSchema": {"span": 1, "type": "string", "title": "风险等级", "copyable": false}}, {"db": {"table": "t_profile_event", "column": "control_station", "mapping": "dept_code_to_dept_name", "jdbcType": "number"}, "name": "control_station", "tableSchema": {"span": 1, "type": "string", "title": "主管单位", "copyable": false}}, {"db": {"table": "t_profile_event", "column": "control_police", "mapping": "dept_code_to_dept_name", "jdbcType": "string"}, "name": "control_police", "tree": {"type": "dept"}, "tableSchema": {"span": 1, "type": "string", "title": "主责警种", "copyable": false}}, {"db": {"table": "t_profile_event", "column": "event_happened", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "profile_event_happened"}, "name": "event_happened", "tableSchema": {"span": 1, "type": "string", "title": "是否发生", "copyable": false}}, {"db": {"table": "t_profile_event", "column": "detail", "jdbcType": "string"}, "name": "detail", "tableSchema": {"span": 2, "type": "string", "title": "事件详情", "copyable": false}}, {"db": {"table": "t_profile_event", "column": "disposal_result", "jdbcType": "string"}, "name": "disposal_result", "tableSchema": {"span": 2, "type": "string", "title": "处置结果", "copyable": false}}, {"db": {"table": "t_profile_event", "column": "description", "jdbcType": "string"}, "name": "description", "tableSchema": {"span": 2, "type": "string", "title": "简要说明", "copyable": false}}], "moduleUi": {"column": 2, "bordered": true}}',
form_schema='{"name": "事件信息", "type": "FORM_SCHEMA", "table": "t_profile_event", "fields": [{"db": {"table": "t_profile_event", "column": "name", "jdbcType": "string"}, "name": "name", "formSchema": {"ui": {"ui:options": {"width": "1", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "事件名称"}}}, {"db": {"table": "t_profile_event", "column": "event_label", "mapping": "label_id_array_to_name", "jdbcType": "json_id_array"}, "name": "event_label", "tree": {"root": "event", "type": "label"}, "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "cascader", "multiple": true, "fieldNames": {"label": "name", "value": "id", "children": "children"}, "titleLocation": "left"}}, "schema": {"type": "array", "items": {"type": "array", "items": {"type": "number"}, "minItems": 1}, "title": "事件类别", "minItems": 1}}}, {"db": {"table": "t_profile_event", "column": "source", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "profile_event_source"}, "name": "source", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "select", "titleLocation": "left"}}, "schema": {"type": "number", "title": "事件来源"}}}, {"db": {"table": "t_profile_event", "column": "related_time", "mapping": "date_time_to_timestamp", "jdbcType": "timestamp"}, "name": "related_time", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "datePicker", "showTime": true, "titleLocation": "left"}}, "schema": {"type": "number", "title": "维权开始时间"}}}, {"db": {"table": "t_profile_event", "column": "related_end_time", "mapping": "date_time_to_timestamp", "jdbcType": "timestamp"}, "name": "related_end_time", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "datePicker", "showTime": true, "titleLocation": "left"}}, "schema": {"type": "number", "title": "维权结束时间"}}}, {"db": {"table": "t_profile_event", "column": "level", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "profile_event_level"}, "name": "level", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "radio", "titleLocation": "left"}}, "schema": {"type": "number", "title": "事件级别"}}}, {"db": {"table": "t_profile_event", "column": "related_address", "jdbcType": "map"}, "name": "related_address", "formSchema": {"ui": {"ui:options": {"width": "1", "widget": "map", "titleLocation": "left"}}, "schema": {"type": "string", "title": "维权地址"}}}, {"db": {"table": "t_profile_event", "column": "belong_location", "mapping": "district_code_to_name", "jdbcType": "string"}, "name": "belong_location", "tree": {"root": "510600", "type": "district"}, "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "cascader", "multiple": false, "fieldNames": {"label": "name", "value": "code", "children": "children"}, "titleLocation": "left"}}, "schema": {"type": "string", "title": "归属地"}}}, {"db": {"table": "t_profile_event", "column": "person_estimation", "jdbcType": "number"}, "name": "person_estimation", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "number", "title": "估计人数"}}}, {"db": {"table": "t_profile_event", "column": "risk_level", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "profile_event_risk_level"}, "name": "risk_level", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "radio", "titleLocation": "left"}}, "schema": {"type": "number", "title": "风险等级"}}}, {"db": {"table": "t_profile_event", "column": "control_station", "mapping": "dept_code_to_dept_name", "jdbcType": "number"}, "name": "control_station", "tree": {"root": "510600000000", "type": "dept"}, "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "cascader", "multiple": false, "fieldNames": {"label": "shortName", "value": "deptCode", "children": "children"}, "titleLocation": "left", "notOnlyChildren": true}}, "schema": {"type": "string", "title": "主管单位"}}}, {"db": {"table": "t_profile_event", "column": "control_police", "jdbcType": "string"}, "name": "controlPolice", "tree": {"root": "510500000000", "type": "dept"}, "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "cascader", "multiple": false, "fieldNames": {"label": "shortName", "value": "deptCode", "children": "children"}, "titleLocation": "left", "notOnlyChildren": true}}, "schema": {"type": "string", "title": "主责警种"}}}, {"db": {"table": "t_profile_event", "column": "detail", "jdbcType": "string"}, "name": "detail", "formSchema": {"ui": {"ui:options": {"width": "1", "widget": "textarea", "titleLocation": "left"}}, "schema": {"type": "string", "title": "事件详情"}}}, {"db": {"table": "t_profile_event", "column": "disposal_result", "jdbcType": "string"}, "name": "disposal_result", "formSchema": {"ui": {"ui:options": {"width": "1", "widget": "textarea", "titleLocation": "left"}}, "schema": {"type": "string", "title": "处置结果"}}}, {"db": {"table": "t_profile_event", "column": "description", "jdbcType": "string"}, "name": "description", "formSchema": {"ui": {"ui:options": {"width": "1", "widget": "textarea", "titleLocation": "left"}}, "schema": {"type": "string", "title": "简要说明"}}}], "required": ["name", "event_label", "source", "related_time"]}'
WHERE id=208;

UPDATE t_profile_module
SET list_schema = '{"name": "事件档案", "type": "LIST_SCHEMA", "table": "t_profile_event", "fields": [{"db": {"table": "t_profile_event", "column": "name", "jdbcType": "string"}, "name": "name", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "事件名称"}, "properties": {"href": "/ys-app/archives/event/details?id={value}", "isName": true, "copyable": false, "editable": false, "required": false, "sortable": false, "isRelatedShow": true}}}, {"db": {"table": "t_profile_event", "column": "risk_score", "mapping": "number_half_adjust_to_string", "jdbcType": "number"}, "name": "riskScore", "listSchema": {"style": {"align": "left", "width": 128}, "schema": {"type": "string", "title": "风险分值"}, "properties": {"color": "#FF6C6C", "copyable": false, "editable": false, "required": false, "sortable": true, "instrLength": 1}}}, {"db": {"table": "t_profile_event", "column": "event_label", "mapping": "label_id_array_to_name", "jdbcType": "label_id_array"}, "name": "eventLabel", "listSchema": {"style": {"align": "left"}, "filter": {"key": "eventLabel", "type": "multiple-tree", "value": ["&&event_label&&"], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "事件类别"}, "schema": {"type": "array", "title": "事件类别"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 2, "isRelatedShow": true}}}, {"db": {"table": "t_profile_event", "column": "create_dept_id", "mapping": "dept_id_to_dept_name", "jdbcType": "number"}, "name": "create_dept_id", "listSchema": {"style": {"align": "center", "width": 120}, "filter": {"key": "create_dept_id", "type": "tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "id", "children": "children"}, "displayName": "录入单位"}, "schema": {"type": "string", "title": "录入单位"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "isRelatedShow": true}}}, {"db": {"table": "t_profile_event", "column": "level", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "profile_event_level"}, "name": "level", "listSchema": {"style": {"align": "center"}, "filter": {"key": "level", "type": "select", "value": ["%%profile_event_level%%"], "fieldNames": {"label": "name", "value": "code"}, "displayName": "事件级别"}, "schema": {"type": "string", "title": "事件级别"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1, "isRelatedShow": true}}}, {"db": {"table": "t_profile_event", "column": "related_time", "mapping": "date_time_to_general_string", "jdbcType": "datetime"}, "name": "relatedTime", "listSchema": {"style": {"align": "center", "width": 120}, "filter": {"key": "relatedTime", "type": "timeParams", "value": [{"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "维权开始时间"}, "schema": {"type": "string", "title": "维权开始时间"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": true}}}, {"db": {"table": "t_profile_event", "column": "related_end_time", "mapping": "date_time_to_general_string", "jdbcType": "datetime"}, "name": "relatedEndTime", "listSchema": {"style": {"align": "center", "width": 120}, "filter": {"key": "relatedEndTime", "type": "timeParams", "value": [{"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "维权结束时间"}, "schema": {"type": "string", "title": "维权结束时间"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": true}}}, {"db": {"table": "t_profile_event", "column": "related_address", "mapping": "map_location_string", "jdbcType": "string"}, "name": "relatedAddress", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "维权地址"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_event", "column": "event_happened", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "profile_event_happened"}, "name": "event_happened", "listSchema": {"style": {"align": "center"}, "filter": {"key": "event_happened", "type": "option", "value": ["%%profile_event_happened%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "是否发生"}, "schema": {"type": "string", "title": "是否发生"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_profile_event", "column": "source", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "profile_event_source"}, "name": "source", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "事件来源"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_profile_event", "column": "status", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "profile_event_status"}, "name": "status", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "事件状态"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_profile_event", "column": "create_time", "mapping": "date_time_to_text", "jdbcType": "datetime"}, "name": "createTime", "listSchema": {"style": {"align": "center", "width": 120}, "schema": {"type": "string", "title": "录入时间"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": true, "sortDefault": "descending"}}}, {"db": {"table": "t_profile_event", "column": "control_station", "mapping": "dept_code_to_dept_name", "jdbcType": "number"}, "name": "control_station", "listSchema": {"style": {"align": "center", "width": 120}, "filter": {"key": "control_station", "type": "tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "deptCode", "children": "children"}, "displayName": "主管单位"}, "schema": {"type": "string", "title": "主管单位"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "isRelatedShow": true}}}, {"db": {"table": "t_profile_event", "column": "control_police", "mapping": "dept_code_to_dept_name", "jdbcType": "number"}, "name": "control_police", "listSchema": {"style": {"align": "center", "width": 120}, "filter": {"key": "control_police", "type": "tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "deptCode", "children": "children"}, "displayName": "主责警种"}, "schema": {"type": "string", "title": "主责警种"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "isRelatedShow": true}}}], "selectable": true, "searchFields": [{"key": "name", "name": "事件名称"}, {"key": "relatedAddress", "name": "事发地点"}]}'
WHERE id=201;