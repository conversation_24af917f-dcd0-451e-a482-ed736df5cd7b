DROP PROCEDURE IF EXISTS insert_dict;
CREATE PROCEDURE insert_dict()
BEGIN
    DECLARE pid INT;
    DELETE FROM t_dict  WHERE type LIKE 'sthy_jq_jjfs%';
    INSERT  INTO t_dict ( `type`, `code`, `name`, `show_number`) VALUES ('sthy_jq_jjfs_group', 0, '三台合一警情接警方式', 1);
    SET pid = LAST_INSERT_ID();
    INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `dict_desc`, `show_number`) VALUES (pid, 'sthy_jq_jjfs', 1, 'VCS自接','01', 1);
    INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `dict_desc`, `show_number`) VALUES (pid, 'sthy_jq_jjfs', 2, 'ICC接警','02', 2);
    INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `dict_desc`, `show_number`) VALUES (pid, 'sthy_jq_jjfs', 3, 'MPA接警','03', 3);
END;
CALL insert_dict();
Drop PROCEDURE if EXISTS insert_dict;