-- 增加短信记录表
CREATE TABLE IF NOT EXISTS `tb_common_duanxing_send` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `cr_time` datetime NOT NULL COMMENT '发送时间',
  `module_name` varchar(255) COLLATE utf8mb4_bin NOT NULL COMMENT '所属模块',
  `module_id` bigint NOT NULL COMMENT '短信模块ID',
  `phone` longtext COLLATE utf8mb4_bin NOT NULL COMMENT '目标手机号多个英文逗号分割',
  `message` longtext COLLATE utf8mb4_bin NOT NULL COMMENT '短信内容',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- 检查任务表
CREATE TABLE IF NOT EXISTS `tb_projects_crj_check_task` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `cr_time` datetime NOT NULL COMMENT '发送时间',
  `start_time` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '所属模块',
  `end_time` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '短信模块ID',
  `custom_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '目标手机号多个英文逗号分割',
  `message` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '短信内容',
  `status` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '状态 \nsuccess: 成功 \nerror：出错 \nwarn：警告',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

DELIMITER $$
DROP PROCEDURE IF EXISTS `add_data` $$
CREATE PROCEDURE add_data()
BEGIN
    IF NOT EXISTS(SELECT * FROM t_message_module WHERE en_name='projects_crj_rzxx')
    THEN
        INSERT INTO t_message_module (id,cn_name,en_name,table_name,key_name,extension_code,template)
        VALUES(5,'出入境入住信息核查','projects_crj_rzxx','tb_common_duanxing_send','id','05',NULL);
    END IF;
END $$
DELIMITER ;
CALL add_data;
DROP PROCEDURE IF EXISTS `add_data`;


