DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF
NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='tb_common_bigscreen_duty_users' AND column_name='xzg_user_ids')
    THEN
ALTER TABLE tb_common_bigscreen_duty_users
    ADD xzg_user_ids varchar(255) NULL COMMENT '协助室人员（格式：deptId-userId）';
END IF;
    IF
NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='tb_common_bigscreen_duty_users' AND column_name='fkg_user_ids')
    THEN
ALTER TABLE tb_common_bigscreen_duty_users
    ADD fkg_user_ids varchar(255) NULL COMMENT '风控岗人员（格式：deptId-userId）';
END IF;
    IF
NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='tb_common_bigscreen_duty_users' AND column_name='db_user_ids')
    THEN
ALTER TABLE tb_common_bigscreen_duty_users
    ADD db_user_ids varchar(255) NULL COMMENT '党办人员（格式：deptId-userId）';
END IF;
    IF
NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='tb_common_bigscreen_duty_users' AND column_name='zag_user_ids')
    THEN
ALTER TABLE tb_common_bigscreen_duty_users
    ADD zag_user_ids varchar(255) NULL COMMENT '治安岗人员（格式：deptId-userId）';
END IF;
    IF
NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='tb_common_bigscreen_duty_users' AND column_name='xing_zhen_user_ids')
    THEN
ALTER TABLE tb_common_bigscreen_duty_users
    ADD xing_zhen_user_ids varchar(255) NULL COMMENT '刑侦岗人员（格式：deptId-userId）';
END IF;
    IF
NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='tb_common_bigscreen_duty_users' AND column_name='wag_user_ids')
    THEN
ALTER TABLE tb_common_bigscreen_duty_users
    ADD wag_user_ids varchar(255) NULL COMMENT '网安岗人员（格式：deptId-userId）';
END IF;
    IF
NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='tb_common_bigscreen_duty_users' AND column_name='jzg_user_ids')
    THEN
ALTER TABLE tb_common_bigscreen_duty_users
    ADD jzg_user_ids varchar(255) NULL COMMENT '技侦岗人员（格式：deptId-userId）';
END IF;
    IF
NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='tb_common_bigscreen_duty_users' AND column_name='zbg_user_ids')
    THEN
ALTER TABLE tb_common_bigscreen_duty_users
    ADD zbg_user_ids varchar(255) NULL COMMENT '政保岗人员（格式：deptId-userId）';
END IF;
    IF
NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='tb_common_bigscreen_duty_users' AND column_name='xcg_user_ids')
    THEN
ALTER TABLE tb_common_bigscreen_duty_users
    ADD xcg_user_ids varchar(255) NULL COMMENT '宣传岗人员（格式：deptId-userId）';
END IF;
    IF
NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='tb_common_bigscreen_duty_users' AND column_name='jing_zhen_user_ids')
    THEN
ALTER TABLE tb_common_bigscreen_duty_users
    ADD jing_zhen_user_ids varchar(255) NULL COMMENT '经侦岗人员（格式：deptId-userId）';
END IF;
    IF
NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='tb_common_bigscreen_duty_users' AND column_name='kxg_user_ids')
    THEN
ALTER TABLE tb_common_bigscreen_duty_users
    ADD kxg_user_ids varchar(255) NULL COMMENT '科信岗人员（格式：deptId-userId）';
END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;