DELIMITER $$
DROP PROCEDURE IF EXISTS `add_index` $$
CREATE PROCEDURE add_index()
BEGIN
IF NOT EXISTS( SELECT * FROM  information_schema.INNODB_INDEXES  WHERE TABLE_ID=(select TABLE_ID from  information_schema.INNODB_TABLES where name=REPLACE(concat((select database()),'/','tb_projects_sync_task'),'-','@002d')) and name ='tb_projects_sync_task_task_target_name_IDX' )
    THEN
CREATE INDEX tb_projects_sync_task_task_target_name_IDX USING BTREE ON tb_projects_sync_task (task_target_name,task_target_key);
END IF;
END $$
DELIMITER ;
CALL add_index;
DROP PROCEDURE IF EXISTS `add_index`;