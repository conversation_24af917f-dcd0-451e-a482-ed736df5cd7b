CREATE TABLE IF NOT EXISTS `tb_search_monitor_result` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `record_id` varchar(255) NOT NULL COMMENT '记录ID(数据表英文名)',
  `of_date` datetime NOT NULL COMMENT '分析时间（日期）',
  `cr_time` datetime NOT NULL COMMENT '分析时间（创建时间）',
  `update_time` datetime NOT NULL COMMENT '分析时间（更新时间）',
  `is_del` int NOT NULL DEFAULT '0' COMMENT '是否被删除（0：否，1：是）',
  `data_status` int NOT NULL DEFAULT '0' COMMENT '数据状态，\n0：正常\n-1：异常\n1：断更',
  `data_total_count` bigint NOT NULL DEFAULT '0' COMMENT '总量',
  `yesterday_increment` bigint NOT NULL DEFAULT '0' COMMENT '昨日增量',
  `yesterday_increment_new` bigint NOT NULL DEFAULT '0' COMMENT '新的昨日增量',
  `reason` longtext NULL COMMENT '异常原因',
  PRIMARY KEY (`id`),
  KEY `tb_search_monitor_result_record_id_IDX` (`record_id`,`of_date`) USING BTREE
) ENGINE=InnoDB COMMENT='检索监控结果表';