DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF EXISTS( SELECT * FROM information_schema.columns WHERE table_schema=(select database()) AND table_name='t_profile_group_risk_person_relation' AND column_name='role_name')
    THEN
ALTER TABLE t_profile_group_risk_person_relation MODIFY COLUMN role_name int NULL COMMENT '身份';
END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;