update t_request_params set params = '{"filterParams": [{"key": "doneStatus", "type": "option", "value": [{"id": "notDone", "name": "处置中", "default": 0}, {"id": "done", "name": "已完结", "default": 0}], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "预警状态"}, {"key": "handle", "type": "option", "value": [{"id": "true", "name": "已处置", "default": 0}, {"id": "false", "name": "不处置", "default": 0}], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "处置结果"}, {"key": "overdueStatus", "type": "option", "value": [{"id": "overdue", "name": "已逾期", "default": 0}, {"id": "notOverdue", "name": "未逾期", "default": 0}], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "逾期状态"}, {"key": "warningLevel", "type": "option", "value": ["%%monitor_level%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "预警级别"}, {"key": "warningTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "预警时间"}, {"key": "activityTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "活动时间"}, {"key": "monitorType", "type": "option", "value": [{"id": 438, "pid": 437, "code": 1, "name": "人员", "children": []}, {"id": 439, "pid": 437, "code": 2, "name": "群体", "children": []}, {"id": 440, "pid": 437, "code": 3, "name": "区域", "children": []}], "linkedKey": ["warningModel"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "预警对象"}, {"key": "warningStatus", "type": "select", "value": ["%%control_warning_status%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "预警状态"}, {"key": "personLabel", "type": "multiple-tree", "value": ["&&person_label&&"], "enableInfo": {"key": "monitorType", "value": 1}, "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "人员标签"}, {"key": "groupType", "type": "multiple-tree", "value": ["&&group_label&&"], "enableInfo": {"key": "monitorType", "value": 2}, "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "群体类别"}, {"key": "areaDistrictName", "type": "multiple-tree", "value": ["&&district&&"], "enableInfo": {"key": "monitorType", "value": 3}, "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "行政区划"}, {"key": "controlType", "type": "option", "value": [{"id": "1", "name": "布控", "default": 0}, {"id": "2", "name": "常控", "default": 0}], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "管控类型"}, {"key": "warningModel", "url": "/control/monitor/warning-model/tree", "type": "multiple-tree", "value": [], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "预警模型"}, {"key": "source", "type": "tree", "value": ["%%control_warning_source_type%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "感知源"}, {"key": "notifyTarget", "type": "multiple-tree", "value": ["&&dept_all&&"], "fieldNames": {"label": "shortName", "value": "id", "children": "children"}, "displayName": "通知对象"}], "searchFields": [{"key": "targetName", "name": "姓名"}, {"key": "idNumber", "name": "证件号码"}, {"key": "address", "name": "活动地点"}]}'
where module = 'warning-list-all';