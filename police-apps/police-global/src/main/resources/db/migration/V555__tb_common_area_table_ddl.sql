DROP TABLE IF EXISTS tb_common_area_table;
CREATE TABLE `tb_common_area_table` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cr_time` datetime DEFAULT NULL,
  `cr_user` varchar(255) DEFAULT NULL,
  `area_alias` varchar(255) NOT NULL,
  `parent_area_name` varchar(255) DEFAULT NULL,
  `parent_area_code` varchar(20) DEFAULT NULL,
  `area_code` varchar(20) NOT NULL,
  `area_short_code` varchar(20) NOT NULL,
  `area_level` varchar(20) NOT NULL,
  `area_name` varchar(255) NOT NULL,
  `default_area_flag` int(2) DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB;