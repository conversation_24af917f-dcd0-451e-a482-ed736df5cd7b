CREATE TABLE IF NOT EXISTS `t_statistics_warning_model`  (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模型名字',
	`model_desc` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模型简介',
	`application_scene` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '应用场景，多个用逗号分隔',
	`data_source` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '数据来源，多个用逗号分隔',
	`type` TINYINT(3) NOT NULL DEFAULT '1' COMMENT '模型类型，1：云哨',
	`third_model_id` int(11) NOT NULL DEFAULT '0' COMMENT '第三方模型id',
	`status` TINYINT(3) NOT NULL DEFAULT '0' COMMENT '状态，0：正常，1：删除',
	`create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
	`update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
	PRIMARY KEY (`id`) USING BTREE
	) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `t_model_daily_statistics`  (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`model_id` int(11) NOT NULL COMMENT '模型id',
	`statistic_date` varchar(30) DEFAULT NULL COMMENT '统计时间',
	`achievement_count` int(11) NOT NULL DEFAULT '0' COMMENT '成果数量',
	`create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
	`update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
	PRIMARY KEY (`id`) USING BTREE
	) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;