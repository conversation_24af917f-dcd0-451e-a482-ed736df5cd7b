CREATE TABLE IF NOT exists `t_srsq` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '名字',
  `time_range` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '统计时间范围',
  `start_time` datetime DEFAULT NULL COMMENT '统计开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '统计结束时间',
  `download_url` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '下载地址',
  `file_id` bigint DEFAULT NULL COMMENT '附件id',
  `oss_info` text COLLATE utf8mb4_bin COMMENT '对象存储上传信息',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB;