DELIMITER $$
DROP PROCEDURE IF EXISTS `add_index` $$
CREATE PROCEDURE add_index()
BEGIN
    IF NOT EXISTS( SELECT * FROM  information_schema.statistics WHERE table_schema=(select database()) AND table_name='tb_jjwfjl_wfjsb' AND index_name='CFJGBH_IDX')
    THEN
ALTER TABLE `tb_jjwfjl_wfjsb` ADD INDEX `CFJGBH_IDX`(`cfjgbh`) USING BTREE COMMENT '处罚结果编号普通索引';
END IF;
END $$
DELIMITER ;
CALL add_index;
DROP PROCEDURE IF EXISTS `add_index`;