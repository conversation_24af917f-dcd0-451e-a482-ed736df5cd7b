UPDATE t_profile_module set list_schema = '{
  "name": "反馈信息",
  "type": "LIST_SCHEMA",
  "table": "t_profile_sthy_fkxx",
  "fields": [
    {
      "db": {
        "table": "t_profile_sthy_fkxx",
        "column": "fkdbh",
        "jdbcType": "string"
      },
      "name": "fkdbh",
      "listSchema": {
        "style": {
          "align": "center",
          "fixed": "left",
          "ellipsis": true
        },
        "schema": {
          "type": "string",
          "title": "反馈编号"
        },
        "properties": {
          "copyable": false,
          "editable": false,
          "required": false,
          "sortable": false
        }
      }
    },
    {
      "db": {
        "table": "t_profile_sthy_fkxx",
        "column": "cjczqk",
        "jdbcType": "string"
      },
      "name": "cjczqk",
      "listSchema": {
        "style": {
          "align": "center",
          "fixed": "left",
          "ellipsis": true
        },
        "schema": {
          "type": "string",
          "title": "出警处置情况"
        },
        "properties": {
          "copyable": false,
          "editable": false,
          "required": false,
          "sortable": false
        }
      }
    },
    {
      "db": {
        "table": "t_profile_sthy_fkxx",
        "column": "jqcljgdm",
        "mapping": "dict_code_to_name",
        "jdbcType": "jwzh"
      },
      "dict": {
        "type": "sthy_jq_cljg"
      },
      "name": "jqcljgdm",
      "listSchema": {
        "style": {
          "align": "center",
          "fixed": "left",
          "ellipsis": true
        },
        "schema": {
          "type": "string",
          "title": "处理结果"
        },
        "properties": {
          "copyable": false,
          "editable": false,
          "required": false,
          "sortable": false
        }
      }
    },
    {
      "db": {
        "table": "t_profile_sthy_fkxx",
        "column": "jqcljgsm",
        "jdbcType": "string"
      },
      "name": "jqcljgsm",
      "listSchema": {
        "style": {
          "align": "center",
          "fixed": "left",
          "ellipsis": true
        },
        "schema": {
          "type": "string",
          "title": "说明"
        },
        "properties": {
          "copyable": false,
          "editable": false,
          "required": false,
          "sortable": false
        }
      }
    },
    {
      "db": {
        "table": "t_profile_sthy_fkxx",
        "column": "jqbwdm",
        "mapping": "dict_code_to_name",
        "jdbcType": "string"
      },
      "dict": {
        "type": "fsbwdm"
      },
      "name": "jqbwdm",
      "listSchema": {
        "style": {
          "align": "center",
          "fixed": "left",
          "ellipsis": true
        },
        "schema": {
          "type": "string",
          "title": "发生部位"
        },
        "properties": {
          "copyable": false,
          "editable": false,
          "required": false,
          "sortable": false
        }
      }
    },
    {
      "db": {
        "table": "t_profile_sthy_fkxx",
        "column": "fkyxm",
        "jdbcType": "string"
      },
      "name": "fkyxm",
      "listSchema": {
        "style": {
          "align": "center",
          "fixed": "left",
          "ellipsis": true
        },
        "schema": {
          "type": "string",
          "title": "反馈人"
        },
        "properties": {
          "copyable": false,
          "editable": false,
          "required": false,
          "sortable": false
        }
      }
    },
    {
      "db": {
        "table": "t_profile_sthy_fkxx",
        "column": "fkdwmc",
        "jdbcType": "string"
      },
      "name": "fkdwmc",
      "listSchema": {
        "style": {
          "align": "center",
          "fixed": "left",
          "ellipsis": true
        },
        "schema": {
          "type": "string",
          "title": "反馈单位"
        },
        "properties": {
          "copyable": false,
          "editable": false,
          "required": false,
          "sortable": false
        }
      }
    }
  ]
}' where id = 504;