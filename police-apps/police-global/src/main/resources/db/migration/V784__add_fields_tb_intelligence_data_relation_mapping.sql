-- 关联表增加obj_dept_id,用于记录关联的单位跟用户所属部门ID，方便后续新全部列表查询
DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS(
       SELECT * FROM  information_schema.columns
       WHERE table_schema=(select database()) AND table_name='tb_intelligence_data_relation_mapping' AND column_name='obj_dept_id'
    )
    THEN
    ALTER TABLE tb_intelligence_data_relation_mapping ADD obj_dept_id BIGINT(20) NULL COMMENT '记录关联的单位跟用户所属部门ID';
    END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;

-- 更新历史数据
UPDATE tb_intelligence_data_relation_mapping SET obj_dept_id=obj_id WHERE obj_type ='dept' AND obj_dept_id IS  NULL;
