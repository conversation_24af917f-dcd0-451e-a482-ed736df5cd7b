DELIMITER $$
DROP PROCEDURE IF EXISTS `insert_dict_data` $$
CREATE PROCEDURE insert_dict_data()
BEGIN
    DECLARE pid INT;
		DELETE FROM t_dict WHERE type like 'risk_dispatch_type%';
    INSERT INTO t_dict (`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        ('risk_dispatch_type_group',0,'风险防控调度类型',0,'otherContentType',0,NULL,NULL,NULL,1);
		SET pid = LAST_INSERT_ID();
		UPDATE t_dict SET p_id = pid WHERE type = 'risk_dispatch_type_group' and code = 0;

		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'risk_dispatch_type',1,'指向近日突出风险',0,'zxjrtcfx',1,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'risk_dispatch_type',2,'网安通报突出风险',0,'watbtcfx',2,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'risk_dispatch_type',3,'技侦通报突出风险',0,'jztbtcfx',3,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'risk_dispatch_type',4,'公安部、区域办通报涉川情况',0,'scqk',4,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'risk_dispatch_type',5,'昨日部“情指行”落实情况',0,'lsqk',5,NULL,null,NULL,1);
END $$
DELIMITER ;
CALL insert_dict_data;
DROP PROCEDURE IF EXISTS insert_dict_data;