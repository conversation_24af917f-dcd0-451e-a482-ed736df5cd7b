# 管控

## 18.3
- XMKFB-8804 合 -【高新】- FK专题-人员档案详情页-异常行为和原型不符
- XMKFB-8923[XMKFB-8885] 高新K平台-预警列表，对于已建档人员需要返回建档ID
- XMKFB-8882 【高新】K平台，K预警人员的性别显示有误
- XMKFB-8886 【高新】K平台，未建档人员档案详情页没有显示出人员照片
- XMKFB-8900 【高新】K平台，相同研判记录数据重复显示多次
- XMKFB-8878 【高新】K平台，预警列表按照相关警情筛选一直处于loading状态
- XMKFB-8796 后-【自贡】- 后端schame配置支持
- XMKFB-8713【自贡】【预警】短信提醒格式调整
- XMKFB-8762【自贡】【感知源库】在筛选条件中增加有无经纬度筛选。
- XMKFB-8771【自贡】【临控中心】群体布控、区域布控实现

## RC20250630
- XMKFB-9010【高新】高新云控禁用的常控预警模型依然在预警问题处理

## RC20250618
- XMKFB-8713 【自贡】【预警】短信提醒格式调整
- XMKFB-8903 【高新】云控，发起区域布控报错

## RC20250617
- XMKFB-8837[XMKFB-8827] 后-【高新】- FK专题历史数据处理

## 18.2
- XMKFB-8866【省厅情指】指挥大屏，天网设备列表补充支持按照设备名称和编号进行搜索
- XMKFB-8786[XMKFB-8775] 后-【省厅情指】- 完成指挥大屏-天网的相关优化
- XMKFB-8630 广安-启动预案功能报错

## 18.1
- XMKFB-8590 布控（临控）中心，重新提交群体布控以后没有发起审批

## RC20250613
- XMKFB-8824【自贡】【预警】已撤销人员还在产生预警

## RC20250530
- XMKFB-8405[XMKFB-8461] 后-【自贡】- 新增 市内预警 模型

## 17.4
- XMKFB-8384[XMKFB-8378] 后-【广安】- 指挥大屏-人员预警撒点慢问题排查
- XMKFB-8492[XMKFB-7620] 后-【高新】- 群体聚集预警

### 配置项
目前只需要在高新添加
commons-kafka.yaml
```properties
kafka.warning.person.esSaveGroupId= gxys_es
kafka.warning.fkrxyj.esSaveGroupId= gxys_es
kafka.warning.fkrxyj.mgqyGroupId= gxys_mgqy
kafka.warning.fkrxyj.jjGroupId= gxys_jj
kafka.warning.fkrxyj.maxJjPollRecords= 50
```
control.yaml
```properties
control.fkry.yp.valid.time=30
control.fkry.import.file.id=-1
control.fkry.source.name.strx=strx
control.fkry.source.code.strx=5
control.fkry.label.root.id=89
control.fkry.label.ten.id=40
control.fkry.zrpcs.id=7
control.fkry.jj.count.person=3
control.fkry.jj.count.group=3
control.fkry.jj.time=5
control.fkry.tgph.control.level=1
```

## RC20250530
- XMKFB-8405[XMKFB-8461] 后-【自贡】- 新增 市内预警 模型
``` 在nacos control.yaml 添加配置
com:
  trs:
    warningModalConfig:
      activeDependencyModel: true
```

## RC
- 
``` 在nacos control.yaml 添加配置
com:
  trs:
    kafka:
      consumer:
        warningPersonType: 'refactoredMethod' 
    warningModalConfig:
      activeLeaveDependencyModel: true
```

## 17.3
- XMKFB-8419[XMKFB-7620] 后-【高新】- 人员预警相关接口提供
- XMKFB-8379 布控(临控)中心，布控人员数量过多以后导出的表格内容为空

# 17.2
- XMKFB-8374: 【自贡】预警中心，当不选择处置措施时执行处置完结操作会报错
- XMKFB-8340: 后-【省厅情指】- 天网接口对接

# 17.1
- XMKFB-7797 【广安】线索布控，预警详情页中的预警对象和预警撒点数据为空
- XMKFB-8257 【自贡】【预警指令】预警列表的布控级别无数据显示

# 16.5
- XMKFB-8029 【自贡】【重点区域库】省厅同步感知源设备屏蔽
- XMKFB-8163[XMKFB-8142] 预警反馈优化
- XMKFB-7488【自贡】【感知源库】实时同步视觉计算平台设备
- XMKFB-8154【自贡】【数据碰撞】【验收】系统错误处理

# 16.4
- XMKFB-7915 后-【广安】- 群体聚集预警实现
```在 nacos control.yaml 下添加配置
com:
  trs:
    gather:
      warning:
        enabled: true
        cron: 0 0 0 * * ?

```
- XMKFB-7935 合-【自贡】-预警处置完结功能改进
- XMKFB-7879[XMKFB-7814] 后-【广安】- 添加进京、赴省、静默 预警
- XMKFB-8009 自贡-后台配置-模型配置中增加删除按钮

# 16.3
- XMKFB-7797 【广安】线索布控，预警详情页中的预警对象和预警撒点数据为空

# 16.1
- XMKFB-7299【广安】线索布控，预警详情页报错问题

# 15.4
- XMKFB-7481 后-【广安】- 群体布控详情接口慢问题排查
- XMKFB-7441 【广安】布控预警统计，统计的临控人数有误
- XMKFB-7458 【广安】布控预警统计，全市统计的预警趋势和预警占比数量有误
- XMKFB-7430 合-【广安】- 布控预警统计优化
- XMKFB-7445 布控审批异常

# 15.3
-XMKFB-7353[XMKFB-7304] 后-线索布控返回布控时间hoursToDeadLine字段
- XMKFB-7366 【自贡】预警签收数据更新逻辑优化
- XMKFB-7376 后-【广安】- 对于失效的布控/常控/无感布控需要调用moye接口取消布控
```在 nacos commons-moye.yaml 修改配置, 原有的配置是错误的接口名称 错误的 cancelComparsion
warning:
    # 撤控
    cancelUrl: http://police-comparison-api-svc:10011/comparison-api/controlCenter/cancelComparison

```

# 15.2
- XMKFB-7114 管控中心，清空重点区域上图信息无效
- 后-【自贡】- 管控模块-我的布控和全部布控列表检索慢
- 【广安】线索布控，线索布控失效以后布控状态仍显示为了布控中
- XMKFB-7231 后-【广安】- 布控的人也支持信令预警

# 15.1
- XMKFB-7117 管控中心，重点区域上图列表中的数据未限制状态
- XMKFB-7119 【省厅情指】管控—布控任务，在布控任务详情里下发人员失败
- XMKFB-7131 后-【自贡】- 常控中心人员核查

# 14.4
- XMKFB-6962 后 -【广安】- 布控/预警检索相关接口提供
- XMKFB-6924 - 广安GA-线索功能新增人员无感布控、查看轨迹

# 14.3
- XMKFB-6556 云哨-管控中心-新增重点区域上图功能
- XMKFB-6813 【省厅情指】布控任务，已布控或已下发的人员需屏蔽退回按钮  
- XMKFB-6838 - 【省厅情指】管控—预警信息，部下发比中轨迹列表中的活动地点区划下拉框数据需作调整
- XMKFB-6825 【高新】管控—预警信息，签收信令预警数据报错
- XMKFB-6840 【省厅情指】管控—预警中心，部下发比中轨迹列表按照活动地点搜索出的结果有误
- XMKFB-6864 后-【泸州】- 感知引擎2.0对接，布控信息优化

## RC20250219
- XMKFB-6872 【高新】管控—常控中心，所有常控人员详情页里均都没有修改常控信息按钮

# 14.2
- XMKFB-6691 【省厅协作】系统设置，跨部门审核模板里显示的适用此模板的市州数据未作区分
- XMKFB-6725[XMKFB-6707] 后端在接口返回值里面添加联系方式字段
- XMKFB-6672[XMKFB-6584] 后-【省厅情指】- 布控任务列表检索优化
- XMKFB-6669[XMKFB-6584] 后-【省厅情指】- 布控详情页优化
- XMKFB-6620 后-【自贡】- 预警详情页人像预警相似度未显示

## 14.1 
- XMKFB-6451 后-【省厅情指】- 接收云控的布控指令并推送给云控平台
- XMKFB-6563 [XMKFB-6436] 后-【省厅情指】- 比中轨迹优化
- XMKFB-6663 [XMKFB-6581] 后端排查childrenSignStatus和issueStatus状态变化是否有误
- XMKFB-6624 【省厅情指】预警中心，反馈预警成功后未显示出预警反馈数据
- XMKFB-6570 后-【省厅情指】- 部在川布控/上报比中轨迹接口提供
- XMKFB-6577 【省厅情指】布控任务，区县分局下发人员失败
- XMKFB-6574 【省厅情指】布控任务，一键下发人员逻辑有误
- XMKFB-6566 [XMKFB-6436] 后-【省厅情指】- 预警列表调整
- XMKFB-6583 【省厅情指】布控任务，已下发且已签收的任务人员可再次修改布控单位重新下发
- XMKFB-6579 【省厅情指】布控任务，区县账号下发人员后无法再查看已下发的人员数据

```yaml
# control.yaml
# 省厅
com:
  trs:
    warning:
      param:
        impl: batch
```

## 13.4
- XMKFB-6451 后-【省厅情指】- 接收云控的布控指令并推送轨迹到云控平台
- XMKFB-6452[XMKFB-6436] 后-【省厅情指】- 增加云控布控指令的关注预警及消费逻辑
- XMKFB-6534 【省厅情指】布控，在任务详情里下发任务失败后未在页面中给出提示

## rc 20250121
- XMKFB-6495 后-【省厅情指】- 布控中心-发起任务问题及优化
- XMKFB-6496 后-【省厅情指】- 布控中心-发起任务-创建任务，导入优化

## rc 20250120
- XMKFB-6498 后-【省厅情指】- 管控中心问题
- XMKFB-6476 [XMKFB-6475] 后端添加相关字段
- XMKFB-6487 【省厅情指】布控—发起任务、接收任务，任务详情页里显示的创建时间格式有误
- XMKFB-6504 后-【省厅情指】- 预警详情页显示字段优化

## 13.3
- XMKFB-6379 后-【省厅情指】- 布控审批流程需按照原型中流程图提供

## RC 20250117
- XMKFB-6477 自贡-管控中心需求调整

## RC 20250116
- XMKFB-6439 后-【省厅情指】- 云控预警指令、签收反馈后部里未收到

## RC 20240115
- XMKFB-6415 后-【省厅情指】- 预警中心-预警详情
- XMKFB-6427[XMKFB-6410] 后端调整预警详情页预警处置部分反馈内容contentJSON字段

## RC 20240114
- XMKFB-6298 后-【泸州】- 云墙人员布控问题处理

## v13.3
- XMKFB-6219 省厅GA-四川公安指挥大屏左侧统计模块替换为布控监测模块

## rc20250110
- XMKFB-6347 后-【省厅情指】- 预警中心-预警列表，点击签收后 ，点击反馈跳转预警详情页面报系统错误

## v13.2
- XMKFB-6184 [XMKFB-5969] 后-【省厅情指】- 比中轨迹消费/接口提供
- XMKFB-6243[XMKFB-5867] 后-【省厅情指】- 完成预警签收/反馈相关改造
- XMKFB-6183[XMKFB-5969] 后-【省厅情指】- 布控/预警列表/预警详情 改造

```yaml
# commons-kafka.yaml
kafka:
  warning:
    bz:
      topic: hit-track-data
      autoStartup: true
      pollDuration: 1
      maxPollRecords: 500
      bootStrapServers: ""
      groupId: hit_track_data_ys_group
      autoOffsetReset: earliest
# control.yaml
# 省厅
com:
  trs:
    warning:
      level:
        display:
          displayMap: {1: 一级,2: 二级,3: 二级,4: 三级}
          ysNameAndCloudNameMap: 红:一级,橙:二级,黄:二级,蓝:三级
# 除省厅外其他环境
com:
  trs:
    warning:
      level:
        display:
          displayMap: {1: 红,2: 橙,3: 黄,4: 蓝}
          ysNameAndCloudNameMap: 红:红,橙:橙,黄:黄,蓝:蓝
```

## v13.1
- XMKFB-5941 [XMKFB-5867] 后-【省厅情指】- 布控任务/详情人员列表/日志接口提供
- XMKFB-6044 后-【广安】- 对接信令预警  
- XMKFB-5997 点击感知源库细览页，报感知源坐标不合法  
- XMKFB-6148 布控中心预警模型分类调整
- XMKFB-6061 合 -【自贡】- 指挥大屏优化
- - XMKFB-5940[XMKFB-5867] 后-【省厅情指】- 创建布控任务接口提供



## v12.4
- XMKFB-5688 预警列表检索慢问题排查
### 配置文件新增

```yaml
# commons-kafka.yaml
kafka:
  warning:
    eswarning:
      topic: es-warning
      autoStartup: true
      pollDuration: 1
      maxPollRecords: 500
      bootStrapServers: ''
      groupId: ''
      autoOffsetReset: earliest

# control.yaml
com:
  trs:
    warning:
      operate:
        impl: es
```

## vRC20241224
- XMKFB-5988 预警消费支持华三kafka
- XMKFB-5988 发起布控支持从用户头像字段获取图片
- XMKFB-5987 临控布控审批表单更换

## v12.3
- XMKFB-5483 后-【高新】- 群体布控需求

## v12.2
- XMKFB-5692 指挥大屏-布控信息-相关预警优化
- XMKFB-5603 常控列表慢问题优化

## v12.1 
- XMKFB-5405 泸州-新增无人机感知源预警推送
- XMKFB-5435 [XMKFB-5404] 后-【泸州】- 同步云墙的常控、临控人员到云哨并发起布控
- XMKFB-5481 后-【高新】- 管控记录同步后问题排查  

## v11.4
- XMKFB-5421 后-【高新】- fk专题-bug

## RC 20241126
- XMKFB-5057 省厅GA-后台配置新增审核模板    

## v11.3
- XMKFB-5183 【广安】广安大屏，预案详情里的摄像头列表数据未作分页处理
- XMKFB-5224 后-【高新】- 以萨车辆数据对接（增加根据id批量发起订阅的接口）
- XMKFB-5226 后-【高新】-机器人-工作管控记录数据对接

## v11.2
- XMKFB-5079 后-【高新】- 机器人同步过来的人员需要自动发起常控
- XMKFB-5099 合-【高新】-商汤聚档数据入云控

## v11.1
- XMKFB-4972 后-【高新】-信令预警

## v10.5
- feat: 视频监控按经纬度检索优化

## v10.4
- XMKFB-4610 合-【高新】-FK预警签收优化
- XMKFB-4794 高新-【线索池优化】

## v10.3

- XMKFB-4383【泸州】融合检索，人员轨迹信息里感知源数据接口响应时间较慢
- XMKFB-4310 [XMKFB-4309] 前-【高新】-常控列表增加责任警种、责任派出所检索
- XMKFB-4568[XMKFB-4557] 【前】—— 感知源的经纬度如果是POINT（1，1），不再展示
- XMKFB-4531 [XMKFB-4402] 后-【高新】-盗窃、扒窃、涉黄 关注预警开发
- XMKFB-4681 泸州-碰撞功能确认
- 

## v10.2

- XMKFB-4309 合-【高新】-常控列表检索优化
- XMKFB-4076 广安GA-预案功能
- XMKFB-4483 后 - 敏感日新增了删不掉

## RC20240925

- XMKFB-4172 后-【高新】-FK专题无最新数据问题排查

## v9.2

- XMKFB-3784 合-【高新】-群体档案增加归档功能
- XMKFB-3855 后-【南充】-人员、预警、感知源接口提供

## RC20240902

- fix(XMKFB-3677): 工作记录填报报错问题修复

## v8.5

- XMKFB-3476 合-【高新】-FK预警增加敏感区域预警
- XMKFB-3461 合-【高新】-人员档案增加归档流程

## v8.4

- XMKFB-3257[XMKFB-3253] 后-【高新】-预警管控相关问题处理
- XMKFB-3263 省厅日志接入

## v8.3

- XMKFB-3104 后-【南充】-常控中心bug
- XMKFB-3223 后-【广安】-编辑区域布控后，大屏没有展示出对应的安保任务
- 区域编辑无日志记录修复

## v8.2

- APP-142 发布的待审核挂账详情页流程页查询接口报系统错误
- APP-156 常控列表按照最近预警时间排序报系统错误
- APP-159 常控列表根据预警信息内容无法筛选数据
- XMKFB-3007 后-【泸州】-关注对象增加区域关注

## v8.1

- XMKFB-2956 后-【泸州】-管控预警消费去掉对命中地域的过滤
- XMKFB-2748 [XMKFB-2747] 云墙布控数据同步
- XMKFB-2884 ys 配置与适配

## v7.4

-- XMKFB-2523 后-【泸州】-全国在逃人员布控预警问题

## RC20240724

- 外来流入人员新返回感知源类型字段

## v7.3

- XMKFB-2256[XMKFB-2032] 摄像头视频实时播放对接
- XMKFB-2599 后-【泸州】-合成作战增加关联对象
- XMKFB-2647 后-【高新】-人员档案优化
- XMKFB-1949[XMKFB-1668] 手机档案关联imsi异常

### 配置文件新增

```yaml
# 20240719 - 肖豪 - 增加获取虚拟信息人员配置项（需要项目上手动配置）
# 配置项结构（修改对应值）：[{"realName":"管理员","idNumber":"admin","policeCode":"111111","dept":{"code":"510500000000"}}]
control:
  virtual:
    haveAuthUsers: ''
```

## v7.1 发版日志

- XMKFB-2410 后-【泸州】-线索挖掘-外来人员流入增加流入时间展示
- XMKFB-2430 自贡GA-勤务日历普通模式针对单个日期编辑时，支持添加、编辑和删除敏感日期

## v6.4 发版日志

- XMKFB-2335 fk人员的静默预警调整
- XMKFB-2344 区域布控审批通过后才会展示为安保任务

## RC20240625

- XMKFB-2247 后-外来人员流入接口提供

### 配置文件新增

```text
control.fx.influx.time=60-30
```

## v6.3发版日志

- XMKFB-1617 【高新】预警签收逻辑调整

## v6.2 发版日志

- XMKFB-2021 感知引擎参数传递
- XMKFB-1909 [XMKFB-1725] 后-自贡大屏-重点场所、感知源、安保区域接口提供
- XMKFB-1904 [XMKFB-1725] 后-后台大屏展示配置模块开发

## v6.1 发版日志

- XMKFB-1824 [XMKFB-1816] 后-重点区域管理，能够按照警种筛选区域、专题筛选区域
- XMKFB-1879 【高新】感知引擎订阅
- XMKFB-2023 后-静默模型数据签收
- XMKFB-1970[XMKFB-1725] 后 - 勤务日历功能开发
-

## v5.4 发版日志

# 20240524

-XMKFB-1782、fk静默预警的头像问题处理

# 20240524

-XMKFB-1726、后-群体档案新增管控工作记录

# 20240522

-XMKFB-1637、【德阳】人员档案&常控中心常控标签修改为专题标签

# sql脚本

```jql--注意，以下3个脚本只能德阳环境执行
#我的常控-检索条件-常控标签修改为专题标签
UPDATE t_request_params set params = '{"filterParams":[{"key":"level","type":"option","value":["%%control_regular_monitor_level%%"],"fieldNames":{"label":"name","value":"code","children":"children"},"displayName":"常控级别"},{"key":"hasWarning","type":"option","value":[{"id":1,"name":"有预警"},{"id":0,"name":"无预警"}],"fieldNames":{"label":"name","value":"id"},"displayName":"是否有预警"},{"key":"status","type":"multiple-tree","value":["%%regular_monitor_status%%"],"fieldNames":{"label":"name","value":"code","children":"children"},"displayName":"常控状态"},{"key":"personStatus","type":"multiple-tree","value":["%%service_person_status%%"],"fieldNames":{"label":"name","value":"code","children":"children"},"displayName":"人员状态"},{"key":"person_label","type":"multiple-tree","value":["&&person_label&&"],"fieldNames":{"label":"name","value":"id","children":"children"},"displayName":"人员标签"},{"key":"regular_label","type":"multiple-tree","value":["&&regular_label&&"],"fieldNames":{"label":"name","value":"id","children":"children"},"displayName":"专题标签"},{"key":"doneStatus","type":"option","value":[{"id":0,"name":"待完成"},{"id":1,"name":"已完成"},{"id":2,"name":"无需完成"}],"fieldNames":{"label":"name","value":"id"},"displayName":"工作记录"},{"key":"createTime","type":"timeParams","value":[{"id":"1","name":"今天"},{"id":"11","name":"昨天"},{"id":"2","name":"本周"},{"id":"12","name":"上周"},{"id":"3","name":"本月"},{"id":"13","name":"上月"},{"id":"4","name":"本季"},{"id":"14","name":"上季"},{"id":"99","name":"自定义"}],"fieldNames":{"label":"name","value":"id"},"displayName":"发起时间"},{"key":"latestWarningTime","type":"timeParams","value":[{"id":"1","name":"今天"},{"id":"11","name":"昨天"},{"id":"2","name":"本周"},{"id":"12","name":"上周"},{"id":"3","name":"本月"},{"id":"13","name":"上月"},{"id":"4","name":"本季"},{"id":"14","name":"上季"},{"id":"99","name":"自定义"}],"fieldNames":{"label":"name","value":"id"},"displayName":"最近预警时间"}],"searchFields":[{"key":"targetName","name":"姓名"},{"key":"idNumber","name":"证件号码"},{"key":"content","name":"预警信息"}]}' where module = 'regular-list-my';
#全部常控-检索条件-常控标签修改为专题标签
UPDATE t_request_params set params = '{"filterParams":[{"key":"level","type":"option","value":["%%control_regular_monitor_level%%"],"fieldNames":{"label":"name","value":"code","children":"children"},"displayName":"常控级别"},{"key":"hasWarning","type":"option","value":[{"id":1,"name":"有预警"},{"id":0,"name":"无预警"}],"fieldNames":{"label":"name","value":"id"},"displayName":"是否有预警"},{"key":"status","type":"multiple-tree","value":["%%regular_monitor_status%%"],"fieldNames":{"label":"name","value":"code","children":"children"},"displayName":"常控状态"},{"key":"personStatus","type":"multiple-tree","value":["%%service_person_status%%"],"fieldNames":{"label":"name","value":"code","children":"children"},"displayName":"人员状态"},{"key":"person_label","type":"multiple-tree","value":["&&person_label&&"],"fieldNames":{"label":"name","value":"id","children":"children"},"displayName":"人员标签"},{"key":"regular_label","type":"multiple-tree","value":["&&regular_label&&"],"fieldNames":{"label":"name","value":"id","children":"children"},"displayName":"专题标签"},{"key":"doneStatus","type":"option","value":[{"id":0,"name":"待完成"},{"id":1,"name":"已完成"},{"id":2,"name":"无需完成"}],"fieldNames":{"label":"name","value":"id"},"displayName":"工作记录"},{"key":"createDept","type":"multiple-tree","value":["&&dept&&"],"fieldNames":{"label":"shortName","value":"id","children":"children"},"displayName":"发起单位"},{"key":"createTime","type":"timeParams","value":[{"id":"1","name":"今天"},{"id":"11","name":"昨天"},{"id":"2","name":"本周"},{"id":"12","name":"上周"},{"id":"3","name":"本月"},{"id":"13","name":"上月"},{"id":"4","name":"本季"},{"id":"14","name":"上季"},{"id":"99","name":"自定义"}],"fieldNames":{"label":"name","value":"id"},"displayName":"发起时间"},{"key":"latestWarningTime","type":"timeParams","value":[{"id":"1","name":"今天"},{"id":"11","name":"昨天"},{"id":"2","name":"本周"},{"id":"12","name":"上周"},{"id":"3","name":"本月"},{"id":"13","name":"上月"},{"id":"4","name":"本季"},{"id":"14","name":"上季"},{"id":"99","name":"自定义"}],"fieldNames":{"label":"name","value":"id"},"displayName":"最近预警时间"}],"searchFields":[{"key":"targetName","name":"姓名"},{"key":"idNumber","name":"证件号码"},{"key":"content","name":"预警信息"}]}' where module = 'regular-list-all';
#业务参数-管控-常控标签修改为专题标签
UPDATE t_business_filter set name = '专题标签' where module='control' and type = 'regular';
```

## v5.3 发版日志

- XMKFB-1617 【高新】预警签收逻辑调整

## v5.2 发版日志

# 20240517

-XMKFB-1595、风险列表慢问题处理

## v5.1 发版日志

# 2024-05-10

- XMKFB-1231、fk关注监控开发

# 2024-05-09

- XMKFB-1469、 后-预警详情推送人员重复显示通知部门重复显示
- XMKFB-1196[XMKFB-1187] 后-police-control 模块熟悉

# nacos配置更新

```
#fk关注监控功能需要配置
#fk人员关注监控的使用的预警配置id
    com.trs.warningModalConfig.sensitiveAreaModalIds
#敏感区域模型的id集合，以逗号进行分割
    com.trs.warningModalConfig.gatherAreaModalIds
#聚集预警模型的id集合，以逗号进行分割
    com.trs.warningModalConfig.fkCareMonitorConfigId
```

## v1.5 发版日志

# 2024-04-30

-XMKFB-1349、后-勤务日历会自动修改勤务等级

# 2024-04-29

-XMKFB-1322、后-临控生产问题

# 2024-04-28

-XMKFB-1232、管控-布控中心，选择布控人员页面自动填充的人员信息显示有误  
-XMKFB-1266、批量车工接口以及批量身份证选中接口提供

# 2024-04-25

-XMKFB-1231、批量撤控功能开发

# 2024-04-23

-XMKFB-1036、发起/编辑人员布控页面中点击下载导入模板按钮后，页面无响应

# 2024-04-18

-XMKFB-1036、发起/编辑人员布控页面中点击下载导入模板按钮后，页面无响应

- 常控列表检索慢问题修复

# 2024-04-16

-XMKFB-1114、【高新】未建档关注人员和已建档关注人员数据导出的文件名称应作修改

# 20240411

- XMKFB-1100、fk的静默预警也需要像其他预警一样进行派出所权限区分
- XMKFB-989、返回活动地址信息
- XMKFB-970、高新-【FK预警】将FK预警中的首次入区预警颜色改为蓝色

## v1.4 发版日志

- XMKFB-921、后-fk预警也要支持民族的筛选
- XMKFB-918、后-fk预警要根据权限进行预警消息查看

## v1.3 发版日志

- XMKFB-758、后-fk专题静默模型开发
- XMKFB-700、后-预警信息要返回头像
- XMKFB-629、【高新】FK预警列表增加模型和模型的检索

## v1.2 发版日志

- XMKFB-587、【高新】FK专题民族优化
- XMKFB-432、【高新】增加新预警模型后台配置调研和开发

## v1.1 发版日志

# 2024-01-29

- XMKFB-439、涉恐人员列表接口开发
- XMKFB-444、反恐未建档人员初始化以及接入
- XMKFB-436、【高新】反恐专题新设计开发
- XMKFB-441、后-发起常控接口提供
- XMKFB-442、后-详情接口的提供
- XMKFB-443、后-导出接口提供

## v1.0 发版日志

# 2024-01-18

- XMKFB-404、【高新】FK专题需求-首次入区模型调整

# nacos配置更新

```

```