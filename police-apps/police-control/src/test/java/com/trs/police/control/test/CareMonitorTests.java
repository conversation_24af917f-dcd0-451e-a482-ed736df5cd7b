package com.trs.police.control.test;

import com.trs.police.common.core.constant.enums.IdentifierTypeEnum;
import com.trs.police.common.core.utils.StringUtil;
import com.trs.police.control.ControlApp;
import com.trs.police.control.domain.entity.warning.InductiveControlEsWarning;
import com.trs.police.control.domain.vo.care.CareMonitorInitialeVO;
import com.trs.police.control.mapper.PersonMapper;
import com.trs.police.control.repository.InductiveControlEsWarningRepository;
import com.trs.police.control.service.CareMonitorService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * *@author:wen.wen
 * *@create 2024-06-21 15:26
 **/
@Slf4j
@SpringBootTest(classes = ControlApp.class)
public class CareMonitorTests {

    @Resource
    private CareMonitorService careMonitorService;

    @Autowired
    private InductiveControlEsWarningRepository repository;

    @Autowired
    private PersonMapper personMapper;

    @Test
    public void inintCareMonitor() throws Exception {

        List<CareMonitorInitialeVO> initialeVOList = new ArrayList<>();
        CareMonitorInitialeVO careMonitorInitialeVO = new CareMonitorInitialeVO();
        careMonitorInitialeVO.setMonitorConfigId(7L);
        careMonitorInitialeVO.setModuleCareMonitorHandlerClassName("com.trs.police.control.handler.care.impl.FxCareMonitorHandler");
        careMonitorInitialeVO.setCertificateType(IdentifierTypeEnum.CAR_NUMBER);
        careMonitorInitialeVO.setCertificateValue("500225199707256876");
        careMonitorInitialeVO.setCreateUserId("2214");
        careMonitorInitialeVO.setCreateDeptId("2");
        initialeVOList.add(careMonitorInitialeVO);
        careMonitorService.initiate(initialeVOList);
        assert true;
    }

    @Test
    public void test() {
        // 测试空参数的情况
        List<Long> deptIds = Arrays.asList(1L, 2L);
        List<Long> personLabels = Arrays.asList(3L, 4L);
        List<String> districts = Arrays.asList("510000", "510821");
        districts = districts.stream().map(StringUtil::getPrefixCode).collect(Collectors.toList());

        // 调用待测方法
        List<Long> actualPersonIds = personMapper.selectPersonIdsByFiter(deptIds, personLabels, districts);

        // 验证结果为空列表
        log.info("actualPersonIds: {}", actualPersonIds);
    }
}
