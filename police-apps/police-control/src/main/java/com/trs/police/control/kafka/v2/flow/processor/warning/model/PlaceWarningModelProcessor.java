package com.trs.police.control.kafka.v2.flow.processor.warning.model;

import com.trs.police.control.kafka.v2.context.WarningMessageContext;
import com.trs.police.control.mapper.MonitorWarningModelMapper;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 场所预警模型
 */

@Component
public class PlaceWarningModelProcessor implements WarningModelProcessor {

    @Resource
    protected MonitorWarningModelMapper monitorWarningModelMapper;

    @Override
    public List<Long> collect(WarningMessageContext context) {
        List<Long> placeCodes = context.getPlaceCodes();
        List<Long> modelIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(placeCodes)) {
            modelIds.addAll(monitorWarningModelMapper.selectModelIdsByPlaceCodes(placeCodes));
        }
        return modelIds;
    }
}
