package com.trs.police.control.domain.vo.regular;

import com.trs.police.common.core.constant.enums.MonitorLevelEnum;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.core.vo.permission.SimpleUserVO;
import com.trs.police.control.domain.entity.monitor.RegularMonitorConfigEntity;
import com.trs.police.common.core.dto.WarningModelVO;
import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/1/6 15:31
 */
@Data
public class RegularMonitorConfigVO implements Serializable {

    private static final long serialVersionUID = -4129577184186120162L;

    private Long id;

    @NotBlank(message = "预警配置名称不能为空！！")
    private String name;
    /**
     * 预警级别
     */
    @NotNull(message = "预警级别不能为空！！")
    private Integer warningLevel;
    /**
     * 常控级别
     */
    @NotEmpty(message = "常控级别不能为空！！")
    private List<Long> regularLevels;
    /**
     * 人员标签
     */
    @NotEmpty(message = "人员标签不能为空！！")
    private List<List<Long>> personLabels;
    /**
     * 常控标签
     */
    private List<List<Long>> regularLabels;
    /**
     * 预警模型
     */
    @NotEmpty(message = "预警模型不能为空！")
    private List<WarningModelVO> warningModel;
    /**
     * 通知人
     */
    private List<SimpleUserVO> notifyPerson;
    /**
     * 通知活动轨迹所在地
     */
    private Boolean notifyWarningArea;
    /**
     * 通知人员管控派出所
     */
    private Boolean notifyControlDept;
    /**
     * 短信通知状态
     */
    private Boolean warningMessageStatus;
    /**
     * 启用状态
     */
    private Boolean enabledStatus;

    /**
     * 转换entity
     *
     * @return entity
     */
    public RegularMonitorConfigEntity toEntity() {
        final RegularMonitorConfigEntity entity = new RegularMonitorConfigEntity();
        entity.setId(getId());
        entity.setName(name);
        entity.setWarningLevel(MonitorLevelEnum.codeOf(warningLevel));
        entity.setNotifyPerson(notifyPerson.toArray(new SimpleUserVO[0]));
        entity.setNotifyWarningArea(notifyWarningArea);
        entity.setNotifyControlDept(notifyControlDept);
        entity.setRegularLevels(regularLevels);
        entity.setWarningMessageStatus(warningMessageStatus);
        entity.setEnabledStatus(enabledStatus);
        entity.setWarningModel(this.getWarningModel().stream().map(WarningModelVO::getId).collect(Collectors.toList()));
        entity.setPersonLabels(KeyValueTypeVO.nestingListSimplification(this.getPersonLabels()).stream()
            .map(labelId -> Long.valueOf(labelId.toString())).collect(Collectors.toList()));
        entity.setRegularLabels(KeyValueTypeVO.nestingListSimplification(regularLabels).stream()
            .map(labelId -> Long.valueOf(labelId.toString())).collect(Collectors.toList()));
        return entity;
    }
}
