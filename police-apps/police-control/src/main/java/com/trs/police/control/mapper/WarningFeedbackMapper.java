package com.trs.police.control.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.common.core.entity.WarningFeedbackEntity;
import com.trs.police.control.domain.vo.warning.WarningFeedbackListVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/18
 */
@Mapper
public interface WarningFeedbackMapper extends BaseMapper<WarningFeedbackEntity> {

    /**
     * 根据预警列表获取反馈信息
     *
     * @param id 预警id
     * @return 反馈
     */
    List<WarningFeedbackListVo> getFeedbackListByWarningId(@Param("id") Long id);
}
