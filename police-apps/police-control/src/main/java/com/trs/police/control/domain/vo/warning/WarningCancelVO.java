package com.trs.police.control.domain.vo.warning;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 取消比对订阅服务
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WarningCancelVO implements Serializable {

    private static final long serialVersionUID = -8822319200812563177L;
    /**
     * 标识符
     */
    private String identifier;

    /**
     * 标识符类型
     */
    private Integer identifierType;

    /**
     * 订阅编号
     */
    private String subscribeCode;

    /**
     * 只填写布控编号
     *
     * @param subscribeCode 布控编号
     */
    public WarningCancelVO(String subscribeCode) {
        this.subscribeCode = subscribeCode;
    }
}
