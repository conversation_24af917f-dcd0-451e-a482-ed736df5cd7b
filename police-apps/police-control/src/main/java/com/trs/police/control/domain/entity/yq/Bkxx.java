package com.trs.police.control.domain.entity.yq;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @ClassName: Bkxx
 * @Description: 布控信息
 * @author: chenran
 * @date: 2018/5/31
 * @version: v1.0
 */
@Data
@NoArgsConstructor
@TableName(value = "T_ALARM_BKXX")
public class Bkxx {
    @TableId
    private String id;
    //布控基本信息
    private String sqrxm; //申请人姓名
    private String sqrsfzh; //申请人身份证号码
    private String sqrdh; //申请人联系电话（可以有多个）
    private String sqrdw; //申请人单位
    private String sqrdwdm; //申请人单位代码
    private Date sqsj; //申请时间（也就是创建时间）
    private Date bkqssj; //布控开始时间
    private Date bkjssj;//布控结束时间  
    private Integer bkts = 90; //布控天数
    private Integer bkjb = 30; //布控级别 10 红色 20 黄色 30 蓝色
    private Integer bkdyfw = 10; //布控地域范围   10 全国 20 全省 30 本地
    private String bksy; //布控事由（必填）
    private Integer czlc; //处置流程
    private String zfyj; //执法依据
    private String czyq; //处置要求（必填）
    private Integer czcs = 5;    //处置措施 1控制2管控3经营4关注5掌握轨迹（默认）
    private String ckly;    //撤控理由（不使用关联表，只保留最近一次，之后撤控直接覆盖）
    private String jbbgly;    //级别变更理由（不使用关联表，只保留最近一次，之后的会覆盖）
    private String xkly;    //续控理由（不使用关联表，只保留最近一次，之后的会覆盖）
    private String glajbh;  // 关联的案事件编号
    private String zfyjlx = "1"; //执法依据类型 1 居留证 2 立案决定书 3 强制隔离戒毒决定书 4 逮捕证 5 接回人员承诺书
    private String fields;    //执法依据附件主键集合
//    private String ryxl_dm;    //人员细类代码，用于判断布控的查看权限
    private String ryxl;//人员细类	（用于展示）
    private String bkbh; //布控编号（后台自动生成）
    private Date lastupdatetime;    //最后操作更新时间，展示在页面上的倒排序时间
    private String county;    //区县（只有批量布控会导入的字段信息，只用于发送短信的内容）
    private String type = "0";    //布控平台类型（0本地1阿里......其他平台）
//    private String prevent_id;    // 疫情防控人员主键

    //流程状态相关
    private String flowid;        //当前流程id
    private String useridcards;    //用户身份证号集合（当前流程可审核人的证件号码集合）
    private Integer status = 0; //持久化状态  0 申请（未提交）  1 审批中 2 审批通过（已布控） 3 审核拒绝  4已撤控
//    private Integer is_use_default_way = 1;    //是否使用默认的方式预警（默认方式为蓝色不产生指令，红黄产生对应布控级别的预警指令并且会发送信息及指令给布控人）
//    private Integer is_syn_to_control = 0;    //是否将布控信息同步到重点人员管控平台
    private Integer zlfbid = 0;    //指令发布主键（在重管平台的主键）
}
