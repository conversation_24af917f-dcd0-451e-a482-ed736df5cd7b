package com.trs.police.control.task;

import com.trs.police.common.core.entity.District;
import com.trs.police.control.domain.entity.basic.SourceEntity;
import com.trs.police.control.mapper.SourceMapper;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.PrecisionModel;
import org.locationtech.jts.io.ParseException;
import org.locationtech.jts.io.WKTReader;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 感知源地区行政区划清洗任务
 */

@Slf4j
@Component
public class WarningSourceDistrictCleanTask {

    private final SourceMapper sourceMapper;

    private static List<District> districts;

    private static WKTReader wktReader;

    public WarningSourceDistrictCleanTask(SourceMapper sourceMapper) {
        this.sourceMapper = sourceMapper;
        //初始化北京/省会区域信息
        districts = Stream.of("510100", "110000")
                .map(sourceMapper::selectGeoByCode)
                .collect(Collectors.toList());
        wktReader = new WKTReader(new GeometryFactory(new PrecisionModel(), 4326));
    }

    private static final Integer BATCH_SIZE = 100;

    /**
     * 感知源地区行政区划清洗任务
     */
    @Scheduled(cron = "0 0 0 * * *")
    public void cleanDistrict() {
        String enabled = BeanFactoryHolder.getEnv().getProperty("com.trs.police.schedule.warningSourceDistrictClean.enabled", "false");
        if (enabled.equals("true")) {
            log.info("开始执行感知源地区行政区划清洗任务");
            long offset = 0L;
            List<SourceEntity> records;

            do {
                records = sourceMapper.batchSelect(BATCH_SIZE, offset);
                updateSource(records);
                offset += BATCH_SIZE;
            } while (!records.isEmpty());

            log.info("感知源地区行政区划清洗任务执行结束");
        }
    }

    /**
     * 更新感知源
     *
     * @param sourceEntities 感知源列表
     */
    private void updateSource(List<SourceEntity> sourceEntities) {
        //source依次判断更新
        sourceEntities.forEach(sourceEntity -> {
            districts.stream()
                    //判断source是否在某一个district的区域内
                    .filter(district -> isIn(sourceEntity.getPoint(), district.getContour()))
                    //只要有一个district满足条件，就短路，source没有匹配到district则不再往下执行，不会触发update
                    .findFirst()
                    //更新source的district信息
                    .ifPresent(district -> {
                        log.info("更新了source:{}", sourceEntity.getId());
                        sourceMapper.updateDistrictInfo(sourceEntity.getId(), district.getCode(), district.getName());
                    });
        });
    }

    private static boolean isIn(String point, String contour) {
        try {
            Geometry p = wktReader.read(point);
            Geometry c = wktReader.read(contour);
            for (int i = 0; i < c.getNumGeometries(); i++) {
                Geometry g = c.getGeometryN(i);
                if (p.within(g)) {
                    return true;
                }
            }
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        return false;
    }

}
