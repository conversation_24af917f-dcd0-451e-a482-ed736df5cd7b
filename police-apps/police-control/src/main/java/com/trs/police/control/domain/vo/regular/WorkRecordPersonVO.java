package com.trs.police.control.domain.vo.regular;

import com.trs.police.common.core.vo.CodeNameVO;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import java.util.List;
import lombok.Data;

/**
 * 工作记录-人员详情
 *
 * <AUTHOR>
 */
@Data
public class WorkRecordPersonVO {

    private Long id;

    private String name;

    private String idNumber;

    private List<String> personLabel;

    private CodeNameVO regularLevel;

    private CodeNameVO regularStatus;

    private String workRecord;

    private List<FileInfoVO> imgs;
}
