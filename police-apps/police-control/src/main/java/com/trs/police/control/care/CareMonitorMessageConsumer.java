package com.trs.police.control.care;

import com.trs.police.control.domain.vo.care.CareMonitorMessage;

import java.util.Objects;

/**
 * 关注预警消息消费者
 *
 * <AUTHOR>
 */
public interface CareMonitorMessageConsumer {

    /**
     * 消费关注预警消息
     *
     * @param message 消息
     */
    void accept(CareMonitorMessage message);

    /**
     * 消费者唯一标识
     *
     * @return 消费者唯一编号
     */
    CareMonitoMsgConsumerEnum id();

    /**
     * 可消费的类型
     *
     * @param id 类型
     * @return 是否可消费
     */
    default Boolean acceptable(CareMonitoMsgConsumerEnum id) {
        return Objects.equals(id, id());
    }
}
