package com.trs.police.control.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 布控任务接收表(t_control_task_receive)
 *
 * <AUTHOR>
 * @date 2024/12/25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "t_control_task_receive")
public class ControlTaskReceiveEntity extends AbstractBaseEntity {

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 接收单位id
     */
    private Long receiveDeptId;

    /**
     * 下发状态：1:未全下发，2：已经全部下发
     */
    private Integer issueStatus;

    /**
     * 签收状态：1：已签收，0：未签收
     */
    private Integer signStatus;

    public ControlTaskReceiveEntity(Long taskId, Long receiveDeptId) {
        this.taskId = taskId;
        this.receiveDeptId = receiveDeptId;
    }
}
