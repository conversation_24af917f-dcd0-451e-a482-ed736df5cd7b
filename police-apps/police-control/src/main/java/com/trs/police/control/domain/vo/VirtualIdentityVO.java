package com.trs.police.control.domain.vo;

import com.trs.police.common.core.vo.profile.PersonVirtualIdentityVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 虚拟身份
 *
 * <AUTHOR>
 * @date 2022/6/21 10:01
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class VirtualIdentityVO {

    private Long id;

    /**
     * 类型
     */
    private Long type;

    /**
     * 虚拟号码
     */
    private String virtualNumber;


    /**
     * 转PersonVirtualIdentityVO
     *
     * @return com.trs.police.common.core.vo.profile.PersonVirtualIdentityVO
     */
    public PersonVirtualIdentityVO toPersonVirtualIdentityVo() {
        PersonVirtualIdentityVO personVirtualIdentityVO = new PersonVirtualIdentityVO();
        personVirtualIdentityVO.setType(this.getType());
        personVirtualIdentityVO.setVirtualNumber(this.getVirtualNumber());
        return personVirtualIdentityVO;
    }
}
