package com.trs.police.control.task.gathering;

import com.trs.police.control.domain.dto.WarningTrackWithGroupId;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * 疑似聚集轨迹
 */
@Data
public class SuspectedGatheringGroup {
    /**
     * 布控ID
     */
    private Long monitorId;

    /**
     * 感知源ID
     */
    private String sourceId;

    /**
     * 群体ID
     */
    private Long groupId;

    /**
     * 聚集开始时间
     */
    private LocalDateTime startTime;

    /**
     * 聚集结束时间
     */
    private LocalDateTime endTime;

    /**
     * 轨迹
     */
    private List<WarningTrackWithGroupId> tracks;

    /**
     * 人员
     */
    private Set<String> identifiers;
}