package com.trs.police.control.service.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.params.ExportParams;
import com.trs.police.common.core.params.SortParams;
import com.trs.police.common.core.params.TimeParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.core.utils.NumberUtil;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.common.openfeign.starter.service.ProfileService;
import com.trs.police.control.domain.vo.RegularStatisticExportVO;
import com.trs.police.control.domain.vo.RegularStatisticVO;
import com.trs.police.control.mapper.RegularMonitorMapper;
import com.trs.police.control.mapper.ServiceCalendarMapper;
import com.trs.police.control.service.RegularStatisticService;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Service;

/**
 * 常控统计
 *
 * <AUTHOR>
 */
@Service
public class RegularStatisticServiceImpl implements RegularStatisticService {

    @Resource
    private RegularMonitorMapper regularMonitorMapper;
    @Resource
    private ProfileService profileService;
    @Resource
    private ServiceCalendarMapper serviceCalendarMapper;
    @Resource
    private PermissionService permissionService;


    @Override
    public PageResult<RegularStatisticVO> regularStatistics(ListParamsRequest request) {
        List<RegularStatisticVO> list = getRegularStatisticList(request);
        return PageResult.of(list, request.getPageParams());
    }

    private List<RegularStatisticVO> getRegularStatisticList(ListParamsRequest request) {
        List<KeyValueTypeVO> filterParams = request.getFilterParams();
        TimeParams timeParams = KeyValueTypeVO.getSingleFilterParam(filterParams, "countPeriod", TimeParams.class);
        String level = KeyValueTypeVO.getSingleFilterParam(filterParams, "level", String.class);
        Long deptId = KeyValueTypeVO.getSingleFilterParam(filterParams, "dept", Long.class);


        if (timeParams == null) {
            throw new TRSException("未传时间筛选参数！");
        }
        String topDeptCode = permissionService.getCurrentUserTopLevelDept();
        String regularLabelStr = getRegularLabelStr(filterParams);
        List<RegularStatisticVO> list = regularMonitorMapper.getRegularStatistics(deptId, level, regularLabelStr,
            topDeptCode, request.getSearchParams(), timeParams);
        return calculateStatistic(list, level, regularLabelStr, timeParams, request.getSortParams());
    }

    private String getRegularLabelStr(List<KeyValueTypeVO> filterParams) {
        KeyValueTypeVO regularLabel = KeyValueTypeVO.get(filterParams, "regular_label");
        if (regularLabel == null) {
            return null;
        } else {
            List<Object> labelIds = KeyValueTypeVO.nestingListSimplification(regularLabel.getValue());
            return JsonUtil.toJsonString(labelIds);
        }
    }

    private List<RegularStatisticVO> calculateStatistic(List<RegularStatisticVO> list, String level, String labelStr,
        TimeParams timeParams, SortParams sortParams) {
        list.parallelStream().forEach(vo -> {
            vo.setArchiveCompletionRate(profileService.getArchiveCompleteRate(vo.getDeptId(), level, labelStr));
            vo.setSignRate(NumberUtil.getRateDouble(vo.getSignTotal(), vo.getWarningTotal()));
            vo.setReplyRate(NumberUtil.getRateDouble(vo.getReplyTotal(), vo.getWarningTotal()));
            final List<Long> regularId = regularMonitorMapper.getRegularIdByDeptIdAndLevel(vo.getDeptId(), level);
            final Integer count = regularId.stream()
                .map(item -> serviceCalendarMapper.getByRegularDoneCount(timeParams.getBeginTime(),
                    timeParams.getEndTime(), item))
                .reduce(0, Integer::sum);
            vo.setWorkRecordAverageDay(NumberUtil.division(count, regularId.size()));
        });
        list.sort((o1, o2) -> {
            switch (sortParams.getSortField()) {
                case "controlTotal":
                    return sortParams.getCompare(o1.getControlTotal(), o2.getControlTotal());
                case "archiveCompletionRate":
                    return sortParams.getCompare(o1.getArchiveCompletionRate(), o2.getArchiveCompletionRate());
                case "workRecordAverageDay":
                    return sortParams.getCompare(o1.getWorkRecordAverageDay(), o2.getWorkRecordAverageDay());
                case "warningTotal":
                    return sortParams.getCompare(o1.getWarningTotal(), o2.getWarningTotal());
                case "signTotal":
                    return sortParams.getCompare(o1.getSignTotal(), o2.getSignTotal());
                case "signRate":
                    return sortParams.getCompare(o1.getSignRate(), o2.getSignRate());
                case "replyTotal":
                    return sortParams.getCompare(o1.getReplyTotal(), o2.getReplyTotal());
                case "replyRate":
                    return sortParams.getCompare(o1.getReplyRate(), o2.getReplyRate());
                default:
                    return sortParams.getCompare(o1.getDeptId(), o2.getDeptId());
            }
        });
        return list;
    }

    @Override
    public void regularStatisticsExport(HttpServletResponse response, ExportParams params)
        throws IOException {
        String fileName = URLEncoder.encode("常控统计", StandardCharsets.UTF_8);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

        List<RegularStatisticExportVO> exportListVos = this.createExportListVos(params);

        EasyExcelFactory.write(response.getOutputStream(), RegularStatisticExportVO.class)
            .sheet("常控统计")
            .includeColumnFiledNames(params.getFieldNames())
            .doWrite(exportListVos);
    }

    private List<RegularStatisticExportVO> createExportListVos(ExportParams params) {
        final ListParamsRequest request = params.getListParamsRequest();
        //按id查询
        if (Boolean.FALSE.equals(params.getIsAll())) {
            List<KeyValueTypeVO> filterParams = request.getFilterParams();
            TimeParams timeParams = KeyValueTypeVO.getSingleFilterParam(filterParams, "countPeriod", TimeParams.class);
            String level = KeyValueTypeVO.getSingleFilterParam(filterParams, "level", String.class);
            String regularLabels = getRegularLabelStr(filterParams);
            List<RegularStatisticVO> list = regularMonitorMapper.getRegularStatisticsByDept(params.getIds(), level, regularLabels, timeParams);
            String regularLabelStr = getRegularLabelStr(filterParams);
            return calculateStatistic(list, level, regularLabelStr, timeParams, request.getSortParams())
                .stream().map(RegularStatisticVO::toExport).collect(Collectors.toList());
        } else {
            return getRegularStatisticList(request)
                .stream().map(RegularStatisticVO::toExport).collect(Collectors.toList());
        }
    }

}
