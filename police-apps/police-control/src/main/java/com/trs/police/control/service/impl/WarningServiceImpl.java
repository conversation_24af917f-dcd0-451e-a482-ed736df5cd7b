package com.trs.police.control.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.trs.police.common.core.constant.enums.*;
import com.trs.police.common.core.constant.log.OperateModule;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.entity.WarningEntity;
import com.trs.police.common.core.entity.WarningFeedbackEntity;
import com.trs.police.common.core.entity.WarningProcessEntity;
import com.trs.police.common.core.request.WarningDoneRequest;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.core.vo.OperateVO;
import com.trs.police.common.core.vo.control.WarningDoneVO;
import com.trs.police.common.core.vo.control.WarningFeedbackDetailVO;
import com.trs.police.common.core.vo.message.ScheduleMessageVO;
import com.trs.police.common.core.vo.permission.SimpleUserVO;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.common.openfeign.starter.service.ScheduleService;
import com.trs.police.control.domain.entity.monitor.MonitorTargetRelationEntity;
import com.trs.police.control.domain.entity.warning.WarningNotifyEntity;
import com.trs.police.control.domain.entity.warning.WarningTrackEntity;
import com.trs.police.control.domain.vo.NameAndIdNumberVO;
import com.trs.police.control.domain.vo.warning.WarningFeedbackHandleVO;
import com.trs.police.control.domain.vo.warning.WarningSignVO;
import com.trs.police.control.helper.CloudControlPushHelper;
import com.trs.police.control.mapper.*;
import com.trs.police.control.properties.WarningProperties;
import com.trs.police.control.proxy.WarningOperateProxy;
import com.trs.police.control.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 预警消息处理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class WarningServiceImpl implements WarningService {

    @Resource
    private WarningMapper warningMapper;
    @Resource
    private WarningTrackMapper warningTrackMapper;
    @Resource
    private WarningDisplayService warningDisplayService;
    @Resource
    private WarningProcessMapper warningProcessMapper;
    @Resource
    private WarningFeedbackMapper warningFeedbackMapper;
    @Resource
    private ScheduleService scheduleService;
    @Resource
    private WarningProperties warningProperties;
    @Resource
    private WarningNotifyMapper warningNotifyMapper;
    @Resource
    private WarningOperateProxy warningOperateProxy;
    @Resource
    private CloudControlPushHelper cloudControlPushHelper;
    @Resource
    private PermissionService permissionService;
    @Resource
    private MonitorService monitorService;
    @Resource
    private RegularMonitorService regularMonitorService;
    @Resource
    private SubscribeService subscribeService;
    @Resource
    private MonitorTargetRelationMapper monitorTargetRelationMapper;

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void signWarning(Long id) {
        CurrentUser user = AuthHelper.getCurrentUser();
        warningDisplayService.readWarning(id);

        List<WarningNotifyEntity> notifyEntities = warningNotifyMapper.selectList(new QueryWrapper<WarningNotifyEntity>().eq("warning_id", id));
        List<Long> ids = notifyEntities.stream().map(WarningNotifyEntity::getProcessId).collect(Collectors.toList());
        Map<Long, Long> processUserMap = notifyEntities.stream().collect(Collectors.toMap(WarningNotifyEntity::getProcessId, WarningNotifyEntity::getUserId,
                (u1, u2) -> u1));
        LambdaQueryWrapper<WarningProcessEntity> queryWrapper = Wrappers.<WarningProcessEntity>lambdaQuery()
                .in(WarningProcessEntity::getId, ids);
        if (Objects.nonNull(user)) {
            queryWrapper.eq(WarningProcessEntity::getDeptId, user.getDeptId());
        }
        List<WarningProcessEntity> processEntities = warningProcessMapper.selectList(queryWrapper);
        for (WarningProcessEntity process : processEntities) {
            //  更新签收状态
            Boolean result = signWarningWithEntity(id, process, processUserMap.get(process.getId()));
            if (Boolean.TRUE.equals(result)) {
                //已签收，开始研判过期计时
                WarningEntity warning = warningMapper.selectById(id);
                pushWarningActionMessage(process.getId(), warning.getWarningLevel(), WarningStatusEnum.SIGN_FINISH);
            }
        }
        // fx预警状态更新
        WarningEntity warning = warningMapper.selectById(id);
        if (warning.getFxWarningStatus() == 1) {
            warning.setFxWarningStatus(2);
            warningMapper.updateById(warning);
        }

        //根据需要更新es
        //process签收状态合并，所以只取一条的状态和warning_id为准
        warningOperateProxy.esUpdateSign(processEntities.get(0), warning);
        // 请求布控预警指令信息签收接口
        if (Objects.equals(warning.getWarningPlatform(),2)) {
            CurrentUser currentUser = AuthHelper.getNotNullUser();
            WarningNotifyEntity warningNotifyEntity = warningNotifyMapper.selectByWarningIdAndUser(id, currentUser.getId(), currentUser.getDeptId());
            List<WarningFeedbackEntity> warningFeedbackEntities = warningFeedbackMapper.selectList(new QueryWrapper<WarningFeedbackEntity>()
                    .eq("process_id", warningNotifyEntity.getProcessId())
                    .eq("type", WarningFeedbackTypeEnum.SIGN.getCode()));
            cloudControlPushHelper.warningSignPush(new WarningSignVO(), id, warningFeedbackEntities.get(0).getId());
        }
    }

    /**
     * 签收单个风险
     *
     * @param id 风险i
     * @param process process
     * @param userId userId
     * @return true 签收成功 false 签收失败
     */
    private Boolean signWarningWithEntity(Long id, WarningProcessEntity process, Long userId) {
        if (process.getStatus().equals(WarningStatusEnum.WAITING_SIGN)) {
            process.setStatus(WarningStatusEnum.SIGN_FINISH);
            OperateVO operateVO = OperateVO.newInstance();
            SimpleUserVO simpleUser = permissionService.findSimpleUser(userId, process.getDeptId());
            operateVO.setUser(simpleUser);
            process.setSign(operateVO);
            warningProcessMapper.updateById(process);

            //签收时需要记录到反馈表
            WarningFeedbackEntity warningFeedbackEntity = new WarningFeedbackEntity();
            warningFeedbackEntity.setType(WarningFeedbackTypeEnum.SIGN);
            warningFeedbackEntity.setProcessId(process.getId());
            warningFeedbackEntity.setWarningId(id);
            warningFeedbackMapper.insert(warningFeedbackEntity);
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void doneWarning(Long id, WarningDoneRequest request) {
        SimpleUserVO currentUser = AuthHelper.getNotNullSimpleUser();
        final WarningProcessEntity process = warningProcessMapper.getUserProcessByWarningId(id,
            currentUser.getUserId(), currentUser.getDeptId());
        WarningDoneVO warningDoneVO = new WarningDoneVO();
        warningDoneVO.setTime(LocalDateTime.now());
        warningDoneVO.setUserInfo(currentUser);
        warningDoneVO.setAttachments(request.getAttachments());
        warningDoneVO.setIsHandle(request.getIsHandle());
        warningDoneVO.setNotHandleReason(request.getNotHandleReason());
        warningDoneVO.setNotHandleDescription(request.getNotHandleDescription());
        warningDoneVO.setComment(request.getComment());
        warningDoneVO.setHandleMeasure(request.getHandleMeasure());
        process.setDone(warningDoneVO);
        process.setStatus(WarningStatusEnum.PROCESS_FINISH);
        warningProcessMapper.updateById(process);
        warningProcessMapper.updateProcessDoneByWarningId(id);

        //根据需要更新es
        warningOperateProxy.esUpdateDone(process);

        //如果处置措施是已抓获/已撤网，则自动撤控
        if (request.getHandleMeasure() != null && (request.getHandleMeasure() == 1 || request.getHandleMeasure() == 3)) {
            List<NameAndIdNumberVO> warningPersonNameAndIdNumber = warningTrackMapper.getWarningPersonNameAndIdNumber(id);
            Optional<WarningTrackEntity> warningTrack = warningTrackMapper.selectList(Wrappers.<WarningTrackEntity>lambdaQuery().eq(WarningTrackEntity::getWarningId, id)).stream().findAny();
            warningTrack.ifPresent(track -> {
                log.info("由于处置措施为{}, 将要针对人员{}，monitorId {} 进行撤控", request.getHandleMeasure(), warningPersonNameAndIdNumber.get(0).getIdNumber(), track.getMonitorId());
                String res = subscribeService.cancelWarningSubscribe(warningPersonNameAndIdNumber.get(0).getIdNumber(), track.getMonitorId());
                log.info("人员{}，monitorId {} 撤控结果{}", warningPersonNameAndIdNumber.get(0).getIdNumber(), track.getMonitorId(), res);
                monitorTargetRelationMapper.delete(Wrappers.<MonitorTargetRelationEntity>lambdaQuery()
                        .eq(MonitorTargetRelationEntity::getMonitorId, track.getMonitorId())
                        .eq(MonitorTargetRelationEntity::getTargetId, track.getPersonId()));
            });
        }
    }

    @Override
    public WarningDoneVO getWarningDone(Long id) {
        return warningProcessMapper.getWarningDone(id).orElse(new WarningProcessEntity()).getDone();
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void feedbackWarning(Long id, WarningFeedbackDetailVO feedbackVO) {
        // 更新状态为已反馈
        List<WarningNotifyEntity> notifyEntities = warningNotifyMapper.selectList(new QueryWrapper<WarningNotifyEntity>().eq("warning_id", id));
        List<Long> ids = notifyEntities.stream().map(WarningNotifyEntity::getProcessId).collect(Collectors.toList());
        List<WarningProcessEntity> processEntities = warningProcessMapper.selectList(new QueryWrapper<WarningProcessEntity>().in("id", ids));
        for (WarningProcessEntity process : processEntities) {
            process.setStatus(WarningStatusEnum.REPLY_FINISH);
            warningProcessMapper.updateById(process);
        }


        // 添加反馈记录
        SimpleUserVO currentUser = AuthHelper.getNotNullSimpleUser();
        WarningProcessEntity process = warningProcessMapper.getUserProcessByWarningId(id, currentUser.getUserId(),
                currentUser.getDeptId());
        WarningFeedbackEntity warningFeedbackEntity = new WarningFeedbackEntity();
        warningFeedbackEntity.setWarningId(id);
        warningFeedbackEntity.setProcessId(process.getId());
        warningFeedbackEntity.setType(WarningFeedbackTypeEnum.FEEDBACK);
        warningFeedbackEntity.setFeedback(feedbackVO);
        warningFeedbackMapper.insert(warningFeedbackEntity);

        //根据需要更新es
        //process签收状态合并，所以只取一条的状态和warning_id为准
        warningOperateProxy.esUpdateReply(processEntities.get(0));
        // 请求布控预警指令信息反馈接口
        WarningEntity warningEntity = warningMapper.selectById(id);
        if (Objects.nonNull(warningEntity) && Objects.nonNull(warningEntity.getWarningPlatform()) && warningEntity.getWarningPlatform() == 2) {
            warningFeedbackPush(id, feedbackVO.getContent(), warningFeedbackEntity.getId());
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteWarningFeedback(Long id) {
        warningFeedbackMapper.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateWarningFeedback(Long id, WarningFeedbackDetailVO feedbackVO) {
        WarningFeedbackEntity warningFeedbackEntity = warningFeedbackMapper.selectById(id);
        if (Objects.nonNull(warningFeedbackEntity)) {
            warningFeedbackEntity.setFeedback(feedbackVO);
            warningFeedbackMapper.updateById(warningFeedbackEntity);
        }

    }

    @Override
    public void esSync(Long warningId) {
        WarningEntity warningEntity = warningMapper.selectById(warningId);
        WarningTrackEntity warningTrackEntity = warningTrackMapper.selectTrackByWarningId(warningId);
        List<WarningNotifyEntity> notifyEntityList = warningNotifyMapper.selectByWarningId(warningId);
        warningOperateProxy.esInsert(warningTrackEntity, warningEntity, notifyEntityList);
    }

    @Override
    public void pushWarningActionMessage(Long processId, MonitorLevelEnum level, WarningStatusEnum status) {
        //除蓝色预警外，开始签收计时
        if (level.equals(MonitorLevelEnum.BLUE)) {
            return;
        }
        ScheduleMessageVO message = new ScheduleMessageVO();
        message.setRelatedId(processId);
        message.setModule(OperateModule.WARNING);
        //待签收状态，计时签收
        if (status.equals(WarningStatusEnum.WAITING_SIGN)) {
            message.setOperation(DelayMessageTypeEnum.WARNING_SIGN);
            message.setTimeLimit(LocalDateTime.now().plusHours(warningProperties.getSignLimit()));
            scheduleService.subscribeDelayJob(message);
        }
        //已签收状态，计时反馈
        else if (status.equals(WarningStatusEnum.SIGN_FINISH)) {
            message.setOperation(DelayMessageTypeEnum.WARNING_REPLY);
            message.setTimeLimit(LocalDateTime.now().plusHours(warningProperties.getReplyLimit()));
            scheduleService.subscribeDelayJob(message);
        }
    }

    private void warningFeedbackPush(Long warningId, String content, Long primaryId) {
        if (JsonUtil.isValidJson(content)) {
            WarningFeedbackHandleVO feedbackHandleVO = JSON.parseObject(content, WarningFeedbackHandleVO.class);
            cloudControlPushHelper.warningFeedbackPush(feedbackHandleVO, warningId, primaryId);
        }
    }
}
