package com.trs.police.control.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 布控任务人员表
 *
 * <AUTHOR>
 * @date 2024/12/26
 */
@Data
@NoArgsConstructor
@TableName(value = "t_control_task_person")
public class ControlTaskPerson extends AbstractBaseEntity {
    /**
     * 布控任务id
     */
    private Long taskId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String idNumber;

    /**
     * 地址
     */
    private String address;

    /**
     * 问题属地
     */
    private String problemLocation;

    /**
     * 信访形式
     */
    private String petitionType;

    /**
     * 布控状态：1:未布控，2：已布控
     */
    private Integer controlStatus;

    /**
     * 管控单位id
     */
    private Long controlDeptId;
}
