package com.trs.police.control.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 *聚集预警计算相关配置
 *
 * <AUTHOR>
 * @since 2025/4/18 10:06
 */
@Component
@ConfigurationProperties(prefix = GatheringWarningProperties.PREFIX)
@Getter
@Setter
public class GatheringWarningProperties {

    public static final String PREFIX = "com.trs.gather.warning";

    private Boolean enabled = false;

    /**
     * 定时调度频率 默认一天执行一次
     */
    private String cron = "0 0 1 * * ?";

    /**
     * 计算结束时间
     */
    private String endTime;

    /**
     * 时间范围 默认60分钟
     */
    private Integer timeScopeMinutes = 60;

    /**
     * 滑动时间间隔(默认5分钟)
     */
    private Integer slideTimeWindowMinutes = 5;

    /**
     * 窗口时间
     */
    private Integer timeWindowMinutes = 30;

    /**
     * 同一个人至少有两条轨迹，以表示在感知源滞留
     */
    private Integer minTrackCount = 2;

    /**
     * 至少要有两人聚集
     */
    private Integer minGatherPersonCount = 2;

    /**
     * 命中群体的人数阈值
     */
    private Double gatheringRatio = 0.5;

    private String topic = "subscription_group_ys";

    private String userName = "ys";

    private String modelName = "聚集预警";

}
