package com.trs.police.control.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.common.base.PreConditionCheck;
import com.trs.common.reflect.ClassUtils;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.constant.enums.MonitorStatusEnum;
import com.trs.police.common.core.dto.GroupWarningDTO;
import com.trs.police.common.core.dto.WarningDTO;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.entity.WarningEntity;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.control.constant.InductiveMonitorConstant;
import com.trs.police.control.domain.entity.monitor.CareMonitorEntity;
import com.trs.police.control.domain.vo.ControlInfo;
import com.trs.police.control.domain.vo.care.CareMonitorInitialeVO;
import com.trs.police.control.domain.vo.care.CareMonitorMessage;
import com.trs.police.control.domain.vo.warning.CareMonitorResumeResult;
import com.trs.police.control.handler.care.ICareMonitorHandler;
import com.trs.police.control.mapper.CareMonitorMapper;
import com.trs.police.control.service.CareMonitorService;
import com.trs.police.control.service.SubscribeService;
import com.trs.web.builder.util.BeanFactoryHolder;
import io.vavr.Tuple;
import io.vavr.control.Either;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.trs.common.utils.TimeUtils.YYYYMMDD_HHMMSS2;

/**
 * 关注监控实现类
 */
@Slf4j
@Service
public class CareMonitorServiceImpl implements CareMonitorService {

    @Resource
    SubscribeService subscribeService;

    @Resource
    private CareMonitorMapper careMonitorMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initiate(List<CareMonitorInitialeVO> careMonitorInitialeVoS) throws Exception {

        if (careMonitorInitialeVoS == null || careMonitorInitialeVoS.isEmpty()) {
            return;
        }

        //检查传入的handler实现是否可能是非ICareMonitorHandler实现
        Boolean isNotCareMonitorHandler = careMonitorInitialeVoS.stream().anyMatch(vo -> {
            String className = vo.getModuleCareMonitorHandlerClassName();
            Either<Throwable, Boolean> isHandlerImplement = Try.of(() -> {
                Class<?> handlerClass = Class.forName(className);
                return ClassUtils.isCompatible(handlerClass, ICareMonitorHandler.class);
            }).toEither();
            return isHandlerImplement.isLeft() || !isHandlerImplement.get();
        });
        PreConditionCheck.checkArgument(!isNotCareMonitorHandler,
            String.format("%s不是ICareMonitorHandler的实现，请确认问题",
                Try.of(() -> careMonitorInitialeVoS.get(0).getModuleCareMonitorHandlerClassName()).getOrElse("")));

        //对已经关注的对象更新器Handler
        List<CareMonitorEntity> existsEntities = findExistsAndUpdateHandler(careMonitorInitialeVoS);
        List<String> existsCertiValue = existsEntities.stream().map(CareMonitorEntity::getCertificateValue)
            .collect(Collectors.toList());

        //去掉已经排重的
        List<CareMonitorInitialeVO> shouldBeMonitored = careMonitorInitialeVoS.stream()
            .filter(vo -> !existsCertiValue.contains(vo.getCertificateValue())).collect(Collectors.toList());

        //保存数据库并将新增订阅和已有订阅返回给handler
        try {
            for (CareMonitorInitialeVO vo : shouldBeMonitored) {
                setMonitorConfigId(vo);
                CareMonitorEntity monitorEntity = vo.toCareMonitorEntity();
                careMonitorMapper.insert(monitorEntity);
                subscribeService.sendWarningSubscribe(monitorEntity);
                vo.getCareMonitorHandlerInstance().onSubscribeSuccessful(Optional.of(monitorEntity));
            }
        } catch (Exception ex) {
            log.error("订阅失败！原因为：", ex);
            throw new Exception(String.format("订阅失败！原因为：%s", ex.getCause().getMessage()), ex);
        }
    }

    private void setMonitorConfigId(CareMonitorInitialeVO vo) {
        //[{"relatedType":"xiansuo","monitorConfigId":48}]
        String config = BeanFactoryHolder.getEnv().getProperty("com.trs.control.inductiveBuKong.config");
        if (StringUtils.isEmpty(config)){
            vo.setMonitorConfigId(-1L);
            return;
        }
        //获取根节点得默认风险标签
        List<JSONObject> jsonObjectList = JSON.parseArray(config, JSONObject.class);
        Optional<JSONObject> configObject = jsonObjectList.stream().filter(e -> e.getString("relatedType").equals(vo.getRelatedType()))
                .findFirst();
        if (configObject.isPresent()){
            JSONObject jsonObject = configObject.get();
            vo.setMonitorConfigId(jsonObject.getLong("monitorConfigId"));
        };

    }

    /**
     * 完成预警消息的消费工作。预警消息在CareMonitorService中主要是解析后通过ICareMonitorHandler提交给各个业务模块消费
     *
     * @param controlInfo controlInfo
     * @param warningInfo warningInfo
     * @param track       track
     * @param warning     warning
     * @param dto         dto
     * @return {@link CareMonitorResumeResult}
     */
    @Override
    public CareMonitorResumeResult consumerMonitorMessage(ControlInfo controlInfo, ControlInfo.WarningInfo warningInfo,
                                                          GroupWarningDTO.Track track, WarningEntity warning, WarningDTO dto) {

        CareMonitorEntity entity = PreConditionCheck.checkNotNull(careMonitorMapper.selectById(controlInfo.getId()));

        entity.getHandlerClassName().forEach(handlerClassName -> {
            ICareMonitorHandler careMonitorHandler = Try.of(
                () -> CareMonitorInitialeVO.getCareMonitorHandlerInstance(handlerClassName)).getOrNull();
            if (careMonitorHandler == null) {
                log.warn("无法通过{}获得ICareMonitorHandler示例，ICareMonitorHandler的ClassName为{}，舍弃该预警。",
                    controlInfo.getId(), handlerClassName);
            }

            //拼装CareMonitorMessage并交由业务系统自行处理
            CareMonitorMessage message = new CareMonitorMessage();
            message.setCareMonitorId(controlInfo.getId());
            message.setWarningInfo(warningInfo);
            message.setTrack(track);
            message.setWarning(warning);
            message.setWarningDto(dto);
            careMonitorHandler.doWarningMessage(message);
        });
        CareMonitorResumeResult resumeResult = new CareMonitorResumeResult();
        resumeResult.setEntity(entity);
        return resumeResult;
    }

    @Override
    public void careMonitorByHand(List<Long> ids) {
        List<CareMonitorEntity> careMonitorEntities = careMonitorMapper.selectList(new QueryWrapper<CareMonitorEntity>()
                .in("id", ids).eq("related_type","xiansuo").eq("monitor_status",MonitorStatusEnum.MONITORING.getCode()));
        for (CareMonitorEntity careMonitorEntity : careMonitorEntities) {
            careMonitorEntity.setSubscribeStartTime(TimeUtils.stringToString(careMonitorEntity.getSubscribeStartTime()
                    ,YYYYMMDD_HHMMSS2));
            careMonitorEntity.setSubscribeEndTime(TimeUtils.stringToString(careMonitorEntity.getSubscribeEndTime()
                    ,YYYYMMDD_HHMMSS2));
            careMonitorEntity.setUpdateTime(LocalDateTime.now());
            careMonitorMapper.updateById(careMonitorEntity);
            subscribeService.sendWarningSubscribe(careMonitorEntity);
        }

    }

    /**
     * 查询已经存在的关注监控记录，并更新他们的handler，并返回那些已经存在的记录
     *
     * @param vos vos
     * @return 结果
     */
    private List<CareMonitorEntity> findExistsAndUpdateHandler(List<CareMonitorInitialeVO> vos) {
        //按照证件类型+证件号码+handler的方式进行group by。这里主要是为了更新做准备
        Map<String, List<CareMonitorInitialeVO>> careMonitorKeyAndList = vos.stream()
            .map(vo -> Tuple.of(String.format("%s-%s-%s",
                    vo.getCertificateType().getCode(), vo.getCertificateValue(), vo.getModuleCareMonitorHandlerClassName()),
                vo))
            .reduce(
                new HashMap<String, List<CareMonitorInitialeVO>>((int) (vos.size() / 0.75) + 1),
                (map, tuple) -> {
                    List<CareMonitorInitialeVO> newList = map.getOrDefault(tuple._1, new ArrayList<>());
                    newList.add(tuple._2);
                    map.put(tuple._1, newList);
                    return map;
                },
                (map1, map2) -> {
                    Map<String, List<CareMonitorInitialeVO>> map = Stream.concat(map1.entrySet().stream(),
                            map2.entrySet().stream())
                        .collect(
                            Collectors.toMap(entry -> entry.getKey(), entry -> entry.getValue(), (list1, list2) -> {
                                list1.addAll(list2);
                                return list1;
                            }));
                    return (HashMap<String, List<CareMonitorInitialeVO>>) map;
                });
        //是否存在查询需要根据证件号码+证件类型+handler来确认
        careMonitorKeyAndList.keySet().stream().forEach(key -> {
            String handlerName = key.split("-")[2];
            careMonitorMapper.updateExistsCareMonitor(handlerName, careMonitorKeyAndList.get(key));
        });
        if (!vos.isEmpty()) {
            List<String> handlers = vos.stream().map(e -> e.getModuleCareMonitorHandlerClassName()).distinct()
                    .collect(Collectors.toList());
            List<CareMonitorEntity> careMonitorEntities = handlers.contains(InductiveMonitorConstant.inductiveHandler)
                    //selectExistsInductiveCareMonitor查询有缺陷，目前集合的relatedDataId都一样
                    //才能这么查询
                    ? careMonitorMapper.selectExistsInductiveCareMonitor(vos)
                    : careMonitorMapper.selectExistsCareMonitor(vos);
            if (handlers.contains(InductiveMonitorConstant.inductiveHandler)){
                updateInductiveCareMonitor(vos, careMonitorEntities);
            }
            return careMonitorEntities;
        } else {
            return new ArrayList<>();
        }
    }

    private void updateInductiveCareMonitor(List<CareMonitorInitialeVO> vos, List<CareMonitorEntity> careMonitorEntities) {
        Map<String, CareMonitorInitialeVO> voMap = vos.stream().collect(Collectors
                .toMap(CareMonitorInitialeVO::getCertificateValue, e -> e));
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        for (CareMonitorEntity entity : careMonitorEntities) {
            CareMonitorInitialeVO vo = voMap.get(entity.getCertificateValue());
            vo = Objects.nonNull(vo) ? vo : new CareMonitorInitialeVO();
            entity.setMonitorStatus(MonitorStatusEnum.MONITORING.getCode());
            entity.setSubscribeStartTime(TimeUtils.stringToString(vo.getSubscribeStartTime(),YYYYMMDD_HHMMSS2));
            entity.setSubscribeEndTime(TimeUtils.stringToString(vo.getSubscribeEndTime(),YYYYMMDD_HHMMSS2));
            entity.setUpdateTime(LocalDateTime.now());
            entity.setUpdateUserId(currentUser.getId());
            entity.setUpdateDeptId(currentUser.getDeptId());
            careMonitorMapper.updateById(entity);
            try {
                vo.getCareMonitorHandlerInstance().onSubscribeSuccessful(Optional.of(entity));
                subscribeService.sendWarningSubscribe(entity);
            } catch (ClassNotFoundException e) {
                log.info("{},无感布控后续操作失败",entity.getCertificateValue());
                log.error(e.getMessage());
            }
        }
    }

    private String getCareMonitorKey(CareMonitorInitialeVO vo) {
        return String.format("care_%s-%s-%s", vo.getCreateDeptId(), vo.getCertificateType().getCode(),
            vo.getCertificateValue());
    }
}
