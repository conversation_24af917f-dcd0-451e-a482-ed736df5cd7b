package com.trs.police.control.service;

import com.trs.police.common.core.params.ExportParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.control.domain.vo.RegularStatisticVO;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
 * 常控统计
 *
 * <AUTHOR>
 */
public interface RegularStatisticService {

    /**
     * 查询常控统计
     *
     * @param request 参数
     * @return 结果
     */
    PageResult<RegularStatisticVO> regularStatistics(ListParamsRequest request);

    /**
     * 常控统计导出
     *
     * @param httpServletResponse response
     * @param params 参数
     * @throws IOException 异常
     */
    void regularStatisticsExport(HttpServletResponse httpServletResponse, ExportParams params) throws IOException;
}
