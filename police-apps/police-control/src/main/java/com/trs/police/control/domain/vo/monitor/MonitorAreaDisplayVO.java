package com.trs.police.control.domain.vo.monitor;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.trs.police.control.domain.vo.warning.WarningAreaVO;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/10/11 16:43
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MonitorAreaDisplayVO extends MonitorVO {

    /**
     * 布控区域信息
     */
    private List<WarningAreaVO> monitorArea;
    /**
     * 布控区域人员筛选条件
     */
    private MonitorAreaPersonListFilter monitorPerson;

    public MonitorAreaDisplayVO(MonitorConfigVO monitorConfig,
        @Valid @NotNull(message = "布控预警信息不能为空！") WarningInfoVO warningInfo,
        @Valid @NotNull(message = "布控信息不能为空！") MonitorInfoVO monitorInfo,
        List<WarningAreaVO> monitorArea, MonitorAreaPersonListFilter monitorPerson) {
        super(monitorConfig, warningInfo, monitorInfo, null);
        this.monitorArea = monitorArea;
        this.monitorPerson = monitorPerson;
    }

    /**
     * 人员筛选参数翻译
     */
    @Data
    public static class MonitorAreaPersonListFilter{
        private String personTag;
        private String dept;
        private String district;
        private ObjectNode listParams;
    }
}
