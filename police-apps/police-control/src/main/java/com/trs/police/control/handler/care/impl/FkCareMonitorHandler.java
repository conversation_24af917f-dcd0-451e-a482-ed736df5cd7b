package com.trs.police.control.handler.care.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.dto.GroupWarningDTO;
import com.trs.police.common.core.entity.WarningEntity;
import com.trs.police.common.core.utils.TimeUtil;
import com.trs.police.control.config.WarningModalConfig;
import com.trs.police.control.constant.WarningFkrxyjConstant;
import com.trs.police.control.constant.enums.GjxxSourceEnum;
import com.trs.police.control.domain.entity.fkrxyj.FkP60Entity;
import com.trs.police.control.domain.entity.fkrxyj.PersonRelateToFkEntity;
import com.trs.police.control.domain.entity.fkrxyj.WarningFkrxyjEntity;
import com.trs.police.control.domain.entity.monitor.CareMonitorEntity;
import com.trs.police.control.domain.vo.ControlInfo;
import com.trs.police.control.domain.vo.care.CareMonitorMessage;
import com.trs.police.control.handler.care.ICareMonitorHandler;
import com.trs.police.control.mapper.FkP60Mapper;
import com.trs.police.control.mapper.MonitorWarningModelMapper;
import com.trs.police.control.mapper.PersonRelateToFkMapper;
import com.trs.police.control.mapper.WarningFkrxyjMapper;
import com.trs.police.control.service.WarningProcessService;
import com.trs.police.control.service.warning.FkWarningUpdater;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.trs.common.utils.TimeUtils.YYYYMMDD_HHMMSS;

/**
 * 反恐专题关注监控Handler
 *
 * <AUTHOR>
 * @date 2024年05月08日 18:43
 */
@Component
public class FkCareMonitorHandler implements ICareMonitorHandler {

    private static PersonRelateToFkMapper personRelateToFkMapper;

    private static WarningModalConfig warningModalConfig;

    private static FkP60Mapper fkP60Mapper;

    private static MonitorWarningModelMapper monitorWarningModelMapper;

    private static WarningFkrxyjMapper warningFkrxyjMapper;

    private static FkWarningUpdater fkWarningUpdater;

    private static WarningProcessService warningProcessService;

    /**
     * setter
     *
     * @param personRelateToFkMapper personRelateToFkMapper
     */
    @Autowired
    public void setPersonRelateToFkMapper(PersonRelateToFkMapper personRelateToFkMapper) {
        FkCareMonitorHandler.personRelateToFkMapper = personRelateToFkMapper;
    }

    /**
     * setter
     *
     * @param warningModalConfig warningModalConfig
     */
    @Autowired
    public void setWarningModalConfig(WarningModalConfig warningModalConfig) {
        FkCareMonitorHandler.warningModalConfig = warningModalConfig;
    }

    /**
     * setter
     *
     * @param fkP60Mapper fkP60Mapper
     */
    @Autowired
    public void setFkP60Mapper(FkP60Mapper fkP60Mapper) {
        FkCareMonitorHandler.fkP60Mapper = fkP60Mapper;
    }

    /**
     * setter
     *
     * @param monitorWarningModelMapper monitorWarningModelMapper
     */
    @Autowired
    public void setRegularMonitorConfigMapper(MonitorWarningModelMapper monitorWarningModelMapper) {
        FkCareMonitorHandler.monitorWarningModelMapper = monitorWarningModelMapper;
    }

    /**
     * setter
     *
     * @param warningFkrxyjMapper warningFkrxyjMapper
     */
    @Autowired
    public void setWarningFkrxyjMapper(WarningFkrxyjMapper warningFkrxyjMapper) {
        FkCareMonitorHandler.warningFkrxyjMapper = warningFkrxyjMapper;
    }

    /**
     * setter
     *
     * @param fkWarningUpdater fk
     */
    @Autowired
    public static void setFkWarningUpdater(FkWarningUpdater fkWarningUpdater) {
        FkCareMonitorHandler.fkWarningUpdater = fkWarningUpdater;
    }

    /**
     * setter
     *
     * @return warningProcessService
     */
    @Autowired
    public static WarningProcessService getWarningProcessService() {
        return warningProcessService;
    }

    @Override
    public void onSubscribeSuccessful(Optional<CareMonitorEntity> newSubscribe) {
        //更新fk人员为已经关注监控状态
        if (newSubscribe.isPresent()) {
            CareMonitorEntity careMonitorEntity = newSubscribe.get();

            PersonRelateToFkEntity personRelateToFk = personRelateToFkMapper.getByIdCard(careMonitorEntity.getCertificateValue());
            if (Objects.nonNull(personRelateToFk)) {
                personRelateToFk.setCareMonitorStatus(WarningFkrxyjConstant.CARE_STATUS_Y);
                personRelateToFkMapper.updateById(personRelateToFk);
            }
        }
    }

    @Override
    public Long getDefaultMonitorConfigId() {
        return warningModalConfig.fkCareMonitorConfigId;
    }

    @Override
    public void doWarningMessage(CareMonitorMessage message) {
        //正常预警实体消息
        WarningEntity warning = message.getWarning();
        //轨迹
        GroupWarningDTO.Track track = message.getTrack();

        //预警信息
        ControlInfo.WarningInfo warningInfo = message.getWarningInfo();
        //根据命中的模型id来确认是哪个模型名
        List<Long> modelIds = warning.getModelId();
        List<String> sensitiveAreaModalIds = warningInfo.getModelIds().stream().map(String::valueOf).collect(Collectors.toList());
        //敏感区域是否命中
        List<Long> sensitiveIntersectionList = getIntersection(modelIds, sensitiveAreaModalIds);
        if (!sensitiveIntersectionList.isEmpty()) {
            WarningFkrxyjEntity entity = buildFkrxyj(track, warning);
            if (Objects.isNull(entity)) {
                return;
            }
            entity.setWarningModel(WarningFkrxyjConstant.WANDERING_ON_SENSITIVE);
            fkWarningUpdater.update(entity);
            //根据模型id获取下面的重点区域id
            List<Long> areaIds = monitorWarningModelMapper.getAreaIdByModelIds(sensitiveIntersectionList);
            //命中的不是重点区域预警模型
            if (!CollectionUtils.isEmpty(areaIds)) {
                entity.setAreaId(JSON.toJSONString(areaIds));
                warningFkrxyjMapper.insert(entity);
            }
            // 保存到es
            warningProcessService.saveStrxToEs(Arrays.asList(entity), GjxxSourceEnum.FK_MONITOR);
        }
    }

    private WarningFkrxyjEntity buildFkrxyj(GroupWarningDTO.Track track
            , WarningEntity warning) {

        //根据身份证号获取fk人员
        QueryWrapper<FkP60Entity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id_card", track.getIdentifier());
        List<FkP60Entity> fkP60Entities = fkP60Mapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(fkP60Entities)) {
            return null;
        }
        FkP60Entity personRelateToFk = fkP60Entities.get(0);
        WarningFkrxyjEntity entity = new WarningFkrxyjEntity();

        entity.setName(personRelateToFk.getName());
        entity.setNation(entity.getName().contains("·")
                ? WarningFkrxyjConstant.NATION_CODE_WZ : WarningFkrxyjConstant.NATION_CODE_HZ);
        entity.setWarningLevel(warning.getWarningLevel());
        entity.setAlertType(warning.getWarningType());
        entity.setFacePhoto("/oss/photo/" + personRelateToFk.getIdCard());
        //entity.setCaptureTime(warning.getActivityTime());
        //entity.setCaptureAddress(warning.getActivityAddress());
        entity.setIdCard(personRelateToFk.getIdCard());
        entity.setDate(TimeUtil.localDateTimeToString(warning.getWarningTime(), ""));
        //经纬度
        entity.setLatitude(track.getSensingMessage().getLatitude() + "");
        entity.setLongitude(track.getSensingMessage().getLongitude() + "");
        //感知源所在派出所
        Map<String, Object> trackDetail = track.getTrackDetail();
        if (trackDetail.get("pcsmc_gxdw") != null){
            entity.setGroup(trackDetail.get("pcsmc_gxdw").toString());
        }
        if (trackDetail.get("dzmc_cjsb") != null) {
            entity.setCaptureAddress(trackDetail.get("dzmc_cfd").toString());
        }
        if (trackDetail.get("bhsj") != null) {
            String time = TimeUtils.stringToString(trackDetail.get("bhsj").toString(), YYYYMMDD_HHMMSS);
            entity.setCaptureTime(TimeUtil.stringToLocalDateTime(time, YYYYMMDD_HHMMSS));
        }
        entity.setCreateTime(LocalDateTime.now());
        entity.setSimilarity("0.99");
        entity.setLibName("重点人员库");
        entity.setPhoto("/oss/photo/" + personRelateToFk.getIdCard());
        entity.setSuspectName(personRelateToFk.getName());
        entity.setSuspectPhoto("/oss/photo/" + personRelateToFk.getIdCard());
        return entity;
    }

    /**
     * 取交集
     *
     * @param longList   long类型的集合
     * @param stringList string类型的集合
     * @return 交集
     */
    public static List<Long> getIntersection(List<Long> longList, List<String> stringList) {
        List<Long> stringAsLongList = stringList.stream()
                .map(Long::valueOf)
                .collect(Collectors.toList());

        return longList.stream()
                .filter(stringAsLongList::contains)
                .collect(Collectors.toList());
    }
}
