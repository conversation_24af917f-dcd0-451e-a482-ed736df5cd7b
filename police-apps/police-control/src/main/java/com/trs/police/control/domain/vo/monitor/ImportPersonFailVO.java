package com.trs.police.control.domain.vo.monitor;

import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * 布控人员导入
 *
 * <AUTHOR> yanghy
 * @date : 2022/8/25 16:17
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ImportPersonFailVO extends ImportPersonVO {

    /**
     * 序号
     */
    private Integer index;
    /**
     * 失败原因
     */
    private String reason;


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        ImportPersonFailVO that = (ImportPersonFailVO) o;
        return Objects.equals(index, that.index) && Objects.equals(reason, that.reason);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), index, reason);
    }
}
