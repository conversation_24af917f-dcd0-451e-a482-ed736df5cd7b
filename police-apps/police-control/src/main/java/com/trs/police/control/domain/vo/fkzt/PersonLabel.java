package com.trs.police.control.domain.vo.fkzt;

import com.trs.police.common.core.vo.profile.LabelVO;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 人员标签
 */
@Data
@NoArgsConstructor
public class PersonLabel {

    /**
     * 标签编码
     */
    private Long id;

    /**
     * 标签名称
     */
    private String name;

    public PersonLabel(LabelVO labelVO) {
        this.id = labelVO.getId();
        this.name = labelVO.getName();
    }
}
