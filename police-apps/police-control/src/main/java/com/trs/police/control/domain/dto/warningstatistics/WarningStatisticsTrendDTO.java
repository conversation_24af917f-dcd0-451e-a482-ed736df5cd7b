package com.trs.police.control.domain.dto.warningstatistics;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 预警趋势DTO
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class WarningStatisticsTrendDTO extends AbstractDTO {

    /**
     * 趋势数据
     */
    private List<Trend> trend;

    /**
     * 趋势数据
     */
    @Data
    public static class Trend {

        /**
         * 日期
         */
        private String date;

        /**
         * 临控数量
         */
        private Integer monitorCount;

        /**
         * 常控数量
         */
        private Integer regularCount;
    }

    public WarningStatisticsTrendDTO(List<Trend> trend) {
        this.trend = trend;
    }
}
