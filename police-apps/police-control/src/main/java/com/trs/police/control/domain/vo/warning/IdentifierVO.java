package com.trs.police.control.domain.vo.warning;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 布控预警人员信息
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IdentifierVO implements Serializable {

    private static final long serialVersionUID = 7700672486758721440L;
    /**
     * 标识符
     */
    private String identifier;

    /**
     * 标识符类型
     */
    private Integer identifierType;

    /**
     * 人像base64编码
     */
    private String picture;

    /**
     * 其他信息
     */
    private CustomInfo customInfo;

    public IdentifierVO(String identifier, Integer identifierType) {
        this.identifier = identifier;
        this.identifierType = identifierType;
    }

    public IdentifierVO(String identifier, Integer identifierType, String picture) {
        this(identifier, identifierType);
        this.picture = picture;
    }

    @Override
    public String toString() {
        return "IdentifierVO{" +
                "identifier='" + identifier + '\'' +
                ", identifierType=" + identifierType +
                ", picture='" + picture + '\'' +
                ", customInfo=" + customInfo.toString() +
                '}';
    }
}
