package com.trs.police.control.service;

import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.control.domain.vo.regular.RegularMonitorConfigDisplayVO;
import com.trs.police.control.domain.vo.regular.RegularMonitorConfigListVO;
import com.trs.police.control.domain.vo.regular.RegularMonitorConfigVO;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/6 15:37
 */
public interface RegularMonitorConfigService {

    /**
     * 创建预警配置
     *
     * @param regularMonitorConfigVO {@link RegularMonitorConfigVO}
     * @return id
     */
    Long createRegularConfig(RegularMonitorConfigVO regularMonitorConfigVO);

    /**
     * 编辑预警配置
     *
     * @param regularMonitorConfigVO {@link RegularMonitorConfigVO}
     */
    void updateRegularConfig(RegularMonitorConfigVO regularMonitorConfigVO);

    /**
     * 查看预警配置
     *
     * @param id id
     * @return {@link RegularMonitorConfigVO}
     */
    RegularMonitorConfigVO getRegularConfigEditInfoById(Long id);

    /**
     * 获取预警配置列表
     *
     * @param request 请求参数
     * @return {@link RegularMonitorConfigListVO}
     */
    PageResult<RegularMonitorConfigListVO> getRegularConfigList(ListParamsRequest request);

    /**
     * 批量更新预警配置启用状态
     *
     * @param ids          预警配置id
     * @param enableStatus 启用状态
     */
    void updateRegularConfigEnableStatus(List<Long> ids, Boolean enableStatus);

    /**
     * 批量删除预警配置
     *
     * @param modelIds 预警配置id
     */
    void deleteRegularConfig(List<Long> modelIds);

    /**
     * 预警配置详情
     *
     * @param id 预警配置id
     * @return {@link RegularMonitorConfigDisplayVO}
     */
    RegularMonitorConfigDisplayVO getRegularConfigById(Long id);
}
