package com.trs.police.control.task;

import com.trs.police.common.core.constant.enums.ControlTypeEnum;
import com.trs.police.common.core.constant.enums.MonitorLevelEnum;
import com.trs.police.common.core.entity.WarningEntity;
import com.trs.police.control.domain.dto.SilenceWarningDTO;
import com.trs.police.control.domain.entity.warning.WarningTrackEntity;
import com.trs.police.control.mapper.MonitorMapper;
import com.trs.police.control.mapper.MonitorWarningModelMapper;
import com.trs.police.control.mapper.WarningMapper;
import com.trs.police.control.mapper.WarningTrackMapper;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 静默预警任务
 */

@Slf4j
@Component
public class SilenceWarningTask {

    @Resource
    private WarningMapper warningMapper;

    @Resource
    private WarningTrackMapper warningTrackMapper;

    @Resource
    private MonitorMapper monitorMapper;

    @Resource
    private MonitorWarningModelMapper monitorWarningModelMapper;

    /**
     * 静默预警任务
     */
    @Scheduled(cron = "0 0 0 * * *")
    public void execute() {
        String enabled = BeanFactoryHolder.getEnv().getProperty("com.trs.police.schedule.silenceWarning.enabled", "false");
        if (enabled.equals("true")) {
            log.info("开始执行静默预警任务");
            long startTime = System.currentTimeMillis();
            LocalDateTime now = LocalDateTime.now();
            Long modelId = monitorWarningModelMapper.getIdByTitle("静默");
            List<SilenceWarningDTO> silenceWarnings = monitorMapper.selectMonitorByWarningModelId(modelId);
            for (SilenceWarningDTO dto : silenceWarnings) {
                WarningTrackEntity newestTrack = warningTrackMapper.getNewestTrackByPersonId(dto.getTargetId());
                //没有轨迹，或最新轨迹活动时间距当前时间超出30天，则触发静默预警
                if (Objects.isNull(newestTrack) || Duration.between(newestTrack.getActivityTime(), LocalDateTime.now()).toDays() > 30) {
                    //新增一条warning记录
                    WarningEntity warningEntity = new WarningEntity();
                    warningEntity.setActivityTime(LocalDateTime.now());
                    String addr = null;
                    String place = null;
                    if (Objects.nonNull(newestTrack) && StringUtils.isNotBlank(newestTrack.getAddress())) {
                        addr = "最近出现地址：" + newestTrack.getAddress();
                    }
                    if (Objects.nonNull(newestTrack) && StringUtils.isNotBlank(newestTrack.getPlace())) {
                        place = "最近出现地点：" + newestTrack.getPlace();
                    }
                    warningEntity.setActivityAddress(addr);
                    warningEntity.setCreateTime(now);
                    warningEntity.setModelId(List.of(modelId));
                    warningEntity.setWarningLevel(MonitorLevelEnum.BLUE);
                    warningEntity.setWarningTime(now);
                    warningEntity.setWarningType("silence");
                    warningEntity.setContent("静默预警");
                    warningEntity.setControlType(ControlTypeEnum.MONITOR);
                    warningEntity.setMonitorId(dto.getMonitorId());
                    log.info("产生了静默预警：{}", warningEntity);
                    warningMapper.insert(warningEntity);
                    //再新增一条warningTrack记录
                    WarningTrackEntity warningTrack = new WarningTrackEntity();
                    warningTrack.setWarningId(warningEntity.getId());
                    warningTrack.setIdentifierType(1);
                    warningTrack.setIdentifier(dto.getIdNumber());
                    warningTrack.setActivityTime(now);
                    warningTrack.setAddress(addr);
                    warningTrack.setPlace(place);
                    warningTrack.setDatasourceType("jmyj");
                    warningTrack.setPersonId(dto.getTargetId());
                    warningTrack.setMonitorId(dto.getMonitorId());
                    warningTrack.setControlType(ControlTypeEnum.MONITOR);
                    warningTrackMapper.insert(warningTrack);
                }
            }
            log.info("静默预警任务执行完毕，耗时{}ms", System.currentTimeMillis() - startTime);
        }
    }
}
