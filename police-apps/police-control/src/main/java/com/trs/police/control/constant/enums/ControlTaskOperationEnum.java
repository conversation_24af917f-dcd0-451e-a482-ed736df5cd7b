package com.trs.police.control.constant.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/26 上午10:26
 * @description 布控任务操作枚举
 */
@Getter
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum ControlTaskOperationEnum {

    VIEW("1", "查看"),

    SIGN("2", "签收"),

    DELETE("3", "删除"),

    RECORD("4", "记录"),

    MONITOR("5", "布控"),

    ISSUE("6", "下发"),

    BACK("7", "退回"),

    ;

    private final String code;
    private final String name;

    ControlTaskOperationEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 获取布控任务列表的操作按钮
     *
     * @param needSignOperation 签收
     * @param needDeleteOperation 删除
     * @return 操作列表
     */
    public static List<ControlTaskOperationEnum> getListOperation(boolean needSignOperation, boolean needDeleteOperation) {
        return Stream.of(VIEW,
                needSignOperation ? SIGN : null,
                needDeleteOperation ? DELETE : null,
                RECORD
        ).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 获取布控任务详情的操作按钮
     *
     * @param needSignOperation 签收
     * @param needDeleteOperation 删除
     * @return 操作列表
     */
    public static List<ControlTaskOperationEnum> getDetailOperation(boolean needSignOperation, boolean needDeleteOperation) {
        return Stream.of(
                needSignOperation ? SIGN : null,
                needDeleteOperation ? DELETE : null,
                RECORD
        ).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 获取布控任务人员操作按钮
     *
     * @param needControlOperation 布控按钮
     * @param needIssueOperation 下发按钮
     * @param needBackOperation 回退按钮
     * @return 操作列表
     */
    public static List<ControlTaskOperationEnum> getControlPersonOperation(boolean needControlOperation, boolean needIssueOperation, boolean needBackOperation) {
        return Stream.of(
                needControlOperation ? MONITOR : null,
                needIssueOperation ? ISSUE : null,
                needBackOperation ? BACK : null
        ).filter(Objects::nonNull).collect(Collectors.toList());
    }
}
