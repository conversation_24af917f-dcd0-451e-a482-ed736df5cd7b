package com.trs.police.control.task.gathering;

import com.trs.data.flow.DataFlowBuilder;
import com.trs.data.reader.ReaderContext;
import com.trs.police.control.properties.GatheringWarningProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 聚集预警计算
 *
 * <AUTHOR>
 * @since 2025/4/17 20:06
 */
@Slf4j
@Component
public class GatheringWarningTask {

    private final GatheringWarningProperties properties;

    /**
     * 轨迹数据读取
     */
    private final TrackDataReader trackDataReader;

    /**
     * 感知源分组处理
     */
    private final SensingDeviceGroupByTrackProcessor sensingDeviceGroupByTrackProcessor;

    /**
     * 群体聚集处理
     */
    private final GroupIdGroupByTrackProcessor groupIdGroupByTrackProcessor;

    /**
     * 推送聚集预警
     */
    private final PushGatheringWarning pushGatheringWarning;

    private final Lock taskLock = new ReentrantLock();

    public final static String START_TIME = "startTime";

    public final static String END_TIME = "endTime";

    public final static String MONITOR_IDS = "monitorIds";

    public GatheringWarningTask(
            GatheringWarningProperties properties,
            TrackDataReader trackDataReader,
            PushGatheringWarning pushGatheringWarning,
            SensingDeviceGroupByTrackProcessor sensingDeviceGroupByTrackProcessor,
            GroupIdGroupByTrackProcessor groupIdGroupByTrackProcessor) {
        this.properties = properties;
        this.trackDataReader = trackDataReader;
        this.sensingDeviceGroupByTrackProcessor = sensingDeviceGroupByTrackProcessor;
        this.groupIdGroupByTrackProcessor = groupIdGroupByTrackProcessor;
        this.pushGatheringWarning = pushGatheringWarning;
    }

    /**
     * 聚集预警定时任务, 1小时执行一次
     */
    @Scheduled(cron = "${com.trs.gather.warning.cron:0 0 0/1 * * ?}")
    public void gatheringWarningTask() {
        try {
            if (properties.getEnabled()) {
                log.info("开始执行聚集预警定时任务");
                // 初始化数据读取上下文参数
                ReaderContext dataObtainContext = new ReaderContext();
                // 获取计算开始时间并转换为LocalDateTime
                LocalDateTime endTime = LocalDateTime.now();
                if (properties.getEndTime() != null) {
                    endTime = LocalDateTime.parse(properties.getEndTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                }
                // 计算开始时间
                LocalDateTime startTime = endTime.minusMinutes(properties.getTimeScopeMinutes());
                dataObtainContext.addProperty(START_TIME, startTime);
                dataObtainContext.addProperty(END_TIME, endTime);
                doProcess(dataObtainContext);
                log.info("聚集预警定时任务执行结束");
            }
        } catch (Exception e) {
            log.error("聚集预警定时任务执行失败", e);
        }
    }

    /**
     *开始聚集计算
     *
     * @param dataObtainContext 数据读取上下文
     */
    public void doProcess(ReaderContext dataObtainContext) {
        try {
            if (taskLock.tryLock()) {
                log.info("开始执行聚集预警定时任务");
                trackDataReader.setMyReadContext(dataObtainContext);
                DataFlowBuilder.<GatheringTrackContext, GatheringTrackContext>name("聚集预警任务")
                        // 获取轨迹数据
                        .reader(trackDataReader)
                        // 按照感知源分组
                        .processor(sensingDeviceGroupByTrackProcessor)
                        // 按照群体分组，并使用滑动窗口时间划分计算聚集
                        .processor(groupIdGroupByTrackProcessor)
                        .processor(pushGatheringWarning)
                        .build().execute();
                log.info("聚集预警任务执行结束");
            }
        } finally {
            taskLock.unlock();
        }
    }
}
