package com.trs.police.control.constant;

import com.trs.police.common.core.constant.enums.MonitorLevelEnum;

import java.util.HashMap;
import java.util.Map;

/**
 * 布控查询常量$
 *
 * <AUTHOR> yanghy
 * @date : 2022/8/25 9:56
 */
public class MonitorConstant {

    private MonitorConstant() {

    }

    /**
     * 全文
     */
    public static final String FULL_TEXT = "fullText";
    /**
     * 预警模型
     */
    public static final String WARNING_MODEL = "warningModel";
    /**
     * 预警时间
     */
    public static final String WARNING_TIME = "warningTime";
    /**
     * 是否开启短信通知
     */
    public static final String MESSAGE_TYPE_OPEN = "开启";
    /**
     * 是否开启短信通知
     */
    public static final String MESSAGE_TYPE_CLOSE = "关闭";
    /**
     * 我的
     */
    public static final String EXPORT_TYPE_MY = "my";
    /**
     * 布控标题前缀
     */
    public static final String FILE_PREFIX_MY = "我的布控_";
    /**
     * 布控标题前缀
     */
    public static final String FILE_PREFIX_ALL = "全部布控_";
    /**
     * 区域预警模型icon
     */
    public static final String AREA_ICON_URL = "area-warning-model.png";
    /**
     * 重点区域预警模型icon
     */
    public static final String IMPORTANT_AREA_ICON_URL = "important-warning-model.png";
    /**
     * 场所预警模型icon
     */
    public static final String PLACE_ICON_URL = "place-warning-model.png";
    /**
     * 布控人员批量导入模板module
     */
    public static final String MONITOR_PERSON_IMPORT_TEMPLATE = "monitorPersonImportTemplate";
    /**
     * admin用户
     */
    public static final String ADMIN = "admin";
    /**
     * 区域预警模型
     */
    public static final String MODEL_NAME_AREA = "区域预警模型";
    /**
     * 场所预警模型
     */
    public static final String MODEL_NAME_PLACE = "场所预警模型";
    /**
     * 添加布控失败异常提示
     */
    public static final String FAIL_TO_ADD_MONITOR = "新建布控失败！";
    /**
     * 布控不存在异常提示
     */
    public static final String MONITOR_NOT_EXISTS = "布控不存在！";
    /**
     * 不能删除异常提示
     */
    public static final String CANNOT_DELETE = "不能删除！";
    /**
     * 布控状态提示
     */
    public static final String CURRENT_MONITOR_STATUS = "当前布控状态为：";
    /**
     * 不能修改提示
     */
    public static final String CANNOT_MODIFY = ",不可修改！";
    /**
     * 更新失败异常提示
     */
    public static final String FAIL_TO_UPDATE = "更新失败！";
    /**
     * 布控级别不能修改提示
     */
    public static final String MONITOR_LEVEL_CANNOT_MODIFY = "布控级别不可修改！";
    /**
     * 不能撤控提示
     */
    public static final String CANNOT_CANCEL_MONITOR = "不可撤控！";
    /**
     * 修改失败提示
     */
    public static final String FAIL_TO_MODIFY = "修改失败！";
    /**
     * 不能撤审异常提示
     */
    public static final String CANNOT_CANCEL_APPLY = "不可撤销申请！";
    /**
     * 获取不了用户信息异常提示
     */
    public static final String CANNOT_FIND_USER_MESSAGE = "无法获取当前用户信息！";
    /**
     * 导入失败异常提示
     */
    public static final String FAIL_TO_IMPORT = "导入失败！";
    /**
     * 布控不存在异常提示
     */
    public static final String MONITOR = "布控";
    /**
     * 布控不存在异常提示
     */
    public static final String NONE_EXISTENT = ",不存在!";
    /**
     * 顶级部门
     */
    public static final String TOP_DEPT = "topDept";
    /**
     * 云墙布控级别映射
     */
    public static final Map<Integer, MonitorLevelEnum> YQ_BKJB_MAP = new HashMap<>();

    static {
        YQ_BKJB_MAP.put(10, MonitorLevelEnum.RED);
        YQ_BKJB_MAP.put(20, MonitorLevelEnum.YELLOW);
        YQ_BKJB_MAP.put(30, MonitorLevelEnum.BLUE);
    }

}
