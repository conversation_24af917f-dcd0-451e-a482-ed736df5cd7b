package com.trs.police.control.domain.entity.warning;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 预警未配置类型数据表(t_warning_noconfig_data)
 *
 * <AUTHOR>
 * @since 2023-11-06 14:04:37
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_warning_noconfig_data")
public class WarningNoconfigEntity {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 轨迹类型英文名
     */
    private String enName;
    /**
     * 轨迹类型中文名
     */
    private String cnName;
    /**
     * 消息数据
     */
    private String data;
}
