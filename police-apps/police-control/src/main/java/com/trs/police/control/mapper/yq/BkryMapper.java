package com.trs.police.control.mapper.yq;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.trs.police.control.domain.entity.yq.Bkry;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 人员档案查询
 *
 * <AUTHOR> yanghy
 * @date : 2022/10/9 18:03
 */
@DS("yq-oracle")
@Mapper
public interface BkryMapper {

    /**
     * 根据布控信息id获取布控人员信息
     *
     * @param bkxxIdList 布控信息id集合
     * @return 布控人员数据
     */
    List<Bkry> selectYqBkryList(@Param("bkxxIdList") List<String> bkxxIdList);
}
