package com.trs.police.control.task;

import com.trs.police.control.service.WarningFkrxyjService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;

/**
 * 处理商汤FK预警信息-只有满足指定时间范围内出现指定次数进行预警
 *
 * <AUTHOR>
 * @date 2024/01/09
 */
@Component
@Slf4j
@EnableScheduling
public class Fkrxyj4ProcessTask {

    @Autowired
    private WarningFkrxyjService warningFkrxyjService;

    /**
     * 处理商汤FK预警信息
     */
    //@Scheduled(fixedDelay = 60L, timeUnit = TimeUnit.MINUTES)
    public void execute() {
        log.info("开始执行历史未配置数据的定时任务");
        //必须用捕获异常，否则定时任务使用fixedDelay可能有定时任务永远停摆的bug
        try {
            warningFkrxyjService.dealFK4ProcessWarning(0L);
        } catch (Exception e) {
            log.error("定时任务二次处理FK数据出错！", e);
        }

        log.info("历史未配置数据的定时任务执行结束");
    }
}
