package com.trs.police.control.domain.entity.yq;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 本地重点人员实体类
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@TableName(value = "T_ALARM_CKRY")
public class Ckry {
    @TableId
    private String id;
    @TableField(value = "zdrybh")
    private String zdrybh;//重点人员编号
    @TableField(value = "xm")
    private String xm;//姓名
    @TableField(value = "xmpy")
    private String xmpy;//姓名拼音
    @TableField(value = "wwxm")
    private String wwxm;//外文姓名
    @TableField(value = "xb")
    private String xb;//性别
    @TableField(value = "xb_dm")
    private String xbDm;//性别代码
    @TableField(value = "csrq")
    private Date csrq;//出生日期
    @TableField(value = "gj")
    private String gj;//国籍
    @TableField(value = "gmsfhm")
    private String gmsfhm;//公民身份号码
    @TableField(value = "qtzjhm")
    private String qtzjhm;//其他证件号码
    @TableField(value = "mz")
    private String mz;//名族
    @TableField(value = "jg")
    private String jg;//籍贯
    @TableField(value = "hjdqh")
    private String hjdqh;//户籍地区划
    @TableField(value = "hjdxz")
    private String hjdxz;//户籍地详址
    @TableField(value = "hjdpcs")
    private String hjdpcs;//户籍地派出所
    @TableField(value = "hjdpcsdm")
    private String hjdpcsdm;//户籍地派出所代码
    @TableField(value = "xzdqh")
    private String xzdqh;//现住地区划
    @TableField(value = "xzxxdz")
    private String xzxxdz;//现住地详址
    @TableField(value = "xzdpcs")
    private String xzdpcs;//现住地派出所
    @TableField(value = "xzdpcsdm")
    private String xzdpcsdm;//现住地派出所代码
    @TableField(value = "gxdw")
    private String gxdw;//管辖单位
    @TableField(value = "gxdwjgdm")
    private String gxdwjgdm;//管辖单位机构代码
    @TableField(value = "ladw")
    private String ladw;//立案单位
    @TableField(value = "ladwjgdm")
    private String ladwjgdm;//立案单位机构代码
    @TableField(value = "zjlasj")
    private Date zjlasj;//最近立案时间
    @TableField(value = "nrbjzdryksj")
    private Date nrbjzdryksj;//纳入部级重点人员库时间
    @TableField(value = "zdrylbbj")
    private String zdrylbbj;//重点人员类别标记
    @TableField(value = "zdryxl")
    private String zdryxl;//重点人员细类
    @TableField(value = "yxx")
    private String yxx;//有效性
    @TableField(value = "jlxzsj")
    private Date jlxzsj;//记录新增时间
    @TableField(value = "jlcxsj")
    private Date jlcxsj;//记录撤销时间
    @TableField(value = "jlbgsj")
    private Date jlbgsj;//记录变更时间
    @TableField(value = "sender")
    private String sender;
    @TableField(value = "rksj")
    private Date rksj;//入库时间

}
