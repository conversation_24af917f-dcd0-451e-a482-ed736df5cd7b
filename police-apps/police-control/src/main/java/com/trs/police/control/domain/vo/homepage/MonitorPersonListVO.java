package com.trs.police.control.domain.vo.homepage;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.trs.police.common.core.json.serializer.SimpleTimeSerializer;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/7/12 09:26
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class MonitorPersonListVO {

    /**
     * 人员id
     */
    private Long personId;
    /**
     * 人员姓名
     */
    private String name;
    /**
     * 身份证
     */
    private String idNumber;
    /**
     * 布控人
     */
    private List<String> monitorUser;

    /**
     * 人员标签
     */
    private List<String> labels;
    /**
     * 最近预警时间
     */
    @JsonSerialize(using = SimpleTimeSerializer.class,nullsUsing = SimpleTimeSerializer.class)
    private LocalDateTime lastWarningTime;
    /**
     * 最近轨迹时间
     */
    @JsonSerialize(using = SimpleTimeSerializer.class,nullsUsing = SimpleTimeSerializer.class)
    private LocalDateTime lastTrackTime;

}
