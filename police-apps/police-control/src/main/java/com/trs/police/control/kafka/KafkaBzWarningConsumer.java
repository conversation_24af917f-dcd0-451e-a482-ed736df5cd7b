package com.trs.police.control.kafka;

import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.control.domain.dto.BzWarningDTO;
import com.trs.police.control.properties.KafkaBzWarningConsumerProperties;
import com.trs.police.control.service.SourceService;
import com.trs.police.control.service.WarningProcessService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2023/08/01
 */
@Component
@Slf4j
@ConditionalOnBean(value = KafkaBzWarningConsumerProperties.class)
public class KafkaBzWarningConsumer {

    private final KafkaConsumer<String, String> consumer;

    private final KafkaBzWarningConsumerProperties properties;

    private final WarningProcessService warningProcessService;

    public KafkaBzWarningConsumer(KafkaBzWarningConsumerProperties properties, SourceService sourceService,
                                  WarningProcessService warningProcessService) {
        this.properties = properties;
        this.warningProcessService = warningProcessService;
        Properties props = new Properties();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, properties.getBootStrapServers());
        props.put(ConsumerConfig.GROUP_ID_CONFIG, properties.getGroupId());
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, properties.getMaxPollRecords());
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, properties.getAutoOffsetReset());
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);//关闭自动提交kafka事务，批量处理时控制回滚策略
        consumer = new KafkaConsumer<>(props);
        consumer.subscribe(List.of(properties.getTopic()));
    }

    /**
     * 消费人员预警信息
     */
    @Scheduled(fixedDelay = 100L)
    public void consumer() {
        final ConsumerRecords<String, String> records = consumer.poll(Duration.ofSeconds(properties.getPollDuration()));
        log.info("本批次数据共消费比中数据：{} 条", records.count());
        List<ConsumerRecord<String, String>> recordList = new ArrayList<>(records.count());
        records.forEach(recordList::add);
        //处理该批次消费到的数据
        recordList.parallelStream().forEach(record -> {
            String message = record.value();
            if (StringUtils.isBlank(message)) {
                log.error("receive warning message blank!");
            }
            BzWarningDTO warningDTO = JsonUtil.parseObject(message, BzWarningDTO.class);
            if (warningDTO != null) {
                log.info("解析比中轨迹消息成功！内容：{}", message);
                try {
                    warningProcessService.receiveBzWarningMessage(warningDTO);
                } catch (Exception exception) {
                    log.info("异常比中轨迹数据：", exception);
                }
            }
        });
        log.info("本批次消费数据入库完毕！");
        //提交kafka事务
        consumer.commitSync();
    }


}
