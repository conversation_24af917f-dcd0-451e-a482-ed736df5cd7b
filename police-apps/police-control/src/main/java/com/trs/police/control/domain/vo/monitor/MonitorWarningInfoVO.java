package com.trs.police.control.domain.vo.monitor;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.trs.police.common.core.constant.enums.MonitorLevelEnum;
import com.trs.police.common.core.utils.TimeUtil;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 布控-预警信息
 *
 * <AUTHOR> yanghy
 * @date : 2022/8/26 14:28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MonitorWarningInfoVO {

    /**
     * 预警id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long warningId;

    /**
     * 预警级别
     */
    private MonitorLevelEnum warningLevel;
    /**
     * 预警时间
     */
    @JsonProperty("dateTime")
    private LocalDateTime warningTime;

    /**
     * 预警展示时间（标题中的时间）
     */
    private String warningSimpleTime;

    /**
     * 预警内容
     */
    private String content;

    /**
     * 是否存在关联
     */
    private Boolean isExistRelation;

    /**
     * getter
     *
     * @param warningTime 展示时间
     */
    public void setWarningSimpleTime(LocalDateTime warningTime) {
        this.warningSimpleTime = TimeUtil.getSimpleTime(warningTime);
    }
}
