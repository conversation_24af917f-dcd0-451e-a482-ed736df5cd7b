package com.trs.police.control.controller;


import com.trs.police.common.core.vo.PageResult;
import com.trs.police.control.domain.dto.ArchivesConfigDTO;
import com.trs.police.control.domain.vo.ArchivesConfigVO;
import com.trs.police.control.service.ArchivesConfigService;
import com.trs.web.builder.base.RestfulResultsV2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/06/07
 */
@RestController
@RequestMapping("/archiver/config")
public class ArchivesConfigController {

    @Autowired
    private ArchivesConfigService archivesConfigService;

    /**
     * 档案配置列表
     *
     * @param dto dto
     * @return ArchivesConfigVO
     */
    @GetMapping("archivesConfigList")
    public PageResult<ArchivesConfigVO> archivesConfigList(ArchivesConfigDTO dto){
        return archivesConfigService.archivesConfigList(dto);
    }


    /**
     * 档案配置新增
     *
     * @param dto dto
     * @return RestfulResultsV2
     */
    @GetMapping("addArchivesConfig")
    public RestfulResultsV2 addArchivesConfig(ArchivesConfigDTO dto){
       return archivesConfigService.addArchivesConfig(dto);
    }

    /**
     * 档案配置删除
     *
     * @param ids ids
     */
    @PostMapping("deleteArchivesConfig")
    public void deleteArchivesConfig(@RequestBody List<Long> ids){
        archivesConfigService.deleteArchivesConfig(ids);
    }

    /**
     * 档案配置编辑
     *
     * @param dto dto
     * @return RestfulResultsV2
     */
    @GetMapping("editArchivesConfig")
    public RestfulResultsV2 editArchivesConfig(ArchivesConfigDTO dto){
        return archivesConfigService.editArchivesConfig(dto);
    }

    /**
     * 置顶
     *
     * @param dto dto
     */
    @GetMapping("/top")
    public void top(ArchivesConfigDTO dto) {
        archivesConfigService.top(dto.getId());
    }

    /**
     * 移动
     *
     * @param dto dto
     */
    @GetMapping("/move")
    public void move(ArchivesConfigDTO dto) {
        archivesConfigService.move(dto.getId(), dto.getIsBefore());
    }
}
