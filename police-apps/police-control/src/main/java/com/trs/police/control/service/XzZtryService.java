package com.trs.police.control.service;

/**
 * 刑侦-在逃人员Service
 *
 * <AUTHOR>
 */
public interface XzZtryService {

    /**
     * 同步在逃人员信息
     *
     */
    void synZtryxx();

    /**
     * 在逃人员常控、撤控
     */
    void ztRegularMonitor();

    /**
     * 新增测试数据
     *
     * @param count 个数
     * @return string
     */
    String insertUpdateData(Integer count);

    /**
     * 更新人员标签
     */
    void updatePersonLabel();

    /**
     * 同步云墙在逃人员信息
     */
    void synYqZtryxx();

    /**
     * 同步云墙常控数据到云哨,每小时执行一次
     *
     * @param id ckry主键
     */
    void synYqCkry(String id);

    /**
     * 同步云墙临控数据到云哨,每小时执行一次
     *
     * @param id bkxx主键
     */
    void synYqBkxx(String id);

}
