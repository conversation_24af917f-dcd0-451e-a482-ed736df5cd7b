package com.trs.police.control.service;

import com.trs.web.builder.base.RestfulResultsV2;

/**
 * 布控任务签收服务
 */
public interface ControlTaskSignService {

    /**
     * 任务单条签收
     *
     * @param deptId 签收单位id
     * @param taskId 任务id
     * @return 是否签收成功
     */
    RestfulResultsV2<String> taskSignSingle(Long deptId, Long taskId);

    /**
     * 检查签收状态
     *
     * @param deptId 接收单位id
     * @param taskId 任务id
     * @return 是否已经签收 true：已签收 false：未签收
     */
    Boolean checkSign(Long deptId, Long taskId);
}
