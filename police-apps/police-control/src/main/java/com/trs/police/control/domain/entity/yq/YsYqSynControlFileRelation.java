package com.trs.police.control.domain.entity.yq;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 警务协作
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "ys_yq_syn_composite_collaboration_relation")
public class YsYqSynControlFileRelation implements Serializable {

	private Long id;

	private String yqControlMonitorId;

	private Long controlMonitorId;

	private String fileDetailId;

	/**
	 * 同步成功状态 0 没成功 1 成功
	 */
	private Integer isSuccess;

	public YsYqSynControlFileRelation(String yqControlMonitorId, Long controlMonitorId, String fileDetailId) {
		this.yqControlMonitorId = yqControlMonitorId;
		this.controlMonitorId = controlMonitorId;
		this.fileDetailId = fileDetailId;
	}
}
