package com.trs.police.control.domain.vo.basic;

import com.trs.police.common.core.request.ListParamsRequest;
import java.util.List;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @author: zhou.youpeng
 * @date: 2022/9/8 10:15
 */
@Data
public class SourceExportRequestVO {

    /**
     * 感知源id
     */
    private ListParamsRequest listParamsRequest;
    /**
     * 更新类型（更新全部，更新选中）
     */
    @NotBlank(message = "更新类型缺失")
    private String exportType;
    /**
     * 感知源id
     */
    private List<Long> sourceIds;
}
