package com.trs.police.control.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.params.SearchParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.CodeNameCountVO;
import com.trs.police.common.core.vo.IdNameCountVO;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.control.domain.dto.fkzt.PersonWarningSourceStatisticDTO;
import com.trs.police.control.domain.entity.fkrxyj.ProfilePerson;
import com.trs.police.control.domain.entity.fkrxyj.WarningFkrxyjEntity;
import com.trs.police.control.domain.vo.fkrxyj.FkPersonPoliceControlVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/10/25 16:12
 */
@Mapper
public interface WarningFkrxyjMapper extends BaseMapper<WarningFkrxyjEntity> {

    /**
     * @param request 列表筛选参数
     * @param page    分页参数
     * @return 预警
     */
    Page<WarningFkrxyjEntity> selectPageList(@Param("request") ListParamsRequest request,
                                             Page<WarningFkrxyjEntity> page);

    /**
     * 获取process
     *
     * @param startTime        startTime
     * @param warningThreshold warningThreshold
     * @return result
     */
    List<WarningFkrxyjEntity> getFK4ProcessWarning(@Param("startTime") LocalDateTime startTime,
                                                   @Param("warningThreshold") int warningThreshold);

    /**
     * 获取一周三天次数
     *
     * @param startTime 开始时间
     * @param idCard    身份证
     * @return 统计次数
     */
    Long getCountDayOfWeeks(@Param("startTime") LocalDateTime startTime,
                            @Param("idCard") String idCard);

    /**
     * 指定点出现次数
     *
     * @param startTime      开始时间
     * @param idCard         身份证
     * @param captureAddress 地址
     * @return 结果
     */
    Long getCountSameAdressDayOfWeeks(@Param("startTime") LocalDateTime startTime,
                                      @Param("idCard") String idCard,
                                      @Param("captureAddress") String captureAddress);

    /**
     * 是否非涉恐在档
     *
     * @param idCard 身份证
     * @return 结果
     */
    Long getCountFromProfileByIdCard(@Param("idCard") String idCard);

    /**
     * @param idCard       身份证号
     * @param page         分页参数
     * @param filterParams 过滤参数
     * @param searchParams 检索参数
     * @return 预警
     */
    Page<WarningFkrxyjEntity> getFkWarningByIdCard(@Param("idCard") String idCard, @Param("filterParams") List<KeyValueTypeVO> filterParams,
                                                   @Param("searchParams") SearchParams searchParams, Page<WarningFkrxyjEntity> page);

    /**
     * 根据身份证获取预警数量
     *
     * @param idCard 身份证
     * @return 预警数量
     */
    Long getWarningCountByIdCard(@Param("idCard") String idCard);

    /**
     * 根据身份证获取近24小时轨迹
     *
     * @param idCard 身份证
     * @return 预警数量
     */
    Long getTrackCountOf24H(@Param("idCard") String idCard);

    /**
     * FK在档人员列表
     *
     * @param label label
     * @return 结果
     */
    List<ProfilePerson> getFkOnRecord(@Param("label") String label);

    /**
     * 获取指定条件的全部预警数量
     *
     * @param idCard     idCard
     * @param searchTime searchTime
     * @return 结果
     */
    Long getCount4NormalWarning(@Param("idCard") String idCard, @Param("searchTime") LocalDateTime searchTime);


    /**
     * 获取人员所属管控派出所
     *
     * @param personId 人档id
     * @return 管控信息
     */
    List<FkPersonPoliceControlVO> getPersonPoliceControl(@Param("personId") Long personId);

    /**
     * 分页检索未签收的预警id
     *
     * @param currentDate currentDate
     * @param page        page
     * @return 预警id集合
     */
    Page<Long> pageSignHistoryWarnings(@Param("currentDate") String currentDate, @Param("page") Page page);

    /**
     * 获取预警数量
     *
     * @param idCard idCard
     * @return 预警数量
     */
    List<CodeNameCountVO> warningCount(@Param("idCard") List<String> idCard);

    /**
     * 获取预警类型统计
     *
     * @param idCard idCard
     * @return 预警数量
     */
    List<IdNameCountVO> fkPersonWarningTypeStatistic(String idCard);

    /**
     * 按照感知源分组
     *
     * @param dto 参数
     * @return 结果
     */
    List<IdNameCountVO> fkPersonWarningSourceStatistic(@Param("dto") PersonWarningSourceStatisticDTO dto);

    /**
     * 根据时间获取数量
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param entity    entity
     * @return  数量
     */
    List<WarningFkrxyjEntity> selectListBytime(@Param("startTime") LocalDateTime startTime,@Param("endTime") LocalDateTime endTime,
                                               @Param("entity")WarningFkrxyjEntity entity);
}
