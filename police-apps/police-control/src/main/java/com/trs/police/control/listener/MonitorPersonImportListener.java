package com.trs.police.control.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.trs.police.common.core.utils.ValidationUtil;
import com.trs.police.control.domain.vo.monitor.ImportPersonFailVO;
import com.trs.police.control.domain.vo.monitor.ImportPersonVO;
import com.trs.police.control.domain.vo.monitor.MonitorImportResultVO;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 布控人员导入
 *
 * <AUTHOR> yanghy
 * @date : 2022/8/25 16:17
 */
public class MonitorPersonImportListener extends AnalysisEventListener<ImportPersonVO> {

    private final Set<ImportPersonVO> successData = new HashSet<>();
    private final List<ImportPersonFailVO> failData = new ArrayList<>();

    @Override
    public void invoke(ImportPersonVO importPersonVO, AnalysisContext context) {
        if (!importPersonVO.validateBlank()) {
            this.addToFailData("字段填写有误", importPersonVO);
            return;
        }
        //校验证件类型
        String certificateType = importPersonVO.getCertificateType();
        if ("身份证".equals(certificateType)) {
            importPersonVO.setCertificateType("1");
            //身份证格式校验
            if (Boolean.FALSE.equals(ValidationUtil.validateIdentity(importPersonVO.getCertificateNumber()))) {
                this.addToFailData("身份证号码格式错误", importPersonVO);
                return;
            }
        } else if ("护照".equals(certificateType)) {
            importPersonVO.setCertificateType("2");
        } else {
            this.addToFailData("证件类型不正确", importPersonVO);
            return;
        }
        //添加到成功的结果中
        if (!successData.add(importPersonVO)) {
            this.addToFailData("重复导入", importPersonVO);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // do nothing
    }

    /**
     * 获取导入结果
     *
     * @return {@link MonitorImportResultVO}
     */
    public MonitorImportResultVO getResult() {
        return new MonitorImportResultVO(new ArrayList<>(successData), failData);
    }

    private void addToFailData(String reason, ImportPersonVO importPersonVO) {
        ImportPersonFailVO importPersonFailVO = importPersonVO.toImportPersonFailVO();
        importPersonFailVO.setReason(reason);
        importPersonFailVO.setIndex(failData.size() + 1);
        failData.add(importPersonFailVO);
    }
}
