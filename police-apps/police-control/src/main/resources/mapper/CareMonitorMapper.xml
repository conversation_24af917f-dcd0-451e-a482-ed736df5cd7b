<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.control.mapper.CareMonitorMapper">

    <resultMap id="map" type="com.trs.police.control.domain.entity.monitor.CareMonitorEntity">
        <result column="warningConfigIds" property="warningConfigIds"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
    </resultMap>

    <update id="updateExistsCareMonitor">
        update t_control_care_monitor
        set
            handler_class_name = JSON_ARRAY_APPEND(handler_class_name,'$',#{handlerClassName})
        where
            JSON_OVERLAPS(handler_class_name, JSON_ARRAY(#{handlerClassName})) = 0
            and certificate_value in
            <foreach collection="datas" item="data" open="(" separator="," close=")">
                #{data.certificateValue}
            </foreach>
    </update>

    <select id="selectExistsCareMonitor" resultType="com.trs.police.control.domain.entity.monitor.CareMonitorEntity">
        select * from t_control_care_monitor t where t.certificate_value in
        <foreach collection="datas" item="data" open="(" separator="," close=")">
            #{data.certificateValue}
        </foreach>
    </select>
    <select id="monitorList" resultMap="map">
        <bind name="filterParams" value="request.filterParams"/>
        <bind name="searchParams" value="request.searchParams"/>
        <bind name="sortParams" value="request.sortParams"/>
        select
        t.*,t.warning_config_ids as warningConfigIds
        from t_control_care_monitor t
        <where>
            1=1
            <if test="filterParams!=null and filterParams.size>0">
                <foreach collection="filterParams" item="param">
                    <bind name="value" value="param.getProcessedValue()"/>
                    <choose>
                        <when test="'relatedType'.equals(param.key)">
                            and t.related_type = #{value}
                        </when>
                        <when test="'monitorStatus'.equals(param.key)">
                            and t.monitor_status  = ${value}
                        </when>
                        <when test="'createDept'.equals(param.key)">
                            and t.create_dept_id = ${value}
                        </when>
                        <when test="'lastWarningTime'.equals(param.key) and param.getProcessedValue().isAll() == false">
                            AND (t.last_warning_time between '${value.beginTime}' AND '${value.endTime}')
                        </when>
                        <when test="'createTime'.equals(param.key) and value.isAll() == false">
                            AND (t.create_time between '${value.beginTime}' AND '${value.endTime}')
                        </when>
                    </choose>
                </foreach>
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(searchParams.searchValue)">
                <bind name="pattern" value="'%' + searchParams.searchValue.trim() + '%'"/>
                <choose>
                    <when test=" 'monitorPerson'==searchParams.searchField">
                        and t.certificate_name like #{pattern}
                    </when>
                    <when test=" 'idNumber'==searchParams.searchField">
                        and t.certificate_value like #{pattern}
                    </when>
                    <when test=" 'related_name'==searchParams.searchField">
                        and t.related_data_name like #{pattern}
                    </when>
                    <otherwise>
                        and (
                        t.certificate_name like #{pattern} or t.certificate_value like #{pattern}
                        or t.related_data_name like #{pattern}
                        )
                    </otherwise>
                </choose>
            </if>
        </where>
        order by
        <if test="sortParams != null">
            <bind name="sortField" value="sortParams.sortField"/>
            <bind name="sortValue" value="sortParams.getProcessedValue()"/>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(sortField) ">
                <if test="sortField == 'lastWarningTime' ">
                    t.last_warning_time
                </if>
                <if test="sortField == 'createTime' ">
                    t.create_time
                </if>
                ${sortValue}
            </if>
        </if>
    </select>


    <resultMap id="BaseResultMap" type="com.trs.police.control.domain.entity.monitor.CareMonitorEntity">
        <!-- 主键字段 -->
        <id column="id" property="id" />
        <!-- 新增字段 -->
        <result column="create_time" property="createTime" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_dept_id" property="createDeptId" />
        <result column="update_time" property="updateTime" />
        <result column="update_user_id" property="updateUserId" />
        <result column="update_dept_id" property="updateDeptId" />
        <!-- 原有字段 -->
        <result column="certificate_value" property="certificateValue" />
        <result column="certificate_type" property="certificateType" />
        <result column="comment" property="comment" />
        <result column="warning_config_ids" property="warningConfigIds" typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler" />
        <result column="handler_class_name" property="handlerClassName" typeHandler="com.trs.police.common.core.handler.typehandler.JsonToStringListHandler" />
        <result column="deleted" property="deleted" />
        <result column="image_url" property="imageUrl" />
        <result column="subscribe_start_time" property="subscribeStartTime" />
        <result column="subscribe_end_time" property="subscribeEndTime" />
        <result column="subscribe_platform" property="subscribePlatform" />
        <result column="car_number" property="carNumber" typeHandler="com.trs.police.common.core.handler.typehandler.JsonToStringListHandler" />
        <result column="tel" property="tel" typeHandler="com.trs.police.common.core.handler.typehandler.JsonToStringListHandler" />
        <result column="imei" property="imei" typeHandler="com.trs.police.common.core.handler.typehandler.JsonToStringListHandler" />
        <result column="imsi" property="imsi" typeHandler="com.trs.police.common.core.handler.typehandler.JsonToStringListHandler" />
        <result column="mac" property="mac" typeHandler="com.trs.police.common.core.handler.typehandler.JsonToStringListHandler" />
        <result column="current_warning_time" property="lastWarningTime" />
        <result column="related_data_id" property="relatedDataId" />
        <result column="related_type" property="relatedType" />
        <result column="certificate_name" property="certificateName" />
        <result column="related_data_name" property="relatedDataName" />
        <result column="monitor_status" property="monitorStatus" />
        <result column="person_id" property="personId" />
    </resultMap>

    <select id="selectExistsInductiveCareMonitor"
            resultMap="BaseResultMap">
        select * from t_control_care_monitor t
        <where>
            <if test="datas!= null and datas.size>0">
                and t.certificate_value in
                <foreach collection="datas" item="data" open="(" separator="," close=")">
                    #{data.certificateValue}
                </foreach>
                and t.related_data_id in
                <foreach collection="datas" item="data" open="(" separator="," close=")">
                    #{data.relatedDataId}
                </foreach>
            </if>
        </where>

    </select>

</mapper>