<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.control.mapper.MonitorTargetRelationMapper">

    <select id="getActiveMonitorIdByGroupId" resultType="java.lang.Long">
        select a.monitor_id
        from t_control_monitor_target_relation a
        left join t_control_monitor m on a.monitor_id = m.id
        <where>
            a.type=2
            and m.monitor_status = 5
            and a.target_id = #{groupId}
        </where>
    </select>

    <select id="getPersonIdByMonitorId" resultType="java.lang.Long">
        select a.target_id
        from t_control_monitor_target_relation a
        where a.monitor_id = #{monitorId}
    </select>

    <select id="selectAreaByMonitor" resultType="com.trs.police.common.core.vo.KeyValueVO">
        select (select a.name from t_control_important_area a where a.id = m.important_area_id)          as `key`,
               (select a.district_name from t_control_important_area a where a.id = m.important_area_id) as `value`
        from t_control_monitor_target_relation t
                 join t_control_monitor_warning_model m on t.target_id = m.id
        where t.monitor_id = #{monitorId}
          and t.type = 3
    </select>

    <select id="selectAreaByMonitorList" resultType="com.trs.police.control.helper.WarningListVoHelper$KeyValueAndWarningIdVO">
        select t.monitor_id as monitorId,
        (select a.name from t_control_important_area a where a.id = m.important_area_id) as `key`,
        (select a.district_name from t_control_important_area a where a.id = m.important_area_id) as `value`
        from t_control_monitor_target_relation t
        join t_control_monitor_warning_model m on t.target_id = m.id
        where t.monitor_id in
        <foreach collection="monitorIds" item="monitorId" open="(" separator="," close=")">
            #{monitorId}
        </foreach>
        and t.type = 3
    </select>

    <select id="selectGroupByMonitor" resultType="com.trs.police.common.core.vo.KeyValueVO">
        select g.name as `key`,
               l.name as `value`
        from t_control_monitor_target_relation r
                 left join t_profile_group g on r.target_id = g.id
                 left join t_profile_label l on l.id = json_extract(g.group_label, '$[0]')
        where r.monitor_id = #{monitorId}
          and r.type = 2
    </select>

    <select id="selectPersonByMonitor" resultType="com.trs.police.common.core.vo.KeyValueVO">
        select t.name as `key`,
               l.name as `value`
        from t_control_monitor_target_relation r
                 left join t_profile_person t on t.id = r.target_id
                 left join t_profile_label l on l.id = json_extract(t.person_label, '$[0]')
        where r.monitor_id = #{monitorId}
          and r.type = 1
    </select>

    <select id="selectPersonByWarning" resultType="com.trs.police.common.core.vo.KeyValueVO">
        select p.name as `key`,
               (select l.name from t_profile_label l where l.id = json_extract(p.person_label, '$[0]')) as `value`
        from t_warning_track t
                 left join t_profile_person p on p.id = t.person_id
        where t.warning_id = #{warningId}
    </select>

    <select id="selectPersonByWarningList" resultType="com.trs.police.control.helper.WarningListVoHelper$KeyValueAndWarningIdVO">
        select t.warning_id as warningId, p.name as `key`,
        (select l.name from t_profile_label l where l.id = json_extract(p.person_label, '$[0]')) as `value`
        from t_warning_track t
        left join t_profile_person p on p.id = t.person_id
        where t.warning_id in
        <foreach collection="warningIds" item="warningId" open="(" separator="," close=")">
            #{warningId}
        </foreach>
    </select>

    <select id="selectGroupByWarning" resultType="com.trs.police.common.core.vo.KeyValueVO">
        select g.name as `key`,
               (select l.name from t_profile_label l where l.id = json_extract(g.group_label, '$[0]')) as `value`
        from t_warning r
                 left join t_profile_group g on r.group_id = g.id
        where r.id = #{warningId}
          and r.warning_type = 'group'
    </select>

    <select id="selectGroupByWarningList" resultType="com.trs.police.control.helper.WarningListVoHelper$KeyValueAndWarningIdVO">
        select r.id as warningId, g.name as `key`,
        (select l.name from t_profile_label l where l.id = json_extract(g.group_label, '$[0]')) as `value`
        from t_warning r
        left join t_profile_group g on r.group_id = g.id
        where r.id in
        <foreach collection="warningIds" item="warningId" open="(" separator="," close=")">
            #{warningId}
        </foreach>
        and r.warning_type = 'group'
    </select>
    <select id="selectPersonByMonitors" resultType="com.trs.police.control.helper.WarningListVoHelper$KeyValueAndMonitorIdVO">
        select t.name as `key`,
               l.name as `value`,
               r.monitor_id as `monitorId`
        from t_control_monitor_target_relation r
                 left join t_profile_person t on t.id = r.target_id
                 left join t_profile_label l on l.id = json_extract(t.person_label, '$[0]')
        where r.type = 1
        <if test="monitorIds != null and monitorIds.size()>0">
            and r.monitor_id in
            <foreach collection="monitorIds" open="(" close=")" item="monitorId" separator=",">
                #{monitorId}
            </foreach>
        </if>
    </select>
    <select id="selectGroupByMonitors" resultType="com.trs.police.control.helper.WarningListVoHelper$KeyValueAndMonitorIdVO">
        select g.name as `key`,
               l.name as `value`,
               r.monitor_id as `monitorId`
        from t_control_monitor_target_relation r
                 left join t_profile_group g on r.target_id = g.id
                 left join t_profile_label l on l.id = json_extract(g.group_label, '$[0]')
        where r.type = 2
        <if test="monitorIds != null and monitorIds.size()>0">
            and r.monitor_id in
            <foreach collection="monitorIds" open="(" close=")" item="monitorId" separator=",">
                #{monitorId}
            </foreach>
        </if>
    </select>
    <select id="selectAreaByMonitors" resultType="com.trs.police.control.helper.WarningListVoHelper$KeyValueAndMonitorIdVO">
        select a.name          as `key`,
               a.district_name as `value`,
               t.monitor_id as `monitorId`
        from t_control_monitor_target_relation t
                 join t_control_monitor_warning_model m on t.target_id = m.id
                 left join t_control_important_area a on m.important_area_id = a.id
        where t.type = 3
        <if test="monitorIds != null and monitorIds.size()>0">
            and t.monitor_id in
            <foreach collection="monitorIds" open="(" close=")" item="monitorId" separator=",">
                #{monitorId}
            </foreach>
        </if>
    </select>

</mapper>