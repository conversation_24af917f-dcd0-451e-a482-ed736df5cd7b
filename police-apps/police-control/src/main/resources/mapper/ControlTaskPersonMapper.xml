<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.control.mapper.ControlTaskPersonMapper">

    <select id="selectControlPersonPage" resultType="com.trs.police.control.domain.vo.ControlTaskPersonListVO">
        select
            tp.id as taskPersonId,
            tp.name as name,
            tp.id_number as idNumber,
            tp.address as address,
            tp.problem_location as problemLocation,
            tp.petition_type as petitionType,
            tp.control_status as controlStatus,
            tp.create_dept_id as createDeptId,
            tpdr.issue_status as issueStatus,
            tp.control_dept_id as controlDeptId,
            tpdr.return_reason as returnReason
        from t_control_task_person tp
                 left join t_control_task_person_dept_relation tpdr
                      on tp.id = tpdr.task_person_id
        where tp.task_id = #{dto.controlTaskId}
        and tpdr.receive_dept_id in
        <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
            #{deptId}
        </foreach>
        <if test="dto.notIssued!=null and dto.notIssued">
            and tpdr.issue_status in(1,3)
        </if>
        <if test="dto.notControlled!=null and dto.notControlled">
            and tp.control_status = 1
        </if>
        order by tp.control_status, tp.control_dept_id
    </select>
</mapper>