<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.control.mapper.HomePageMapper">
    <resultMap id="monitorPersonMap" type="com.trs.police.control.domain.vo.homepage.MonitorPersonVO">
        <id property="personId" column="personId"/>

        <collection property="labels" column="personId" ofType="java.lang.String"
            select="getPersonLabels"/>
    </resultMap>
    <resultMap id="warningEntity" type="com.trs.police.common.core.entity.WarningEntity">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_dept_id" property="updateDeptId"/>
        <result column="warning_type" property="warningType"/>
        <result column="warning_level" property="warningLevel"/>
        <result column="content" property="content"/>
        <result column="warning_time" property="warningTime"/>
        <result column="monitor_id" property="monitorId"/>
        <result column="group_id" property="groupId"/>
        <result column="control_type" property="controlType"/>
        <result column="activity_address" property="activityAddress"/>
        <result column="activity_time" property="activityTime"/>
        <result column="model_id" property="modelId"
            typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
    </resultMap>
    <resultMap id="personMap" type="com.trs.police.control.domain.vo.homepage.PersonMapVO">
        <id column="id" property="id"/>
        <result column="point" property="point"/>
        <result column="name" property="name"/>
        <result column="url" property="url"/>
        <result column="time" property="time"/>
        <result column="idNumber" property="idNumber"/>
        <result column="address" property="address"/>
        <result column="personLabel" property="personLabel"
            typeHandler="com.trs.police.common.core.handler.typehandler.JsonToStringListHandler"/>
    </resultMap>
    <resultMap id="warningHomePageMap" type="com.trs.police.control.domain.vo.homepage.WarningHomePageListVO">
        <result property="id" column="id"/>
        <result column="name" property="name"/>
        <result column="idNumber" property="idNumber"/>
        <result column="sourceType" property="sourceType"/>
        <result column="model" property="model"/>
        <result column="time" property="time"/>
        <result column="address" property="address"/>
        <result column="status" property="status"/>
        <result column="point" property="point"/>
        <result column="trackId" property="trackId"/>
        <result column="warningLevel" property="warningLevel"/>
        <result column="content" property="content"/>
        <result column="areaGeometries" property="areaGeometries"
            typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>
    <sql id="handle">
        <where>
            <foreach collection="filterParams" item="filterParam">
                <choose>
                    <when test="filterParam.key == 'dept'">
                        <bind name="value"
                            value="@com.trs.police.common.core.utils.StringUtil@getPrefixCode(filterParam.value) + '%'"/>
                        and p.dept_id in (select id from t_dept where code like #{value})
                    </when>
                    <when test="filterParam.key == 'permissionDept'">
                        and p.dept_id in
                        <foreach collection="filterParam.value" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </when>
                    <when test="filterParam.key == 'timeRange'">
                        and (w.warning_time >= #{filterParam.value.beginTime}
                        and w.warning_time &lt; #{filterParam.value.endTime})
                    </when>
                    <when test="filterParam.key == 'level' and filterParam.value != null">
                        and w.warning_level = #{filterParam.value}
                    </when>
                    <when test="filterParam.key == 'personLabel'">
                        <bind name="personLabel"
                            value="filterParam.getProcessedValue()"/>
                        and exists(select * from t_profile_person p left join t_warning_track t on p.id = t.person_id
                        where
                        t.warning_id = w.id and
                        <foreach collection="personLabel" item="item" open="(" separator="OR" close=")">
                            (JSON_OVERLAPS(p.person_label
                            ,(SELECT JSON_ARRAYAGG( l.id )
                            FROM t_profile_label l
                            WHERE CONCAT(l.path, l.id, '-')
                            LIKE CONCAT('%-', ${item}, '-%' ))
                            )
                            >0)
                        </foreach>
                        )
                    </when>
                    <when test="filterParam.key == 'warningModel'">
                        <bind name="modelIds"
                            value="@com.trs.police.control.domain.builder.WarningListBuilder@getModelIds(filterParam.getProcessedValue())"/>
                        AND JSON_OVERLAPS((ifnull(w.model_id,'[]')),
                        (select JSON_ARRAYAGG(id) from t_control_monitor_warning_model m where m.id in
                        <foreach collection="modelIds" item="modelId" separator="," open="(" close=")">
                            ${modelId}
                        </foreach>
                        ))>0
                    </when>
                </choose>
            </foreach>
        </where>
    </sql>
    <select id="getWarningHandleStatistic" resultType="com.trs.police.common.core.vo.KeyValueVO">
        select count(1) as 'value', p.status as 'key'
        from t_warning_process p join t_warning w on p.warning_id = w.id
        and w.warning_type = 'person'
        and w.control_type = 1
        <include refid="handle"/>
        group by p.status
    </select>

    <select id="getWarningLevelStatistic" resultType="com.trs.police.common.core.vo.KeyValueVO">
        select count(1) as 'value', w.warning_level as 'key'
        from t_warning_process p join t_warning w on p.warning_id = w.id
        and w.warning_type = 'person'
        and w.control_type = 1
        <include refid="handle"/>
        group by w.warning_level
    </select>

    <select id="getMonitorPerson" resultMap="monitorPersonMap">
        <bind name="searchParams" value="params.searchParams"/>
        <bind name="filterParams" value="params.filterParams"/>
        SELECT c.person_id as personId,
        concat('POINT(' ,t.longitude, ' ', t.latitude, ')') as track_point,
        ifnull( t.address,'- -') as track_address,
        max(t.activity_time) as track_time
        from ( select distinct(jt.person_id) as person_id FROM t_control_monitor mo,
        JSON_TABLE(mo.profile_target_id, '$[*]' COLUMNS (person_id JSON PATH '$')) jt
        where mo.monitor_type = 1 and mo.monitor_status = 5 and mo.deleted != 1
        <foreach collection="filterParams" item="filterParam">
            <choose>
                <when test="filterParam.key == 'level'">
                    and mo.monitor_level = #{filterParam.value}
                </when>
                <when test="filterParam.key == 'dept'">
                    <bind name="value"
                        value="@com.trs.police.common.core.utils.StringUtil@getPrefixCode(filterParam.value) + '%'"/>
                    and mo.monitor_person_unit in (select id from t_dept where code like #{value})
                </when>
                <when test="filterParam.key == 'personLabel'">
                    <bind name="personLabel"
                        value="filterParam.getProcessedValue()"/>
                    and exists(select 1 from t_profile_person p where
                    jt.person_id = p.id and     <foreach collection="personLabel" item="item" open="(" separator="OR"
                    close=")">
                    (JSON_OVERLAPS(p.person_label
                    ,(SELECT JSON_ARRAYAGG( l.id )
                    FROM t_profile_label l
                    WHERE CONCAT(l.path, l.id, '-')
                    LIKE CONCAT('%-', ${item}, '-%' ))
                    )
                    >0)
                </foreach>)
                </when>
                <when test="filterParam.key == 'permissionDept'">
                    and mo.monitor_person_unit in
                    <foreach collection="filterParam.value" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </when>
                <when test="filterParam.key == 'currentUser'">
                    and mo.monitor_person_id = #{filterParam.value}
                </when>
            </choose>
        </foreach>
        ) c
        left join (select address,person_id,create_time,longitude,latitude,activity_time from t_warning_track where control_type = 1) t on c.person_id = t.person_id
        <where>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(searchParams.searchValue)">
                <bind name="pattern" value="'%' + searchParams.searchValue.trim() + '%'"/>
                and (t.address like #{pattern}
                or c.person_id in (select p.id from t_profile_person p where p.name like #{pattern}
                or p.id_number like #{pattern}
                or p.tel like #{pattern}
                or json_overlaps(p.virtual_identity_ids, (SELECT JSON_ARRAYAGG(id) from t_profile_virtual_identity where
                virtual_number like #{pattern}))
                or json_overlaps(p.vehicle_ids, (SELECT JSON_ARRAYAGG(id) from t_profile_vehicle where car_number like
                #{pattern}))
                ))
            </if>
        </where>
        group by c.person_id
        order by t.create_time desc
    </select>

    <select id="getPersonMonitorUsers" resultType="com.trs.police.common.core.vo.KeyValueVO">
        select (select real_name from t_user where id = c.monitor_person_id) 'key', (select short_name from t_dept where id = c.monitor_person_unit) 'value'
        from t_control_monitor c
        where #{personId} member of (c.profile_target_id) and c.deleted != 1
        group by c.monitor_person_id, c.monitor_person_unit
    </select>

    <select id="getPersonMonitorUsersByIds" resultType="com.trs.police.common.core.vo.IdNameVO">
        select
            jt.item as id,
            (select real_name from t_user where id = c.monitor_person_id) 'uuid',
            (select short_name from t_dept where id = c.monitor_person_unit) 'name'
        from
            t_control_monitor c,
            JSON_TABLE(c.profile_target_id, '$[*]' COLUMNS (item BIGINT PATH '$')) AS jt
        where c.deleted != 1 and jt.item in
        <foreach collection="personId" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        group by jt.item,c.monitor_person_id, c.monitor_person_unit
    </select>

    <select id="getPersonMonitorUsersList" resultType="java.lang.String">
        select concat((select real_name from t_user where id = c.monitor_person_id), '(',
                      (select short_name from t_dept where id = c.monitor_person_unit), ')')
        from t_control_monitor c
        where #{personId} member of (c.profile_target_id)
        and c.deleted != 1
        and c.monitor_status = 5
        and c.monitor_type = 1
        group by c.monitor_person_id, c.monitor_person_unit
    </select>


    <select id="getPersonLabels" resultType="java.lang.String">
        SELECT NAME
        FROM t_profile_label
        WHERE id member of ( (SELECT person_label from t_profile_person WHERE id = #{personId}))
    </select>

    <resultMap id="personInfoMap" type="com.trs.police.control.domain.vo.homepage.PersonInfoVO">
        <result property="tel" column="tel"
            typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>

        <collection property="carNumber" column="vehicle_ids" ofType="java.lang.String"
            select="com.trs.police.common.core.mapper.CommonMapper.getPersonVehicle"/>

        <collection property="virtualIdentity" column="virtual_identity_ids" ofType="java.lang.String"
            select="com.trs.police.common.core.mapper.CommonMapper.getVirtualIdentity"/>
    </resultMap>

    <select id="getPersonInfo" resultMap="personInfoMap">
        select tel, vehicle_ids, virtual_identity_ids
        from t_profile_person
        where id = #{personId}
    </select>

    <select id="getPersonWarningList" resultMap="com.trs.police.control.mapper.WarningMapper.warningEntity">
        <bind name="filterParams" value="params.filterParams"/>
        select w.id as id,
        w.warning_level,
        w.warning_time,
        w.monitor_id,
        w.model_id,
        w.activity_address
        from t_warning w
        where w.warning_type = 'person' and w.control_type = 1
        and w.id in (select warning_id from t_warning_track t where t.person_id = #{personId} and t.control_type = 1)
        <foreach collection="filterParams" item="filterParam">
            <choose>
                <when test="filterParam.key == 'level'">
                    and w.warning_level = #{filterParam.value}
                </when>
            </choose>
        </foreach>
        order by w.warning_time desc
    </select>

    <select id="getWarnMapData" resultMap="warningHomePageMap">
        <include refid="warningHomePageList"/>
    </select>

    <select id="getWarningPageList" resultMap="warningHomePageMap">
        <include refid="warningHomePageList"/>
    </select>
    <select id="getPersonMapData" resultMap="personMap">
        <bind name="filterParams" value="request.filterParams"/>
        SELECT p.name as name,
        concat('POINT(' ,t.longitude, ' ', t.latitude, ')') as point,
        t.activity_time as time,
        concat('/oss/photo/',p.id_number) as url,
        p.id as id,
        p.id_number as idNumber,
        t.address as address,
        ifnull((select JSON_ARRAYAGG(name) from t_profile_label where id member of (p.person_label)),'[]') as personLabel
        from (select person_id, max(create_time) as activity_time,monitor_id,longitude, latitude,address from t_warning_track
        <where>
            control_type = 1
            <foreach collection="filterParams" item="filterParam">
                <choose>
                    <when test="filterParam.key == 'areaWkt'">
                        and ST_CONTAINS(ST_SRID(ST_PolygonFromText(#{filterParam.value}), 4326),
                        ST_SRID(Point(longitude,latitude),4326))
                    </when>
                    <when test="filterParam.key == 'timeRange'">
                        and (create_time >= #{filterParam.value.beginTime}
                        and create_time &lt; #{filterParam.value.endTime})
                    </when>
                </choose>
            </foreach>
        </where>
        group by person_id) t
        join (select id,name,id_number,person_label from t_profile_person
        <where>
            <foreach collection="filterParams" item="filterParam">
                <choose>
                    <when test="filterParam.key == 'personLabel'">
                        <bind name="personLabel" value="filterParam.getProcessedValue()"/>
                        <foreach collection="personLabel" item="item" open="(" separator="OR"
                            close=")">
                            (JSON_OVERLAPS(person_label
                            ,(SELECT JSON_ARRAYAGG( l.id )
                            FROM t_profile_label l
                            WHERE CONCAT(l.path, l.id, '-')
                            LIKE CONCAT('%-', ${item}, '-%' ))
                            )
                            >0)
                        </foreach>
                    </when>
                </choose>
            </foreach>
        </where>
        ) p on p.id = t.person_id
        join (select id from t_control_monitor c where monitor_type=1
        <foreach collection="filterParams" item="filterParam">
            <choose>
                <when test="filterParam.key == 'level' and filterParam.value !='0' ">
                    and c.monitor_level = #{filterParam.value}
                </when>
                <when test="filterParam.key == 'dept'">
                    <bind name="value"
                        value="@com.trs.police.common.core.utils.StringUtil@getPrefixCode(filterParam.value) + '%'"/>
                    and c.monitor_person_unit in (select id from t_dept where code like #{value})
                </when>
                <when test="filterParam.key == 'permissionDept'">
                    and c.monitor_person_unit in
                    <foreach collection="filterParam.value" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </when>
                <when test="filterParam.key == 'currentUser'">
                    and c.monitor_person_id = #{filterParam.value}
                </when>
            </choose>
        </foreach>
        ) c on c.id = t.monitor_id
    </select>
    <sql id="warningHomePageList">
        <bind name="filterParams" value="request.filterParams"/>
        <bind name="searchParams" value="request.searchParams"/>
        select w.id as id,
        t.id as trackId,
        p.name as name,
        p.id_number as idNumber,
        (select name from t_dict where type='monitor_level' and code=w.warning_level) as warningLevel,
        (select group_concat(title) from t_control_monitor_warning_model where id member of (w.model_id)) as model,
        (SELECT name FROM t_dict t1 where type='control_warning_source_type' and code = t.source_type) as sourceType,
        t.address as address,
        w.warning_time as time,
        w.content as content,
        concat('POINT(' ,t.longitude, ' ', t.latitude, ')') as point,
        if((exists(select * from t_warning_process wp where wp.warning_id=w.id and wp.status=4)),'已完结','处置中') as
        status,
        '[]' as areaGeometries
        from (select w.id,w.warning_level,w.model_id,w.warning_time,w.area_id,w.content from t_warning w
        where w.warning_type='person' and w.control_type=1
        <foreach collection="filterParams" item="param">
            <choose>
                <when test="param.key == 'permissionDept'">
                    AND exists (select * from t_warning_notify n where n.warning_id=w.id and n.dept_id in
                    <foreach collection="param.value" open="(" close=")" separator="," item="dept">
                        #{dept}
                    </foreach>
                    )
                </when>
                <when test="param.key == 'currentUser'">
                    AND exists (select * from t_warning_notify n where n.warning_id=w.id and n.user_id =
                    #{value})
                </when>
                <when test="param.key == 'dept'">
                    <bind name="value"
                        value="@com.trs.police.common.core.utils.StringUtil@getPrefixCode(param.value) + '%'"/>
                    and exists (select 1 from t_warning_notify n where n.warning_id=w.id and (select d.code from
                    t_dept d where d.id=n.dept_id) like #{value})
                </when>
                <when test="param.key == 'personLabel'">
                    <bind name="personLabel"
                        value="param.getProcessedValue()"/>
                    and
                    <foreach collection="personLabel" item="item" open="(" separator="OR" close=")">
                        (JSON_OVERLAPS(w.person_label
                        ,(SELECT JSON_ARRAYAGG( l.id )
                        FROM t_profile_label l
                        WHERE CONCAT(l.path, l.id, '-')
                        LIKE CONCAT('%-', ${item}, '-%' ))
                        )
                        >0)
                    </foreach>
                </when>
                <when test="'level'.equals(param.key)">
                    and w.warning_level = ${param.value}
                </when>
                <when test="'timeRange'.equals(param.key) and param.getProcessedValue().isAll() == false">
                    AND (w.warning_time >= '${param.value.beginTime}' and w.warning_time &lt; '${param.value.endTime}')
                </when>
                <when test="'warningModel'.equals(param.key) and param.value.size() > 0">
                    <bind name="modelIds"
                        value="@com.trs.police.control.domain.builder.WarningListBuilder@getModelIds(param.getProcessedValue())"/>
                    AND JSON_OVERLAPS((ifnull(w.model_id,'[]')),
                    (select JSON_ARRAYAGG(id) from t_control_monitor_warning_model where id in
                    <foreach collection="modelIds" item="modelId" separator="," open="(" close=")">
                        ${modelId}
                    </foreach>
                    ))>0
                </when>
            </choose>
        </foreach>
        ) w
        join (select id, address,longitude,latitude,source_type,person_id,warning_id from t_warning_track where
        longitude is not null and latitude is not null and control_type = 1
        <foreach collection="filterParams" item="param">
            <choose>
                <when test="param.key == 'areaWkt'">
                    and ST_CONTAINS(ST_SRID(ST_PolygonFromText(#{param.value}), 4326),
                    ST_SRID(Point(longitude,latitude),4326))
                </when>
                <when test="param.key == 'personId'">
                    and person_id = #{param.value}
                </when>
            </choose>
        </foreach>
        ) t on w.id=t.warning_id
        join t_profile_person p on p.id=t.person_id
        <where>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(searchParams.searchValue)">
                <bind name="pattern" value="'%' + searchParams.searchValue.trim() + '%'"/>
                and (t.address like #{pattern}
                or p.name like #{pattern}
                or p.id_number like #{pattern}
                or p.tel like #{pattern}
                or json_overlaps(p.virtual_identity_ids, (SELECT JSON_ARRAYAGG(id) from t_profile_virtual_identity where
                virtual_number like #{pattern}))
                or json_overlaps(p.vehicle_ids, (SELECT JSON_ARRAYAGG(id) from t_profile_vehicle where car_number like
                #{pattern}))
                )
            </if>
        </where>
        order by w.warning_time desc
    </sql>


    <select id="getMonitorStatistics" resultType="com.trs.police.common.core.vo.KeyValueVO">
        <bind name="filterParams" value="params.filterParams"/>
        SELECT '管控总人数' AS `key`, COUNT(DISTINCT t.value) AS `value`
        FROM (
        SELECT jt.person_id AS `value`
        FROM t_control_monitor t, JSON_TABLE(t.profile_target_id, '$[*]' COLUMNS (person_id JSON PATH '$')) jt
        <include refid="monitorCountSql"/>
        UNION ALL
        SELECT r.target_id AS `value`
        FROM t_control_regular_monitor r
        <include refid="regularCountSql"/>
        ) AS t
        UNION ALL
        SELECT '临控人数' AS `key`, COUNT(DISTINCT(jt.person_id)) AS `value`
        FROM t_control_monitor t, JSON_TABLE(t.profile_target_id, '$[*]' COLUMNS (person_id JSON PATH '$')) jt
        <include refid="monitorCountSql"/>
        UNION ALL
        SELECT '常控人数' AS `key`, COUNT(DISTINCT(r.target_id)) AS `value`
        FROM t_control_regular_monitor r
        <include refid="regularCountSql"/>
    </select>

    <sql id="monitorCountSql">
        WHERE t.monitor_status = 5 and t.monitor_type = 1 and t.deleted != 1
        <if test="filterParams != null ">
            <foreach collection="filterParams" item="param">
                <choose>
                    <when test="param.key == 'permissionDept'">
                        and t.monitor_person_unit in
                        <foreach collection="param.value" open="(" close=")"
                            separator="," item="dept">
                            #{dept}
                        </foreach>
                    </when>
                    <when test="param.key == 'currentUser'">
                        and t.monitor_person_id = #{param.value}
                    </when>
                    <when test="param.key == 'dept'">
                        <bind name="value"
                            value="@com.trs.police.common.core.utils.StringUtil@getPrefixCode(param.value) + '%'"/>
                        and t.monitor_person_unit in (select id from t_dept where code like #{value})
                    </when>
                    <when test="param.key == 'level'">
                        and t.monitor_level = #{param.value}
                    </when>
                    <when test="param.key == 'personLabel'">
                        <bind name="personLabel"
                            value="param.getProcessedValue()"/>
                        and exists(select 1 from t_profile_person p where
                        jt.person_id = p.id and
                        <foreach collection="personLabel" item="item" open="(" separator="OR" close=")">
                        JSON_OVERLAPS(p.person_label
                        ,(SELECT JSON_ARRAYAGG( l.id )
                        FROM t_profile_label l
                        WHERE CONCAT(l.path, l.id, '-')
                        LIKE CONCAT('%-', ${item}, '-%' ))
                        )
                    </foreach>)
                    </when>
                    <when test="'timeRange'.equals(param.key) and param.getProcessedValue().isAll() == false">
                        AND (t.create_time >= #{param.value.beginTime} and t.create_time &lt; #{param.value.endTime})
                    </when>
                </choose>
            </foreach>
        </if>
    </sql>

    <sql id="regularCountSql">
        WHERE r.status = 5
        <if test="filterParams != null ">
            <foreach collection="filterParams" item="param">
                <choose>
                    <when test="param.key == 'permissionDept'">
                        and r.create_dept_id in
                        <foreach collection="param.value" open="(" close=")"
                            separator="," item="dept">
                            #{dept}
                        </foreach>
                    </when>
                    <when test="param.key == 'currentUser'">
                        and r.create_user_id = #{param.value}
                    </when>
                    <when test="param.key == 'dept'">
                        <bind name="value"
                            value="@com.trs.police.common.core.utils.StringUtil@getPrefixCode(param.value) + '%'"/>
                        and r.create_dept_id in (select id from t_dept where code like #{value})
                    </when>
                </choose>
            </foreach>
        </if>
    </sql>
    <select id="getHeatMapData" resultType="com.trs.police.control.domain.vo.homepage.HeatMapVO">
        <bind name="filterParams" value="request.filterParams"/>
        SELECT
        concat('POINT(' ,t.longitude, ' ', t.latitude, ')') as point,
        count(0) as count,
        t.address as address
        from (select longitude,latitude,address,warning_id from t_warning_track where longitude is not null and latitude
        is not null
        <foreach collection="filterParams" item="param">
            <bind name="value" value="param.getProcessedValue()"/>
            <choose>
                <when test="param.key == 'areaWkt'">
                    and ST_CONTAINS(ST_SRID(ST_PolygonFromText(#{param.processedValue}), 4326),
                    ST_SRID(Point(longitude,latitude),4326))
                </when>
                <when test="param.key == 'personLabel'">
                    and exists(select * from t_profile_person p where person_id=p.id and
                    <foreach collection="value" item="item" open="(" separator="OR" close=")">
                        (JSON_OVERLAPS(p.person_label
                        ,(SELECT JSON_ARRAYAGG( l.id )
                        FROM t_profile_label l
                        WHERE CONCAT(l.path, l.id, '-')
                        LIKE CONCAT('%-', ${item}, '-%' ))
                        )
                        >0)
                    </foreach>)
                </when>
            </choose>
        </foreach>
        ) t
        join (select * from t_warning w
        where w.warning_type='person' and w.control_type=1
        <foreach collection="filterParams" item="param">
            <bind name="value" value="param.getProcessedValue()"/>
            <choose>
                <when test="param.key == 'permissionDept'">
                    AND exists (select * from t_warning_notify n where n.warning_id=w.id and n.dept_id in
                    <foreach collection="value" open="(" close=")" separator="," item="dept">
                        #{dept}
                    </foreach>
                    )
                </when>
                <when test="param.key == 'currentUser'">
                    AND exists (select * from t_warning_notify n where n.warning_id=w.id and n.user_id =
                    #{value})
                </when>
                <when test="param.key == 'dept'">
                    <bind name="code"
                        value="@com.trs.police.common.core.utils.StringUtil@getPrefixCode(param.value) + '%'"/>
                    and exists (select 1 from t_warning_notify n where n.warning_id=w.id and (select d.code from
                    t_dept d where d.id=n.dept_id ) like #{code})
                </when>
                <when test="'level'.equals(param.key)">
                    and w.warning_level = ${value}
                </when>
                <when test="'timeRange'.equals(param.key) and param.getProcessedValue().isAll() == false">
                    AND (w.warning_time >= '${value.beginTime}' AND w.warning_time &lt; '${value.endTime}')
                </when>
                <when test="'warningModel'.equals(param.key) and param.value.size() > 0">
                    <bind name="modelIds"
                        value="@com.trs.police.control.domain.builder.WarningListBuilder@getModelIds(value)"/>
                    AND JSON_OVERLAPS((ifnull(w.model_id,'[]')),
                    (select JSON_ARRAYAGG(id) from t_control_monitor_warning_model where id in
                    <foreach collection="modelIds" item="modelId" separator="," open="(" close=")">
                        ${modelId}
                    </foreach>
                    ))>0
                </when>
            </choose>
        </foreach>
        ) w on w.id = t.warning_id
        group by t.longitude,t.longitude
    </select>
    <select id="getPersonWarningCount" resultType="com.trs.police.common.core.vo.KeyValueVO">
        <bind name="filterParams" value="params.filterParams"/>
        select
        warning_level as 'key',count(1) as 'value'
        from t_warning w
        where w.warning_type = 'person' and w.control_type = 1
        and w.id in (select t.warning_id from t_warning_track t where t.person_id = #{personId} and t.control_type = 1)
        GROUP BY w.warning_level
        ORDER BY w.warning_level
    </select>

    <select id="getLevelStatistics" resultType="com.trs.police.common.core.vo.KeyValueVO">
        <bind name="filterParams" value="params.filterParams"/>
        SELECT t.monitor_level as 'key', count(distinct (jt.person_id)) AS `value`
        FROM t_control_monitor t, JSON_TABLE(t.profile_target_id, '$[*]' COLUMNS (person_id JSON PATH '$')) jt
        <include refid="monitorCountSql"/>
        group by t.monitor_level
    </select>


    <select id="getLabelStatistics" resultType="com.trs.police.common.core.vo.CodeNameCountVO">
        <bind name="filterParams" value="params.filterParams"/>
        select temp.* from (
        <foreach collection="personLabels" item="personLabel" separator=" union all">
            (SELECT (select name from t_profile_label where id = #{personLabel.key}) as name,#{personLabel.key} as code,
            count(p.id) AS count
            from t_profile_person p
            join (SELECT DISTINCT(jt.person_id) as person_id
            FROM t_control_monitor t,
            JSON_TABLE(
            t.profile_target_id,
            '$[*]' COLUMNS ( person_id JSON PATH '$' )) jt
            <include refid="monitorCountSql"/>) t on p.id = t.person_id
            where JSON_OVERLAPS(p.person_label,
            #{personLabel.processedValue,typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler}))
        </foreach>
        ) temp order by temp.count desc
    </select>


    <select id="getPlaceCount" resultType="com.trs.police.control.domain.vo.homepage.PlaceCountVO">
        select place, address, count(*) as count
        from t_warning_track t left join t_warning w on t.warning_id = w.id
        where t.source_id like concat('%', #{type})
        <foreach collection="params" item="param">
            <bind name="value" value="param.getProcessedValue()"/>
            <choose>
                <when test="param.key == 'dept'">
                    <bind name="value"
                        value="@com.trs.police.common.core.utils.StringUtil@getPrefixCode(filterParam.value) + '%'"/>
                    and exists (select * from t_control_monitor m where m.id=w.monitor_id and (select d.code from
                    t_dept d where d.id=m.create_dept_id) like #{value})
                </when>
                <when test="'level'.equals(param.key)">
                    and w.warning_level = ${value}
                </when>
                <when test="'timeRange'.equals(param.key) and param.getProcessedValue().isAll() == false">
                    AND t.activity_time >= '${value.beginTime}' and t.activity_time &lt; '${value.endTime}'
                </when>
                <when test="param.key == 'personLabel'">
                    <bind name="personLabel"
                        value="param.getProcessedValue()"/>
                    and
                    <foreach collection="personLabel" item="item" open="(" separator="OR" close=")">
                        (JSON_OVERLAPS(w.person_label
                        ,(SELECT JSON_ARRAYAGG( l.id )
                        FROM t_profile_label l
                        WHERE CONCAT(l.path, l.id, '-')
                        LIKE CONCAT('%-', ${item}, '-%' ))
                        )
                        >0)
                    </foreach>
                </when>
            </choose>
        </foreach>
        group by place
        order by count desc
        limit 10
    </select>

    <select id="getDistributionStatistics" resultType="com.trs.police.common.core.vo.CodeNameCountVO">
        <bind name="filterParams" value="params.filterParams"/>
        <if test="depts.size() == 0">
            SELECT t.monitor_person_id as code,
            concat(
            (select real_name from t_user where id = t.monitor_person_id ),'(',
            (select short_name from t_dept where id = t.monitor_person_unit),')'
            ) as name,
            count(distinct (jt.person_id)) AS count
            FROM t_control_monitor t, JSON_TABLE(t.profile_target_id, '$[*]' COLUMNS (person_id JSON PATH '$')) jt
            <include refid="monitorCountSql"/>
            group by t.monitor_person_id,t.monitor_type
            order by count desc
        </if>
        <if test="depts.size() > 0">
            select temp.* from (
            <foreach collection="depts" item="d" separator=" union all">
                (select #{d.shortName} as name,
                #{d.code} as code,
                count(distinct (jt.person_id)) AS count
                FROM t_control_monitor t, JSON_TABLE(t.profile_target_id, '$[*]' COLUMNS (person_id JSON PATH '$')) jt
                WHERE t.monitor_status = 5 and t.monitor_type = 1 and t.deleted != 1
                and t.monitor_person_unit in (select id from t_dept where code like concat(#{d.prefixCode},'%'))
                <if test="filterParams != null ">
                    <foreach collection="filterParams" item="param">
                        <choose>
                            <when test="param.key == 'permissionDept'">
                                and t.monitor_person_unit in
                                <foreach collection="param.value" open="(" close=")"
                                    separator="," item="dept">
                                    #{dept}
                                </foreach>
                            </when>
                            <when test="param.key == 'currentUser'">
                                and t.monitor_person_id = #{param.value}
                            </when>
                            <when test="param.key == 'level'">
                                and t.monitor_level = #{param.value}
                            </when>
                            <when test="param.key == 'personLabel'">
                                <bind name="personLabel" value="param.getProcessedValue()"/>
                                and exists(select 1 from t_profile_person p where
                                jt.person_id = p.id and
                                <foreach collection="personLabel" item="item" open="(" separator="OR" close=")">
                                     JSON_OVERLAPS(p.person_label
                                    ,(SELECT JSON_ARRAYAGG( l.id )
                                    FROM t_profile_label l
                                    WHERE CONCAT(l.path, l.id, '-')
                                    LIKE CONCAT('%-', ${item}, '-%' ))
                                    )
                                </foreach>)
                            </when>
                        </choose>
                    </foreach>
                </if>
                )
            </foreach>
            ) temp order by temp.count desc
        </if>

    </select>

    <select id="getWarningLevelCount" resultType="com.trs.police.common.core.vo.CodeNameCountVO">
        <bind name="filterParams" value="request.filterParams"/>
        SELECT w.warning_level as code,count(w.id) as count  FROM t_warning w
        <where>
            w.warning_type = 'person'
            and w.control_type = 1
            AND (w.warning_time >= #{timeParams.beginTime} and w.warning_time &lt; #{timeParams.endTime})
            <foreach collection="filterParams" item="param">
                <choose>
                    <when test="param.key == 'dept'">
                        <bind name="value"
                            value="@com.trs.police.common.core.utils.StringUtil@getPrefixCode(param.value) + '%'"/>
                        and exists(select id
                        from t_warning_process p where p.warning_id = w.id
                        and p.dept_id in (select id from t_dept d where d.code like #{value}))
                    </when>
                    <when test="param.key == 'permissionDept'">
                        and exists(select id
                        from t_warning_process p where p.warning_id = w.id
                        and p.dept_id in
                        <foreach collection="param.value" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>)
                    </when>
                    <when test="'level'.equals(param.key)">
                        and w.warning_level = #{param.value}
                    </when>
                    <when test="param.key == 'personLabel'">
                        <bind name="personLabel"
                            value="param.getProcessedValue()"/>
                        and
                        <foreach collection="personLabel" item="item" open="(" separator="OR" close=")">
                            (JSON_OVERLAPS(w.person_label
                            ,(SELECT JSON_ARRAYAGG( l.id )
                            FROM t_profile_label l
                            WHERE CONCAT(l.path, l.id, '-')
                            LIKE CONCAT('%-', ${item}, '-%' ))
                            )
                            >0)
                        </foreach>
                    </when>
                </choose>
            </foreach>
        </where>
        group by w.warning_level
    </select>
    <select id="getModelCount" resultType="com.trs.police.control.domain.vo.homepage.ModelCountVo">
        <bind name="filterParams" value="request.filterParams"/>
        SELECT (select title from t_control_monitor_warning_model where id=modelId) as modelName,
        w.modelId,
        (select name from t_dict where type='control_model_type' and code=(select type from
        t_control_monitor_warning_model where id=w.modelId)) as modelType,
        count(1) as count,
        count(CASE WHEN exists (select  1 from t_warning_process p where p.warning_id=w.warning_id and p.status=4 ) THEN 1 END) as doneCount
        from
        (SELECT w.id as warning_id, jt.modelId,w.warning_level,w.person_label,w.warning_time FROM t_warning w,
        json_table(w.model_id, '$[*]' COLUMNS ( modelId JSON PATH '$' )) jt
        <where>
            w.warning_type = 'person'
            and w.control_type = 1
            <foreach collection="filterParams" item="param">
                <choose>
                    <when test="param.key == 'permissionDept'">
                        AND exists (select * from t_warning_notify n where n.warning_id=w.id and n.dept_id in
                        <foreach collection="param.value" open="(" close=")" separator="," item="dept">
                            #{dept}
                        </foreach>
                        )
                    </when>
                    <when test="param.key == 'currentUser'">
                        AND exists (select * from t_warning_notify n where n.warning_id=w.id and n.user_id =
                        #{param.value})
                    </when>
                    <when test="param.key == 'dept'">
                        <bind name="value"
                            value="@com.trs.police.common.core.utils.StringUtil@getPrefixCode(param.value) + '%'"/>
                        and exists (select * from t_warning_notify n where n.warning_id=w.id and (select d.code from
                        t_dept d where d.id=n.dept_id ) like #{value})
                    </when>
                    <when test="'level'.equals(param.key)">
                        and w.warning_level = #{param.value}
                    </when>
                    <when test="'timeRange'.equals(param.key) and param.getProcessedValue().isAll() == false">
                        AND (w.warning_time >= '${param.value.beginTime}' and w.warning_time &lt; '${param.value.endTime}')
                    </when>
                    <when test="param.key == 'personLabel'">
                        <bind name="personLabel"
                            value="param.getProcessedValue()"/>
                        and
                        <foreach collection="personLabel" item="item" open="(" separator="OR" close=")">
                            (JSON_OVERLAPS(w.person_label
                            ,(SELECT JSON_ARRAYAGG( l.id )
                            FROM t_profile_label l
                            WHERE CONCAT(l.path, l.id, '-')
                            LIKE CONCAT('%-', ${item}, '-%' ))
                            )
                            >0)
                        </foreach>
                    </when>
                </choose>
            </foreach>
        </where>
        ) w
        GROUP BY w.modelId
    </select>

    <select id="getDistrictHandle" resultType="com.trs.police.common.core.vo.CodeNameCountVO">
        SELECT
        p.STATUS as code,
        count( p.id ) AS count
        FROM
        t_warning_process p
        <where>
            <bind name="deptCodePrefix"
                value="@com.trs.police.common.core.utils.StringUtil@getPrefixCode(deptCode) + '%'"/>
            p.dept_id in (select id from t_dept d where d.code like CONCAT(#{deptCodePrefix},'%'))
            AND EXISTS (SELECT id FROM t_warning w
            <where>
                w.warning_type = 'person'
                AND w.control_type = 1
                AND w.id = p.warning_id
                <foreach collection="request.filterParams" item="filterParam">
                    <choose>
                        <when test="filterParam.key=='timeRange'">
                            AND w.warning_time >= #{filterParam.value.beginTime} and w.warning_time &lt; #{filterParam.value.endTime}
                        </when>
                        <when test="filterParam.key=='level'">
                            AND w.warning_level = #{filterParam.value}
                        </when>
                    </choose>
                </foreach>
            </where>
            )
        </where>
        GROUP BY p.STATUS
    </select>
    <select id="getMonitorPersonList"
        resultType="com.trs.police.control.domain.vo.homepage.MonitorPersonListVO">
        <bind name="filterParams" value="params.filterParams"/>
        <bind name="sortParams" value="params.sortParams"/>
        SELECT c.person_id,t.create_time as lastWarningTime,t.activity_time as lastTrackTime
        from ( select distinct(jt.person_id) as person_id FROM t_control_monitor mo,
        JSON_TABLE(mo.profile_target_id, '$[*]' COLUMNS (person_id JSON PATH '$')) jt
        where mo.monitor_type = 1 and mo.monitor_status = 5 and mo.deleted != 1
        <foreach collection="filterParams" item="filterParam">
            <choose>
                <when test="filterParam.key == 'level'">
                    and mo.monitor_level = #{filterParam.value}
                </when>
                <when test="filterParam.key == 'dept'">
                    <bind name="value"
                        value="@com.trs.police.common.core.utils.StringUtil@getPrefixCode(filterParam.value) + '%'"/>
                    and mo.monitor_person_unit in (select id from t_dept where code like #{value})
                </when>
                <when test="filterParam.key == 'personLabel'">
                    <bind name="personLabel"
                          value="filterParam.getProcessedValue()"/>
                    and exists(select 1 from t_profile_person p where
                    jt.person_id = p.id and
                    <foreach collection="personLabel" item="item" open="(" separator="OR" close=")">
                        (JSON_OVERLAPS(p.person_label
                        ,(SELECT JSON_ARRAYAGG( l.id )
                        FROM t_profile_label l
                        WHERE CONCAT(l.path, l.id, '-')
                        LIKE CONCAT('%-', ${item}, '-%' ))
                        )
                        >0)
                    </foreach>)
                </when>
                <when test="filterParam.key == 'permissionDept'">
                    and mo.monitor_person_unit in
                    <foreach collection="filterParam.value" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </when>
                <when test="filterParam.key == 'currentUser'">
                    and mo.monitor_person_id = #{filterParam.value}
                </when>
                <when test="filterParam.key == 'currentUserDept'">
                    and mo.monitor_person_unit = #{filterParam.value}
                </when>
            </choose>
        </foreach>
        ) c
        left join (select create_time,activity_time,person_id from t_warning_track where control_type = 1) t on
        c.person_id = t.person_id
        <where>
            <foreach collection="filterParams" item="filterParam">
                <choose>
                    <!--<when test="filterParam.key == 'timeRange' and filterParam.getProcessedValue().isAll() == false ">
                        and t.create_time &gt;= #{filterParam.value.beginTime}
                        and t.create_time &lt;= #{filterParam.value.endTime}
                    </when>-->
                    <when test="filterParam.key == 'hasWarning'">
                        <choose>
                            <when test="filterParam.value == 1">
                                and t.create_time is not null
                            </when>
                            <!--<when test="filterParam.value == 0 ">
                                and t.create_time is null
                            </when>-->
                        </choose>
                    </when>
                </choose>
            </foreach>
        </where>
        group by c.person_id
        <if test="sortParams != null">
            <bind name="sortField" value="sortParams.sortField"/>
            <bind name="sortValue" value="sortParams.getProcessedValue()"/>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(sortField) ">
                order by
                <choose>
                    <when test="sortField == 'lastWarningTime' ">
                        t.create_time ${sortValue}
                    </when>
                    <when test="sortField == 'lastTrackTime'">
                        t.activity_time ${sortValue}
                    </when>
                    <otherwise>
                        t.create_time desc
                    </otherwise>
                </choose>
            </if>
        </if>
    </select>

    <select id="getUserHandle" resultType="com.trs.police.common.core.vo.CodeNameCountVO">
        SELECT t.CODE,
        t.NAME,
        count(process.id) as count
        FROM (SELECT CODE, NAME FROM t_dict d WHERE d.type = 'control_warning_status') t
        LEFT JOIN
        (
        SELECT p.id, p.status
        FROM t_warning_process p
        <where>
            EXISTS(select id from t_warning_notify n where n.user_id = #{userId} and n.process_id = p.id)
            AND EXISTS (SELECT id
            FROM t_warning w
            <where>
                w.warning_type = 'person'
                AND w.control_type = 1
                AND w.id = p.warning_id
                <foreach collection="request.filterParams" item="filterParam">
                    <choose>
                        <when test="filterParam.key == 'timeRange'">
                            AND w.warning_time >= #{filterParam.value.beginTime}
                            and w.warning_time &lt; #{filterParam.value.endTime}
                        </when>
                        <when test="filterParam.key == 'level'">
                            AND w.warning_level = #{filterParam.value}
                        </when>
                    </choose>
                </foreach>
            </where>
            )
        </where>
        ) process
        ON t.CODE = process.STATUS
        GROUP BY t.CODE
        ORDER BY t.CODE
    </select>

    <select id="countHaveTrackPersonByDept" resultType="java.lang.Integer">
        select count(id)
        from t_profile_person tpp where
        <foreach collection="request.filterParams" item="filterParam">
            <choose>
                <when test="filterParam.key == 'personLabel'">
                    <bind name="personLabel"
                        value="filterParam.getProcessedValue()"/>
                    <foreach collection="personLabel" item="item" open="(" separator="OR" close=")">
                        (JSON_OVERLAPS(tpp.person_label
                        ,(SELECT JSON_ARRAYAGG( l.id )
                        FROM t_profile_label l
                        WHERE CONCAT(l.path, l.id, '-')
                        LIKE CONCAT('%-', ${item}, '-%' ))
                        )
                        >0)
                    </foreach>
                    and
                </when>
            </choose>
        </foreach>
        exists(select *
        from t_warning_track twt
        left join t_control_monitor tcm on twt.monitor_id = tcm.id
        where twt.person_id = tpp.id
        and tcm.monitor_unit_code like concat(#{deptCodePrefix}, '%')
        <foreach collection="request.filterParams" item="filterParam">
            <choose>
                <when test="filterParam.key == 'level'">
                    AND tcm.monitor_level = #{filterParam.value}
                </when>
                <when test="filterParam.key == 'timeRange'">
                    AND twt.activity_time >= #{filterParam.value.beginTime} and twt.activity_time &lt; #{filterParam.value.endTime}
                </when>
            </choose>
        </foreach>
        )
    </select>

    <select id="countPersonByDept" resultType="java.lang.Integer">
        select count(id)
        from t_profile_person tpp where
        <foreach collection="request.filterParams" item="filterParam">
            <choose>
                <when test="filterParam.key == 'personLabel'">
                    <bind name="personLabel"
                        value="filterParam.getProcessedValue()"/>
                    <foreach collection="personLabel" item="item" open="(" separator="OR" close=")">
                        (JSON_OVERLAPS(tpp.person_label
                        ,(SELECT JSON_ARRAYAGG( l.id )
                        FROM t_profile_label l
                        WHERE CONCAT(l.path, l.id, '-')
                        LIKE CONCAT('%-', ${item}, '-%' ))
                        )
                        >0)
                    </foreach>
                    and
                </when>
            </choose>
        </foreach>
        exists(select * from t_control_monitor tcm
        where json_contains(tcm.profile_target_id, convert(tpp.id, char))
        and tcm.monitor_unit_code like concat(#{deptCodePrefix}, '%')
        <foreach collection="request.filterParams" item="filterParam">
            <choose>
                <when test="filterParam.key == 'level'">
                    AND tcm.monitor_level = #{filterParam.value}
                </when>
            </choose>
        </foreach>
        )
    </select>

    <select id="countHaveTrackPersonByUser" resultType="java.lang.Integer">
        select count(id)
        from t_profile_person tpp where
        <foreach collection="request.filterParams" item="filterParam">
            <choose>
                <when test="filterParam.key == 'personLabel'">
                    <bind name="personLabel"
                        value="filterParam.getProcessedValue()"/>
                    <foreach collection="personLabel" item="item" open="(" separator="OR" close=")">
                        (JSON_OVERLAPS(tpp.person_label
                        ,(SELECT JSON_ARRAYAGG( l.id )
                        FROM t_profile_label l
                        WHERE CONCAT(l.path, l.id, '-')
                        LIKE CONCAT('%-', ${item}, '-%' ))
                        )
                        >0)
                    </foreach>
                    and
                </when>
            </choose>
        </foreach>
        exists(select *
        from t_warning_track twt
        left join t_control_monitor tcm on twt.monitor_id = tcm.id
        where twt.person_id = tpp.id
        and tcm.monitor_person_id = #{userId}
        <foreach collection="request.filterParams" item="filterParam">
            <choose>
                <when test="filterParam.key == 'level'">
                    AND tcm.monitor_level = #{filterParam.value}
                </when>
                <when test="filterParam.key == 'timeRange'">
                    AND twt.activity_time >= #{filterParam.value.beginTime} and twt.activity_time &lt; #{filterParam.value.endTime}
                </when>
            </choose>
        </foreach>
        )
    </select>

    <select id="countPersonByUser" resultType="java.lang.Integer">
        select count(id)
        from t_profile_person tpp where
        <foreach collection="request.filterParams" item="filterParam">
            <choose>
                <when test="filterParam.key == 'personLabel'">
                    <bind name="personLabel"
                        value="filterParam.getProcessedValue()"/>
                    <foreach collection="personLabel" item="item" open="(" separator="OR" close=")">
                        (JSON_OVERLAPS(tpp.person_label
                        ,(SELECT JSON_ARRAYAGG( l.id )
                        FROM t_profile_label l
                        WHERE CONCAT(l.path, l.id, '-')
                        LIKE CONCAT('%-', ${item}, '-%' ))
                        )
                        >0)
                    </foreach>
                    and
                </when>
            </choose>
        </foreach>
        tpp.id in (
        select distinct(jt.person_id) as person_id
        FROM t_control_monitor mo,
        JSON_TABLE(mo.profile_target_id, '$[*]' COLUMNS (person_id JSON PATH '$')) jt
        where mo.monitor_type = 1
        and mo.monitor_status = 5
        and mo.monitor_person_id = #{userId}
        <foreach collection="request.filterParams" item="filterParam">
            <choose>
                <when test="filterParam.key == 'level'">
                    AND mo.monitor_level = #{filterParam.value}
                </when>
            </choose>
        </foreach>
          )
    </select>

    <select id="selectMonitorUser" resultType="com.trs.police.common.core.vo.IdNameVO">
        select u.id as id, u.real_name as name
        from t_user u
        where EXISTS(select *
                     from t_control_monitor tcm
                     where tcm.monitor_unit_code = #{deptCode}
                       and tcm.monitor_person_id = u.id)
    </select>

    <select id="getLastTrack" resultType="com.trs.police.control.domain.vo.homepage.TrackVO">
        select concat('POINT(', track.longitude, ' ', track.latitude, ')') as point,
               ifnull(track.address, '- -')                                as address,
               track.create_time                                           as time
        from t_warning_track track
        where person_id = #{personId} and control_type = 1
        order by create_time desc
        limit 1
    </select>

    <select id="getLastTrackBatch" resultType="com.trs.police.control.domain.vo.homepage.TrackVO">
        SELECT
            concat('POINT(', t.longitude, ' ', t.latitude, ')') as point,
            ifnull(t.address, '- -') as address,
            t.create_time as time,
            t.person_id as personId
        FROM (
            SELECT
                track.*,
                ROW_NUMBER() OVER (PARTITION BY person_id ORDER BY create_time DESC) as rn
                FROM t_warning_track track
                WHERE person_id IN <foreach collection="personId" item="it" open="(" separator="," close=")">#{it}</foreach>
                AND control_type = 1
        ) t
        WHERE t.rn = 1
    </select>

    <select id="getWarningList" resultMap="warningEntity">
        <bind name="filterParams" value="request.filterParams"/>
        <bind name="sortParams" value="request.sortParams"/>
        select w.*
        from (select w.* from t_warning w
        where w.warning_type='person' and w.control_type=1
        <foreach collection="filterParams" item="param">
            <choose>
                <when test="param.key == 'permissionDept'">
                    AND exists (select * from t_warning_notify n where n.warning_id=w.id and n.dept_id in
                    <foreach collection="param.value" open="(" close=")" separator="," item="dept">
                        #{dept}
                    </foreach>
                    )
                </when>
                <when test="param.key == 'currentUser'">
                    AND exists (select * from t_warning_notify n where n.warning_id=w.id and n.user_id =
                    #{param.value})
                </when>
                <when test="param.key == 'dept'">
                    <bind name="value"
                        value="@com.trs.police.common.core.utils.StringUtil@getPrefixCode(param.value) + '%'"/>
                    and exists (select 1 from t_warning_notify n where n.warning_id=w.id and (select d.code from
                    t_dept d where d.id=n.dept_id) like #{value})
                </when>
                <when test="param.key == 'personLabel'">
                    <bind name="personLabel" value="param.getProcessedValue()"/>
                    and
                    <foreach collection="personLabel" item="item" open="(" separator="OR" close=")">
                        (JSON_OVERLAPS(w.person_label
                        ,(SELECT JSON_ARRAYAGG( l.id )
                        FROM t_profile_label l
                        WHERE CONCAT(l.path, l.id, '-')
                        LIKE CONCAT('%-', ${item}, '-%' ))
                        )
                        >0)
                    </foreach>
                </when>
                <when test="'level'.equals(param.key) and param.value !=0">
                    and w.warning_level = ${param.value}
                </when>
                <when
                    test="('timeRange'.equals(param.key) or 'trendTime'.equals(param.key)) and param.getProcessedValue().isAll() == false">
                    AND (w.warning_time >= '${param.value.beginTime}' and w.warning_time &lt; '${param.value.endTime}')
                </when>
                <when test="'warningModel'.equals(param.key) and param.value.size() > 0">
                    <bind name="modelIds"
                        value="@com.trs.police.control.domain.builder.WarningListBuilder@getModelIds(param.getProcessedValue())"/>
                    AND JSON_OVERLAPS((ifnull(w.model_id,'[]')),
                    (select JSON_ARRAYAGG(id) from t_control_monitor_warning_model where id in
                    <foreach collection="modelIds" item="modelId" separator="," open="(" close=")">
                        ${modelId}
                    </foreach>
                    ))>0
                </when>
                <when test="param.key == 'modelType'">
                    and #{param.value} member of(w.model_id)
                </when>
            </choose>
        </foreach>
        ) w
        join (select id, address,longitude,latitude,source_type,person_id,warning_id from t_warning_track where
        control_type = 1) t on w.id=t.warning_id
        join t_profile_person p on p.id=t.person_id
        <if test="sortParams != null">
            <bind name="sortField" value="sortParams.sortField"/>
            <bind name="sortValue" value="sortParams.getProcessedValue()"/>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(sortField) ">
                order by
                <choose>
                    <when test="sortField == 'warningTime' ">
                        w.warning_time ${sortValue}
                    </when>
                    <when test="sortField == 'activityTime'">
                        w.activity_time ${sortValue}
                    </when>
                    <otherwise>
                        w.warning_time desc
                    </otherwise>
                </choose>
            </if>
        </if>
        <if test="sortParams == null">
            order by w.warning_time desc
        </if>
    </select>

</mapper>