<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.control.mapper.WarningStxfMapper">

    <resultMap id="warningStxfMap" type="com.trs.police.control.domain.entity.stxf.WarningStxfEntity">
       <result column="id" property="id"/>
        <result column="component_id" property="componentId"/>
        <result column="alarm_humans" property="alarmHumans" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="happened_time" property="happenedTime"/>
        <result column="latitude" property="latitude"/>
        <result column="longitude" property="longitude"/>
        <result column="bkgPic_url" property="bkgPicUrl"/>
        <result column="event_type" property="eventType"/>
        <result column="uuid" property="uuid"/>
        <result column="input_source_name" property="inputSourceName"/>
        <result column="event_Level" property="eventLevel"/>
        <result column="refrence_pic_url" property="refrencePicUrl"/>
        <result column="similarity" property="similarity"/>
        <result column="face_pic_url" property="facePicUrl"/>
        <result column="deployment_id" property="deploymentId"/>
        <result column="source_uuid" property="sourceUuid"/>
        <result column="input_source_index_code" property="inputSourceIndexCode"/>
        <result column="deployment_name" property="deploymentName"/>
        <result column="event_ojective_name" property="eventOjectiveName"/>
        <result column="event_objective_index_code" property="eventObjectiveIndexCode"/>
        <result column="input_source_type" property="inputSourceType"/>
        <result column="deployment_name" property="deploymentName"/>
    </resultMap>
    <select id="selectPageList" resultMap="warningStxfMap">
        <bind name="filterParams" value="request.filterParams"/>
        <bind name="searchParams" value="request.searchParams"/>
        select * from  t_warning_stxf
        <where>
            <if test="filterParams!=null and filterParams.size>0">
                <foreach collection="filterParams" item="param">
                    <bind name="value" value="param.getProcessedValue()"/>
                    <choose>
                        <when test="param.key == 'personType'">
                          and event_ojective_name= '${value}'
                        </when>
                        <when test="param.key == 'warningLevel'">
                            and event_Level= '${value}'
                        </when>
                        <when test="param.key == 'sourceType'">
                            and input_source_type= '${value}'
                        </when>
                        <when test="'warningTime'.equals(param.key) and param.getProcessedValue().isAll() == false">
                            AND (happened_time between '${value.beginTime}' AND '${value.endTime}')
                        </when>
                 </choose>
                </foreach>
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(searchParams.searchValue)">
                <bind name="pattern" value="'%' + searchParams.searchValue.trim() + '%'"/>
                <choose>
                    <when test=" 'targetName'==searchParams.searchField">
                        and alarm_humans->>"$.humanName" like #{pattern}
                    </when>
                    <when test=" 'idNumber'==searchParams.searchField">
                        and alarm_humans->>"$.certificateNumber" like #{pattern}
                    </when>
                    <when test=" 'address'==searchParams.searchField">
                        and input_source_name like #{pattern}
                    </when>
                    <otherwise>
                        and (
                        alarm_humans->>"$.humanName" like #{pattern}
                        or alarm_humans->>"$.certificateNumber" like #{pattern}
                        or input_source_name like #{pattern}
                        )
                    </otherwise>
                </choose>
            </if>
        </where>
        order by happened_time desc
    </select>
</mapper>