<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.search.traffic.police.mapper.TrafficJjwfjlWfjsbMapper">

    <update id="updateWfjs">
        update tb_jjwfjl_wfjsb set wfjs=(
            select count(*) from tb_jjwfjlb where is_del=0 and invisible=0 and status_code != 302 and wfjs_data_id=#{wfjsDataId}
        )
        <where>
            data_id=#{wfjsDataId}
        </where>
    </update>

    <select id="countWfjs" resultType="java.lang.Integer">
        select count(*) from tb_jjwfjlb
        <where>
            is_del=0
            and invisible=0
            and status_code != 302
            and wfjs_data_id=#{wfjsDataId}
        </where>
    </select>
</mapper>