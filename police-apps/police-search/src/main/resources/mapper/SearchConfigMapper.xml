<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.search.mapper.SearchConfigMapper">

    <select id="getByTableEnNameAndRequest" resultType="com.trs.police.search.domain.entity.SearchConfigEntity">
        <bind name="searchParams" value="request.searchParams"/>
        <bind name="filterParams" value="request.filterParams"/>
        select * from t_search_config
        <where>
             table_en_name = #{tableEnName}
            <foreach collection="filterParams" item="filterParam">
                <choose>
                    <when test="filterParam.key == 'is_search'">
                      and  is_search= #{filterParam.value}
                    </when>
                    <when test="filterParam.key == 'is_overview'">
                        and is_overview= #{filterParam.value}
                    </when>
                    <when test="filterParam.key == 'is_detail'">
                        and   is_detail= #{filterParam.value}
                    </when>
                    <when test="filterParam.key == 'is_hign_level_search'">
                        and  is_high_level_search= #{filterParam.value}
                    </when>
                    <when test="filterParam.key == 'is_filter'">
                        and  is_filter= #{filterParam.value}
                    </when>
                </choose>
            </foreach>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(searchParams.searchValue)">
                <bind name="pattern" value="'%' + searchParams.searchValue.trim() + '%'"/>
                and zh_name like #{pattern}
            </if>
        </where>
    </select>
    <select id="listBySearchDTO" resultType="com.trs.police.search.domain.entity.SearchConfigEntity"
            parameterType="com.trs.police.search.panorama.dto.SearchConfigDTO">
        SELECT  * FROM `t_search_config`
        <where>
            `is_del` = 0 AND
            `table_en_name` = #{dto.tableEnName}
            <if test="dto.isFilter != null">
                <if test="dto.isFilter == true">
                    AND `is_filter` = 1
                </if>
                <if test="dto.isFilter == false">
                    AND `is_filter` = 0
                </if>
            </if>
            <if test="dto.isOverview != null">
                <if test="dto.isOverview == true">
                    AND `is_overview` = 1
                </if>
                <if test="dto.isOverview == false">
                    AND `is_overview` = 0
                </if>
            </if>
            <if test="dto.isDetail != null">
                <if test="dto.isDetail == true">
                    AND `is_detail` = 1
                </if>
                <if test="dto.isDetail == false">
                    AND `is_detail` = 0
                </if>
            </if>
            <if test="dto.isOrder != null">
                <if test="dto.isOrder == true">
                    AND `is_order` = 1
                </if>
                <if test="dto.isOrder == false">
                    AND `is_order` = 0
                </if>
            </if>
            <if test="dto.keyword != null and dto.keyword != ''">
                AND CONCAT(IFNULL(`table_en_name`,''),IFNULL(`zh_name`,''),IFNULL(`en_name`,'')) LIKE CONCAT('%',#{dto.keyword},'%')
            </if>
            <if test="dto.affiliatedEntities != null and dto.affiliatedEntities != ''">
                AND `affiliated_entity` IN
                <foreach collection="dto.affiliatedEntities.split(',')" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY `order` ASC
    </select>
</mapper>