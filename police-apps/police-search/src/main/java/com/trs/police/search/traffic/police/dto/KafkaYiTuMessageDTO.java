package com.trs.police.search.traffic.police.dto;

import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import lombok.Data;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;

/**
 * @ClassName YiTuMessageDTO
 * @Description 依图推送消息dto
 * <AUTHOR>
 * @Date 2023/12/20 9:23
 **/
@Data
public class KafkaYiTuMessageDTO implements Serializable {

    /**
     * faceId
     */
    private String faceId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 小图base64
     */
    private String smallImageBase64;

    /**
     * 大图（场景）base64
     */
    private String sceneImageBase64;

    /**
     * 相似度
     */
    private Double similarity;

    /**
     * 违法行为代码
     */
    private String illegalActCode;

    /**
     * 精度
     */
    private Double longitude;

    /**
     * 维度
     */
    private Double latitude;

    /**
     * 省名称
     */
    private String province;

    /**
     * 市名称
     */
    private String city;

    /**
     * 区县名称
     */
    private String county;

    /**
     * 乡镇/街道名称
     */
    private String town;

    /**
     * 抓拍时所处道路名称
     */
    private String road;

    /**
     * 摄像头国标编码
     */
    private String internationalCode;

    /**
     * 抓拍时间，13位时间戳
     */
    private Long captureTimestamp;

    /**
     * <ul>
     *     <li>1: 代表移动</li>
     *     <li>2: 代表电信</li>
     * </ul>
     */
    private String provider;

    /**
     * makeAge<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/3/20 14:11
     */
    public Integer makeAge() {
        if (StringUtils.isNotEmpty(getIdCard())) {
            Integer year = Integer.valueOf(getIdCard().substring(6, 10));
            Integer now = TimeUtils.getFieldOfDate(new Date(), Calendar.YEAR);
            return Math.abs(now - year);
        }
        return 0;
    }

}
