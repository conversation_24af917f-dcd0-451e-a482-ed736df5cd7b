package com.trs.police.search.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * SearchTableTypeEntity
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/11/2 20:36
 * @since 1.0
 */
@Data
@TableName("tb_search_table_type")
public class SearchTableTypeEntity implements Serializable {

    @TableId(type = IdType.AUTO)
    public Long id;

    /**
     * 一级分类
     */
    @TableField
    private String type;

    /**
     * 二级分类
     */
    @TableField
    private String subType;

    /**
     * 排序，从小到大排序
     */
    private Integer orderNum;
}
