package com.trs.police.search.panorama.vo;

import com.trs.common.pojo.BaseVO;
import com.trs.police.common.core.vo.search.KeyValueTypeVoForSearch;
import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * @ClassName PanoramaListVO
 * @Description 全景检索响应VO
 * <AUTHOR>
 * @Date 2023/9/27 17:04
 **/
@Data
public class PanoramaVO extends BaseVO {

    private String schemaName;

    private String zhName;

    private String recordId;

    private String idField;

    private List<PanoramaVO> list;

    private List<KeyValueTypeVoForSearch> fields;

    private List<String> special;

    public PanoramaVO() {
        this.special = Collections.emptyList();
    }
}
