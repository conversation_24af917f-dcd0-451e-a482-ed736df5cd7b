package com.trs.police.search.traffic.police.vo;

import com.trs.common.pojo.BaseVO;
import com.trs.police.common.core.vo.permission.UnitTreeVO;
import lombok.Data;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 创建时间：2023/12/21 11:18
 * @since 1.0
 */
@Data
public class DataListVO extends BaseVO {

    /**
     * 记录所属级别
     */
    private Integer level;

    /**
     * 姓名
     */
    private String name;

    /**
     * 数据ID
     */
    private Long dataId;

    /**
     * 关联的违法计数ID
     */
    private Long wfjsDataId;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 户籍地址
     */
    private String hjdz;

    /**
     * 小图base64
     */
    private String smallImageBase64;

    /**
     * 大图(场景)base64
     */
    private String sceneImageBase64;

    /**
     * 图片标识
     * <ul>
     *     <li>Base64</li>
     *     <li>url</li>
     * </ul>
     */
    private String imageFlag;


    /**
     * 相似度
     */
    private Double similarity;

    /**
     * 违法行为代码
     */
    private String illegalActCode;

    /**
     * 违法行为名称
     */
    private String illegalActName;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 经度
     */
    private Double latitude;

    /**
     * 省名称
     */
    private String province;

    /**
     * 市名称
     */
    private String city;

    /**
     * 区/县名称
     */
    private String county;

    /**
     * 乡镇/街道名称
     */
    private String town;

    /**
     * 抓拍时所处道路名称
     */
    private String road;

    /**
     * 摄像头国标编码
     */
    private String internationalCode;

    /**
     * 状态码
     */
    private String statusCode;

    /**
     * 状态名
     */
    private String statusName;

    /**
     * 抓拍时间 yy-MM-dd HH:mm:ss
     */
    private String captureTimestamp;

    /**
     * 预警时间 yy-MM-dd HH:mm:ss
     */
    private String warnTime;

    /**
     * 处理时间 yy-MM-dd HH:mm:ss
     */
    private String dealTime;

    /**
     * 下发时间 yy-MM-dd HH:mm:ss
     */
    private String assignTime;

    /**
     * 导出时间 yy-MM-dd HH:mm:ss
     */
    private String exportTime;

    /**
     * 处置单位
     */
    private UnitTreeVO czdw;

    /**
     * 违法计数
     */
    private Integer wfjs;

    /**
     * 处理结果
     */
    private String dealResult;

    /**
     * <ul>
     *     <li>1: 代表移动</li>
     *     <li>2: 代表电信</li>
     * </ul>
     */
    private String provider;

    /**
     * 所属乡镇街道名称
     */
    private String xzjdmc;

    /**
     * 所属行政村名称
     */
    private String xzcmc;

    /**
     * 是否能够被指派
     */
    private String rollbackType;

}
