package com.trs.police.search.constant.enums;

import com.trs.common.base.PreConditionCheck;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * TableFieldDataTypeEnum
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023-11-05 16:35
 * @since 1.0
 */
@Getter
public enum TableFieldDataTypeEnum {
    STRING(
            "string",
            "字符型",
            "c",
            Arrays.asList(
                    TableFieldInputTypeEnum.INPUT,
                    TableFieldInputTypeEnum.QUERY_STRING
            )
    ),
    DATE(
            "date",
            "日期型",
            "d8",
            Arrays.asList(
                    TableFieldInputTypeEnum.DATE,
                    TableFieldInputTypeEnum.DATERANGE
            )
    ),
    DATETIME(
            "datetime",
            "日期时间型",
            "d14",
            Arrays.asList(
                    TableFieldInputTypeEnum.TIME,
                    TableFieldInputTypeEnum.TIMERANGE,
                    TableFieldInputTypeEnum.DATE,
                    TableFieldInputTypeEnum.DATERANGE
            )
    ),
    TIME(
            "time",
            "时间型",
            "d14",
            Arrays.asList(
                    TableFieldInputTypeEnum.TIME,
                    TableFieldInputTypeEnum.TIMERANGE,
                    TableFieldInputTypeEnum.DATE,
                    TableFieldInputTypeEnum.DATERANGE
            )
    ),
    BOOLEAN(
            "boolean",
            "布尔型",
            "bl",
            Arrays.asList(
                    TableFieldInputTypeEnum.INPUT
            )
    ),
    BINARY(
            "binary",
            "二进制型",
            "bn",
            Arrays.asList(
                    TableFieldInputTypeEnum.INPUT
            )
    ),
    NUMERIC(
            "numeric",
            "数值型",
            "n..10,2",
            Arrays.asList(
                    TableFieldInputTypeEnum.INPUT,
                    TableFieldInputTypeEnum.RANGE
            )
    ),
    TIMESTAMP(
            "timestamp",
            "时间戳型",
            "tms",
            Arrays.asList(
                    TableFieldInputTypeEnum.INPUT,
                    TableFieldInputTypeEnum.RANGE,
                    TableFieldInputTypeEnum.DATERANGE,
                    TableFieldInputTypeEnum.TIMERANGE
            )
    );

    private String dataType;

    private String dataTypeCn;

    private String displayFormat;

    private List<TableFieldInputTypeEnum> inputType;

    TableFieldDataTypeEnum(String dataType, String dataTypeCn, String displayFormat, List<TableFieldInputTypeEnum> inputType) {
        this.dataType = dataType;
        this.dataTypeCn = dataTypeCn;
        this.displayFormat = displayFormat;
        this.inputType = inputType;
    }

    /**
     * findByType<BR>
     *
     * @param dataType 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023-11-05 17:20
     */
    public static TableFieldDataTypeEnum findByType(String dataType) {
        PreConditionCheck.checkNotEmpty(dataType, "类型不能为空");
        for (TableFieldDataTypeEnum value : TableFieldDataTypeEnum.values()) {
            if (dataType.equals(value.getDataType())) {
                return value;
            }
        }
        throw new IllegalArgumentException("未知类型[" + dataType + "]");
    }
}
