package com.trs.police.search.panorama.dto;

import com.trs.police.common.openfeign.starter.DTO.BaseArchivesSearchDTO;
import lombok.Data;

/**
 * @ClassName CarArchivesSearchDTO
 * @Description 车辆检索dto
 * <AUTHOR>
 * @Date 2023/10/24 11:21
 **/
@Data
public class CarArchivesSearchDTO extends BaseArchivesSearchDTO {

    /**
     * 号牌号码
     */
    private String number;

    /**
     * 号牌种类
     */
    private String type;

    /**
     * 号牌颜色
     */
    private String color;

    /**
     * 车辆品牌
     */
    private String brand;

    /**
     * 车辆识别代码
     */
    private String idNumber;

    /**
     * 车辆制造厂
     */
    private String manufacturer;

    /**
     * 车主姓名
     */
    private String ownerName;

    /**
     * 车主身份证号码
     */
    private String ownerIdCard;

    /**
     * 能源种类代码
     */
    private String energyTypeCode;

    /**
     * 国产尽快分类代码
     */
    private String importTypeCode;

    /**
     * 机动车序号
     */
    private String serialNumber;

    /**
     * 机动车状态
     */
    private String status;

    /**
     * 发动机号
     */
    private String engineNumber;
}
