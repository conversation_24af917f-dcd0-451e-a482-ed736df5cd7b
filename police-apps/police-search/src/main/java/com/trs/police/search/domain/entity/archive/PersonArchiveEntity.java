package com.trs.police.search.domain.entity.archive;

import com.trs.db.sdk.annotations.TableField;
import lombok.Data;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/1/30 16:08
 * @since 1.0
 */
@Data
public class PersonArchiveEntity extends BaseArchiveEntity {

    @TableField
    private String xm;

    @TableField
    private String zjhm;

    @TableField
    private String cym;

    @TableField
    private String xb;

    @TableField
    private String mz;

    @TableField
    private String csrq;

    @TableField
    private String jg;

    @TableField
    private String zy;

    @TableField
    private String fwcs;

    @TableField
    private String whcd;

    @TableField
    private String hyzk;

    @TableField
    private String byzk;

    @TableField
    private String zjxy;

    @TableField
    private String zzmm;

    @TableField
    private String xx;

    @TableField
    private String sg;

    @TableField
    private String xzdz;

    @TableField("unit_code")
    private String unitCode;

    @TableField("unit_name")
    private String unitName;

    @TableField("area_code")
    private String areaCode;

    @TableField(multivalue = true, objectValue = true)
    private String tags;

}
