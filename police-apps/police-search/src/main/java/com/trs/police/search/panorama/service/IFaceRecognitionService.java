package com.trs.police.search.panorama.service;

import com.trs.common.exception.ServiceException;
import com.trs.police.search.panorama.dto.FaceRecognitionDTO;
import com.trs.vo.TargetVo;
import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * IFaceRecognitionService
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/1/9 19:19
 * @since 1.0
 */
public interface IFaceRecognitionService {

    /**
     * 人像识别<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/9 19:20
     */
    List<TargetVo> faceRecognition(FaceRecognitionDTO dto) throws ServiceException;

    /**
     * findParses<BR>
     *
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/11 21:42
     */
    List<BaseFaceRecognitionParse> findParses() throws ServiceException;
}
