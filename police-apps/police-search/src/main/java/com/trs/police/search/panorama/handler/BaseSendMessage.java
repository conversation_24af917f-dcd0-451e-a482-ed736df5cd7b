package com.trs.police.search.panorama.handler;

import com.trs.common.exception.ServiceException;
import com.trs.common.utils.StringUtils;
import com.trs.mq.kafka.config.KafkaAbstractConfiguration;
import com.trs.mq.kafka.config.KafkaConfigurations;
import com.trs.mq.kafka.config.KafkaConfigurations.CONFIG_TYPE;
import com.trs.mq.kafka.config.KafkaConfigurations.HARDWARE_LEVEL;
import com.trs.mq.kafka.producer.KafkaProducers;
import com.trs.mq.kafka.producer.TopicProducer;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.web.builder.util.KeyMgrFactory;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.DependsOn;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Optional;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * BaseSendMessage
 *
 * @param <T>   参数
 * @param <OUT> 参数
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/1/19 12:12
 * @since 1.0
 */
@Slf4j
public abstract class BaseSendMessage<T, OUT> implements ISendMessage<T> {

    private Optional<TopicProducer<String, String>> producer;

    /**
     * findByKey<BR>
     *
     * @param key 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/11/28 14:16
     */
    public static Optional<BaseSendMessage> findByKey(String key) {
        return Optional.ofNullable(key)
                .filter(StringUtils::isNotEmpty)
                .map(it -> Try.of(() -> KeyMgrFactory.findMgrByKey(BaseSendMessage.class, it)).getOrNull());
    }

    /**
     * init<BR>
     *
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/19 17:26
     */
    @PostConstruct
    @DependsOn("beanFactoryHolder")
    public void init() {
        String topic = makeTopic();
        String bootServer = makeBootServer();
        if (StringUtils.isNotEmpty(bootServer)) {
            log.info("开始初始化kafka，bootServer=[{}],topic=[{}]", bootServer, topic);
            KafkaAbstractConfiguration pConfig = KafkaConfigurations.getInstance()
                    .getConfigurationSimply(
                            bootServer,
                            HARDWARE_LEVEL.PC_LEVEL,
                            CONFIG_TYPE.PRODUCER
                    );
            producer = Optional.ofNullable(KafkaProducers.getFactory()
                    .getProduer(pConfig, topic, String.class, String.class));
        } else {
            producer = Optional.empty();
        }
    }

    /**
     * makeTopic<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/31 19:12
     */
    public abstract String makeTopic();

    /**
     * makeBootServer<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/31 19:12
     */
    public abstract String makeBootServer();

    /**
     * sendData<BR>
     *
     * @param data 参数
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/19 16:45
     */
    public final void sendData(List<T> data) {
        for (T t : data) {
            Try.run(() -> sendData(t)).onFailure(e -> log.warn("数据[{}]发送异常", t, e));
        }
    }

    /**
     * sendData<BR>
     *
     * @param data 参数
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/19 12:10
     */
    @Override
    public final void sendData(T data) throws ServiceException {
        OUT out = convertDataToOut(data);
        if (out != null) {
            log.info("数据[{}]转换为[{}]，开始发送", data, out);
            doSend(data, out);
            doSomeThingAfterSend(data, out);
            log.info("数据[{}]，完成发送", data);
        } else {
            log.warn("数据[{}]转换为空，所以跳过发送", data);
        }
    }

    /**
     * convertDataToOut<BR>
     *
     * @param data 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/19 12:14
     */
    public abstract OUT convertDataToOut(T data) throws ServiceException;

    /**
     * doSend<BR>
     *
     * @param data 参数
     * @param out  参数
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/19 12:14
     */
    public void doSend(T data, OUT out) throws ServiceException {
        if (producer.isEmpty()) {
            log.warn("没能生成对应的发送器");
            return;
        }
        producer.ifPresent(it -> {
            String json = convertOutToSendInfo(out);
            String key = makeMessageKey(data, out);
            it.send(key, json);
            if (!it.isDone()) {
                it.flush();
            }
        });
    }

    /**
     * convertOutToSendInfo<BR>
     *
     * @param out 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/31 20:01
     */
    public String convertOutToSendInfo(OUT out) {
        return JsonUtil.toJsonString(out);
    }

    @Override
    public String desc() {
        return key();
    }

    /**
     * makeKey<BR>
     *
     * @param data 参数
     * @param out  参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/31 19:09
     */
    public abstract String makeMessageKey(T data, OUT out);

    /**
     * doSomeThingAfterSend<BR>
     *
     * @param data 参数
     * @param out  参数
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/19 12:14
     */
    public abstract void doSomeThingAfterSend(T data, OUT out) throws ServiceException;
}
