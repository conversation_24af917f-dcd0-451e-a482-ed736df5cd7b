package com.trs.police.search.traffic.police.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 预导出-统计结果保存
 *
 * <AUTHOR>
 * @version v0
 * @since 1.0.0
 * @since 2024-08-28 11:25:01
 */
@Data
@TableName("tb_traffic_pre_statistics")
@Accessors(chain = true)
public class PreStatisticsEntity {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    private Date crTime;
    private Integer year;
    private Integer month;
    private String timeType;
    private String statisticalUnitCode;
    private Integer level;
    private String type;
    private String preStatisticsName;
    private String preStatisticsValue;

}
