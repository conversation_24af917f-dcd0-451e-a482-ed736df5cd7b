package com.trs.police.search.panorama.dto;

import com.trs.common.base.PreConditionCheck;
import com.trs.common.exception.ServiceException;
import com.trs.common.pojo.BaseDTO;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * @ClassName TableImportDTO
 * @Description 检索表导入dto
 * <AUTHOR>
 * @Date 2024/1/9 11:04
 **/
@Data
public class SearchTableImportDTO extends BaseDTO {

    /**
     * 解析起始行数
     */
    private Integer startRow;

    /**
     * 表英文名称所在列，excel字母表示
     */
    private String enNameCol;

    /**
     * 上传的文件
     */
    private MultipartFile file;

    private String type;

    private String subType;

    private String enName;

    private String zhName;

    private Integer accessedDataNum;

    /**
     * 表中字段是否有备注
     * 有
     * 无
     */
    private String fieldHasRemark;

    @Override
    protected boolean checkParams() throws ServiceException {
        PreConditionCheck.checkNotEmpty(this.getEnName(), "表英文名称不能为空！");
        PreConditionCheck.checkNotEmpty(this.getZhName(), "表中文名称不能为空！");
        PreConditionCheck.checkNotEmpty(this.getType(), "所属一级分类不能为空！");
        PreConditionCheck.checkNotEmpty(this.getSubType(), "表所属二级分类不能为空！");
        return false;
    }
}
