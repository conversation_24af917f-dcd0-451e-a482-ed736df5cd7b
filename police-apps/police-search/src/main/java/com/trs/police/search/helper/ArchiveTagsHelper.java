package com.trs.police.search.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.trs.common.exception.ServiceException;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.vo.search.ArchivesVO;
import com.trs.police.common.core.vo.search.KeyValueTypeVoForSearch;
import com.trs.police.common.openfeign.starter.DTO.BaseArchivesSearchDTO;
import com.trs.police.search.panorama.constant.enums.ArchivesEnum;
import com.trs.police.search.panorama.dto.ModifyTagsDTO;
import com.trs.police.search.panorama.handler.BaseSendMessage;
import com.trs.police.search.panorama.manager.impl.ArchivesDetailSceneManager;
import lombok.RequiredArgsConstructor;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.trs.police.search.constant.RedListConstant.JSON_KEY_SOURCE_FROM;
import static com.trs.police.search.constant.RedListConstant.JSON_KEY_TAG_NAME;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * <p>
 * 档案标签操作类
 *
 * <AUTHOR> zhang.yang  E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/9/24 10:42
 * @since 1.0
 */
@Component
@RequiredArgsConstructor
public class ArchiveTagsHelper {

    private final ArchivesDetailSceneManager detailSceneManager;

    /**
     * 构建标签并更新标签信息
     *
     * @param vo         参数
     * @param insertTags 新增标签
     * @param removeTags 移除标签
     * @param tagFrom    标签来源
     * @throws ServiceException 异常
     */
    public void buildTagAndSendArchivesMessage(ArchivesVO vo, String insertTags, String removeTags, String tagFrom)
            throws ServiceException {
        boolean needSend = false;
        List<KeyValueTypeVoForSearch> updateFieldList = new ArrayList<>(1);
        for (KeyValueTypeVoForSearch field : vo.getFields()) {
            if ("标签信息".equals(field.getKey())) {
                final Object value = field.getValue();
                JSONArray array = null;
                if (Objects.nonNull(value)) {
                    array = JSON.parseArray(String.valueOf(value));
                }
                if (CollectionUtils.isEmpty(array)) {
                    array = new JSONArray();
                }
                String tags = "";
                tags = removeTags(array, removeTags, tagFrom);
                tags = addTags(tags, insertTags, tagFrom);
                if (!isEqualObj(array, JSON.parseArray(tags))) {
                    needSend = true;
                }
                field.setValue(tags);
                updateFieldList.add(field);
                break;
            }
        }
        if (CollectionUtils.isNotEmpty(updateFieldList)) {
            vo.setFields(updateFieldList);
        }
        if (needSend) {
            sendMessage(vo);
        }
    }

    private boolean isEqualObj(JSONArray array, JSONArray parseArray) {
        if (array.size() != parseArray.size()) {
            return false;
        }
        if (array.isEmpty()) {
            //都为空时 不必更新
            return true;
        }
        //相同标签数量  生成MD5比较
        List<String> md5ListA = new ArrayList<>(0);
        for (Object o : array) {
            JSONObject json = (JSONObject) o;
            md5ListA.add(DigestUtils.md5Hex(
                    json.getString(JSON_KEY_SOURCE_FROM) + json.getString(JSON_KEY_TAG_NAME)
            ));
        }
        List<String> md5ListB = new ArrayList<>(0);
        for (Object o : parseArray) {
            JSONObject json = (JSONObject) o;
            md5ListB.add(DigestUtils.md5Hex(
                    json.getString(JSON_KEY_SOURCE_FROM) + json.getString(JSON_KEY_TAG_NAME)
            ));
        }
        for (String md5 : md5ListA) {
            if (!md5ListB.contains(md5)) {
                return false;
            }
        }
        return true;
    }

    private String addTags(String tags, String insertTags, String tagFrom) {
        JSONArray array = JSON.parseArray(tags);
        JSONArray temp = array;
        if (StringUtils.isNotEmpty(insertTags)) {
            String[] split = insertTags.split(",");
            if (array.size() == 0) {
                for (String tag : split) {
                    JSONObject object = new JSONObject();
                    object.put(JSON_KEY_TAG_NAME, tag);
                    object.put(JSON_KEY_SOURCE_FROM, tagFrom);
                    temp.add(object);
                }
            } else {
                for (String tag : split) {
                    boolean exist = false;
                    for (Object o : array) {
                        JSONObject json = (JSONObject) o;
                        if (tag.equals(json.getString(JSON_KEY_TAG_NAME))) {
                            exist = true;
                            break;
                        }
                    }
                    if (!exist) {
                        JSONObject object = new JSONObject();
                        object.put(JSON_KEY_TAG_NAME, tag);
                        object.put(JSON_KEY_SOURCE_FROM, tagFrom);
                        temp.add(object);
                    }
                }
            }
        }
        return temp.toJSONString();
    }


    private String removeTags(JSONArray array, String removeTags, String tagFrom) {
        if (StringUtils.isEmpty(removeTags)) {
            return array.toJSONString();
        }
        JSONArray tags = new JSONArray(0);
        List<String> list = Arrays.asList(removeTags.split(","));
        for (Object o : array) {
            JSONObject json = (JSONObject) o;
            if (list.contains(json.getString(JSON_KEY_TAG_NAME))
                    && tagFrom.equals(json.getString(JSON_KEY_SOURCE_FROM))) {
                continue;
            }
            tags.add(json);
        }
        return tags.toJSONString();
    }

    /**
     * 编辑档案标签
     *
     * @param dto 参数
     * @throws ServiceException 异常
     */
    public void modifyTags(ModifyTagsDTO dto) throws ServiceException {
        final ArchivesEnum instance = ArchivesEnum.getInstance(dto.getArchivesType());
        if (Objects.isNull(instance)) {
            throw new ServiceException("不支持的档案类型");
        }
        BaseArchivesSearchDTO searchDTO = new BaseArchivesSearchDTO();
        searchDTO.setNeedLog(Boolean.FALSE);
        searchDTO.setArchivesType(dto.getArchivesType());
        searchDTO.setRecordId(dto.getRecordId());
        searchDTO.setNeedAuth(Boolean.FALSE);
        searchDTO.setFieldName(instance.getDetailField());
        Optional<ArchivesVO> optional = detailSceneManager.findByDTO(searchDTO);
        if (optional.isPresent()) {
            ArchivesVO vo = optional.get();
            buildTagAndSendArchivesMessage(
                    vo,
                    Optional.ofNullable(dto.getInsertTags()).orElse(null),
                    Optional.ofNullable(dto.getRemoveTags()).orElse(null),
                    dto.getTagFrom()
            );
        } else {
            throw new ServiceException("未找到对应的档案数据！");
        }
    }

    private void sendMessage(ArchivesVO data) throws ServiceException {
        if (Objects.isNull(data)) {
            return;
        }
        // 发送对应mq消息，进行标签的设置
        var mgr = BaseSendMessage.findByKey(ArchivesVO.class.getName()).orElse(null);
        if (mgr != null) {
            mgr.sendData(data);
        }
    }
}
