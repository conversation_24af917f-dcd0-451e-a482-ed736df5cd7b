package com.trs.police.search.panorama.dto;

import com.trs.common.base.PreConditionCheck;
import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.common.pojo.BaseDTO;
import lombok.Data;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * FaceRecognitionDTO
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/1/9 19:17
 * @since 1.0
 */
@Data
public class FaceRecognitionDTO extends BaseDTO {

    private String fileUrl;

    private Double similaritydegree;

    private String name;

    private String idNumber;

    @Override
    protected boolean checkParams() throws ServiceException {
        PreConditionCheck.checkNotEmpty(getFileUrl(), new ParamInvalidException("文件链接不能为空"));
        return true;
    }
}
