package com.trs.police.search.domain.entity.archive;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.search.panorama.vo.MultiTrackAnalysisPeronVO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName MultiTrackAnalysisPersonEntity
 * @Description 多轨分析关联人员
 * <AUTHOR>
 * @Date 2024/8/20 14:47
 **/
@Data
@TableName("tb_search_multi_track_analysis_person")
public class MultiTrackAnalysisPersonEntity implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField
    private Long analysisId;

    @TableField
    private String name;

    @TableField
    private String idCard;

    @TableField
    private Integer isDel;

    @TableField
    private LocalDateTime crTime;

    @TableField
    private Long crUserId;

    @TableField
    private String crUser;

    @TableField
    private String crUserTrueName;

    @TableField
    private Long crUserDeptId;

    @TableField
    private String crUserDeptName;

    @TableField
    private String crUserDeptCode;

    /**
     * 转换为VO
     *
     * @return 结果
     */
    public MultiTrackAnalysisPeronVO toVO() {
        MultiTrackAnalysisPeronVO vo = new MultiTrackAnalysisPeronVO();
        vo.setId(this.getId());
        vo.setName(this.getName());
        vo.setIdCard(this.getIdCard());
        vo.setAnalysisId(this.getAnalysisId());
        vo.setCrUserId(this.getCrUserId());
        vo.setCrUser(this.getCrUser());
        vo.setCrUserTrueName(this.getCrUserTrueName());
        vo.setCrUserDeptId(this.getCrUserDeptId());
        vo.setCrUserDeptName(this.getCrUserDeptName());
        vo.setCrUserDeptCode(this.getCrUserDeptCode());
        vo.setIsDel(this.getIsDel());
        vo.setCrTime(this.getCrTime());
        return vo;
    }

}
