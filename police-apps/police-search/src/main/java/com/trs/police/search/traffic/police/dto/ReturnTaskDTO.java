package com.trs.police.search.traffic.police.dto;

import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.common.pojo.BaseDTO;
import lombok.Data;

import static com.trs.common.base.PreConditionCheck.checkNotEmpty;
import static com.trs.common.base.PreConditionCheck.checkNotNull;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> zhang.yang  E-mail: <EMAIL>
 * @date 创建时间：2023/12/20 15:33
 * @version 1.0
 * @since 1.0
 */
@Data
public class ReturnTaskDTO extends BaseDTO {

    /**
     * 数据ID
     */
    private Long dataId;

    /**
     * 退回原因
     */
    private String content;

    /**
     * 等级：2-区县  3-派出所
     */
    private Integer level;

    /**
     * 回退类型描述
     */
    private String type;

    @Override
    protected boolean checkParams() throws ServiceException {
        checkNotNull(getDataId(), new ParamInvalidException("数据ID不能为空"));
        checkNotEmpty(getContent(), new ParamInvalidException("退回原因不能为空"));
        checkNotNull(getLevel(), new ParamInvalidException("level不能为空"));
        return true;
    }
}
