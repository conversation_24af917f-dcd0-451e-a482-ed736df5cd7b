package com.trs.police.search.panorama.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.trs.common.exception.ServiceException;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.openfeign.starter.DTO.SearchLogDTO;
import com.trs.police.search.panorama.vo.SearchLogVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 新版检索日志业务操作接口层
 */
public interface ISearchLogNewService {

    /**
     * 日志记录
     *
     * @param dto 请求参数
     * @throws ServiceException 相关异常
     */
    void doLog(SearchLogDTO dto) throws ServiceException;

    /**
     * 日志记录
     *
     * @param dto  请求参数
     * @param user 用户信息
     * @throws ServiceException 相关异常
     */
    void doLog(SearchLogDTO dto, CurrentUser user) throws ServiceException;

    /**
     * 日志记录
     *
     * @param dto  请求参数
     * @param user 用户信息
     * @param ip   ip地址
     * @throws ServiceException 相关异常
     */
    void doLog(SearchLogDTO dto, CurrentUser user, String ip) throws ServiceException;

    /**
     * 日志检索
     *
     * @param dto 请求参数
     * @return 分页内容
     * @throws ServiceException 相关异常
     */
    IPage<SearchLogVO> pageList(SearchLogDTO dto) throws ServiceException;

    /**
     * 获取操作行为类型
     *
     * @return 行为类型对象列表
     */
    List<KeyValueTypeVO> operateTypes();

    /**
     * 导出日志记录行为
     *
     * @param response 响应流
     * @param dto      请求参数
     */
    void export(HttpServletResponse response, SearchLogDTO dto) throws ServiceException;

    /**
     * 功能模块列表
     *
     * @return 结果
     */
    List<KeyValueTypeVO> moduleNames();

}
