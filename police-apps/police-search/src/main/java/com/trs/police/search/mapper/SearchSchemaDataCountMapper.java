package com.trs.police.search.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.search.domain.entity.SearchSchemaDataCountEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * SearchSchemaDataCountMapper
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/3/26 10:32
 * @since 1.0
 */
@Mapper
public interface SearchSchemaDataCountMapper extends BaseMapper<SearchSchemaDataCountEntity> {

    /**
     * findByEnNameAndDataTime<BR>
     *
     * @param enName   参数
     * @param dataTime 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/3/26 10:33
     */
    @Select("select * from tb_search_schema_data_count where en_name=#{enName} AND data_time=#{dataTime}")
    SearchSchemaDataCountEntity findByEnNameAndDataTime(
        @Param("enName") String enName,
        @Param("dataTime") String dataTime
    );

    /**
     * findByEnNameAndDataTime<BR>
     *
     * @param enName   参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/3/26 10:33
     */
    @Select("select * from tb_search_schema_data_count where en_name=#{enName}")
    SearchSchemaDataCountEntity findByEnName(@Param("enName") String enName);
}
