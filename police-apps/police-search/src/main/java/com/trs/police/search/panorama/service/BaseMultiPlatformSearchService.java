package com.trs.police.search.panorama.service;

import com.alibaba.fastjson.JSON;
import com.trs.common.concurrent.FutureCallback;
import com.trs.common.concurrent.ListenableExecutorService;
import com.trs.common.concurrent.ListenableFuture;
import com.trs.common.concurrent.ListeningCallbacks;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.ThreadUtil;
import com.trs.police.common.core.constant.search.ArchivesConstants;
import com.trs.police.common.core.vo.search.ArchivesVO;
import com.trs.police.common.openfeign.starter.DTO.BaseArchivesSearchDTO;
import com.trs.police.common.redis.starter.service.RedisService;
import com.trs.police.search.domain.entity.SearchConfigEntity;
import com.trs.police.search.domain.entity.archive.SearchArchivesOperateEntity;
import com.trs.police.search.mapper.SearchConfigMapper;
import com.trs.police.search.panorama.constant.enums.ArchivesEnum;
import com.trs.police.search.panorama.handler.BaseSendMessage;
import com.trs.police.search.panorama.vo.ExtPageList;
import com.trs.police.search.service.IRedListService;
import com.trs.web.builder.util.BeanFactoryHolder;
import io.vavr.Tuple2;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * BaseMultiPlatformSearchService
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/1/31 10:51
 * @since 1.0
 */
@Slf4j
public abstract class BaseMultiPlatformSearchService
        implements IMultiPlatformSearchService<BaseMultiPlatformSearchService> {

    @Resource
    private IRedListService iRedListService;

    @Resource
    protected RedisService redisService;

    @Resource
    private ISearchArchivesOperateService archivesOperateService;

    @Resource
    private SearchConfigMapper searchConfigMapper;

    private List<SearchConfigEntity> enZhMap = new ArrayList<>(0);

    /**
     * 后续的处理器
     */
    private BaseMultiPlatformSearchService next = null;

    /**
     * 线程池
     */
    public static final ListenableExecutorService EXECUTOR_SERVICE = ThreadUtil.makeService(
            "BaseMultiPlatformSearchService-Search-"
    );

    /**
     * 发送消息的redis key
     */
    private static final String REDIS_KEY_SEND_MESSAGE_PREFIX = "ARCHIVES:SYNC:MESSAGE";

    /**
     * makeEnZhMap<BR>
     *
     * @param fieldValueMap 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/4/29 19:16
     */
    public List<SearchConfigEntity> makeEnZhMap(Map<String, Object> fieldValueMap) {
        if (enZhMap.isEmpty()) {
            synchronized (REDIS_KEY_SEND_MESSAGE_PREFIX) {
                if (enZhMap.isEmpty()) {
                    List<SearchConfigEntity> result = new ArrayList<>(fieldValueMap.size());
                    // 初始化字段列表
                    searchConfigMapper.getByTableEnName(
                                    Objects.requireNonNull(ArchivesEnum.getInstance(archiveType())).getTableName()
                            ).stream()
                            .filter(it -> fieldValueMap.containsKey(it.getEnName()))
                            .forEach(result::add);
                    enZhMap = result;
                }
            }
        }
        return enZhMap;
    }

    /**
     * supportFlushOnDetail<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/8/8 12:01
     */
    public Boolean supportFlushOnDetail() {
        return false;
    }

    /**
     * flushDataOnDetail<BR>
     *
     * @param recordId 参数
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/8/8 11:54
     */
    @Override
    public void flushDataOnDetail(String recordId) {
        if (supportFlushOnDetail()) {
            throw new RuntimeException("未实现刷新数据行为");
        }
    }

    /**
     * 检索数据<BR>
     *
     * @param totalOffset 总偏移量
     * @param resultSize  当前结果数
     * @param dto         检索参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/31 10:49
     */
    @Override
    public ExtPageList<ArchivesVO> searchData(Long totalOffset, Integer resultSize, BaseArchivesSearchDTO dto) {
        ExtPageList<ArchivesVO> pageList;
        try {
            Tuple2<Integer, Integer> tuple2 = countPage(totalOffset, resultSize, dto);
            log.info("[{}]场景计算非分页为[{},{}]", desc(), tuple2._1, tuple2._2);
            Integer pageNum;
            Integer pageSize;
            if (tuple2._2 == 0) {
                pageNum = 1;
                pageSize = 1;
            } else {
                pageNum = tuple2._1;
                pageSize = tuple2._2;
            }
            // 数量为0时，只需要返回对应的总量跟耗时即可，用于后续统计
            if (checkNeedSearch(pageNum, pageSize, dto)) {
                if (tuple2._2 == 0) {
                    pageList = doSearchData(pageNum, pageSize, dto);
                    pageList.setContents(Collections.emptyList());
                } else {
                    pageList = doSearchData(pageNum, pageSize, dto);
                }
            } else {
                log.info("检索器[{}]不支持搜索", desc());
                pageList = getNoDataPageList(pageNum, pageSize, null, 0L);
            }
            if (resultNeedSendMessage() && !pageList.isEmpty()) {
                resultSendMessage(pageList.getContents());
            }
            if (!pageList.isEmpty()) {
                // 补充order
                pageList.getContents().forEach(it -> it.setOrder(order()));
            }
        } catch (Exception e) {
            log.error("检索器[{}]检索异常", desc(), e);
            pageList = getNoDataPageList(
                    dto.getPageNum(),
                    dto.getPageSize(),
                    String.format("检索器[%s]检索异常，ERR=[%s]", desc(), e.getMessage()),
                    0L
            );
        }
        return pageList;
    }

    protected ExtPageList<ArchivesVO> getNoDataPageList(Integer pageNum, Integer pageSize, String condition, Long took) {
        ExtPageList<ArchivesVO> resultPageList = new ExtPageList<>();
        resultPageList.setPageNum(pageNum);
        resultPageList.setPageSize(pageSize);
        resultPageList.setTotal(0L);
        resultPageList.setContents(Collections.emptyList());
        resultPageList.setCondition(StringUtils.showEmpty(condition));
        resultPageList.setTook(Optional.ofNullable(took).orElse(0L));
        return resultPageList;
    }

    /**
     * 进行检索<BR>
     *
     * @param pageNum  页码（从1开始）
     * @param pageSize 分页大小
     * @param dto      检索参数（忽略其中分页的传参）
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/31 17:32
     */
    public abstract ExtPageList<ArchivesVO> doSearchData(Integer pageNum, Integer pageSize, BaseArchivesSearchDTO dto);

    /**
     * 根据目前检索数据的情况计算相关分页<BR>
     *
     * @param totalOffset 前面所有处理器能查询到的数据总量
     * @param resultSize  目前已查询到的数据量
     * @param dto         参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/31 11:17
     */
    public Tuple2<Integer, Integer> countPage(Long totalOffset, Integer resultSize, BaseArchivesSearchDTO dto) {
        var pageSize = Math.max(0, dto.getPageSize() - resultSize);
        var pageNum = (dto.getPageNum() * dto.getPageSize() - totalOffset.intValue()) / dto.getPageSize();
        return new Tuple2<>(Math.max(1, pageNum), pageSize);
    }

    /**
     * 在命中数据之后是否跳过后续处理器（不论是否满足分页需求）<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/31 11:20
     */
    @Override
    public Boolean skipNextOnHit() {
        return false;
    }

    /**
     * 检索结果是否需要发送消息<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/31 10:44
     */
    @Override
    public Boolean resultNeedSendMessage() {
        return true;
    }

    /**
     * filterDataOnSend<BR>
     *
     * @param result 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/12/17 13:55
     */
    private List<ArchivesVO> filterDataOnSend(List<ArchivesVO> result) {
        final List<SearchArchivesOperateEntity> list = archivesOperateService.listByArchivesList(result);
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        final Set<String> operatedUniqueIdList = list.stream()
                .map(SearchArchivesOperateEntity::getUniqueId)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toSet());
        return result.stream().filter(a -> {
            final String uniqueId = a.makeUniqueId();
            return !operatedUniqueIdList.contains(uniqueId);
        }).collect(Collectors.toList());
    }

    /**
     * 发送结果<BR>
     *
     * @param result 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/31 10:45
     */
    @Override
    public Boolean resultSendMessage(List<ArchivesVO> result) {
        if (!checkNeedSendMessage(result)) {
            log.info("检测器[{}]跳过消息发送", desc());
            return true;
        }
        ListenableFuture<Try<Void>> task = EXECUTOR_SERVICE.submit(() -> Try.run(() -> {
            log.info("[{}]开始发送消息", desc());
            BaseSendMessage.findByKey(ArchivesVO.class.getName())
                    .ifPresent(mgr -> mgr.sendData(filterDataOnSend(result)));
            log.info("[{}]完成发送消息", desc());
        }));
        ListeningCallbacks.addCallback(task, new FutureCallback<Try<Void>>() {
            @Override
            public void onSuccess(Try<Void> result) {
                if (result.isFailure()) {
                    onFail(result.getCause());
                } else {
                    log.info("[{}]成功发送消息", desc());
                }
            }

            @Override
            public void onFail(Throwable error) {
                log.error("[{}]消息发送出错了", desc(), error);
            }
        });
        return true;
    }

    @Override
    public boolean checkNeedSendMessage(List<ArchivesVO> result) {
        if (CollectionUtils.isEmpty(result)) {
            return false;
        }
        final Long expireTime = BeanFactoryHolder.getEnv()
                .getProperty("search.sendMessage.redis.expireTime", Long.class, 0L);
        // 为0时就默认一直需要刷新
        if (0L == expireTime) {
            return true;
        }
        boolean res = false;
        for (ArchivesVO archivesVO : result) {
            final String redisKey = buildRedisKey(archivesVO);
            final Boolean hasKey = redisService.hasKey(redisKey);
            if (!hasKey) {
                redisService.set(redisKey, JSON.toJSONString(archivesVO), expireTime);
                res = true;
                break;
            }
        }
        return res;
    }

    private String buildRedisKey(ArchivesVO archivesVO) {
        return String.format("%s:%s:%s", REDIS_KEY_SEND_MESSAGE_PREFIX, archivesVO.getType(), archivesVO.makeRealRecordId());
    }

    /**
     * 是否支持对应平台<BR>
     *
     * @param archiveType 检索类型
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/31 10:11
     */
    @Override
    public Boolean supportArchiveType(String archiveType) {
        if (StringUtils.isEmpty(archiveType)) {
            return false;
        }
        return ArchivesConstants.ARCHIVES_TYPE_ALL.equalsIgnoreCase(archiveType)
                || archiveType.equalsIgnoreCase(archiveType());
    }

    @Override
    public String key() {
        return getClass().getName();
    }

    /**
     * 是否还有后续处理器<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/31 10:41
     */
    @Override
    public Boolean haveNext() {
        return next != null;
    }

    /**
     * 是否还有后续处理器<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/31 10:42
     */
    @Override
    public BaseMultiPlatformSearchService next() {
        return next;
    }

    /**
     * 后续处理器<BR>
     *
     * @param next 下一个结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/31 10:45
     */
    @Override
    public void setNext(BaseMultiPlatformSearchService next) {
        this.next = next;
    }

    /**
     * checkNeedSearch<BR>
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param dto      参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/2/1 17:40
     */
    public Boolean checkNeedSearch(Integer pageNum, Integer pageSize, BaseArchivesSearchDTO dto) {
        final String queryText = StringUtils.showEmpty(dto.getKeyword(), dto.getWholeContent());
        return Optional.ofNullable(ArchivesEnum.getInstance(dto.getArchivesType()))
                .map(it -> {
                    var flag = iRedListService.isNotInRedList(it, queryText);
                    if (!flag) {
                        log.info("关键词[{}]命中了红名单", queryText);
                    }
                    return flag;
                }).orElse(true);
    }
}
