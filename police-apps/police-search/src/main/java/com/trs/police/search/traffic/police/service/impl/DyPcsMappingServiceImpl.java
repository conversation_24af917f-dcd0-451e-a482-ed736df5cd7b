package com.trs.police.search.traffic.police.service.impl;

import com.trs.common.base.PreConditionCheck;
import com.trs.common.exception.ServiceException;
import com.trs.common.utils.AreaCodeUtil;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.dto.UnitTreeDTO;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.entity.DyPcsMappingEntity;
import com.trs.police.common.core.mapper.DyPcsMappingMapper;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.vo.permission.DeptVO;
import com.trs.police.common.core.vo.permission.UnitTreeVO;
import com.trs.police.search.traffic.police.constant.TrafficPoliceConstants;
import com.trs.police.search.traffic.police.entity.XiangZhenJieDaoDmMcMappingEntity;
import com.trs.police.search.traffic.police.mapper.XiangZhenJieDaoDmMcMappingMapper;
import com.trs.police.search.traffic.police.service.IDyPcsMappingService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * DyPcsMappingServiceImpl
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/12/20 17:49
 * @since 1.0
 */
@Slf4j
@Service
@AllArgsConstructor
public class DyPcsMappingServiceImpl implements IDyPcsMappingService {

    private final DyPcsMappingMapper mapper;

    private final XiangZhenJieDaoDmMcMappingMapper xzMappingMapper;

    /**
     * synchronizedMapping<BR>
     *
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/12/20 17:50
     */
    @Override
    public void synchronizedMapping() {
        var map = mapper.findAllDept()
                .stream()
                .collect(Collectors.toMap(
                        DeptVO::getDeptCode,
                        it -> it,
                        (a, b) -> a
                ));
        map.forEach((key, dept) -> {
            String value = dept.getShortName();
            if (value.endsWith(TrafficPoliceConstants.PCS_ZH)) {
                try {
                    var entity = Optional.ofNullable(mapper.findByPcsbm(key))
                            .orElse(new DyPcsMappingEntity());
                    var jd = key;
                    var qx = AreaCodeUtil.getParentAreaCodeByAreaCode(jd);
                    var sj = AreaCodeUtil.getParentAreaCodeByAreaCode(qx);
                    entity.setPcsmc(value);
                    entity.setPcsbm(key);
                    entity.setJdbm(jd);
                    entity.setJdmc(value);
                    entity.setQxbm(qx);
                    final DeptVO qxdw = map.get(qx);
                    final DeptVO sjdw = map.get(sj);
                    String qxmc = StringUtils.showEmpty(qxdw.getPath(), entity.getQxmc());
                    String sjmc = StringUtils.showEmpty(sjdw.getPath(), entity.getSjmc());
                    if (qxmc.startsWith(sjmc)) {
                        qxmc = qxmc.substring(sjmc.length());
                    }
                    entity.setQxmc(qxmc);
                    entity.setSjbm(sj);
                    entity.setSjmc(sjmc);
                    entity.setPcsqc(dept.getDeptName());
                    entity.setSjdwmc(sjdw.getShortName());
                    entity.setSjdwqc(sjdw.getDeptName());
                    entity.setQxdwmc(qxdw.getShortName());
                    entity.setQxdwqc(qxdw.getDeptName());
                    if (Optional.ofNullable(entity.getId()).filter(r -> r > 0L).isPresent()) {
                        mapper.updateById(entity);
                    } else {
                        mapper.insert(entity);
                    }
                } catch (Exception e) {
                    log.warn("[{}:{}]处理异常", key, dept.getDeptCode(), e);
                }
            }
        });
    }

    /**
     * dealUnitList<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/12/20 18:31
     */
    @Override
    public List<UnitTreeVO> dealUnitList(UnitTreeDTO dto) throws ServiceException {
        final CurrentUser user = AuthHelper.getCurrentUser();
        Set<String> out = new HashSet<>(1);
        String dyLongCode = dto.makeDyCode();
        String dy = null;
        if (StringUtils.isNotEmpty(dyLongCode)) {
            out.add(dyLongCode);
            dy = AreaCodeUtil.getAreaShortCode(dyLongCode);
        }
        List<DyPcsMappingEntity> list = mapper.findByQxDyShortCode(dy);
        if (TrafficPoliceConstants.PCS.equals(StringUtils.showEmpty(dto.getLevel())) && Objects.nonNull(user)) {
            list = list.stream().filter(a -> a.getPcsbm().equals(user.getDept().getCode())).collect(Collectors.toList());
        }
        final List<XiangZhenJieDaoDmMcMappingEntity> xzjdMapping = xzMappingMapper.selectList(null);
        final Map<String, List<XiangZhenJieDaoDmMcMappingEntity>> pcmbmXzMap = xzjdMapping.stream()
                .filter(a -> StringUtils.isNotEmpty(a.getPcsbm()))
                .collect(Collectors.groupingBy(XiangZhenJieDaoDmMcMappingEntity::getPcsbm));
        Map<String, UnitTreeVO> map = new HashMap<>(list.size());
        for (DyPcsMappingEntity entity : list) {
            if (StringUtils.isEmpty(dyLongCode)) {
                out.add(entity.getSjbm());
            }
            // 保存市级
            UnitTreeVO sj = map.getOrDefault(entity.getSjbm(), new UnitTreeVO());
            sj.setValue(entity.getSjbm());
            sj.setName(entity.getSjmc());
            sj.setType(TrafficPoliceConstants.DY);
            // 保存区县
            UnitTreeVO qx = map.getOrDefault(entity.getQxbm(), new UnitTreeVO());
            qx.setValue(entity.getQxbm());
            qx.setName(entity.getQxmc());
            qx.setType(TrafficPoliceConstants.DY);
            // 派出所
            UnitTreeVO pcs = getPcsUnitTree(pcmbmXzMap, entity);
            qx.addChildren(pcs);
            if (TrafficPoliceConstants.PCS.equals(StringUtils.showEmpty(dto.getLevel()))) {
                map.put(entity.getPcsbm(), pcs);
                out.add(entity.getPcsbm());
                continue;
            }
            if (!map.containsKey(entity.getQxbm())) {
                sj.addChildren(qx);
            }
            map.put(entity.getSjbm(), sj);
            map.put(entity.getQxbm(), qx);
        }
        return map.values()
                .stream()
                .filter(r -> out.contains(r.getValue()))
                .sorted(Comparator.comparing(UnitTreeVO::getValue))
                .collect(Collectors.toList());
    }

    private UnitTreeVO getPcsUnitTree(Map<String, List<XiangZhenJieDaoDmMcMappingEntity>> pcmbmXzMap, DyPcsMappingEntity entity) {
        UnitTreeVO pcs = new UnitTreeVO();
        pcs.setValue(entity.getPcsbm());
        pcs.setName(entity.getPcsmc());
        pcs.setType(TrafficPoliceConstants.PCS);
        // 乡镇
        final List<XiangZhenJieDaoDmMcMappingEntity> xzjddmEntityList = pcmbmXzMap.get(entity.getPcsbm());
        if (CollectionUtils.isNotEmpty(xzjddmEntityList)) {
            for (XiangZhenJieDaoDmMcMappingEntity xzjdEntity : xzjddmEntityList) {
                pcs.addChildren(UnitTreeVO.of(TrafficPoliceConstants.XZ, xzjdEntity.getCode(), xzjdEntity.getMc()));
            }
        }
        return pcs;
    }

    /**
     * xiaQuList<BR>
     *
     * @param level 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/12 19:22
     */
    @Override
    public List<UnitTreeVO> xiaQuList(Integer level) throws ServiceException {
        PreConditionCheck.checkNotNull(level, new ServiceException("层级不能为空"));
        final List<DyPcsMappingEntity> list = mapper.findByQxDyShortCode(null);
        final List<String> out = new ArrayList<>(list.size());
        final Map<String, UnitTreeVO> map = new HashMap<>(list.size());
        for (DyPcsMappingEntity entity : list) {
            String put = level == 1 ? entity.getSjbm() : (level == 2 ? entity.getQxbm() : entity.getPcsbm());
            if (!out.contains(put)) {
                out.add(put);
            }
            // 保存市级
            UnitTreeVO sj = map.getOrDefault(entity.getSjbm(), new UnitTreeVO());
            sj.setValue(entity.getSjbm());
            sj.setName(entity.getSjdwmc());
            sj.setType(TrafficPoliceConstants.UNIT);
            // 保存区县
            UnitTreeVO qx = map.getOrDefault(entity.getQxbm(), new UnitTreeVO());
            qx.setValue(entity.getQxbm());
            qx.setName(entity.getQxdwmc());
            qx.setType(TrafficPoliceConstants.UNIT);
            // 派出所
            UnitTreeVO pcs = new UnitTreeVO();
            pcs.setValue(entity.getPcsbm());
            pcs.setName(entity.getPcsmc());
            pcs.setType(TrafficPoliceConstants.UNIT);

            qx.addChildren(pcs);
            if (!map.containsKey(entity.getQxbm())) {
                sj.addChildren(qx);
            }
            map.put(entity.getSjbm(), sj);
            map.put(entity.getQxbm(), qx);
            map.put(entity.getPcsbm(), pcs);
        }
        return out.stream().map(map::get).collect(Collectors.toList());
    }
}
