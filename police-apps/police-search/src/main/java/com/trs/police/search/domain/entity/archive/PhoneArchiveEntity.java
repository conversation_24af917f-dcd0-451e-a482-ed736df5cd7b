package com.trs.police.search.domain.entity.archive;

import com.trs.db.sdk.annotations.TableField;
import lombok.Data;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/1/30 16:08
 * @since 1.0
 */
@Data
public class PhoneArchiveEntity extends BaseArchiveEntity {

    @TableField
    private String sjhm;

    @TableField
    private String yys;

    @TableField
    private String gsd;

    @TableField
    private String rwsj;

    @TableField
    private String zt;

    @TableField
    private String tjsj;

    @TableField
    private String jzzjhm;

    @TableField
    private String jzxm;

    @TableField
    private String djdz;

    @TableField(multivalue = true, objectValue = true)
    private String glr;

    @TableField(multivalue = true, objectValue = true)
    private String misis;

    @TableField(multivalue = true, objectValue = true)
    private String imsis;

    @TableField
    private String imsi;

    @TableField(multivalue = true, objectValue = true)
    private String imeis;

    @TableField
    private String imei;

    @TableField(multivalue = true, objectValue = true)
    private String macs;

    @TableField
    private String mac;

    @TableField(multivalue = true, objectValue = true)
    private String tags;
}
