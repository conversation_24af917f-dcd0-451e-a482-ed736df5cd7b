package com.trs.police.search.panorama.handler;

import com.trs.common.exception.ServiceException;
import com.trs.web.builder.base.IKey;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * ISendMessage
 *
 * @param <T> 参数
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/1/19 12:08
 * @since 1.0
 */
public interface ISendMessage<T> extends IKey {

    /**
     * sendData<BR>
     *
     * @param data 参数
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/19 12:10
     */
    void sendData(T data) throws ServiceException;
}
