package com.trs.police.search.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.search.constant.enums.SearchOperationEnum;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 查询日志entity
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "t_search_log", autoResultMap = true)
public class SearchLogEntity {

    /**
     * 数据主键（Mysql 推荐使用连续自增的整数）
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT, value = "create_time")
    private LocalDateTime createTime;

    /**
     * 创建用户主键
     */
    @TableField(fill = FieldFill.INSERT, value = "create_user_id")
    private Long createUserId;

    /**
     * 创建单位主键
     */
    @TableField(fill = FieldFill.INSERT, value = "create_dept_id")
    private Long createDeptId;

    /**
     * 单次操作uuid
     */
    private String uuid;
    /**
     * 查询表名
     */
    private String tableEnName;
    /**
     * 操作
     */
    private SearchOperationEnum operation;
    /**
     * ip地址
     */
    private String ipAddress;
    /**
     * 查询关键词
     */
    private String keyword;
    /**
     * 查询总数
     */
    private Long resultCount;
    /**
     * 相关字段
     */
    private String fields;

}
