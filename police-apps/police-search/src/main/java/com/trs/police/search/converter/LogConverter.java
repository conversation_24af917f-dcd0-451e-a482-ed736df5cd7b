package com.trs.police.search.converter;

import com.trs.police.common.core.dto.log.LogContext;
import com.trs.police.search.domain.entity.SearchLogEntityNew;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @date
 * <AUTHOR>
 */
@Mapper
public interface LogConverter {

    LogConverter LOG_CONVERTER = Mappers.getMapper(LogConverter.class);

    /**
     * 实体转context
     *
     * @param entity 实体
     * @return context
     */
    LogContext logDo2Context(SearchLogEntityNew entity);

}
