package com.trs.police.search.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.trs.common.pojo.BaseVO;
import lombok.Data;

import java.util.Date;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * DataSearchAuthorizationInfoVO
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/10/26 13:14
 * @since 1.0
 */
@Data
public class DataSearchAuthorizationInfoVO extends BaseVO {

    private Long dataId;

    private String crUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date crTime;

    private String updateUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    private String authObjType;

    private String authKey;

    private String objType;

    private String objId;

    private Integer status;
}
