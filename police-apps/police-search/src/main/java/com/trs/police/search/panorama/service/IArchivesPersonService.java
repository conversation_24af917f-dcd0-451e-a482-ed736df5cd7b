package com.trs.police.search.panorama.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.trs.common.exception.ServiceException;
import com.trs.police.search.domain.request.BasicSourceDTO;
import com.trs.police.search.domain.request.GroupInfoDTO;
import com.trs.police.search.domain.vo.BasicSourceVO;
import com.trs.police.search.domain.vo.GroupInfoVO;
import com.trs.police.search.panorama.dto.PersonFootholdDTO;
import com.trs.police.search.panorama.vo.PersonFootholdCountVO;
import com.trs.police.search.panorama.vo.PersonFootholdDateCountVO;

/**
 * 人员档案相关业务类接口
 */
public interface IArchivesPersonService {

    /**
     * 地点数量统计
     *
     * @param dto 请求参数
     * @return 结果
     * @throws ServiceException 相关异常
     */
    IPage<PersonFootholdCountVO> countList(PersonFootholdDTO dto) throws ServiceException;

    /**
     * 地点次数详细列表
     *
     * @param dto 请求参数
     * @return 结果
     * @throws ServiceException 相关异常
     */
    IPage<PersonFootholdDateCountVO> dateCountList(PersonFootholdDTO dto) throws ServiceException;

    /**
     * 根据身份证号获取所属群体信息
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 相关异常
     */
    IPage<GroupInfoVO> groupInfoBySfzh(GroupInfoDTO dto) throws ServiceException;

    /**
     * 获得感知源信息
     *
     * @param dto 请求参数
     * @return 结果
     * @throws ServiceException 相关异常
     */
    IPage<BasicSourceVO> basicSourceInfo(BasicSourceDTO dto) throws ServiceException;
}
