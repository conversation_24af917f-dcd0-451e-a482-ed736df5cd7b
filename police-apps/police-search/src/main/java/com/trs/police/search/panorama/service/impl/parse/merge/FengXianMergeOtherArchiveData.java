package com.trs.police.search.panorama.service.impl.parse.merge;

import com.trs.common.exception.ServiceException;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.vo.search.ArchivesVO;
import com.trs.police.common.openfeign.starter.DTO.BaseArchivesSearchDTO;
import com.trs.police.search.panorama.constant.enums.ArchivesEnum;
import com.trs.police.search.panorama.dto.PanoramaSearchDTO;
import com.trs.police.search.panorama.manager.impl.ArchivesDetailSceneManager;
import com.trs.police.search.panorama.service.BaseMergeOtherArchiveData;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2025</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：风险档案需要合并警情编号用于反查
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2025/1/2 17:58
 * @since 1.0
 */
@Service
@AllArgsConstructor
public class FengXianMergeOtherArchiveData extends BaseMergeOtherArchiveData {

    private final ArchivesDetailSceneManager detailSceneManager;

    /**
     * archives<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/1/2 17:48
     */
    @Override
    public ArchivesEnum archives() {
        return ArchivesEnum.BUSINESS_FOR_FENG_XIAN;
    }

    /**
     * mergeOtherArchiveData<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/1/2 17:45
     */
    @Override
    public PanoramaSearchDTO mergeOtherArchiveData(PanoramaSearchDTO dto) throws ServiceException {
        if (Objects.nonNull(dto) && StringUtils.isNotEmpty(dto.getKeyword())) {
            BaseArchivesSearchDTO detail = new BaseArchivesSearchDTO();
            detail.setRecordId(dto.getKeyword());
            detail.setArchivesType(dto.getArchivesType());
            Optional<ArchivesVO> vo = detailSceneManager.findByDTO(detail);
            vo.ifPresent(it -> dto.setKeyword(
                    Stream.of(it.getRecordId(), it.getSecondRecordId())
                            .filter(StringUtils::isNotEmpty)
                            .collect(Collectors.joining(" OR "))));
        }
        return dto;
    }
}
