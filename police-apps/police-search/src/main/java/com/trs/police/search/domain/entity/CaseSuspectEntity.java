package com.trs.police.search.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * 案件嫌疑人信息表
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/12/4 11:00
 * @since 1.0
 */
@Data
@TableName("tb_case_suspect")
public class CaseSuspectEntity implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField
    private String recordId;

    @TableField
    private String sfzh;

    @TableField
    private String xm;

    @TableField
    private String xb;

    @TableField
    private Long gjchcs;

}
