package com.trs.police.search.panorama.service;

import com.trs.police.search.panorama.constant.enums.ArchivesEnum;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * BaseCarAiLabelParseService
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/3/20 19:13
 * @since 1.0
 */
public abstract class BasePhoneAiLabelParseService extends BaseAiLabelParseService {

    /**
     * archivesEnum<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/30 14:41
     */
    @Override
    public ArchivesEnum archivesEnum() {
        return ArchivesEnum.PHONE;
    }

}
