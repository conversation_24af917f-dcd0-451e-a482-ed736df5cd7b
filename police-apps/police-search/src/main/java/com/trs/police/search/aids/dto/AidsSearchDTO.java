package com.trs.police.search.aids.dto;

import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.common.pojo.BaseDTO;
import lombok.Data;

import static com.trs.common.base.PreConditionCheck.checkNotEmpty;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/6/17 16:37
 * @since 1.0
 */
@Data
public class AidsSearchDTO extends BaseDTO {

    private String recordId;

    @Override
    protected boolean checkParams() throws ServiceException {
        checkNotEmpty(getRecordId(), new ParamInvalidException("证件号码不能为空"));
        return true;
    }
}
