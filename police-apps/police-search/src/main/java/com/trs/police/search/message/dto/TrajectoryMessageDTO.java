package com.trs.police.search.message.dto;


import com.trs.common.exception.ServiceException;
import com.trs.common.pojo.BaseDTO;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.entity.ThemeGjxxbEntity;
import com.trs.police.common.core.utils.GeoHashUtils;
import com.trs.police.search.message.utils.SensingTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

/**
 * 德阳 - 轨迹数据 - DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-11-06 10:30:31
 */
@Data
public class TrajectoryMessageDTO extends BaseDTO {

    private String enName;
    private String name;
    private String identifierType;
    private String identifier;
    private String sensingType;
    private String sensingId;
    private String longitude;
    private String latitude;
    private String address;
    private String eventTime;
    private String district;
    private String dataClassification;
//    private String trackDetail;

    /**
     * 将 {@link TrajectoryMessageDTO } 转换为 {@link ThemeGjxxbEntity }
     *
     * @return {@link ThemeGjxxbEntity }
     * <AUTHOR>
     * @since 1.0.0
     * @since 2024-11-08 15:50:02
     */
    public ThemeGjxxbEntity toThemeGjxxbEntity() {
        ThemeGjxxbEntity result = new ThemeGjxxbEntity();
        result.setSjlyxtfldm(getEnName());
        result.setXxrksj(TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD_HHMMSS));
        result.setGjlx("7");
        result.setTzlx("车牌");
        result.setTzzhm(getIdentifier());
        if (StringUtils.isNotEmpty(getLatitude()) && StringUtils.isNotEmpty(getLongitude())) {
            result.setJdwgs84(getLongitude());
            result.setWdwgs84(getLatitude());
            result.setGeohash(GeoHashUtils.encode(Double.parseDouble(getLatitude()), Double.parseDouble(getLongitude())));
        }
        LocalDateTime localDataTime = LocalDateTime.parse(getEventTime(), DateTimeFormatter.ofPattern(TimeUtils.YYYYMMDD_HHMMSS2));
        result.setHdsj(localDataTime.format(DateTimeFormatter.ofPattern(TimeUtils.YYYYMMDD_HHMMSS)));
        result.setYear(String.valueOf(localDataTime.getYear()));
        result.setMonth(String.valueOf(localDataTime.getMonthValue()));
        result.setDay(String.valueOf(localDataTime.getDayOfMonth()));
        result.setHour(String.valueOf(localDataTime.getHour()));
        result.setMinute(String.valueOf(localDataTime.getMinute()));
        SensingTypeEnum type = SensingTypeEnum.getByType(getSensingType());
        if(Objects.nonNull(type)){
            result.setGzylx(type.getLabel());
        }
        result.setGzybh(getSensingId());
        return result;
    }

    @Override
    protected boolean checkParams() throws ServiceException {
        return true;
    }
}
