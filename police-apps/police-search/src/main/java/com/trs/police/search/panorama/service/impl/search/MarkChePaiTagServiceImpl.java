package com.trs.police.search.panorama.service.impl.search;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.trs.common.exception.ServiceException;
import com.trs.common.utils.JsonUtils;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.constant.search.ArchivesConstants;
import com.trs.police.common.core.vo.search.ArchivesVO;
import com.trs.police.common.openfeign.starter.DTO.BaseArchivesSearchDTO;
import com.trs.police.search.panorama.constant.enums.ArchivesEnum;
import com.trs.police.search.panorama.context.SceneContext;
import com.trs.police.search.panorama.manager.impl.ArchivesDetailSceneManager;
import com.trs.police.search.panorama.manager.impl.ArchivesListSceneManager;
import com.trs.police.search.panorama.service.BaseMultiPlatformSearchService;
import com.trs.police.search.panorama.util.ArchivesUtils;
import com.trs.police.search.panorama.util.LabelUtils;
import com.trs.police.search.panorama.vo.ExtPageList;
import com.trs.web.entity.PageList;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：标记车牌为车辆过户情况的行为
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/9/1 10:36
 * @since 1.0
 */
@Service
@Slf4j
public class MarkChePaiTagServiceImpl extends BaseMultiPlatformSearchService {

    private static final String TAG_NAME = "车辆信息已注销";
    private final ChengDuChePaiApiMultiPlatformSearchServiceImpl chengDu;
    private final SiChuanChePaiApiMultiPlatformSearchServiceImpl siChuan;
    private final ArchivesDetailSceneManager detailSceneManager;
    private final ArchivesListSceneManager listSceneManager;

    public MarkChePaiTagServiceImpl(
            ArchivesListSceneManager listSceneManager,
            ArchivesDetailSceneManager detailSceneManager,
            @Nullable ChengDuChePaiApiMultiPlatformSearchServiceImpl chengDu,
            @Nullable SiChuanChePaiApiMultiPlatformSearchServiceImpl siChuan
    ) {
        this.listSceneManager = listSceneManager;
        this.detailSceneManager = detailSceneManager;
        this.chengDu = chengDu;
        this.siChuan = siChuan;
        log.info("初始化[{}],Flag=[{}]", desc(), supportArchiveType(ArchivesConstants.ARCHIVES_TYPE_ALL));
    }

    @Override
    public void flushDataOnDetail(String recordId) {
        log.info("开始刷新[{}]", recordId);
        List<String> hmAndLx = StringUtils.getList(recordId, StringUtils.SEPARATOR_SEMICOLON);
        if (hmAndLx.isEmpty()) {
            return;
        }

        if (!ArchivesUtils.findCheckAndParse(ArchivesConstants.ARCHIVES_TYPE_CAR)
                .map(it -> it.check(hmAndLx.get(0))).orElse(false)) {
            return;
        }
        if (!hmAndLx.get(0).startsWith("川")) {
            log.info("[{}]非川牌，不处理", recordId);
            return;
        }
        var t = Try.of(() -> {
            BaseArchivesSearchDTO dto = new BaseArchivesSearchDTO();
            dto.setArchivesType(ArchivesEnum.CAR.getType());
            dto.setRecordId(recordId);
            dto.setNeedLog(false);
            return detailSceneManager.findByDTO(dto)
                    .orElseThrow(() -> new ServiceException("数据[" + recordId + "]未入库ES"));
        }).onFailure(e -> log.warn("数据[{}]查询异常", recordId, e));
        if (t.isSuccess()) {
            BaseArchivesSearchDTO dto = new BaseArchivesSearchDTO();
            dto.setKeyword(hmAndLx.get(0));
            dto.setNeedLog(false);
            dto.setArchivesType(ArchivesEnum.CAR.getType());
            final ExtPageList<ArchivesVO> extPageList;
            if (siChuan != null) {
                extPageList = siChuan.doSearchData(1, 1, dto);
            } else if (chengDu != null) {
                extPageList = chengDu.doSearchData(1, 1, dto);
            } else {
                return;
            }
            var vo = t.get();
            final var tagNames = LabelUtils.getLabelFromFields(vo.getFields());
            final ArchivesVO target = new ArchivesVO();
            target.setType(vo.getType());
            target.setRecordId(vo.getRecordId());
            target.setOrder(vo.getOrder());
            target.setSecondRecordId(vo.getSecondRecordId());
            // 接口没有查询到数据
            if (extPageList.isEmpty()) {
                log.info("[{}]准备更新标签,原有Tag为[{}]", recordId, tagNames);
                // 不包含对应标签需要追加
                if (!tagNames.contains(TAG_NAME)) {
                    log.info("[{}]追加标签", recordId);
                    vo.getFields().forEach(it -> {
                        if (Objects.equals("tags", it.getFieldName())) {
                            JSONArray tags = new JSONArray();
                            if (JsonUtils.isValidArray(String.valueOf(it.getValue()))) {
                                tags.addAll(JSONArray.parseArray(String.valueOf(it.getValue())));
                            }
                            JSONObject guohu = new JSONObject();
                            guohu.put("tag_name", TAG_NAME);
                            guohu.put("source_from", TAG_NAME);
                            tags.add(guohu);
                            it.setValue(tags);
                            target.setFields(List.of(it));
                        }
                    });
                    log.info("追加：待更新的数据为[{}]", target);
                    resultSendMessage(List.of(target));
                }
            } else if (tagNames.contains(TAG_NAME)) {
                log.info("[{}]移除标签", recordId);
                // 不为空且包含了对应标签，需要移除
                vo.getFields().forEach(it -> {
                    if (Objects.equals("tags", it.getFieldName())) {
                        JSONArray tags = JSONArray.parseArray(String.valueOf(it.getValue()));
                        for (int i = tags.size() - 1; i >= 0; i--) {
                            JSONObject tag = tags.getJSONObject(i);
                            if (tag != null && tag.getString("tag_name").equals(TAG_NAME)) {
                                tags.remove(i);
                            }
                        }
                        it.setValue(tags);
                        target.setFields(List.of(it));
                    }
                });
                log.info("移除：待更新的数据为[{}]", target);
                resultSendMessage(List.of(target));
            }
        }
        log.info("结束刷新[{}]", recordId);
    }

    @Override
    public Boolean supportFlushOnDetail() {
        return true;
    }

    @Override
    public Boolean resultNeedSendMessage() {
        return true;
    }

    @Override
    public boolean checkNeedSendMessage(List<ArchivesVO> result) {
        // 标记的行为不进行判断
        return CollectionUtils.isNotEmpty(result);
    }

    @Override
    public ExtPageList<ArchivesVO> doSearchData(Integer pageNum, Integer pageSize, BaseArchivesSearchDTO dto) {
        CompletableFuture.runAsync(() -> {
            try {
                log.info("检索器[{}]开始搜索[{}]", desc(), dto.getKeyword());
                BaseArchivesSearchDTO searchDTO = new BaseArchivesSearchDTO();
                searchDTO.setNeedLog(false);
                searchDTO.setNeedAuth(false);
                searchDTO.setArchivesType(ArchivesEnum.CAR.getType());
                searchDTO.setRecordId(dto.getKeyword());
                // 这个数据不需要搜索，只需要刷新数据即可
                if (ArchivesUtils.findCheckAndParse(ArchivesConstants.ARCHIVES_TYPE_PERSON)
                        .map(it -> it.check(dto.getKeyword())).orElse(false)) {
                    searchDTO.setFieldName(ArchivesEnum.CAR.getPersonFieldName());
                } else if (ArchivesUtils.findCheckAndParse(ArchivesConstants.ARCHIVES_TYPE_CAR)
                        .map(it -> it.check(dto.getKeyword())).orElse(false)) {
                    searchDTO.setFieldName(ArchivesEnum.CAR.getDetailField());
                }
                Optional<ExtPageList<ArchivesVO>> opt = listSceneManager.fetchData(SceneContext.of(searchDTO));
                opt.map(PageList::getContents).filter(CollectionUtils::isNotEmpty)
                        .ifPresent(it -> it.forEach(item -> {
                            String recordId = item.getRecordId() + StringUtils.SEPARATOR_SEMICOLON + item.getSecondRecordId();
                            Try.run(() -> flushDataOnDetail(recordId))
                                    .onFailure(e -> log.error("车辆[{}]刷新错误", recordId, e));
                        }));
            } catch (Exception e) {
                log.error("车档中根据[{}]标记过户情况出现异常", dto.getKeyword(), e);
            }
        }, EXECUTOR_SERVICE);
        return getNoDataPageList(pageNum, pageSize, "", 0L);
    }

    @Override
    public String archiveType() {
        return ArchivesConstants.ARCHIVES_TYPE_ALL;
    }

    /**
     * 是否支持对应平台<BR>
     *
     * @param archiveType 检索类型
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/31 10:11
     */
    @Override
    public Boolean supportArchiveType(String archiveType) {
        if (StringUtils.isEmpty(archiveType)) {
            return false;
        }
        // 第三方接口开启了之后才能调用
        return chengDu != null || siChuan != null;
    }

    @Override
    public Boolean checkNeedSearch(Integer pageNum, Integer pageSize, BaseArchivesSearchDTO dto) {
        // 是否支持检索
        if (!supportArchiveType(dto.getArchivesType())) {
            return false;
        }
        // 检索词是否为空
        if (StringUtils.isEmpty(dto.getKeyword())) {
            return false;
        }
        // 是否满足父类检索要求
        if (!super.checkNeedSearch(pageNum, pageSize, dto)) {
            return false;
        }
        var archivesEnum = ArchivesEnum.getInstance(dto.getArchivesType());
        if (archivesEnum == null) {
            return false;
        }
        switch (archivesEnum) {
            case ALL:
            case CAR:
                // 车主身份证跟车牌两个搜索
                return ArchivesUtils.findCheckAndParse(ArchivesConstants.ARCHIVES_TYPE_PERSON)
                        .map(it -> it.check(dto.getKeyword())).orElse(false)
                        || ArchivesUtils.findCheckAndParse(ArchivesConstants.ARCHIVES_TYPE_CAR)
                        .map(it -> it.check(dto.getKeyword())).orElse(false);
            default:
                return false;
        }
    }

    @Override
    public Integer order() {
        return 30;
    }

    @Override
    public String desc() {
        return "标记车牌为车辆过户情况的行为";
    }
}
