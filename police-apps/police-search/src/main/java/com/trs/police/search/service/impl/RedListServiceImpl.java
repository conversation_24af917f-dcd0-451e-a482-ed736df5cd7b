package com.trs.police.search.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.base.PreConditionCheck;
import com.trs.common.exception.ServiceException;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.constant.search.ArchivesConstants;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.search.constant.RedListConstant;
import com.trs.police.search.domain.entity.RedListEntity;
import com.trs.police.search.domain.request.RedListAddOrEditDTO;
import com.trs.police.search.domain.request.RedListQueryDTO;
import com.trs.police.search.domain.vo.RedListVO;
import com.trs.police.search.helper.RedListHelper;
import com.trs.police.search.mapper.RedListMapper;
import com.trs.police.search.panorama.constant.SearchConstant;
import com.trs.police.search.panorama.constant.enums.ArchivesEnum;
import com.trs.police.search.panorama.service.ISearchDataService;
import com.trs.police.search.panorama.vo.SearchDataObjVO;
import com.trs.police.search.service.IRedListService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-08-21 15:34:21
 */
@Service
@RequiredArgsConstructor
public class RedListServiceImpl implements IRedListService {

    private final RedListMapper redListMapper;

    private final RedListHelper redListHelper;

    private final ISearchDataService searchDataService;

    @Override
    public String addOrEdit(RedListAddOrEditDTO dto) throws ServiceException {
        dto.isValid();
        checkParams(dto);
        CurrentUser user = AuthHelper.getNotNullUser();
        if (Objects.nonNull(dto.getId())) {
            RedListEntity entity = redListMapper.selectById(dto.getId());
            if (Objects.isNull(entity)) {
                throw new ServiceException();
            }
            var diff = redListHelper.getDiff(entity, dto);
            redListHelper.delOldLabel(diff._1);
            redListHelper.addNewLabel(diff._2);
            redListMapper.update(null, new UpdateWrapper<RedListEntity>().lambda()
                    .eq(RedListEntity::getId, dto.getId())
                    .set(RedListEntity::getName, dto.getName())
                    .set(RedListEntity::getPhone, dto.getPhone())
                    .set(RedListEntity::getIdCard, dto.getIdCard())
                    .set(RedListEntity::getPlateNo, dto.getPlateNo())
                    .set(RedListEntity::getRemark, dto.getRemark())
                    .set(RedListEntity::getGender, dto.getGender())
                    .set(RedListEntity::getVirtual, dto.getVirtual())
                    .set(RedListEntity::getStatus, dto.getStatus())
                    .set(RedListEntity::getUpdateTime, new Date())
                    .set(RedListEntity::getUpdateUser, user.getRealName())
                    .set(RedListEntity::getUpdateUserId, user.getId())
                    .set(RedListEntity::getUpdateUserDeptId, user.getDeptId())
                    .set(RedListEntity::getUpdateUserDeptName, user.getDept().getName())
            );
            return "编辑成功";
        } else {
            RedListEntity entity = new RedListEntity();
            setBaseInfo(entity, user);
            entity.setName(dto.getName());
            entity.setRemark(dto.getRemark());
            entity.setIdCard(dto.getIdCard());
            entity.setPhone(dto.getPhone());
            entity.setPlateNo(dto.getPlateNo());
            entity.setGender(dto.getGender());
            entity.setStatus(dto.getStatus());
            entity.setVirtual(dto.getVirtual());
            entity.setUpdateTime(new Date());
            entity.setUpdateUser(user.getRealName());
            entity.setUpdateUserId(user.getId());
            entity.setUpdateUserDeptId(user.getDeptId());
            entity.setUpdateUserDeptName(user.getDept().getName());
            redListMapper.insert(entity);
            redListHelper.addNewLabel(redListHelper.entity2map(entity));
            return "新增成功";
        }
    }

    private void checkParams(RedListAddOrEditDTO dto) throws ServiceException {
        Function<String, Boolean> check = StringUtils::isNotEmpty;
        Map<String, String> baseMap = new HashMap<>();
        if (Objects.nonNull(dto.getId())) {
            baseMap.put("notInId", String.valueOf(dto.getId()));
        }
        if (check.apply(dto.getIdCard())) {
            Map<String, String> map = new HashMap<>(baseMap);
            map.put("idCard", dto.getIdCard());
            if (isIn(map)) {
                throw new ServiceException(dto.getIdCard() + "证件号已存在");
            }
        }
        Function<String, List<String>> str2list = str -> StringUtils.isNotEmpty(str)
                ? Stream.of(str.split(StringUtils.SEPARATOR_COMMA_OR_SEMICOLON))
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList())
                : new ArrayList<>();
        if (check.apply(dto.getPlateNo())) {
            Map<String, String> map = new HashMap<>(baseMap);
            map.put("plateNo", "");
            List<String> in = new ArrayList<>();
            for (String plateNo : str2list.apply(dto.getPlateNo())) {
                map.replace("plateNo", plateNo);
                if (isIn(map)) {
                    in.add(plateNo);
                }
            }
            if (CollectionUtils.isNotEmpty(in)) {
                throw new ServiceException(String.join(",", in) + "车牌号已存在");
            }
        }
        if (check.apply(dto.getPhone())) {
            Map<String, String> map = new HashMap<>(baseMap);
            map.put("phone", "");
            List<String> in = new ArrayList<>();
            for (String phone : str2list.apply(dto.getPhone())) {
                map.replace("phone", phone);
                if (isIn(map)) {
                    in.add(phone);
                }
            }
            if (CollectionUtils.isNotEmpty(in)) {
                throw new ServiceException(String.join(",", in) + "手机号已存在");
            }
        }
    }

    private void setBaseInfo(RedListEntity entity, CurrentUser user) {
        entity.setCrTime(new Date());
        entity.setCrUserId(user.getId());
        entity.setCrUserTrueName(user.getRealName());
        entity.setCrUserDeptId(user.getDeptId());
        entity.setCrUserDeptName(user.getDept().getName());
    }


    @Override
    public String remove(String ids) throws ServiceException {
        if (StringUtils.isEmpty(ids)) {
            throw new ServiceException("ids不能为空");
        }
        List<String> collect = Stream.of(ids.split(StringUtils.SEPARATOR_COMMA_OR_SEMICOLON))
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            throw new ServiceException("ids值无效");
        }
        List<RedListEntity> list = redListMapper.selectList(new QueryWrapper<RedListEntity>()
                .lambda().in(RedListEntity::getId, collect));
        for (RedListEntity entity : list) {
            redListHelper.delOldLabel(redListHelper.entity2map(entity));
        }
        redListMapper.update(null,
                new UpdateWrapper<RedListEntity>().lambda()
                        .in(RedListEntity::getId, collect)
                        .set(RedListEntity::getIsDel, 1)
        );
        return "移除成功";
    }

    @Override
    public IPage<RedListVO> pageList(RedListQueryDTO dto) throws ServiceException {
        IPage<RedListEntity> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        QueryWrapper<RedListEntity> wrapper = new QueryWrapper<RedListEntity>().eq("is_del", 0);
        if (StringUtils.isNotEmpty(dto.getKeyword()) && StringUtils.isNotEmpty(dto.getSearchType())) {
            wrapper.like(getDbSearchField(dto.getSearchType()), dto.getKeyword());
        }
        if (StringUtils.isNotEmpty(dto.getOrderField()) && StringUtils.isNotEmpty(dto.getOrderType())) {
            if ("asc".equalsIgnoreCase(dto.getOrderType())) {
                wrapper.orderByAsc(getDbOrderFiled(dto.getOrderField()));
            } else {
                wrapper.orderByDesc(getDbOrderFiled(dto.getOrderField()));
            }
        } else {
            wrapper.orderByDesc("cr_time");
        }
        final IPage<RedListEntity> pageList = redListMapper.selectPage(page, wrapper);
        return pageList.convert(RedListEntity::toVO);
    }

    /**
     * isInRedList<BR>
     *
     * @param user         参数
     * @param archivesEnum 参数
     * @param recordId     参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/8/29 11:13
     */
    @Override
    public Boolean isInRedList(CurrentUser user, ArchivesEnum archivesEnum, String recordId) {
        if (StringUtils.isEmpty(recordId)) {
            return false;
        }
        PreConditionCheck.checkNotNull(archivesEnum, "档案类型不能为空");
        boolean isAll = Objects.equals(ArchivesEnum.ALL, archivesEnum);
        if (!isAll && !List.of(ArchivesEnum.PERSON, ArchivesEnum.CAR, ArchivesEnum.PHONE).contains(archivesEnum)) {
            return false;
        }
        Map<String, String> map = new HashMap<>(3);
        boolean idCard = isAll || Objects.equals(ArchivesEnum.PERSON, archivesEnum);
        if (idCard) {
            map.put("idCard", recordId);
        }
        boolean plateNo = isAll || Objects.equals(ArchivesEnum.CAR, archivesEnum);
        if (plateNo) {
            map.put("plateNo", recordId);
        }
        boolean phone = isAll || Objects.equals(ArchivesEnum.PHONE, archivesEnum);
        if (phone) {
            map.put("phone", recordId);
        }
        final boolean inRedList = isIn(map);
        final boolean hasRedListTagAuth = hasRedListTagAuth(user, archivesEnum.getType());
        // 在红名单 且 没有权限，不允许检索
        return inRedList && !hasRedListTagAuth;
    }

    private boolean isIn(Map<String, String> map) {
        return redListMapper.isInRedList(map) > 0;
    }

    private boolean isIn(boolean idCard, boolean plateNo, boolean phone, String recordId) {
        return new LambdaQueryChainWrapper<>(redListMapper)
                .eq(RedListEntity::getIsDel, 0)
                .and(it -> it.eq(idCard, RedListEntity::getIdCard, recordId)
                        .or()
                        .like(plateNo, RedListEntity::getPlateNo, recordId)
                        .or()
                        .like(phone, RedListEntity::getPhone, recordId)
                ).select().list().stream().anyMatch(obj -> {
                    boolean equalsIdCard = false;
                    boolean equalsPlateNo = false;
                    boolean equalsPhone = false;
                    if (idCard) {
                        equalsIdCard = Objects.equals(obj.getIdCard(), recordId);
                    }
                    if (plateNo) {
                        equalsPlateNo = Arrays.asList(obj.getPlateNo().split(",")).contains(recordId);
                    }
                    if (phone) {
                        equalsPhone = Arrays.asList(obj.getPhone().split(",")).contains(recordId);
                    }
                    return equalsIdCard || equalsPlateNo || equalsPhone;
                });
    }

    private String getDbSearchField(String searchType) {
        if (StringUtils.isEmpty(searchType)) {
            return "id_card";
        }
        switch (searchType) {
            case "phone":
                return "phone";
            case "idCard":
                return "id_card";
            case "plateNo":
                return "plate_no";
            case "remark":
                return "remark";
            case "addUser":
                return "cr_user_true_name";
            case "name":
            default:
                return "name";
        }
    }

    private String getDbOrderFiled(String orderField) {
        if (Objects.equals("CR_TIME", orderField)) {
            return orderField.toLowerCase();
        }
        return "cr_time";
    }

    @Override
    public boolean hasRedListTagAuth(CurrentUser user, String archivesType) {
        return !getNotHasAuthObjIdSet(user, archivesType).contains(RedListConstant.RED_LIST_ZH_NAME);
    }

    private Set<String> getNotHasAuthObjIdSet(CurrentUser user, String archivesType) {
        try {
            String authorizationKey = SearchConstant.RIGHT_SEARCH;
            final List<SearchDataObjVO> list;
            if (ArchivesConstants.ARCHIVES_TYPE_ALL.equals(archivesType)) {
                list = new ArrayList<>();
                list.addAll(
                        searchDataService.getLabelObjs(user, ArchivesConstants.ARCHIVES_TYPE_PERSON, authorizationKey, true));
                list.addAll(
                        searchDataService.getLabelObjs(user, ArchivesConstants.ARCHIVES_TYPE_CAR, authorizationKey, true));
                list.addAll(
                        searchDataService.getLabelObjs(user, ArchivesConstants.ARCHIVES_TYPE_CASE, authorizationKey, true));
                list.addAll(
                        searchDataService.getLabelObjs(user, ArchivesConstants.ARCHIVES_TYPE_PHONE, authorizationKey, true));
            } else {
                list = searchDataService.getLabelObjs(user, archivesType, authorizationKey, true);
            }
            if (CollectionUtils.isEmpty(list)) {
                return new HashSet<>(0);
            }
            return list.stream().map(SearchDataObjVO::getObjId).collect(Collectors.toSet());
        } catch (ServiceException e) {
            return new HashSet<>(0);
        }
    }

}
