package com.trs.police.search.traffic.police.dto;

import com.trs.common.base.PreConditionCheck;
import com.trs.common.exception.ServiceException;
import com.trs.common.pojo.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> zhang.yang  E-mail: <EMAIL>
 * @date 创建时间：2024/1/31 14:13
 * @version 1.0
 * @since 1.0
 */
@Data
public class StatisticalReportDTO extends BaseDTO {

    @ApiModelProperty(value = "统计单位")
    private String statisticalUnit;

    @ApiModelProperty(value = "统计单位编码")
    private String statisticalUnitCode;

    @ApiModelProperty(value = "开始日期")
    private String startDate;

    @ApiModelProperty(value = "结束日期")
    private String endDate;

    @ApiModelProperty(value = "权限等级")
    private Integer level;

    @ApiModelProperty(value = "编码级别")
    private String type;

    /**
     * 1：待劝导，没有超过阈值 且 没有导出劝导书 且 状态不是无法处理
     * 2：待处罚 超过阈值 且 没有收到处罚反馈编号 且 状态不是不予处罚
     * 3：已处罚 已填写处罚结果编号
     */
    @ApiModelProperty(value = "统计类型")
    private Integer waitedOperate;

    @ApiModelProperty(value = "预导出文件名")
    private String preStatisticsName;

    @Override
    protected boolean checkParams() throws ServiceException {
        PreConditionCheck.checkNotEmpty(getStatisticalUnit(), "统计单位不能为空");
        PreConditionCheck.checkNotEmpty(getStatisticalUnitCode(), "统计单位编码不能为空");
        PreConditionCheck.checkNotEmpty(getStartDate(), "开始日期不能为空");
        PreConditionCheck.checkNotEmpty(getEndDate(), "结束日期不能为空");
        PreConditionCheck.checkNotNull(getLevel(), "权限等级level不能为空");
        PreConditionCheck.checkNotEmpty(getType(), "编码级别不能为空");
        return true;
    }

}
