package com.trs.police.search.businessarchives.service.proxy;

import com.trs.police.common.core.vo.message.BusinessArchivesMessageVO;
import com.trs.web.builder.base.IKey;

import java.util.List;

/**
 * 对Repository做一层代理
 * *@author:wen.wen
 * *@create 2024-05-07 20:25
 **/
public interface IRepositoryProxy extends IKey {

    /**
     * 进行数据持久化
     *
     * @param messageVOList 需要进行持久化的结构体
     * @param tableName tableName
     * @throws Throwable Throwable
     */
    void insert(String tableName, List<BusinessArchivesMessageVO> messageVOList) throws Throwable;

    /**
     * 插入/更新操作
     *
     * @param messageVOList 需要进行持久化的结构体
     * @param tableName tableName
     * @throws Throwable Throwable
     */
    void saveOrUpdate(String tableName, List<BusinessArchivesMessageVO> messageVOList) throws Throwable;


    /**
     * 更新操作
     *
     * @param messageVOList 需要进行持久化的结构体
     * @param tableName tableName
     * @throws Throwable Throwable
     */
    void delete(String tableName, List<BusinessArchivesMessageVO> messageVOList) throws Throwable;


    /**
     * 数据库类型
     *
     * @return {@link String}
     */
    String dbType();

    /**
     * key
     *
     * @return {@link String}
     */
    default String key() {
        return dbType();
    }
}
