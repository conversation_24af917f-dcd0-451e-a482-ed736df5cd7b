package com.trs.police.search.properties;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * ShenFenZhengApiProperties
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/2/1 16:05
 * @since 1.0
 */
@Component
@ConfigurationProperties(prefix = "search.api.shenfenzheng")
@ConditionalOnProperty(prefix = "search.api.shenfenzheng", name = "enable", havingValue = "true")
public class ShenFenZhengApiProperties extends BaseApiProperties {

}
