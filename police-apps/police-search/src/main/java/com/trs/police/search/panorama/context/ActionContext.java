package com.trs.police.search.panorama.context;

import com.trs.common.pojo.BaseDTO;
import lombok.Data;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * ActionContext
 *
 * @param <T> 泛型
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/9/27 18:20
 * @since 1.0
 */
@Data
public class ActionContext<T extends BaseDTO> {

    /**
     * 数据id串
     */
    private String recordIds;

    /**
     * 只记录日志
     */
    private Boolean onlyLog;

    /**
     * data
     */
    T data;

    /**
     * of<BR>
     *
     * @param <T>       泛型
     * @param recordIds 参数
     * @param data      参数
     * @return com.trs.police.search.panorama.context.ActionContext
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/9/27 18:23
     */
    public static <T extends BaseDTO> ActionContext of(String recordIds, T data) {
        return of(recordIds, data, false);
    }

    /**
     * of<BR>
     *
     * @param <T>       泛型
     * @param recordIds 参数
     * @param data      参数
     * @param onlyLog   参数
     * @return com.trs.police.search.panorama.context.ActionContext
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/9/27 18:23
     */
    public static <T extends BaseDTO> ActionContext of(String recordIds, T data, Boolean onlyLog) {
        ActionContext<T> context = new ActionContext<>();
        context.setData(data);
        context.setRecordIds(recordIds);
        context.setOnlyLog(onlyLog);
        return context;
    }
}
