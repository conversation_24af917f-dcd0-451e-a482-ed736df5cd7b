package com.trs.police.search.panorama.util.builder;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;
import static com.trs.police.search.panorama.util.ExpressionHelper.buildOrConditionValue;
import static com.trs.police.search.panorama.util.ExpressionHelper.makeQueryString;

import com.trs.common.utils.StringUtils;
import com.trs.common.utils.expression.Expression;
import com.trs.police.common.openfeign.starter.DTO.BaseArchivesSearchDTO;
import java.util.List;

/**
 * @ClassName PhoneArchivesExpressionBuilder
 * @Description 电话档案条件拼接类
 * <AUTHOR>
 * @Date 2023/10/24 9:20
 **/
public class PhoneArchivesExpressionBuilder extends BaseArchivesExpressionBuilder<BaseArchivesSearchDTO> {

    @Override
    public List<Expression> build(BaseArchivesSearchDTO dto) {
        final List<Expression> conditions = super.build(dto);
        if (StringUtils.isNotEmpty(dto.getPhone())) {
            conditions.add(Condition("sjhm", makeQueryString(), buildOrConditionValue(dto.getPhone())));
        }
        if (StringUtils.isNotEmpty(dto.getBelonging())) {
            conditions.add(Condition("gsd", makeQueryString(), buildOrConditionValue(dto.getBelonging())));
        }
        if (StringUtils.isNotEmpty(dto.getOperators())) {
            conditions.add(Condition("yys", makeQueryString(), buildOrConditionValue(dto.getOperators())));
        }
        return conditions;
    }
}
