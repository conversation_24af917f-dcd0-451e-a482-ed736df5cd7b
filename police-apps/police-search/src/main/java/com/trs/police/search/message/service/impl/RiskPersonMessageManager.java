package com.trs.police.search.message.service.impl;

import com.trs.police.common.core.constant.search.ArchivesConstants;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.core.vo.search.ArchivesVO;
import com.trs.police.common.core.vo.search.KeyValueTypeVoForSearch;
import com.trs.police.search.message.config.RiskPersonMessage;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 风险人员消息服务
 *
 * <AUTHOR>
 * @date 2024/9/27
 */
@Component
@EnableBinding(RiskPersonMessage.class)
public class RiskPersonMessageManager {
    @Resource
    private RiskPersonMessage riskPersonMessage;

    /**
     * 发送风险人员消息
     *
     * @param archivesVO archivesVO
     */
    public void sendRiskPersonMessageMessage(ArchivesVO archivesVO){
        if(ArchivesConstants.ARCHIVES_TYPE_PERSON.equals(archivesVO.getType())){
            List<KeyValueTypeVoForSearch> fields = archivesVO.getFields();
            if(!CollectionUtils.isEmpty(fields)){
                Map<String, Object> map = fields.stream()
                        .filter(field-> Objects.nonNull(field.getValue()))
                        .collect(Collectors.toMap(KeyValueTypeVO::getKey, KeyValueTypeVO::getValue));
                riskPersonMessage.riskPersonOut().send(MessageBuilder.withPayload(map).build());
            }
        }
    }
}
