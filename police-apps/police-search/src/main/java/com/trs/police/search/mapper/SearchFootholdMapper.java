package com.trs.police.search.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.search.domain.entity.SearchFootholdEntity;
import com.trs.police.search.panorama.dto.PersonFootholdDTO;
import com.trs.police.search.panorama.vo.PersonFootholdCountVO;
import com.trs.police.search.panorama.vo.PersonFootholdDateCountVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 检索落脚点mapper
 */
@Mapper
public interface SearchFootholdMapper extends BaseMapper<SearchFootholdEntity> {

    /**
     * 地址计数统计
     *
     * @param page 分页参数
     * @param dto  请求参数
     * @return 分页结果
     */
    IPage<PersonFootholdCountVO> countList(@Param("page") Page<SearchFootholdEntity> page, @Param("dto") PersonFootholdDTO dto);

    /**
     * 日期数量统计列表
     *
     * @param page 分页参数
     * @param dto  请求参数
     * @return 结果
     */
    IPage<PersonFootholdDateCountVO> dateCountList(@Param("page") Page<SearchFootholdEntity> page, @Param("dto") PersonFootholdDTO dto);

}
