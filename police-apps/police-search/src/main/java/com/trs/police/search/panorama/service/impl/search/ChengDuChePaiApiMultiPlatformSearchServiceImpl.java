package com.trs.police.search.panorama.service.impl.search;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.trs.common.exception.ServiceException;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.constant.search.ArchivesConstants;
import com.trs.police.common.core.vo.search.ArchivesVO;
import com.trs.police.common.openfeign.starter.DTO.BaseArchivesSearchDTO;
import com.trs.police.search.domain.entity.archive.CarArchiveEntity;
import com.trs.police.search.mapper.SearchConfigMapper;
import com.trs.police.search.panorama.service.BaseApiMultiPlatformSearchService;
import com.trs.police.search.panorama.util.ArchivesUtils;
import com.trs.police.search.panorama.util.PanoramaUtils;
import com.trs.police.search.properties.ChengDuChePaiApiProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * ChengDuChePaiApiMultiPlatformSearchServiceImpl
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/2/1 16:19
 * @since 1.0
 */
@Service
@Slf4j
@ConditionalOnBean(value = ChengDuChePaiApiProperties.class)
public class ChengDuChePaiApiMultiPlatformSearchServiceImpl
        extends BaseApiMultiPlatformSearchService<ChengDuChePaiApiProperties> {

    public ChengDuChePaiApiMultiPlatformSearchServiceImpl(
            SearchConfigMapper searchConfigMapper, ChengDuChePaiApiProperties properties) {
        super(searchConfigMapper, properties);
    }

    @Override
    public Boolean checkNeedSearch(Integer pageNum, Integer pageSize, BaseArchivesSearchDTO dto) {
        if (!supportArchiveType(dto.getArchivesType())) {
            return false;
        }

        return StringUtils.isNotEmpty(dto.getKeyword())
                && ArchivesUtils.findCheckAndParse(ArchivesConstants.ARCHIVES_TYPE_CAR)
                .map(it -> it.check(dto.getKeyword())).orElse(false)
                && super.checkNeedSearch(pageNum, pageSize, dto);
    }

    @Override
    public String makeRequestBody(Integer pageNum, Integer pageSize, BaseArchivesSearchDTO dto) {
        JSONObject json = new JSONObject();
        json.put("plateValue", dto.getKeyword());
        return json.toJSONString();
    }

    @Override
    public List<ArchivesVO> convertToArchiveVo(String resp) throws ServiceException {
        JSONObject json = JSONObject.parseObject(resp);
        List<ArchivesVO> list = new ArrayList<>(0);
        if (json.getInteger("code") == 200) {
            JSONArray result = json.getJSONObject("data").getJSONArray("result");
            for (int i = 0, len = result.size(); i < len; i++) {
                JSONObject car = result.getJSONObject(i);
                if (car != null && !car.isEmpty()) {
                    CarArchiveEntity entity = new CarArchiveEntity();
                    String carType = StringUtils.showEmpty(car.getString("carType"));
                    entity.setHpzl("other".equalsIgnoreCase(carType) ? "其他" : carType);
                    entity.setHphm(car.getString("licenseNo"));
                    if (car.containsKey("extFields")) {
                        JSONObject extFields = car.getJSONObject("extFields");
                        entity.setSyxz(extFields.getString("vehicleUse"));
                        entity.setFzrz(TimeUtils.stringToString(
                                extFields.getString("dealDate"),
                                TimeUtils.YYYYMMDD
                        ));
                        entity.setCcdjrq(TimeUtils.stringToString(
                                extFields.getString("registerTime"),
                                TimeUtils.YYYYMMDD
                        ));
                        entity.setCcrq(TimeUtils.stringToString(
                                extFields.getString("productDate"),
                                TimeUtils.YYYYMMDD
                        ));
                    }
                    entity.setCzxm(car.getString("ownerName"));
                    entity.setCzsfzhm(car.getString("idCardNo"));
                    entity.setLxdh(car.getString("phoneNumber"));
                    entity.setClph(car.getString("carBrand"));
                    if (Objects.equals(ArchivesConstants.CN_WEIZHI, entity.getHpzl())) {
                        entity.setHpzl(null);
                    }
                    if (StringUtils.isEmpty(entity.getHpzl()) && StringUtils.isEmpty(entity.getCzsfzhm())) {
                        continue;
                    }
                    // 转换成map
                    Map<String, Object> fieldValueMap = JSONObject.parseObject(JSONObject.toJSONString(entity))
                            .getInnerMap();
                    if (fieldValueMap.isEmpty()) {
                        continue;
                    }
                    var enZhMap = makeEnZhMap(fieldValueMap);
                    ArchivesVO vo = new ArchivesVO();
                    vo.setType(archiveType());
                    vo.setRecordId(entity.getHphm());
                    vo.setSecondRecordId(StringUtils.showEmpty(entity.getHpzl()));
                    vo.setOrder(order());
                    // XMKFB-5169 注入排重Key
                    vo.setDetectDuplicationKeys(Set.of(
                            String.format("%s-%s-%s", archiveType(), entity.getHphm(), StringUtils.showEmpty(entity.getCzsfzhm()))
                    ));
                    vo.setFields(PanoramaUtils.buildFieldResult(fieldValueMap, enZhMap));
                    list.add(vo);
                }
            }
        } else {
            log.error("接口响应异常，内容为[{}]", resp);
            throw new ServiceException("接口异常！");
        }
        return list;
    }

    /**
     * archiveType<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/31 10:53
     */
    @Override
    public String archiveType() {
        return ArchivesConstants.ARCHIVES_TYPE_CAR;
    }

    /**
     * 顺序，越小越靠前(取值范围0到无穷，不能重复)<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/31 10:11
     */
    @Override
    public Integer order() {
        return 21;
    }

    @Override
    public String desc() {
        return "车牌号查询接口（成都市）";
    }
}
