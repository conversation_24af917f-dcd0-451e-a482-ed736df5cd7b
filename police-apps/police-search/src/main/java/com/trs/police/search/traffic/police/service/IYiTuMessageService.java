package com.trs.police.search.traffic.police.service;

import com.trs.common.exception.ServiceException;
import com.trs.police.search.traffic.police.dto.KafkaYiTuMessageDTO;

/**
 * @ClassName IYiTuMessageService
 * @Description 依图消息消费业务处理类
 * <AUTHOR>
 * @Date 2023/12/20 9:47
 **/
public interface IYiTuMessageService {

    /**
     * 依图mq消息关联行为
     *
     * @param offset 偏移量
     * @param dto    消息对象
     * @throws ServiceException 相关异常
     */
    void makeDataCorrelations(Long offset, KafkaYiTuMessageDTO dto) throws ServiceException;

}
