package com.trs.police.search.panorama.util.builder;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;
import static com.trs.police.search.panorama.util.ExpressionHelper.buildOrConditionValue;
import static com.trs.police.search.panorama.util.ExpressionHelper.makeQueryString;

import com.trs.common.utils.StringUtils;
import com.trs.common.utils.expression.Expression;
import com.trs.police.common.openfeign.starter.DTO.BaseArchivesSearchDTO;
import java.util.List;

/**
 * @ClassName CarArchivesExpressionBuilder
 * @Description 车辆档案条件拼接实现类
 * <AUTHOR>
 * @Date 2023/10/24 9:17
 **/
public class CarArchivesExpressionBuilder extends BaseArchivesExpressionBuilder<BaseArchivesSearchDTO> {

    @Override
    public List<Expression> build(BaseArchivesSearchDTO dto) {
        final List<Expression> conditions = super.build(dto);
        if (StringUtils.isNotEmpty(dto.getNumber())) {
            conditions.add(Condition("hphm", makeQueryString(), buildOrConditionValue(dto.getNumber())));
        }
        if (StringUtils.isNotEmpty(dto.getType())) {
            conditions.add(Condition("hpzl", makeQueryString(), buildOrConditionValue(dto.getType())));
        }
        if (StringUtils.isNotEmpty(dto.getColor())) {
            conditions.add(Condition("csys", makeQueryString(), buildOrConditionValue(dto.getColor())));
        }
        if (StringUtils.isNotEmpty(dto.getBrand())) {
            conditions.add(Condition("clph", makeQueryString(), buildOrConditionValue(dto.getBrand())));
        }
        if (StringUtils.isNotEmpty(dto.getIdNumber())) {
            conditions.add(Condition("clsbdm", makeQueryString(), buildOrConditionValue(dto.getIdNumber())));
        }
        if (StringUtils.isNotEmpty(dto.getManufacturer())) {
            conditions.add(Condition("zzsmc", makeQueryString(), buildOrConditionValue(dto.getManufacturer())));
        }
        if (StringUtils.isNotEmpty(dto.getOwnerName())) {
            conditions.add(Condition("czxm", makeQueryString(), buildOrConditionValue(dto.getOwnerName())));
        }
        if (StringUtils.isNotEmpty(dto.getOwnerIdCard())) {
            conditions.add(Condition("czsfzhm", makeQueryString(), buildOrConditionValue(dto.getOwnerIdCard())));
        }
        if (StringUtils.isNotEmpty(dto.getEnergyTypeCode())) {
            conditions.add(Condition("nyzl", makeQueryString(), buildOrConditionValue(dto.getEnergyTypeCode())));
        }
        if (StringUtils.isNotEmpty(dto.getImportTypeCode())) {

        }
        if (StringUtils.isNotEmpty(dto.getSerialNumber())) {
            conditions.add(Condition("jdccchgzbh", makeQueryString(), buildOrConditionValue(dto.getSerialNumber())));
        }
        if (StringUtils.isNotEmpty(dto.getStatus())) {
            conditions.add(Condition("wfzt", makeQueryString(), buildOrConditionValue(dto.getStatus())));
        }
        if (StringUtils.isNotEmpty(dto.getEngineNumber())) {
            conditions.add(Condition("fdjbh", makeQueryString(), buildOrConditionValue(dto.getEngineNumber())));
        }
        return conditions;
    }
}
