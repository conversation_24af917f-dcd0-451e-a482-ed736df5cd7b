package com.trs.police.search.panorama.service.impl;

import com.trs.common.exception.ServiceException;
import com.trs.police.common.core.constant.search.ArchivesConstants;
import com.trs.police.search.PoliceSearchApplication;
import com.trs.police.search.panorama.dto.AiLabelDTO;
import com.trs.police.test.BaseTestCase;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = PoliceSearchApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class AiLabelServiceImplTest extends BaseTestCase {

    @Autowired
    private AiLabelServiceImpl service;

    @Test
    void getFetchRestfulResults() throws ServiceException {
        AiLabelDTO dto = new AiLabelDTO();
        dto.setArchivesType(ArchivesConstants.ARCHIVES_TYPE_CASE);
        dto.setRecordId("912012771");
        print(service.getFetchRestfulResults(dto));
    }

}