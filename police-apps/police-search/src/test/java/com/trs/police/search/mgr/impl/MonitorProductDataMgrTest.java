package com.trs.police.search.mgr.impl;

import com.trs.common.utils.TimeUtils;
import com.trs.police.search.PoliceSearchApplication;
import com.trs.police.search.mapper.SearchSchemaMapper;
import com.trs.police.test.BaseTestCase;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = PoliceSearchApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class MonitorProductDataMgrTest extends BaseTestCase {

    @Autowired
    private MonitorProductDataMgr mgr;

    @Autowired
    private SearchSchemaMapper searchSchemaMapper;

    @Test
    void checkHit() {
        var entity = searchSchemaMapper.getByEnName("archive_fengxian");
        mgr.checkHit(entity, TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD));
    }
}