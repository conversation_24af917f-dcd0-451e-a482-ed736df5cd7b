package com.trs.police.search.traffic.police.service;

import com.trs.common.exception.ServiceException;
import com.trs.police.search.PoliceSearchApplication;
import com.trs.police.search.traffic.police.vo.DealReasonVo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> zhang.yang  E-mail: <EMAIL>
 * @date 创建时间：2023/12/21 18:18
 * @version 1.0
 * @since 1.0
 */
@SpringBootTest(classes = PoliceSearchApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class ITrafficPoliceTopicServiceTest {

    @Autowired
    private ITrafficPoliceTopicService service;

    @Test
    public void testReasonList() throws ServiceException {
        List<DealReasonVo> dealReasonVos = service.reasonList(7L);
        System.out.println(dealReasonVos.size());
    }
}
