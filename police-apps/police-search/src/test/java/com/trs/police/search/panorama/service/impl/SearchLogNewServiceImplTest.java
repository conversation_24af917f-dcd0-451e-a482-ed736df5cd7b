package com.trs.police.search.panorama.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.trs.common.exception.ServiceException;
import com.trs.police.search.PoliceSearchApplication;
import com.trs.police.common.openfeign.starter.DTO.SearchLogDTO;
import com.trs.police.search.panorama.service.ISearchLogNewService;
import com.trs.police.search.panorama.vo.SearchLogVO;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = PoliceSearchApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
class SearchLogNewServiceImplTest {

    @Autowired
    private ISearchLogNewService service;

    @BeforeEach
    void setUp() {
    }

    @AfterEach
    void tearDown() {
    }

    @Test
    void doLog() {
        String s = BeanFactoryHolder.getEnv().getProperty("warning.subscribe.searchArchivesTabs");
        log.info(s);
    }

    @Test
    void pageList() throws ServiceException {
        SearchLogDTO dto = new SearchLogDTO();
        dto.setPageNum(1);
        dto.setPageSize(10);
        final IPage<SearchLogVO> page = service.pageList(dto);
        System.out.println(page.getRecords());
    }
}