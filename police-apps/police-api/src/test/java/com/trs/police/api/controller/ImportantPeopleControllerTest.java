package com.trs.police.api.controller;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.RequestBuilder;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;

import javax.annotation.Resource;




@SpringBootTest
class ImportantPeopleControllerTest {
    private MockMvc mvc;

    @Autowired
    private WebApplicationContext wac;

    @Test
    void test1() {
        mvc = MockMvcBuilders.webAppContextSetup(wac).build();
        RequestBuilder resquest = get("http://127.0.0.1:8080/api/important/test");
        try{
            String response = mvc.perform(resquest).andReturn().getResponse().getContentAsString();
            System.out.println(response);
        }catch (Exception e){
            e.printStackTrace();
        }

    }
}