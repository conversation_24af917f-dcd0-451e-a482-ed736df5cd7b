package com.trs.police.schedule.service.impl;

import static com.trs.police.common.schedule.starter.constant.ExchangeConstant.DELAY_EXCHANGE;
import static com.trs.police.common.schedule.starter.constant.ExchangeConstant.SCHEDULE_EXCHANGE;
import static com.trs.police.common.schedule.starter.constant.ExchangeConstant.SCHEDULE_RESULT_EXCHANGE;

import com.trs.police.common.core.constant.enums.message.JobTypeEnum;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.core.vo.message.DelayMessage;
import com.trs.police.common.core.vo.message.ScheduleMessageVO;
import com.trs.police.common.core.vo.message.ScheduleResultVO;
import com.trs.police.common.rabbitmq.starter.utils.RabbitmqUtils;
import com.trs.police.schedule.domain.entity.JobConfigEntity;
import com.trs.police.schedule.domain.entity.JobEntity;
import com.trs.police.schedule.domain.entity.JobLogEntity;
import com.trs.police.schedule.mapper.JobConfigMapper;
import com.trs.police.schedule.mapper.JobLogMapper;
import com.trs.police.schedule.mapper.JobMapper;
import com.trs.police.schedule.service.ScheduleService;
import com.trs.police.schedule.utils.QuartzUtil;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Argument;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ScheduleServiceImpl implements ScheduleService {

    @Resource
    private Scheduler scheduler;
    @Resource
    private JobConfigMapper jobConfigMapper;
    @Resource
    private JobMapper jobMapper;
    @Resource
    private JobLogMapper jobLogMapper;
    @Resource
    private RabbitTemplate rabbitTemplate;

    /**
     * 初始化定时任务
     */
    @PostConstruct
    public void initiateScheduleTasks() {
        List<JobConfigEntity> list = jobConfigMapper.selectJobEnable();
        if (list == null || list.isEmpty()) {
            log.info("定时任务配置不存在！");
            return;
        }
        list.forEach(schedule -> {
            String identity = String.valueOf(schedule.getId());
            Map<String, Object> map = new HashMap<>(2);
            map.put("module", schedule.getModule());
            map.put("operation", schedule.getOperation());
            //创建新定时任务
            boolean result = false;
            try {
                result = QuartzUtil.createScheduleJob(scheduler, identity, schedule.getCron(),
                    schedule.getOperation().getJobClassPath(), map);
            } catch (ClassNotFoundException e) {
                log.error("the fully qualified class name error, module={},classPath={}",schedule.getModule().getCnName(),schedule.getOperation().getJobClassPath(), e);
//                throw new TRSException("job类未找到！");
            } catch (SchedulerException e) {
                log.error("create job fail，module={}，operation={}",schedule.getModule().getCnName(),schedule.getOperation().getName(), e);
//                throw new TRSException("定时任务加载出错！");
            }
            log.info("加载定时任务id：{} 结果：{}", schedule.getId(), result);
        });
    }

    @Override
    public void subscribeDelayJob(ScheduleMessageVO message) {
        log.info("接收到逾期任务订阅：{}", JsonUtil.toJsonString(message));
        JobConfigEntity jobConfigEntity =
            jobConfigMapper.selectByModuleAndOperation(message.getModule().getCode(), message.getOperation().getName());
        if (jobConfigEntity == null) {
            throw new TRSException("任务配置不存在！");
        }
        if (!jobConfigEntity.getEnable()) {
            log.info("任务配置未启用！");
            return;
        }
        //存储process
        JobEntity process = JobEntity.builder()
            .configId(jobConfigEntity.getId())
            .module(message.getModule())
            .operation(message.getOperation())
            .relatedId(message.getRelatedId())
            .timeLimit(message.getTimeLimit())
            .build();
        jobMapper.insert(process);
        //需要定时任务的等定时任务自动执行，直接使用延时队列的在此时发送队列消息
        if (JobTypeEnum.DELAY_MESSAGE.equals(jobConfigEntity.getType())) {
            Duration duration = Duration.between(LocalDateTime.now(), message.getTimeLimit());
            DelayMessage delayMessage = new DelayMessage();
            delayMessage.setJobId(process.getId());
            delayMessage.setOperation(message.getOperation());
            delayMessage.setService(message.getModule());
            delayMessage.setRelatedId(message.getRelatedId());
            delayMessage.setDelayLength((int) duration.toMillis());
            RabbitmqUtils.sendDelayMessage(rabbitTemplate, DELAY_EXCHANGE, delayMessage);
        }
    }

    /**
     * 接收延迟消息，并通知给业务系统
     *
     * @param message 消息字符串
     */
    @RabbitHandler
    @RabbitListener(bindings = @QueueBinding(
        value = @Queue(autoDelete = "true"),
        exchange = @Exchange(value = DELAY_EXCHANGE,
            type = "x-delayed-message",
            arguments = @Argument(name = "x-delayed-type", value = "fanout"))))
    public void receiveDelayMessage(String message) {
        log.info("收到delayMessage{}:", message);
        DelayMessage delayMessage = RabbitmqUtils.getMessage(message, DelayMessage.class);
        JobConfigEntity jobConfigEntity =
            jobConfigMapper.selectByModuleAndOperation(delayMessage.getService().getCode(),
                delayMessage.getOperation().getName());
        if (jobConfigEntity == null || !jobConfigEntity.getEnable()) {
            log.info("任务配置不存在或未启用！");
            return;
        }
        ScheduleMessageVO schedule = new ScheduleMessageVO();
        schedule.setModule(delayMessage.getService());
        schedule.setOperation(delayMessage.getOperation());
        schedule.setRelatedId(delayMessage.getRelatedId());
        schedule.setJobId(delayMessage.getJobId());
        RabbitmqUtils.sendMessageToMq(rabbitTemplate, SCHEDULE_EXCHANGE, JsonUtil.toJsonString(schedule));
    }

    /**
     * 接收任务消息，记录日志
     *
     * @param message 消息
     */
    @RabbitHandler
    @RabbitListener(bindings = @QueueBinding(
        value = @Queue(autoDelete = "true"),
        exchange = @Exchange(value = SCHEDULE_RESULT_EXCHANGE, type = ExchangeTypes.FANOUT)))
    @Transactional(rollbackFor = RuntimeException.class)
    public void receiveScheduleMessage(String message) {
        ScheduleResultVO result = RabbitmqUtils.getMessage(message, ScheduleResultVO.class);
        if (result.getJobId() != null && result.getJobId() > 0L) {
            JobEntity jobEntity = jobMapper.selectById(result.getJobId());
            if (jobEntity.getModule().equals(result.getModule())
                && jobEntity.getOperation().equals(result.getOperation())) {
                JobLogEntity entity = jobLogMapper.checkLogExist(result.getJobId());
                if (entity == null) {
                    JobLogEntity log = JobLogEntity.builder()
                        .jobId(result.getJobId())
                        .executeTime(result.getExecuteTime())
                        .success(result.getSuccess())
                        .build();
                    jobLogMapper.insert(log);
                } else {
                    if (Boolean.FALSE.equals(result.getSuccess())) {
                        entity.setSuccess(result.getSuccess());
                        jobLogMapper.updateById(entity);
                    }
                }
            }
        } else {
            log.info("消息[{}]中的jobId为空,不需要更新", message);
        }
    }

}
