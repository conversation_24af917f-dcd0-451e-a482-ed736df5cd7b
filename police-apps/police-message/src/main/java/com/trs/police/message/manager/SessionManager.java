package com.trs.police.message.manager;

import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.entity.NoticeEntity;
import com.trs.police.common.core.entity.message.ChannelSession;
import com.trs.police.common.core.entity.message.UserSession;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.core.vo.intelligence.ObjVo;
import com.trs.police.common.core.vo.message.Channel;
import com.trs.police.common.core.vo.message.NoticeVO;
import com.trs.police.common.core.vo.message.WebsocketMessageVO;
import com.trs.police.common.core.vo.permission.UserDeptVO;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.message.mapper.MessageMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.websocket.Session;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/03/24
 */
@Component
@Slf4j
public class SessionManager extends ConcurrentHashMap<UserDeptVO, UserSession> {

    private static final long serialVersionUID = -613632496135437495L;

    @Resource
    private PermissionService permissionService;

    /**
     * 将当前长连接信息更新到sessionManger中
     *
     * @param user    当前用户
     * @param channel 长连接channel
     * @param session 长连接session
     */
    public static void updateSessionManger(UserDeptVO user, Channel channel, Session session) {
        SessionManager sessionManager = BeanUtil.getBean(SessionManager.class);
        //判断当前sessionManage是否存在当前user
        boolean isExistUser = false;
        for (Map.Entry<UserDeptVO, UserSession> sessionEntry : sessionManager.entrySet()) {
            if (sessionEntry.getKey().equals(user)) {
                //当前通道存在则 add  不存在则新增
                if (sessionEntry.getValue().containsKey(channel)) {
                    sessionEntry.getValue().get(channel).getSessions().add(session);
                } else {
                    sessionEntry.getValue().put(channel, new ChannelSession(
                            LocalDateTime.now(), new ArrayList<>(Collections.singletonList(session))));
                }

                log.info("{}用户增加socket连接，sessionId:{},channel:{}", user, session.getId(), channel);
                isExistUser = true;
                break;
            }
        }
        //如果不存在则新增
        if (!isExistUser) {
            log.info("{}用户创建socket连接，sessionId:{},channel:{}", user, session.getId(), channel);
            UserSession userSession = new UserSession();
            userSession.put(channel,
                    new ChannelSession(LocalDateTime.now(), new ArrayList<>(Collections.singletonList(session))));
            sessionManager.put(user, userSession);
        }

    }

    /**
     * 从内存中删除当前用户session
     *
     * @param channel 当前用户链接分组
     * @param user    用户i
     */
    public void closeCurrentUserSession(UserDeptVO user, Channel channel) {
        UserSession userSession = this.get(user);
        if (Objects.nonNull(userSession)) {
            ChannelSession channelSession = userSession.get(channel);
            if (Objects.nonNull(channelSession)) {
                log.info("{}用户根据channel:{}找到了可以关闭的连接", user.getUserId(), channel);
                channelSession.getSessions().removeIf(next -> Objects.isNull(next) || !next.isOpen());
            } else {
                log.warn("{}用户根据channel:{}没能找到可以关闭的连接", user.getUserId(), channel);
            }
        } else {
            log.warn("{}用户不存在于SessionManager中", user.getUserId());
        }
    }

    /**
     * 获取当前用户在该系统中的session
     *
     * @param user    用户
     * @param channel 频道
     * @return ws 的session
     */
    public List<Session> getUserSession(UserDeptVO user, Channel channel) {
        UserSession userSession = this.get(user);
        return Objects.isNull(userSession) ? new ArrayList<>() : userSession.entrySet().stream()
                .filter(item -> Objects.nonNull(item) && item.getKey().equals(channel))
                .findAny()
                .map(item -> item.getValue().getSessions())
                .orElse(new ArrayList<>());
    }

    /**
     * 向用户发送合成消息
     *
     * @param websocketMessageVO {@link WebsocketMessageVO}
     */
    public void sendFightMessage(WebsocketMessageVO websocketMessageVO) {
        final MessageMapper messageMapper = BeanUtil.getBean(MessageMapper.class);
        // 当前合成参与人id
        List<UserDeptVO> userByCompositeId = messageMapper.findUserByCompositeId(websocketMessageVO.getConversationId());
        // 根据用户id分组
        Map<Long, List<UserDeptVO>> userIdUser = userByCompositeId.stream()
                .collect(Collectors.groupingBy(UserDeptVO::getUserId));
        // 根据用户找到在线的session
        Map<UserDeptVO, List<Session>> userSessionMap = new HashMap<>();
        userIdUser.forEach((key, useurDeptList) -> {
            for (UserDeptVO ud : useurDeptList) {
                List<Session> sessions = this.getUserSession(ud, websocketMessageVO.getSystemInfo())
                        .stream()
                        .filter(Session::isOpen)
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(sessions)) {
                    userSessionMap.put(ud, sessions);
                    break;
                }
            }
        });
        // 向在线用户发送websocket消息
        userSessionMap.forEach((user, sessions) -> sessions.forEach(item -> {
            CompletableFuture.runAsync(
                    () -> item.getAsyncRemote().sendText(JsonUtil.toJsonString(websocketMessageVO), result -> {
                                if (result.isOK()) {
                                    log.info("向用户:{},sessionId:{},发送合成消息：{}", user, item.getId(),
                                            JsonUtil.toJsonString(websocketMessageVO));
                                } else {
                                    log.warn("向用户:{},sessionId:{},发送合成消息失败", user, item.getId(),
                                            result.getException());
                                }
                            }
                    ));
        }));
    }

    /**
     * 向用户发送情指行消息
     *
     * @param websocketMessageVO {@link WebsocketMessageVO}
     */
    public void sendIntelligenceMessage(WebsocketMessageVO websocketMessageVO) {
        final MessageMapper messageMapper = BeanUtil.getBean(MessageMapper.class);
        List<ObjVo> list = messageMapper.findIntelligenceRelationByChatId(
                websocketMessageVO.getConversationId()
        );
        // 会话人创建单位信息注入其中
        Optional.ofNullable(
                messageMapper.findIntelligenceChatCrDeptByChatId(
                        websocketMessageVO.getConversationId()
                )
        ).filter(CollectionUtils::isNotEmpty).ifPresent(list::addAll);
        if (Objects.isNull(websocketMessageVO.getCrDept())) {
            Optional.ofNullable(websocketMessageVO.getSender())
                    .map(it -> permissionService.getDeptById(it.getDeptId()).toSimpleVO())
                    .ifPresent(websocketMessageVO::setCrDept);
        }
        Map<String, UserDeptVO> users = new HashMap<>(list.size());
        CurrentUser sender = websocketMessageVO.getSender();
        // 把自己加入其中
        users.put(sender.getDeptId() + ":" + sender.getId(), new UserDeptVO(sender.getId(), sender.getDeptId()));
        list.forEach(it -> {
            switch (StringUtils.showEmpty(it.getObjType())) {
                case "user":
                    users.put(it.getObjDeptId() + ":" + it.getObjId(), new UserDeptVO(it.getObjId(), it.getObjDeptId()));
                    break;
                case "dept":
                    DeptDto dept = permissionService.getDeptById(it.getObjId());
                    if (dept != null) {
                        permissionService.getUserListByDeptCodePrefix(dept.getCode()).forEach(u -> users.put(
                                dept.getId() + ":" + u.getId(),
                                new UserDeptVO(u.getId(), dept.getId())
                        ));
                    }
                    break;
                default:
                    break;
            }
        });
        //当前参与人id
        users.values()
                .forEach(user -> this.getUserSession(user, websocketMessageVO.getSystemInfo())
                        .stream()
                        .filter(Session::isOpen)
                        .forEach(item -> CompletableFuture.runAsync(
                                () -> item.getAsyncRemote().sendText(JsonUtil.toJsonString(websocketMessageVO), result -> {
                                            if (result.isOK()) {
                                                log.info("向用户:{},sessionId:{},发送情指行消息：{}", user, item.getId(),
                                                        JsonUtil.toJsonString(websocketMessageVO));
                                            } else {
                                                log.warn("向用户:{},sessionId:{},发送情指行消息失败", user, item.getId(),
                                                        result.getException());
                                            }
                                        }
                                ))));
    }


    /**
     * 向用户发送合成消息-阻塞式
     *
     * @param notice {@link NoticeEntity}
     */
    public void sendMessageCenterByBasic(NoticeVO notice) throws Exception {
        List<Session> sessions = this.getUserSession(new UserDeptVO(notice.getReceiver().getUserId(), notice.getReceiver().getDeptId()),
                        Channel.getMessageCenterChannel()).stream()
                .filter(Session::isOpen)
                .collect(Collectors.toList());
        for (Session session : sessions) {
            session.getBasicRemote().sendText(JsonUtil.toJsonString(notice));
        }
    }

    /**
     * 向用户发送合成消息
     *
     * @param notice {@link NoticeEntity}
     */
    public void sendMessageCenter(NoticeVO notice) {
        this.getUserSession(new UserDeptVO(notice.getReceiver().getUserId(), notice.getReceiver().getDeptId()),
                        Channel.getMessageCenterChannel()).stream()
                .filter(Session::isOpen)
                .forEach(item -> item.getAsyncRemote().sendText(JsonUtil.toJsonString(notice),
                        result -> {
                            if (result.isOK()) {
                                log.info("向用户:{},sessionId:{},发送系统消息：{}", notice.getReceiver().getUserId(),
                                        item.getId(),
                                        JsonUtil.toJsonString(notice));
                            } else {
                                log.error("向用户:{},sessionId:{},发送系统消息失败！", notice.getReceiver().getUserId(),
                                        item.getId(), result.getException());
                            }
                        }));
    }

    /**
     * 向用户发送检索结果消息
     *
     * @param sessionId  会话id
     * @param userDeptVO 用户信息
     * @param resultStr  信息字符串
     */
    public void sendSearchResultMessage(String sessionId, UserDeptVO userDeptVO, String resultStr) {
        Channel channel = Channel.getSearchChannel();
        channel.setChannelId(sessionId);
        this.getUserSession(userDeptVO, channel).stream()
                .filter(Session::isOpen)
                .forEach(item -> item.getAsyncRemote().sendText(resultStr,
                        result -> {
                            if (result.isOK()) {
                                log.info("向用户:{} sessionId:{} 发送检索消息：{}", userDeptVO.getUserId(), item.getId(),
                                        result);
                            } else {
                                log.error("向用户:{} sessionId:{} 发送检索消息失败！", userDeptVO.getUserId(), item.getId(),
                                        result.getException());
                            }
                        }));
    }
}
