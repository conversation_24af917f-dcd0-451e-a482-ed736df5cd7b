package com.trs.police.message.listener;

import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.core.vo.message.SearchMessageVO;
import com.trs.police.message.manager.SessionManager;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

/**
 * 接收检索结果
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class RabbitMqListener {

    @Resource
    private SessionManager sessionManager;

    /**
     * 接收任务消息，记录日志
     *
     * @param message 消息
     */
    @RabbitHandler
    @RabbitListener(bindings = @QueueBinding(
        value = @Queue(autoDelete = "true"),
        exchange = @Exchange(value = "search_exchange", type = ExchangeTypes.FANOUT)))
    public void receiveSearchMessage(String message) {
        log.info("接收到消息：{}", message);
        SearchMessageVO vo = JsonUtil.parseObject(message, SearchMessageVO.class);
        if (vo == null) {
            log.error("检索请求处理失败！message：{}", message);
            return;
        }
        if (vo.getType().equals("search-result")) {
            sessionManager.sendSearchResultMessage(vo.getSessionId(), vo.getSender(), vo.getResult());
        }
    }

}
