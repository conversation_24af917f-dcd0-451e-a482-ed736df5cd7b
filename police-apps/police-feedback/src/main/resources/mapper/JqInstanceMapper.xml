<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.feedback.mapper.JqInstanceMapper">


    <update id="batchUpdateStatus">
        update t_jq_instance set jq_status = #{destStatus}
        where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

    </update>
</mapper>