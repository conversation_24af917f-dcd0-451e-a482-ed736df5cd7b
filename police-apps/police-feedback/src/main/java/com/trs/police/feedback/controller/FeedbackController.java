package com.trs.police.feedback.controller;

import com.grt.condify.exception.CondifyException;
import com.trs.police.feedback.domain.dto.FeedbackMessageDTO;
import com.trs.police.feedback.domain.dto.FeedbackMessageListDTO;
import com.trs.police.feedback.domain.vo.FeedbackMessageVO;
import com.trs.police.feedback.service.FeedbackService;
import com.trs.web.builder.base.RestfulResultsV2;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @description 反馈信息相关接口
 * @date 2023/11/13 15:20
 */
@RestController
@Api(value = "反馈信息相关接口", tags = "反馈信息相关接口")
@RequestMapping("/feedback")
@RequiredArgsConstructor
public class FeedbackController {

    private final FeedbackService feedbackService;

    /**
     * 反馈信息保存
     *
     * @param feedbackMessageDTO dto
     * @return 保存结果
     */
    @PostMapping("save")
    @ApiOperation(value = "反馈信息保存", notes = "反馈信息保存")
    public RestfulResultsV2<Long> createComposite(@Validated @RequestBody FeedbackMessageDTO feedbackMessageDTO) {
        return feedbackService.save(feedbackMessageDTO);
    }

    /**
     * 列表查询
     *
     * @param feedbackMessageListDTO 查询dto
     * @return 列表
     * @throws CondifyException 条件构造异常
     */
    @PostMapping("queryList")
    @ApiOperation(value = "列表查询", notes = "列表查询")
    public RestfulResultsV2<FeedbackMessageVO> queryList(@Validated @RequestBody FeedbackMessageListDTO feedbackMessageListDTO) throws CondifyException {
        return feedbackService.queryList(feedbackMessageListDTO);
    }

    /**
     * 我的历史反馈列表
     *
     * @param feedbackMessageListDTO 查询条件
     * @return 反馈列表
     * @throws CondifyException 异常
     */
    @PostMapping("queryMyFeedbackList")
    @ApiOperation(value = "我的历史反馈列表", notes = "我的历史反馈列表")
    public RestfulResultsV2<FeedbackMessageVO> queryMyFeedbackList(@Validated @RequestBody FeedbackMessageListDTO feedbackMessageListDTO) throws CondifyException {
        return feedbackService.queryMyFeedbackList(feedbackMessageListDTO);
    }

    /**
     * 获取反馈详情
     *
     * @param id id
     * @return 详情
     */
    @GetMapping("getFeedbackDetail")
    @ApiOperation(value = "获取反馈详情", notes = "获取反馈详情")
    public RestfulResultsV2<FeedbackMessageVO> getFeedbackDetail(@RequestParam(value = "id") Long id) {
        return feedbackService.getFeedbackDetail(id);
    }

    /**
     * 回复
     *
     * @param id       id
     * @param replyMsg 回复内容
     * @return 回复结果
     */
    @PostMapping("reply")
    @ApiOperation(value = "反馈回复", notes = "反馈回复")
    public RestfulResultsV2<String> reply(@RequestParam(value = "id") Long id, @RequestParam(value = "replyMsg") String replyMsg) {
        return feedbackService.reply(id, replyMsg);
    }

    /**
     * 设置已读
     *
     * @param id       id
     * @return 设置结果
     */
    @PostMapping("read")
    @ApiOperation(value = "设置已读", notes = "设置已读")
    public RestfulResultsV2<String> read(@RequestParam(value = "id") Long id) {
        return feedbackService.read(id);
    }

}
