package com.trs.police.feedback.common.util;

import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.UserDto;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import com.trs.police.common.openfeign.starter.service.OssService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.feedback.converter.BasicInfoCache;
import net.sf.json.JSONArray;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 数据转换帮助类
 * @date 2024/1/18 14:52
 */
public class ConverterHelper {

    /**
     * 用户名处理
     *
     * @param userId 用户id
     * @return 用户名
     */
    public static String dealUserName(Long userId) {
        UserDto user = getUser(userId);
        //万一有脏数据
        return user == null ? "" : user.getRealName();
    }

    /**
     * 根据用户id获取用户信息并缓存
     *
     * @param userId 用户id
     * @return 用户详情
     */
    public static UserDto getUser(Long userId) {
        UserDto user = BasicInfoCache.UserInfoCache.get(userId);
        if (user != null) {
            return user;
        }

        final PermissionService permissionService = BeanUtil.getBean(PermissionService.class);
        user = permissionService.getUserById(userId);
        if (user != null) {
            BasicInfoCache.UserInfoCache.put(userId, user);
        }
        return user;
    }

    /**
     * 用户手机号处理
     *
     * @param userId 用户id
     * @return 用户名
     */
    public static String dealUserMobile(Long userId) {
        UserDto user = getUser(userId);

        return user == null ? "" : user.getTel();
    }

    /**
     * 部门名处理
     *
     * @param deptId 部门id
     * @return 部门名
     */
    public static String dealDeptName(Long deptId) {
        DeptDto dept = BasicInfoCache.DeptInfoCache.get(deptId);
        if (dept == null) {
            final PermissionService permissionService = BeanUtil.getBean(PermissionService.class);
            dept = permissionService.getDeptById(deptId);

            if (dept != null) {
                BasicInfoCache.DeptInfoCache.put(deptId, dept);
            }
        }

        return dept == null ? "" : dept.getShortName();
    }

    /**
     * 附件处理--dto->entity
     *
     * @param fileListId 附件id集合
     * @return 附件集合
     */
    public static String dealAppendix(List<Long> fileListId) {
        List<FileInfoVO> appendix = new ArrayList<>();
        if (Objects.nonNull(fileListId) && !fileListId.isEmpty()) {
            final OssService ossService = BeanUtil.getBean(OssService.class);
            appendix = fileListId.stream().map(id -> FileInfoVO.idNumberToImg(ossService.getFileInfo(id))).collect(Collectors.toList());
        }

        JSONArray jsonArray = JSONArray.fromObject(appendix);

        return jsonArray.toString();
    }
}
