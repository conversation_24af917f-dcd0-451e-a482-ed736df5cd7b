package com.trs.police.syncud.common.constants;

import java.util.HashMap;
import java.util.Map;

/**
 * 云墙协同作战同步静态数据
 *
 * <AUTHOR>
 * @date 2014/11/27 20:14
 */
public class BattleDemandConstants {

    public static final Map<Integer, Long> COLLABORATION_BATTLE_STATUS_MAP = new HashMap<>();

    public static final Map<String, Long> COLLABORATION_BATTLE_SUBMIT_TYPE_MAP = new HashMap<>();

    static {
        COLLABORATION_BATTLE_STATUS_MAP.put(1, 1L);
        COLLABORATION_BATTLE_STATUS_MAP.put(2, 3L);
        COLLABORATION_BATTLE_STATUS_MAP.put(3, 4L);

        COLLABORATION_BATTLE_SUBMIT_TYPE_MAP.put("数据查询", 552682L);
        COLLABORATION_BATTLE_SUBMIT_TYPE_MAP.put("数据分析", 552683L);
        COLLABORATION_BATTLE_SUBMIT_TYPE_MAP.put("数据监测", 552684L);
    }

}
