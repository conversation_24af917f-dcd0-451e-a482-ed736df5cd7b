package com.trs.police.syncud.service.impl;

import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.entity.FileInfo;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.syncud.common.constants.enums.FileTypeEnum;
import com.trs.police.syncud.config.MinioProperties;
import com.trs.police.syncud.domain.entity.FileDetail;
import com.trs.police.syncud.domain.vo.FileInfoVO;
import com.trs.police.syncud.mapper.FileInfoMapper;
import com.trs.police.syncud.service.FileService;
import io.minio.BucketExistsArgs;
import io.minio.MakeBucketArgs;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.errors.MinioException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.security.GeneralSecurityException;
import java.util.Map;
import java.util.Objects;

/**
 * @author: dingkeyu
 * @date: 2024/10/10
 * @description:
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class FileServiceImpl implements FileService {

    /**
     * 互联网地址
     */
    @Value("${com.trs.internetAddress:--}")
    private String internetAddress;

    private final MinioClient minioClient;

    private final MinioProperties minioProperties;

    private final FileInfoMapper fileInfoMapper;

    /**
     * 检查bucket是否存在并创建
     *
     * @throws IOException              io异常
     * @throws MinioException           minio异常
     * @throws GeneralSecurityException 安全异常
     */
//    @PostConstruct
    public void checkBucketExist()
            throws IOException, MinioException, GeneralSecurityException {
        final String bucketName = minioProperties.getBucket();
        createBucket(bucketName);
    }

    private void createBucket(String bucketName)
            throws IOException, MinioException, GeneralSecurityException {
        BucketExistsArgs bucketArgs = BucketExistsArgs.builder()
                .bucket(bucketName).build();
        //bucket不存在
        if (!minioClient.bucketExists(bucketArgs)) {
            log.info("minio bucket[{}] 不存在!", bucketName);
            MakeBucketArgs makeBucketArgs = MakeBucketArgs.builder()
                    .bucket(bucketName).build();
            minioClient.makeBucket(makeBucketArgs);
            log.info("创建 bucket [{}] 成功!", bucketName);
        }
    }

    @Override
    public FileInfoVO uploadByMinio(MultipartFile file) {

        try (InputStream inputStream = file.getInputStream()){
            String fileName = file.getOriginalFilename();
            long start = System.currentTimeMillis();
            log.info("上传文件：{}, 开始时间: {}", fileName, start);
            if (StringUtils.isEmpty(fileName)) {
                throw new Exception("文件目录创建失败，请重试！");
            }
            // .后缀名
            String suffixName = fileName.substring(fileName.lastIndexOf("."));
            String md5Hex = DigestUtils.md5Hex(inputStream);
            // 重置输入流，因为 DigestUtils.md5Hex 已经读取完它
            inputStream.reset();
            String finalFileName = md5Hex + suffixName;
            PutObjectArgs putObjectArgs = PutObjectArgs.builder().bucket(minioProperties.getBucket())
                    .contentType(file.getContentType()).object(finalFileName)
                    .stream(inputStream, inputStream.available(), -1)
                    .build();
            minioClient.putObject(putObjectArgs);
            long end = System.currentTimeMillis();
            log.info("上传文件：{}结束, 持续时间: {}", fileName, (end - start) / 1000.0);
            // 构建文件的可访问地址
            String fileUrl = "/oss/file/" + finalFileName;
            FileTypeEnum fileTypeEnum = FileTypeEnum.ofExtension(suffixName.substring(1));
            com.trs.police.common.core.vo.oss.FileInfoVO infoVO = new com.trs.police.common.core.vo.oss.FileInfoVO();
            infoVO.setId(-1L);

            FileInfoVO fileInfoVO = new FileInfoVO(-1L, fileName, fileUrl, fileTypeEnum.getCode());
            return fileInfoVO;
        }catch (Exception e){
            log.error("文件上传失败！", e);
        }
        return null;
    }

    /**
     * 流式上传文件
     *
     * @param bucketName  bucket名称
     * @param fileDetail  文件详情
     * @param fileUrl     文件地址
     * @param requestMap  请求头参数
     * @param contentType contentType
     * @return 文件信息
     */
    public com.trs.police.common.core.vo.oss.FileInfoVO storage(String bucketName, FileDetail fileDetail, String fileUrl, Map<String, String> requestMap, String contentType) {
        FileInfo fileInfo = new FileInfo();
        InputStream inputStream = null;
        try {
            URL url = new URL(fileUrl);
            URLConnection connection = url.openConnection();
            if (requestMap != null) {
                requestMap.forEach(connection::setRequestProperty);
            }
            inputStream = connection.getInputStream();
            //获取文件md5
            final String md5 = DigestUtils.md5Hex(inputStream);
            //获取文件后缀名
            String originalFileName = fileDetail.getName();
            String nameExt = FilenameUtils.getExtension(originalFileName);
            final String ext = StringUtils.isEmpty(nameExt) ? contentType.substring(contentType.indexOf("/") + 1) : nameExt;
            String fileName = null;
            bucketName = StringUtils.isEmpty(bucketName) ? minioProperties.getBucket() : bucketName;
            if (!Objects.equals(minioProperties.getBucket(), bucketName)) {
                fileName = bucketName + "-" + md5 + "." + ext;
            } else {
                fileName = md5 + "." + ext;
            }
            //当为图片时，直接使用文件名
            if (Objects.equals(bucketName, minioProperties.getPhotoBucket())) {
                fileName = originalFileName;
            }
            long start = System.currentTimeMillis();
            log.info("上传文件：{}, 开始时间: {}", fileName, start);
            fileInfo.setName(originalFileName);
            fileInfo.setExt(ext);
            fileInfo.setContentType(com.trs.police.common.core.constant.enums.FileTypeEnum.ofExtension(ext));
            fileInfo.setSize(fileDetail.getFilesize());
            fileInfo.setMd5(md5);
            fileInfo.setStatus(Byte.valueOf("0"));
            fileInfo.setPath(String.join("/", bucketName, md5));
            fileInfo.setUrl(String.join(
                    "/",
                    "/oss",
                    !Objects.equals(minioProperties.getBucket(), bucketName) ? bucketName : "file",
                    fileName
            ));
            fileInfo.fillAuditFields();
            //文件预处理,生成预览图
            fileInfoMapper.insert(fileInfo);
            //获取文件输入流
            PutObjectArgs putObjectArgs = PutObjectArgs.builder()
                    .bucket(bucketName)
                    .contentType(fileInfo.getContentType().getCode())
                    .object(fileName)
                    .stream(inputStream, inputStream.available(), -1)
                    .build();
            minioClient.putObject(putObjectArgs);
            long end = System.currentTimeMillis();
            log.info("上传文件：{}结束, 持续时间: {}", fileName, (end - start) / 1000.0);
        } catch (Exception e) {
            log.error("文件流输入失败", e);
            throw new TRSException("文件流输入失败", e);
        } finally {
            if (inputStream != null) {
                IOUtils.closeQuietly(inputStream);
            }
        }
        return com.trs.police.common.core.vo.oss.FileInfoVO.idNumberToImg(fileInfo);
    }

}
