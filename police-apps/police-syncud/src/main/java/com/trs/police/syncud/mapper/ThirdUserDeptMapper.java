package com.trs.police.syncud.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.syncud.domain.entity.SpUserDept;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * 第三方用户部门信息关联表
 *
 * <AUTHOR>
 * @date 2024/3/19 17:50
 */
@Mapper
@DS("user-mysqldb")
public interface ThirdUserDeptMapper extends BaseMapper<SpUserDept> {

    /**
     * 根据用户id查询用户部门关联信息
     *
     * @param id 部门代码
     * @return 第三方用户部门信息
     */
    @Select("select * from yh_user_dept where user_id = #{id}")
    SpUserDept selectByUserId(Long id);
}
