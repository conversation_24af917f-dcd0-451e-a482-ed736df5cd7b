package com.trs.police.syncud.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.syncud.domain.entity.LnDept;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * 第三方部门信息持久层
 *
 * <AUTHOR>
 * @date 2023/12/05 17:59
 */
@Mapper
public interface LnDeptMapper extends BaseMapper<LnDept> {
    /**
     * 根据名称获取部门编码
     *
     * @param name 名称
     * @return 部门编码
     */
    @Select("select code from ln_dept where name = #{name}")
    String selectCodeByName(String name);

}