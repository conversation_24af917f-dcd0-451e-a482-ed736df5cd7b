package com.trs.police.authorize.domain;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/2/17 14:19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserLockVO {

    private int loginFailCount;

    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime firstLoginFailTime;

    public UserLockVO(LocalDateTime now) {
        this.loginFailCount = 1;
        this.firstLoginFailTime = now;
    }

    /**
     * 登陆失败
     */
    public void lock() {
        this.loginFailCount = this.loginFailCount + 1;
    }
}
