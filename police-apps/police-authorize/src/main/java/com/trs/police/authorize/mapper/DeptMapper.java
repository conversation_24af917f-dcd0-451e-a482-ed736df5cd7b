package com.trs.police.authorize.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.authorize.domain.entity.Dept;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * t_dept 表查询接口
 *
 * <AUTHOR>
 */
@Mapper
public interface DeptMapper extends BaseMapper<Dept> {

    /**
     * 根据单位代码获取单位信息
     *
     * @param code 单位代码
     * @return 单位信息
     */
    @Select("select * from t_dept d where d.code = #{code} and d.deleted != 1")
    Dept findByCode(@NotEmpty @Param("code") String code);

    /**
     * 根据用户id查询用户的主要部门
     *
     * @param userId 用户id
     * @return 单位信息
     */
    @Select("select d.* from t_dept d join t_user_dept_relation tudr on d.id = tudr.dept_id where tudr.user_id=#{userId}")
    List<Dept> findAllByUserId(@NotEmpty @Param("userId") Long userId);

    /**
     * 根据pid查找
     *
     * @param pid  pid
     * @param page 分页信息
     * @return 单位分页信息
     */
    @Select("select * from t_dept d where d.pid = #{pid}")
    Page<Dept> findByPid(@NotNull @Param("pid") Long pid, Page<Dept> page);

    /**
     * 查全部
     *
     * @return {@link Dept}
     */
    @Select("select * from t_dept")
    List<Dept> findAll();

    /**
     * 根据父id查询
     *
     * @param id 父id
     * @return {@link Dept}
     */
    @Select("select * from t_dept d where d.pid = #{id}")
    List<Dept> findAllByParentId(Long id);
}
