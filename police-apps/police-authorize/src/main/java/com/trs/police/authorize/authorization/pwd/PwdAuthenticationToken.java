package com.trs.police.authorize.authorization.pwd;

import com.trs.police.authorize.authorization.AbstractYsAuthenticationToken;
import java.util.Collection;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.SpringSecurityCoreVersion;

/**
 * 用户名密码认证token
 *
 * <AUTHOR>
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public class PwdAuthenticationToken extends AbstractYsAuthenticationToken {

    private static final long serialVersionUID = SpringSecurityCoreVersion.SERIAL_VERSION_UID;

    private final Object principal;
    private final String passWord;
    /**
     * 登录IP
     */
    private String remoteAddress;
    private static final String LOGIN_TYPE = "pwd";

    /**
     * 构造方法
     *
     * @param sfzh          身份证
     * @param passWord      密码
     * @param defaultUnitId 默认单位
     */
    public PwdAuthenticationToken(final String sfzh, final String passWord, final String defaultUnitId) {
        super(defaultUnitId, LOGIN_TYPE);
        this.principal = sfzh;
        this.passWord = passWord;
        setAuthenticated(false);
    }

    /**
     * 构造方法
     *
     * @param principal     凭证
     * @param authorities   属性
     * @param passWord      密码
     * @param defaultUnitId 默认单位
     */
    public PwdAuthenticationToken(final Object principal, Collection<? extends GrantedAuthority> authorities,
        final String passWord, final String defaultUnitId) {
        super(authorities, defaultUnitId, LOGIN_TYPE);
        this.principal = principal;
        this.passWord = passWord;
        super.setAuthenticated(true);
    }

    /**
     * The credentials that prove the principal is correct. This is usually a password, but could be anything relevant
     * to the
     * <code>AuthenticationManager</code>. Callers are expected to populate the
     * credentials.
     *
     * @return the credentials that prove the identity of the <code>Principal</code>
     */
    @Override
    public Object getCredentials() {
        return null;
    }


    @Override
    public void setAuthenticated(boolean isAuthenticated) throws IllegalArgumentException {
        if (isAuthenticated) {
            throw new IllegalArgumentException(
                "Cannot set this token to trusted - use constructor which takes a GrantedAuthority list instead");
        }

        super.setAuthenticated(false);
    }
}
