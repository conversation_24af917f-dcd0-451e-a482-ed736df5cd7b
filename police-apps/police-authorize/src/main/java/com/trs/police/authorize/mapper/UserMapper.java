package com.trs.police.authorize.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.authorize.domain.entity.User;
import com.trs.police.common.core.vo.permission.SimpleUserVO;

import java.util.List;
import java.util.Set;
import javax.validation.constraints.NotEmpty;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Select;

/**
 * t_user 表查询接口
 *
 * <AUTHOR>
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 通过用户名查找用户信息
     *
     * @param username 用户名
     * @return 用户信息
     */
    @Select("select * from t_user t where t.username = #{username}")
    @Result(column = "idcard", property = "idNumber")
    User findByName(@Param("username") String username);

    /**
     * 通过手机号查找用户信息
     *
     * @param mobile 手机号
     * @return 用户信息
     */
    @Select("select * from t_user t where t.mobile = #{mobile}")
    @Result(column = "idcard", property = "idNumber")
    List<User> findByMobile(@Param("mobile") String mobile);

    /**
     * 根据用户身份证号码查找用户
     *
     * @param idcard 身份证号码
     * @return 用户信息
     */
    @Select("select * from t_user u where u.idcard = #{idcard} ")
    @Result(column = "idcard", property = "idNumber")
    User findByIdentify(@NotEmpty @Param("idcard") String idcard);

    /**
     * 获取角色信息
     *
     * @param userId 用户id
     * @param deptId 部门id
     * @return 角色
     */
    @Select("select role_id from t_user_role_relation where user_id = #{userId} and dept_id = #{deptId}")
    Set<Long> getRoles(@Param("userId") Long userId, @Param("deptId") Long deptId);

    /**
     * 获取用户信息
     *
     * @param userId 用户id
     * @param deptId 部门id
     * @return SimpleUserVO
     */
    SimpleUserVO findSimpleUser(@Param("userId") Long userId, @Param("deptId") Long deptId);

    /**
     * 根据用户id获取用户部门id
     *
     * @param userId 用户id
     * @return 用户部门id
     */
    @Select("select dept_id from t_user_dept_relation where user_id=#{userId} order by create_time desc limit 1")
    Long findDefaultUnitId(Long userId);
}
