package com.trs.police.authorize.authorization;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.Map;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.authentication.SimpleUrlAuthenticationSuccessHandler;
import org.springframework.stereotype.Component;

/**
 * 自定义登录成功/失败后处理逻辑
 *
 * @author: luoxu
 */
@Component
@Slf4j
public class ClientAuthenticationSuccessAndFailureHandler extends SimpleUrlAuthenticationSuccessHandler implements
    AuthenticationSuccessHandler, AuthenticationFailureHandler {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    @Resource
    private ClientDetailsService clientDetailsService;

    @Lazy
    @Resource
    private AuthorizationServerTokenServices authorizationServerTokenServices;

    @Override
    public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response,
        AuthenticationException exception) throws IOException {

        log.debug("登录失败", exception);
        response.setContentType("application/json;charset=utf-8");
        response.getWriter()
            .write(OBJECT_MAPPER.writeValueAsString(Map.of("code", 0, "message", exception.getMessage())));

    }

    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response,
        Authentication authentication) throws IOException {

        log.info("登录成功");
        response.setContentType("application/json;charset=UTF-8");

        try (PrintWriter writer = response.getWriter()) {

            new ObjectMapper().writeValue(writer, authentication);
        }


    }

}
