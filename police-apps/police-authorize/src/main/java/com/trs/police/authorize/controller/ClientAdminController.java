package com.trs.police.authorize.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.authorize.domain.entity.OauthClientDetails;
import com.trs.police.authorize.service.OauthClientDetailsService;
import javax.annotation.Resource;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * oauth2 client管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/client")
public class ClientAdminController {

    @Resource
    private OauthClientDetailsService oauthClientDetailsService;

    /**
     * 分页查询用户列表
     *
     * @param pageable 分页参数
     * @return {@link OauthClientDetails}
     */
    @GetMapping("/list")
    public Page<OauthClientDetails> page(Pageable pageable) {
        return oauthClientDetailsService.pageAll(Page.of(pageable.getPageNumber(), pageable.getPageSize()));
    }

    /**
     * 查询用户
     *
     * @param clientId clientId
     * @return {@link  OauthClientDetails}
     */
    @GetMapping("/get/{clientId}")
    public OauthClientDetails get(@PathVariable("clientId") String clientId) {
        return (OauthClientDetails) oauthClientDetailsService.loadClientByClientId(clientId);
    }

    /**
     * 生成密钥
     *
     * @param secret secret
     * @return 结果
     */
    @GetMapping("/generateClientSecret")
    public String generateClientSecret(String secret) {
        return oauthClientDetailsService.generateClientSecret(secret);
    }
}
