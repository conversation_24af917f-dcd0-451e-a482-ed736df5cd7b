<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.fight.mapper.FightCompositeCaseEventRelationMapper">
    <resultMap id="seriesCaseMap" type="com.trs.police.common.core.vo.profile.SeriesCaseListVO">
        <id column="id" property="id"/>
        <result column="asjbh" property="asjbh"/>
        <result column="caseStatus" property="caseStatus"/>
        <result column="caseName" property="caseName"/>
        <result column="caseLevel" property="caseLevel"/>
        <result column="caseTopType" property="caseTopType"/>
        <result column="caseFineType" property="caseFineType"/>
        <result column="sponsor" property="sponsor"/>
        <result column="organizer" property="organizer"/>
        <result column="occurTime" property="occurTime"/>
    </resultMap>
    <select id="getCaseEventByCompositeId" resultMap="seriesCaseMap">
        select t1.id as id,
               t1.asjbh as asjbh,
               t1.important_level as caseLevel,
               t2.ajywztmxdm as caseStatus,
               t2.ajmc as caseName,
               (select ct from JWZH_DICT2 where zdbh='GA_D_XSAJLBDM' and dm = t2.ajlbdm)as caseTopType,
               (select ct from JWZH_DICT2 where zdbh='GA_D_XSAJXALBDM' and dm = t2.ajxlbdm)as caseFineType,
               (select real_name from t_user where idcard = t2.zbr_gmsfhm )as sponsor,
               (select name from t_dept where code = t2.badw_gajgjgdm )as organizer,
               cast(t2.xt_lrsj as DATETIME)    as   occurTime,
               t2.slsj as acceptTime,
               t2.jyaq as caseAbstract
        from t_profile_case t1
                 join JWZH_XSAJ_AJ t2 on t1.asjbh = t2.asjbh
                 join t_fight_composite_case_event_relation t3 on t1.id=t3.case_event_id
        where t3.composite_id = #{compositeId}
    </select>
    <select id="getCaseEventByCompositeIds" resultMap="seriesCaseMap">
        select t1.id as id,
               t1.asjbh as asjbh,
               t1.important_level as caseLevel,
               t2.ajywztmxdm as caseStatus,
               t2.ajmc as caseName,
               t3.composite_id as compositeId,
               (select ct from JWZH_DICT2 where zdbh='GA_D_XSAJLBDM' and dm = t2.ajlbdm)as caseTopType,
               (select ct from JWZH_DICT2 where zdbh='GA_D_XSAJXALBDM' and dm = t2.ajxlbdm)as caseFineType,
               (select real_name from t_user where idcard = t2.zbr_gmsfhm )as sponsor,
               (select name from t_dept where code = t2.badw_gajgjgdm )as organizer,
               cast(t2.xt_lrsj as DATETIME)    as   occurTime
        from t_profile_case t1
                 join JWZH_XSAJ_AJ t2 on t1.asjbh = t2.asjbh
                 join t_fight_composite_case_event_relation t3 on t1.id=t3.case_event_id
        where t3.composite_id in
        <foreach collection="compositeIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>
</mapper>