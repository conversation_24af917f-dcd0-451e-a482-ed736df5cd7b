package com.trs.police.fight.domain.entity;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.stream.Collectors;

/**
 * 预案记录
 *
 * <AUTHOR>
 * @date 2024/10/09
 */
@Data
@TableName("t_plan_record")
@AllArgsConstructor
@NoArgsConstructor
public class PlanRecordEntity extends AbstractBaseEntity {

    /**
     * 预案内容
     */
    @TableField(value = "content")
    private String content;

    /**
     * 预案id
     */
    @TableField(value = "plan_id")
    private Long planId;

    /**
     * 处突id
     */
    @TableField(value = "composite_id")
    private Long compositeId;

    /**
     * 附件信息
     */
    @TableField(value = "attachments")
    private String attachments;

    /**
     * 操作，0代表停用，1代表启用，2代表调度反馈
     */
    @TableField(value = "operation")
    private Integer operation;

    /**
     * 生成内容
     *
     * @param jqInfo 警情信息
     * @param warningInfo 预警信息
     */
    public void buildContent(JSONArray jqInfo, JSONArray warningInfo) {
        String jq = jqInfo.stream().map(e -> {
            JSONObject object = (JSONObject) e;
            return object.getString("content");
        }).collect(Collectors.joining(","));
        String warning = warningInfo.stream().map(e -> {
            JSONObject object = (JSONObject) e;
            return object.getString("content");
        }).collect(Collectors.joining(","));
        this.content = content +  (!StringUtils.isEmpty(jq)
                ? ("关联警情：" + jq + "。") : "");
        this.content = content +  (!StringUtils.isEmpty(warning)
                ? ("关联预警：" + warning) : "");
    }
}
