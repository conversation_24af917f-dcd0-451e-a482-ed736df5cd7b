package com.trs.police.fight.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.constant.log.OperateModule;
import com.trs.police.common.core.constant.log.Operation;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 日志管理
 *
 * <AUTHOR>
 * @since 2022/5/11 10:08
 **/
@EqualsAndHashCode(callSuper = true)
@TableName("t_operation_log")
@Data
public class OperationLogEntity extends AbstractBaseEntity {

    private static final long serialVersionUID = 7120151440515995231L;
    /**
     * 操作模块
     */
    private OperateModule module;
    /**
     * 操作目标id
     */
    private Long relatedId;
    /**
     * 操作类型
     */
    private Operation operation;
    /**
     * ip地址
     */
    private String ipAddress;
    /**
     * 数据变更详情
     */
    private String detail;
    /**
     * 修改原因
     */
    private String reason;
    /**
     * 审批id
     */
    private Long approvalId;

    /**
     * 操作内容 业务自己定义
     */
    private String operateContent;
}
