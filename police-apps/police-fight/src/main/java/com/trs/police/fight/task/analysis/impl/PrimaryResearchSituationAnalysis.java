package com.trs.police.fight.task.analysis.impl;

import com.trs.police.fight.constant.CluePoolConstant;
import com.trs.police.fight.domain.entity.ClueEntity;
import com.trs.police.fight.domain.entity.CluePoolAnalysisResult;
import com.trs.police.fight.task.analysis.IClueItemAnalysis;
import com.trs.police.fight.task.context.ClueItemContext;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: dingkeyu
 * @date: 2024/03/26
 * @description: 初研情况统计
 */
@Component
public class PrimaryResearchSituationAnalysis implements IClueItemAnalysis {

    @Override
    public void itemAnalysis(ClueItemContext itemContext, CluePoolAnalysisResult cluePoolAnalysisResult) {
        List<ClueEntity> clueEntities = itemContext.getClueEntities();
        Map<Integer, List<ClueEntity>> collect = clueEntities.stream()
                .collect(Collectors.groupingBy(ClueEntity::getStatus));
        //无效数量
        cluePoolAnalysisResult.setClueInspectionInvalidCount(getCount(collect, CluePoolConstant.Status.WUXIAO.getCode()));
        //关注数量
        cluePoolAnalysisResult.setClueInspectionCareCount(getCount(collect, CluePoolConstant.Status.GUANZHU.getCode()));
        //立线数量
        cluePoolAnalysisResult.setClueInspectionEstablishCount(getCount(collect, CluePoolConstant.Status.LIXIAN.getCode()));
        //成案数量
        cluePoolAnalysisResult.setClueInspectionCompleteCount(getCount(collect, CluePoolConstant.Status.CHENGAN.getCode()));
    }

    /**
     * 初研统计
     *
     * @param map  map
     * @param code 状态码
     * @return 结果
     */
    private Long getCount(Map<Integer, List<ClueEntity>> map, Integer code) {
        List<ClueEntity> count = map.get(code);
        return CollectionUtils.isEmpty(count) ? 0L : count.size();
    }

    @Override
    public String key() {
        return "primary_research_situation";
    }

    @Override
    public String desc() {
        return "初研情况";
    }
}
