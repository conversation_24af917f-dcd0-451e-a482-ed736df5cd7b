package com.trs.police.fight.service.impl.fightResultService;

import com.trs.police.fight.constant.enums.FightResultFillingEnum;
import com.trs.police.fight.domain.dto.FightResultDto;
import com.trs.police.fight.domain.entity.CluePoolAnalysisResult;
import com.trs.police.fight.domain.entity.FightResultOtherInfoRelation;
import com.trs.police.fight.task.analysis.vo.FightResultCombination;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: dingkeyu
 * @date: 2024/03/21
 * @description: 战果部级集群战役填报
 */
@Service
public class FightResultBattleServiceImpl extends AbsFightResultOtherinfoServiceImpl {

    @Override
    public Integer operateType() {
        return FightResultFillingEnum.CLUSTER_BATTLE.getCode();
    }

    @Override
    public Function<FightResultDto, List<FightResultDto.OtherInfo>> otherInfoFunction() {
        return FightResultDto::getClusterBattle;
    }

    @Override
    public BiConsumer<FightResultDto, List<FightResultDto.OtherInfo>> otherInfoBiConsumer() {
        return FightResultDto::setClusterBattle;
    }

    @Override
    public BiConsumer<FightResultCombination, List<FightResultOtherInfoRelation>> combinationBiConsumer() {
        return FightResultCombination::setCampaignFightResults;
    }

    @Override
    public void filterFightResult(List<Long> fightResultIds, FightResultCombination fightResultCombination) {
        List<FightResultOtherInfoRelation> fightResults = fightResultCombination.getCampaignFightResults().stream()
                .filter(e -> fightResultIds.contains(e.getFightResultId()))
                .collect(Collectors.toList());
        fightResultCombination.setCampaignFightResults(fightResults);
    }

    @Override
    public void fightResultClueAnalysis(FightResultCombination fightResultCombination, CluePoolAnalysisResult cluePoolAnalysisResult) {
        List<FightResultOtherInfoRelation> campaignFightResults = fightResultCombination.getCampaignFightResults();
        if (!CollectionUtils.isEmpty(campaignFightResults)) {
            cluePoolAnalysisResult.setFightResultMinisterialCampaignCount((long) campaignFightResults.size());
        } else {
            cluePoolAnalysisResult.setFightResultMinisterialCampaignCount(0L);
        }
    }
}
