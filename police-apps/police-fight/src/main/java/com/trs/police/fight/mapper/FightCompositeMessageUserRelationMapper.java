package com.trs.police.fight.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.common.core.entity.FightCompositeMessageUserRelation;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * t_fight_composite_message_user_relation 表查询接口
 *
 * <AUTHOR>
 */
@Mapper
public interface FightCompositeMessageUserRelationMapper extends BaseMapper<FightCompositeMessageUserRelation> {

    /**
     * 根据合成id和消息id查询关联
     *
     * @param userId    用户id
     * @param messageId 消息id
     * @return 用户列表
     */
    @Select(value = "select * from t_fight_composite_message_user_relation where user_id = #{userId} and message_id = #{messageId}")
    Optional<FightCompositeMessageUserRelation> findByUserIdAndMessageId(@Param("userId") Long userId,
        @Param("messageId") Long messageId);

    /**
     * 根据消息id查询
     *
     * @param messageId 消息id
     * @return 关联列表
     */
    @Select(value = "select * from t_fight_composite_message_user_relation where message_id = #{messageId}")
    List<FightCompositeMessageUserRelation> findByMessageId(Long messageId);

    /**
     * 根据用户id查询at列表
     *
     * @param userId 用户id
     * @param time 七天前
     * @return 关联列表
     */
    @Select(value = "select * from t_fight_composite_message_user_relation where user_id = #{userId} and is_at = true and #{time} <= create_time order by create_time desc")
    List<FightCompositeMessageUserRelation> findByUserIdIsAt(@Param("userId") Long userId, @Param("time") LocalDateTime time);

    /**
     * 获取用户未读消息
     *
     * @param userId      用户id
     * @param compositeId 合成id
     * @return 关联列表
     */
    @Select(value = "select t1.*  from t_fight_composite_message_user_relation t1 join t_fight_composite_message t2 on t1.message_id = t2.id where  t1.is_read =false and t1.user_id=#{userId} and t2.composite_id=#{compositeId}")
    List<FightCompositeMessageUserRelation> findUnreadMessageListByUserIdAndCompositeId(
        @Param("compositeId") Long compositeId, @Param("userId") Long userId);


    /**
     * 获取用户未读消息
     *
     * @param userId      用户id
     * @param compositeIds 合成id
     * @return 关联列表
     */
    List<FightCompositeMessageUserRelation> findUnreadMessageListByUserIdAndCompositeIds(
            @Param("compositeIds") List<Long> compositeIds, @Param("userId") Long userId);

    /**
     * 获取用户未读消息
     *
     * @param userId      用户id
     * @param deptId   部门id
     * @return 关联列表
     */
    @Select(value = "select count(distinct t1.id)  from t_fight_composite_message_user_relation t1 join t_fight_composite_message t2 on t1.message_id = t2.id where  t1.is_read =false and t1.user_id=#{userId} and t2.composite_id in (select composite_id from t_fight_composite_user_relation where user_id =#{userId} and dept_id=#{deptId})")
    Long countUnreadMessageByUserId(
        @Param("userId") Long userId,@Param("deptId") Long deptId);
}