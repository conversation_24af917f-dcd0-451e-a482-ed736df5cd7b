package com.trs.police.fight.controller;

import com.trs.police.common.core.entity.Collaboration;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.permission.DeptVO;
import com.trs.police.common.core.vo.permission.UserDeptVO;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.fight.domain.dto.CollaborationDto;
import com.trs.police.fight.domain.dto.FeedbackRefreshDTO;
import com.trs.police.fight.domain.params.collaboration.ApprovalParams;
import com.trs.police.fight.domain.request.CollaborationListRequest;
import com.trs.police.fight.domain.vo.Collaboration.ApprovalInfo;
import com.trs.police.fight.domain.vo.Collaboration.CollaborationJqVO;
import com.trs.police.fight.domain.vo.Collaboration.ReviewInfoVO;
import com.trs.police.fight.domain.vo.*;
import com.trs.police.fight.service.CollaborationService;
import com.trs.police.fight.service.CollaborationTimeAxisService;
import com.trs.police.fight.service.collaboration.CollaborationApprovalService;
import com.trs.police.fight.service.collaboration.CollaborationDownloadService;
import com.trs.police.fight.service.collaboration.JqService;
import com.trs.web.builder.base.RestfulResultsV2;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @author: dingkeyu
 * @date: 2024/03/06
 * @description: 协作
 */
@RestController
@RequestMapping(value = {"/collaboration", "/public/collaboration"})
public class CollaborationController {

    @Resource
    private CollaborationService collaborationService;

    @Resource
    private CollaborationTimeAxisService collaborationTimeAxisService;

    @Autowired
    private CollaborationApprovalService collaborationApprovalService;

    @Autowired
    private JqService jqService;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private CollaborationDownloadService collaborationDownloadService;

    /**
     * 发起协作
     *
     * @param dto dto
     * @return {@link Collaboration}
     */
    @PostMapping("startCollaboration")
    public Collaboration startCollaboration(@RequestBody CollaborationDto dto) {
        return collaborationService.createCollaboration(dto);
    }

    /**
     * 编辑协作
     *
     * @param dto dto
     */
    @PostMapping("updateCollaboration")
    public void updateCollaboration(@RequestBody CollaborationDto dto) {
        collaborationService.updateCollaboration(dto);
    }

    /**
     * 协作附件上传
     *
     * @param params params
     */
    @PostMapping("uploadCollaborationFile")
    public void uploadCollaborationFile(@RequestBody ApprovalParams params) {
        collaborationService.uploadCollaborationFile(params);
    }

    /**
     * 协作列表
     *
     * @param collaborationParams collaborationParams
     * @return {@link PageResult}<{@link CollaborationVO}>
     */
    @PostMapping("collaborationList")
    public PageResult<CollaborationVO> collaborationList(@RequestBody CollaborationListRequest collaborationParams) {
        return collaborationService.collaborationList(collaborationParams);
    }

    /**
     * 协作详情
     *
     * @param id 协作id
     * @return {@link CollaborationVO}
     */
    @GetMapping("detail")
    public CollaborationVO collaborationDetail(Long id) {
        return collaborationService.collaborationDetail(id, false);
    }

    /**
     * 未读协作数量
     *
     * @param type 类型
     * @return {@link Long}
     */
    @GetMapping("unreadCount")
    public CollaborationUnreadVO unreadCollaborationCount(Long type) {
        return collaborationService.unreadCollaborationCount(type);
    }

    /**
     * 协作-时间轴
     *
     * @param id 协作id
     * @return {@link List}<{@link CollaborationTimeAxisVO}>
     */
    @GetMapping("timeAxis")
    public List<CollaborationTimeAxisVO> listTimeAxis(Long id) {
        return collaborationTimeAxisService.listTimeAxis(id);
    }

    /**
     * 关联作战
     *
     * @param compositeId 作战id
     * @param collaborationId 协作id
     */
    @PostMapping("relateFight")
    public void relateFight(Long compositeId, Long collaborationId) {
        collaborationService.relateFight(compositeId, collaborationId);
    }

    /**
     * 审核信息
     *
     * @param collaborationId 协作id
     * @param status 状态
     * @return 审核信息
     */
    @GetMapping("reviewInfo")
    public List<ReviewInfoVO> reviewInfo(Long collaborationId, String status) {
        return collaborationService.reviewInfo(collaborationId, status);
    }

    /**
     * 审核信息
     *
     * @param collaborationId 协作id
     * @param status 状态
     * @param type 类型 审批表：spb 指令单：zld
     * @return 审核信息
     */
    @GetMapping("spbReviewInfo")
    public List<ReviewInfoVO> spbReviewInfo(Long collaborationId, String status, String type) {
        return collaborationService.spbReviewInfo(collaborationId, status, type);
    }

    /**
     * 审批表信息
     *
     * @param collaborationId 协作id
     * @return {@link CollaborationVO}
     */
    @GetMapping("approveFormInfo")
    public CollaborationVO approveFormInfo(Long collaborationId) {
        return collaborationService.approveFormInfo(collaborationId);
    }

    /**
     * 根据id获取协作
     *
     * @param collaborationId 协作id
     * @return {@link Collaboration}
     */
    @GetMapping("getCollaborationById")
    public Collaboration getCollaborationById(Long collaborationId) {
        return collaborationService.getCollaborationById(collaborationId);
    }

    /**
     * 转换成作战
     *
     * @param id 协作id
     * @return {@link CollaborationVO}
     */
    @PostMapping("convertToFight")
    public CollaborationVO convertToFight(Long id) {
        return collaborationService.convertToFight(id);
    }

    /**
     * 获取首次审批用户列表
     *
     * @param collaborationType 协作类别
     * @param targetDeptId 目标部门id
     * @param startDept 发起部门id
     * @return 首次审批用户列表
     */
    @GetMapping("firstApprovalUser")
    public ApprovalInfo firstApprovalUser(Long collaborationType, Long targetDeptId, Long startDept) {
        CurrentUser currentUser = Objects.isNull(startDept)
                ? AuthHelper.getCurrentUser()
                : permissionService.findCurrentUser(AuthHelper.getCurrentUser().getId(), startDept);
        return collaborationApprovalService.firstApprovalUser(currentUser, collaborationType, targetDeptId);
    }

    /**
     * 获取查询类别
     *
     * @param pCode pid
     * @param type type
     * @param collaborationTypeId 协作类别id
     * @return 查询类别
     */
    @GetMapping("getQueryCategory")
    public List<QueryCategoryVO> getQueryCategory(Long pCode, String type, Long collaborationTypeId) {
        return collaborationService.getQueryCategory(pCode, type, collaborationTypeId);
    }

    /**
     * 根据协作类别获取到表单类别
     *
     * @param collaborationTypeId 协作id
     * @return 表单序号
     */
    @GetMapping("formOrder")
    public Integer formOrder(Long collaborationTypeId) {
        return collaborationService.formOrder(collaborationTypeId);
    }

    /**
     * 获取协作默认部门
     *
     * @param collaborationTypeId 协作类别id
     * @return {@link DefaultCollaborationDeptVO}
     */
    @GetMapping("getDefaultCollaborationDept")
    public DefaultCollaborationDeptVO getDefaultCollaborationDept(Long collaborationTypeId) {
        return collaborationService.getDefaultCollaborationDept(collaborationTypeId);
    }

    /**
     * 获取默认反馈时限
     *
     * @return {@link String}
     */
    @GetMapping("getDefaultFeedbackTime")
    public String getDefaultFeedbackTime() {
        return collaborationService.getDefaultFeedbackTime();
    }

    /**
     * 获取协作单位
     *
     * @param collaborationTypeId 协作类别id
     * @return {@link List}<{@link DeptVO}>
     */
    @GetMapping("getCollaborationDept")
    public List<DeptVO> getCollaborationDept(Long collaborationTypeId) {
        return collaborationService.getCollaborationDept(collaborationTypeId);
    }

    /**
     * 增加协理人
     *
     * @param collaborationId 协作id
     * @param assistantsVO  协理人
     */
    @PostMapping("/{id}/add-assistants")
    public void addAssistants(@PathVariable("id") Long collaborationId, @RequestBody List<UserDeptVO> assistantsVO) {
        collaborationService.addAssistants(collaborationId, assistantsVO);
    }

    /**
     * 反馈刷新
     *
     * @param collaborationId 协作id
     * @param dto dto
     */
    @PostMapping("/{id}/feedbackRefresh")
    public void feedbackRefresh(@PathVariable("id") Long collaborationId, @RequestBody FeedbackRefreshDTO dto) {
        collaborationService.feedbackRefresh(collaborationId, dto);
    }

    /**
     * 根据警情编号查询警情信息
     *
     * @param jqbh 警情编号
     * @return 警情信息
     */
    @GetMapping("jq")
    public CollaborationJqVO jq(String jqbh) {
        return jqService.jq(jqbh);
    }

    /**
     * 获取模板id
     *
     * @return 模板id
     */
    @GetMapping("/defaultTemplate")
    public Integer getDefaultTemplate() {
        Integer id = BeanFactoryHolder.getEnv().getProperty("fight.collaboration.default.template", Integer.class);
        return id;
    }

    /**
     * 导出审批表
     *
     * @param collaborationId 协作id
     */
    @GetMapping("/exportSpb")
    public void exportSpb(Long collaborationId) {
        collaborationDownloadService.exportSpb(collaborationId);
    }

    /**
     * 导出紧急警情excel
     *
     * @param collaborationId 协作id
     */
    @GetMapping("/exportJijq")
    public void exportJijq(Long collaborationId) {
        collaborationDownloadService.exportJijq(collaborationId);
    }

    /**
     * 下载协作的指令单
     *
     * @param collaborationId 协作ID
     */
    @GetMapping("/downloadZld")
    public void downloadZld(Long collaborationId) {
        collaborationDownloadService.downloadZld(collaborationId);
    }

    /**
     * 是否 需要展示 下载word 按钮
     *
     * @param collaborationId 协作id
     * @return 是否需要
     */
    @GetMapping("/needWordDownload")
    public RestfulResultsV2<Boolean> needWordDownload(@RequestParam("collaborationId") Long collaborationId){
        return collaborationDownloadService.needWordDownload(collaborationId);
    }
}
