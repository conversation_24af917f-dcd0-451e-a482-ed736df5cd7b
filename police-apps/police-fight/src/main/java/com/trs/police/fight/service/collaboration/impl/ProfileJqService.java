package com.trs.police.fight.service.collaboration.impl;

import com.alibaba.fastjson.JSON;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.utils.TimeUtil;
import com.trs.police.common.core.vo.profile.JqCommonVO;
import com.trs.police.common.openfeign.starter.service.ProfileService;
import com.trs.police.fight.domain.bean.JqBjr;
import com.trs.police.fight.domain.vo.Collaboration.CollaborationJqVO;
import com.trs.police.fight.service.collaboration.JqService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 档案得警情服务
 *
 * <AUTHOR>
 */
@Component
@ConditionalOnProperty(name = "ys.fight.collaboration.jq.version", havingValue = "df", matchIfMissing = true)
public class ProfileJqService implements JqService {

    @Resource
    private ProfileService profileService;

    @Override
    public CollaborationJqVO jq(String jqbh) {
        JqCommonVO byBh = syncAndGetJq(jqbh);
        if (Objects.isNull(byBh)) {
            throw new TRSException(String.format("根据编号：%s未能查询到相关数据", jqbh));
        }
        CollaborationJqVO vo = new CollaborationJqVO();
        vo.setJqlbdm(byBh.getJqlbmc());
        vo.setJqbh(byBh.getJjdbh());
        vo.setJqSj(TimeUtil.localDateTimeToString(byBh.getAlarmTime(), "yyyy-MM-dd HH:mm:ss"));
        JqBjr bjr = new JqBjr();
        bjr.setBjrName(byBh.getBjrmc());
        bjr.setBjrTel(byBh.getBjdh());
        if (Objects.isNull(byBh.getBjrxb()) || "0".equals(byBh.getBjrxb())) {
            bjr.setBjrSex("未知");
        }
        if (Objects.isNull(byBh.getBjrxb()) || "1".equals(byBh.getBjrxb())) {
            bjr.setBjrSex("男");
        }
        if (Objects.isNull(byBh.getBjrxb()) || "2".equals(byBh.getBjrxb())) {
            bjr.setBjrSex("女");
        }
        bjr.setBjrIdCard(byBh.getBjrsfz());
        vo.setBjrInfo(bjr);
        vo.setJqJyQk(byBh.getContent());
        vo.setXqCzQk(null);
        vo.setLxMjXm(byBh.getLxmjxm());
        vo.setLxMjTel(byBh.getTel());
        vo.setJqRequestResult(JSON.toJSONString(byBh));
        return vo;
    }

    private JqCommonVO syncAndGetJq(String jqbh) {
        JqCommonVO byBh = profileService.findByBh(jqbh);
        if (Objects.isNull(byBh)) {
            // 同步一次jq数据
            profileService.importJq(jqbh, "idCard");
            byBh = profileService.findByBh(jqbh);
        }
        return byBh;
    }
}
