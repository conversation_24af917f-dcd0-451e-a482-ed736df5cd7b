package com.trs.police.fight.service;

import com.trs.police.fight.domain.dto.FightResultDto;
import com.trs.police.fight.domain.entity.CluePoolAnalysisResult;
import com.trs.police.fight.task.analysis.vo.FightResultCombination;

import java.util.List;

/**
 * @author: dingkeyu
 * @date: 2024/03/21
 * @description: 战果填报服务层
 */
public interface FightResultFillingService {

    /**
     * 战果填报
     *
     * @param fightResultDto fightResultDto
     */
    void fightResultFilling(FightResultDto fightResultDto);

    /**
     * 删除旧的填报数据
     *
     * @param fightResultDto fightResultDto
     */
    void fightResultDelete(FightResultDto fightResultDto);

    /**
     * 查询战果数据
     *
     * @param fightResultDto fightResultDto
     */
    void queryFightResult(FightResultDto fightResultDto);

    /**
     * 查询战果数据
     *
     * @param fightResultIds 战果id
     * @param fightResultCombination 战果组合结果
     */
    void queryFightResult(List<Long> fightResultIds, FightResultCombination fightResultCombination);

    /**
     * 过滤战果
     *
     * @param fightResultIds 战果id
     * @param fightResultCombination 战果组合结果
     */
    void filterFightResult(List<Long> fightResultIds, FightResultCombination fightResultCombination);

    /**
     * 战果统计分析
     *
     * @param fightResultCombination 战果组合结果
     * @param cluePoolAnalysisResult 线索池统计分析结果
     */
    void fightResultClueAnalysis(FightResultCombination fightResultCombination, CluePoolAnalysisResult cluePoolAnalysisResult);

    /**
     * 操作类型
     *
     * @return {@link Integer}
     */
    Integer operateType();
}
