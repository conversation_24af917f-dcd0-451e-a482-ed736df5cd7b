package com.trs.police.fight.statistic.service.impl;

import com.trs.police.fight.statistic.DTO.FightResultDTO;
import com.trs.police.fight.statistic.DTO.StatisticContext;
import com.trs.police.fight.statistic.constant.SceneType;
import com.trs.police.fight.statistic.domain.vo.FightResultVO;
import com.trs.police.fight.statistic.mapper.FightStatisticMapper;
import com.trs.police.fight.statistic.service.CountStatisticService;
import com.trs.police.fight.statistic.service.FightStatisticAreaUtils;
import com.trs.police.statistic.domain.VO.CountStatisticVO;
import com.trs.police.statistic.domain.bean.CountItem;
import com.trs.police.statistic.domain.bean.NoOtherValue;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;

import static com.trs.police.fight.statistic.constant.SceneType.XT_FIGHT_RESULT;

/**
 * 作战工作质效
 *
 * <AUTHOR>
 */
@Component
public class FightResultServiceImpl implements CountStatisticService<FightResultDTO, NoOtherValue> {

    @Resource
    private FightStatisticMapper fightStatisticMapper;

    @Override
    public CountStatisticVO<NoOtherValue> doStatistic(StatisticContext<FightResultDTO> context) {
        String areaCode = FightStatisticAreaUtils.getRelaCode(null, context.getCommonParams().getAreaCode());
        // 统计案件信息
        final FightResultVO caseInfo = fightStatisticMapper.fightCaseResult(context.getCommonParams(), context.getParams(), areaCode);
        // 统计嫌疑人信息
        final FightResultVO suspectResult = fightStatisticMapper.fightCaseSuspectResult(context.getCommonParams(), context.getParams(), areaCode);
        // 统计其它战果
        final FightResultVO other = fightStatisticMapper.fightCaseOtherResult(context.getCommonParams(), context.getParams(), areaCode);
        // 构造vo
        CountItem caseTotal = new CountItem();
        caseTotal.setKey("cazl");
        caseTotal.setCount(caseInfo.getCaseTotal());

        CountItem xzCount = new CountItem();
        xzCount.setKey("xzaj");
        xzCount.setCount(caseInfo.getCaseTotal() - caseInfo.getXsCount());

        CountItem suspectCount = new CountItem();
        suspectCount.setKey("zbxyr");
        suspectCount.setCount(suspectResult.getSuspectCount());

        CountItem dbCount = new CountItem();
        dbCount.setKey("db");
        dbCount.setCount(other.getDbCount());

        CountItem hdCount = new CountItem();
        hdCount.setKey("hd");
        hdCount.setCount(other.getHdCount());

        CountItem bpjqzyCount = new CountItem();
        bpjqzyCount.setKey("bpjqzy");
        bpjqzyCount.setCount(other.getBpjqzyCount());

        CountItem ldpsCount = new CountItem();
        ldpsCount.setKey("ldzs");
        ldpsCount.setCount(other.getLdpsCount());

        CountStatisticVO<NoOtherValue> vo = new CountStatisticVO<>(
                Arrays.asList(caseTotal, xzCount, suspectCount, dbCount, hdCount, bpjqzyCount, ldpsCount),
                new NoOtherValue()
        );
        return vo;
    }

    @Override
    public SceneType sceneType() {
        return XT_FIGHT_RESULT;
    }

}
