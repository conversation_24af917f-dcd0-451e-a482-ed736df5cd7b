package com.trs.police.fight.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import com.trs.police.common.core.utils.AuthHelper;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 合成作战-协作关联表
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "t_fight_composite_collaboration_relation",autoResultMap = true)
@NoArgsConstructor
public class CompositeCollaborationRelation extends AbstractBaseEntity {

    /**
     * 协作id
     */
    @TableField("collaboration_id")
    private Long collaborationId;

    /**
     * 作战id
     */
    @TableField("composite_id")
    private Long compositeId;

    public CompositeCollaborationRelation(Long collaborationId, Long compositeId) {
        this.collaborationId = collaborationId;
        this.compositeId = compositeId;
        super.fillAuditFields(AuthHelper.getCurrentUser());
    }
}
