package com.trs.police.fight.builder;

import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.core.vo.Dict2VO;
import com.trs.police.common.openfeign.starter.service.DictService;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @author: dingkeyu
 * @date: 2024/10/15
 * @description:
 */
public class CollaborationListBuilder {

    /**
     * 根据筛选条件计算协作类别id （该方法用于mapper查询）
     *
     * @param value value
     * @return {@link List}<{@link Long}>
     */
    public static List<Long> getCollaborationTypeIds(Object value) {
        Set<Long> set = new HashSet<>();
        List<Long> valueList = JsonUtil.castList(value, Long.class);
        DictService dictService = BeanUtil.getBean(DictService.class);
        List<Dict2VO> dict2Vos = dictService.commonSearch("collaboration_category", null, null, null);
        for (Long typeId : valueList) {
           dict2Vos.stream()
                   .filter(vo -> typeId.equals(vo.getId().longValue()) || typeId.equals(vo.getPId()))
                   .forEach(vo -> set.add(vo.getId().longValue()));

        }
        return new ArrayList<>(set);
    }
}
