package com.trs.police.fight.constant.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Objects;

/**
 * 预案调度措施
 *
 * <AUTHOR>
 * @date 2024/10/10 10:19
 */
public enum PlanOperationEnum {


    START_OPERATION(1, "启动预案"),
    FEEDBACK_OPERATION(2, "调度反馈");

    PlanOperationEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @Getter
    @EnumValue
    @JsonValue
    private final int code;

    @Getter
    private final String name;

    /**
     * code转换枚举
     *
     * @param code 编码
     * @return 枚举
     */
    @JsonCreator
    public static PlanOperationEnum codeOf(Integer code) {
        if (Objects.nonNull(code)) {
            for (PlanOperationEnum typeEnum : PlanOperationEnum.values()) {
                if (code.equals(typeEnum.getCode())) {
                    return typeEnum;
                }
            }
        }
        return null;
    }
}
