package com.trs.police.fight.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.fight.domain.entity.CollaborationUserRelation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * @author: dingkeyu
 * @date: 2024/03/08
 * @description: 协作人员关联
 */
@Mapper
public interface CollaborationUserRelationMapper extends BaseMapper<CollaborationUserRelation> {

    /**
     * 设置协作已读未读状态
     *
     * @param collaborationId 协作id
     * @param isRead isRead
     * @param userId userId
     */
    @Update("update t_collaboration_user_relation set is_read = #{isRead} where collaboration_id = #{collaborationId}" +
            " and related_user_id = #{userId}")
    void setCollaborationIsRead(@Param("collaborationId") Long collaborationId, @Param("isRead") Boolean isRead,
                                @Param("userId") Long userId);
}
