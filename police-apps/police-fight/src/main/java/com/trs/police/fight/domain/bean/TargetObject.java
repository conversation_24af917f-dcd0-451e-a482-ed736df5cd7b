package com.trs.police.fight.domain.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 查询对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class TargetObject implements Serializable {

    /**
     * id
     */
    private String id;

    /**
     * 名字
     */
    @ExcelProperty("姓名")
    private String name;

    /**
     * 证件号码
     */
    @ExcelProperty("证件类型及号码")
    private String identity;

    /**
     * 标识信息
     */
    @ExcelProperty("标识码类型及标识码")
    private String signatureMessage;

    /**
     * 是否特俗
     */
    @ExcelProperty("特殊身份")
    private String special;
}
