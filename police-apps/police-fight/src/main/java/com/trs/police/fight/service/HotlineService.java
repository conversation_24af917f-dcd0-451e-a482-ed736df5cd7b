package com.trs.police.fight.service;


import com.trs.police.common.core.vo.permission.DeptVO;
import com.trs.police.fight.domain.dto.HotlineDto;
import com.trs.police.fight.domain.dto.HotlineFeedbackDTO;
import com.trs.police.fight.domain.dto.HotlineInitDTO;
import com.trs.police.fight.domain.params.collaboration.FeedbackParams;
import com.trs.police.fight.domain.vo.HotlineVO;
import com.trs.web.builder.base.RestfulResultsV2;

import java.util.List;


/**
 * 公共部分服务
 *
 * <AUTHOR>
 * @since 2024/8/13
 **/
public interface HotlineService {

    /**
     * 添加热线流转
     *
     * @param dto 添加参数
     */
    void add(HotlineInitDTO dto);

    /**
     * 获取热线列表
     *
     * @param dto 参数
     * @return 结果返回
     */
    RestfulResultsV2<HotlineVO> queryListHotline(HotlineDto dto);

    /**
     * 操作热线，删除或完结
     *
     * @param dto 参数
     */
    void statusAction(HotlineDto dto);

    /**
     * 热线反馈or回复
     *
     * @param dto 参数
     */
    void hotLineReply(HotlineFeedbackDTO dto);

    /**
     * 获取热线详情
     *
     * @param id 热线id
     * @return 热线详情
     */
    RestfulResultsV2<HotlineVO> selectDetailById(Integer id);

    /**
     * 接受协作的反馈
     *
     * @param feedbackParams 反馈参数
     */
    void acceptCollaborationFeedback(FeedbackParams feedbackParams);

    /**
     * 获取处置部门
     *
     * @param deptId 申请人部门id
     * @return 处置部门集合
     */
    List<DeptVO> getDealPerson(Long deptId);
}
