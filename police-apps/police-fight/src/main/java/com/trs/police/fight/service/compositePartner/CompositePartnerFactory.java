package com.trs.police.fight.service.compositePartner;

import com.trs.police.fight.constant.enums.CompositePartnerWay;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: dingkeyu
 * @date: 2024/12/26
 * @description:
 */
@Component
public class CompositePartnerFactory {

    @Autowired
    private List<ICompositePartnerService> compositePartnerServiceList;

    /**
     * 根据way获取捞取参与人员的方式
     *
     * @param way way
     * @return {@link ICompositePartnerService}
     */
    public ICompositePartnerService getCompositePartnerService(CompositePartnerWay way) {
        return compositePartnerServiceList.stream()
                .filter(service -> service.way().equals(way))
                .findFirst().orElse(null);
    }

    /**
     * 获取所有捞取参与人员的方式
     *
     * @return {@link List}<{@link ICompositePartnerService}>
     */
    public List<ICompositePartnerService> getAllCompositePartnerService() {
        return compositePartnerServiceList;
    }
}
