package com.trs.police.fight.statistic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.common.core.entity.Dept;
import com.trs.police.common.core.entity.District;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
/**
 * deptMapper
 */
@Mapper
public interface StDeptMapper extends BaseMapper<Dept> {

    /**
     * 获取区域
     *
     * @param currentAreaCode code
     * @return jieguo
     */
    @Select("select * from t_district where code = #{currentAreaCode}")
    District selectDistrictByCode(String currentAreaCode);


}
