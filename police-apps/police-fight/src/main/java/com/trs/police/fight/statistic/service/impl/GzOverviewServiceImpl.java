package com.trs.police.fight.statistic.service.impl;

import com.trs.police.fight.statistic.DTO.NoParamsDTO;
import com.trs.police.fight.statistic.DTO.StatisticContext;
import com.trs.police.fight.statistic.constant.SceneType;
import com.trs.police.statistic.domain.bean.CountItem;
import com.trs.police.statistic.domain.bean.NoOtherValue;
import com.trs.police.statistic.domain.VO.CountStatisticVO;
import com.trs.police.fight.statistic.domain.vo.GzOverviewVO;
import com.trs.police.fight.statistic.mapper.GzStatisticMapper;
import com.trs.police.fight.statistic.service.CountStatisticService;
import com.trs.police.fight.statistic.service.FightStatisticAreaUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;

import static com.trs.police.fight.statistic.constant.SceneType.GZ_OVERVIEW;

/**
 * 挂账总览统计
 *
 * <AUTHOR>
 */
@Service
public class GzOverviewServiceImpl implements CountStatisticService<NoParamsDTO, NoOtherValue> {

    @Autowired
    private GzStatisticMapper gzStatisticMapper;

    @Override
    public CountStatisticVO<NoOtherValue> doStatistic(StatisticContext<NoParamsDTO> context) {
        GzOverviewVO overview = gzStatisticMapper.overview(
                context.getCommonParams(),
                FightStatisticAreaUtils.getRelaCode(null, context.getCommonParams().getAreaCode())
        );
        CountItem faqk = new CountItem();
        faqk.setKey("fqqk");
        faqk.setChildren(Arrays.asList(
                new CountItem("fqxss", overview.getTotalCount()),
                new CountItem("ldps", overview.getLdpsCount()),
                new CountItem("mgasj", overview.getMgasjCount()),
                new CountItem("sjjb", overview.getSjjbCount())
        ));
        CountItem statueStatistic = new CountItem();
        statueStatistic.setKey("lzqk");
        statueStatistic.setChildren(Arrays.asList(
                new CountItem("shz", overview.getShzCount()),
                new CountItem("ybh", overview.getYbhCount()),
                new CountItem("zbz", overview.getZbzCount()),
                new CountItem("gwhjz", overview.getGwhjzCount()),
                new CountItem("ybj", overview.getYbjXount())
        ));

        return new CountStatisticVO<>(Arrays.asList(faqk, statueStatistic), new NoOtherValue());
    }

    @Override
    public SceneType sceneType() {
        return GZ_OVERVIEW;
    }
}
