package com.trs.police.fight.constant.enums;

import java.util.Objects;
import lombok.Getter;

/**
 * 业务模块状态枚举
 *
 * <AUTHOR>
 * @since 2022/05/16
 */

public enum ServiceStatusEnum {

    /**
     * 已删除
     */
    IN_USE(0, "使用中"),
    DELETED(1, "已移除"),
    REJECTED(2, "已驳回"),
    PASSED(3, "已通过"),
    WITHDRAWN(4, "已撤回"),
    IN_APPROVE(5, "审批中");

    @Getter
    private final Integer code;

    @Getter
    private final String name;

    ServiceStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据枚举值的code解析枚举值
     *
     * @param code code
     * @return {@link ServiceStatusEnum}
     */
    public static ServiceStatusEnum codeOf(Integer code) {
        if (Objects.nonNull(code)) {
            for (ServiceStatusEnum statusEnum : ServiceStatusEnum.values()) {
                if (code.equals(statusEnum.code)) {
                    return statusEnum;
                }
            }
        }
        return null;
    }

    /**
     * 已删除由布尔值转换为中文
     *
     * @param deleted 已删除
     * @return 枚举
     */
    public static Integer getDeleted(Integer deleted) {
        if (Objects.isNull(deleted)) {
            return DELETED.getCode();
        }
        return IN_USE.getCode();
    }
}
