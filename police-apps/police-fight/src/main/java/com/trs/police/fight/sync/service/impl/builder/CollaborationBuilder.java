package com.trs.police.fight.sync.service.impl.builder;

import com.alibaba.fastjson.JSON;
import com.trs.police.common.core.constant.enums.YsModule;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.entity.Collaboration;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.fight.domain.entity.CollaborationUserRelation;
import com.trs.police.fight.service.CollaborationUserRelationMpService;
import com.trs.police.fight.sync.bean.Message;
import com.trs.police.fight.sync.bean.SyncCollaboration;
import com.trs.police.fight.sync.bean.SyncDeptInfo;
import com.trs.police.fight.sync.bean.SyncUserInfo;
import com.trs.police.fight.sync.constant.DataSource;
import com.trs.police.fight.sync.service.MsgBuilder;
import com.trs.police.fight.sync.service.YsPermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.trs.police.fight.sync.constant.FightMessageType.COMPOSITE_ENTITY;

/**
 * 协作的构造器
 *
 * <AUTHOR>
 */
@Component
public class CollaborationBuilder implements MsgBuilder<Collaboration> {

    @Autowired
    private YsPermissionService ysPermissionService;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private CollaborationUserRelationMpService userRelationMpService;

    @Override
    public Message buildMsg(Collaboration collaboration) {
        DataSource dataSource = ysPermissionService.getLocalDataSource();
        Message message = new Message(
                dataSource.getSystemFlag(),
                YsModule.FIGHT.getType(),
                COMPOSITE_ENTITY.getType()
        ).addMainEntitySource(collaboration.getDataSource(), collaboration.getSourcePrimaryKey());

        List<Long> did = userRelationMpService.lambdaQuery()
                .eq(CollaborationUserRelation::getCollaborationId, collaboration.getId())
                .list()
                .stream()
                .map(CollaborationUserRelation::getRelatedUserDeptId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        List<DeptDto> dept = CollectionUtils.isEmpty(did) ? new ArrayList<>() : permissionService.getDeptByIds(did);
        List<String> area = dept.stream()
                .map(DeptDto::getDistrictCode)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        SyncCollaboration syncCollaboration = new SyncCollaboration();
        syncCollaboration.setCollaboration(collaboration);
        syncCollaboration.setAreaCode(area);

        message.setMessageBody(JSON.toJSONString(Arrays.asList(syncCollaboration)));


        List<SyncUserInfo> userInfo = ysPermissionService.getUserInfo(collaboration.getCreateUserId(), collaboration.getUpdateUserId(), collaboration.getCollaborationUserId());
        List<SyncDeptInfo> deptInfo = ysPermissionService.getDeptInfo(collaboration.getCreateDeptId(), collaboration.getUpdateDeptId(), collaboration.getCollaborationDeptId());
        message.setUserInfoList(JSON.toJSONString(userInfo));
        message.setDeptInfoList(JSON.toJSONString(deptInfo));
        return message;
    }
}
