package com.trs.police.fight.service.collaboration;

import com.alibaba.cola.statemachine.Action;
import com.trs.police.fight.constant.enums.CollaborationEvent;
import com.trs.police.common.core.constant.enums.CollaborationStatusEnum;
import com.trs.police.common.core.entity.Collaboration;
import com.trs.police.fight.domain.params.collaboration.CollaborationProcessContext;

/**
 * 协作的事件相应
 *
 * <AUTHOR>
 * @param <PARAM> 上下文参数
 */
public interface CollaborationEventAction<PARAM>
        extends Action<CollaborationStatusEnum, CollaborationEvent, CollaborationProcessContext> {

    /**
     * 事件相应逻辑
     *
     * @param from 原始状态
     * @param to 目标状态
     * @param event 事件
     * @param collaboration 协作
     * @param context 上下文
     */
    void action(CollaborationStatusEnum from, CollaborationStatusEnum to, CollaborationEvent event, Collaboration collaboration, PARAM context);

    /**
     * 执行事件相应
     *
     * @param from 原始状态
     * @param to 目标状态
     * @param event 事件
     * @param context 参数
     */
    @Override
    default void execute(CollaborationStatusEnum from, CollaborationStatusEnum to, CollaborationEvent event, CollaborationProcessContext context) {
        action(from, to, event, context.getCollaboration(), (PARAM) context.getParam());
    }
}
