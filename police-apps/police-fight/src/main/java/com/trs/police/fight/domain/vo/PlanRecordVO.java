package com.trs.police.fight.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.trs.police.fight.constant.enums.PlanOperationEnum;
import com.trs.police.fight.domain.entity.PlanRecordEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.format.DateTimeFormatter;

/**
 * 预案记录VO
 *
 * <AUTHOR>
 * @date 2024/10/09
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PlanRecordVO {

    /**
     * 预案内容
     */
    private String content;

    /**
     * 预案名称
     */
    private String planName;

    /**
     * 附件信息
     */
    private String attachments;

    /**
     * 启动时间 yyyy-MM-dd HH:mm:ss
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String startTime;

    /**
     * 启动时间 yyyy-MM-dd
     */
    private String time;

    /**
     * 处突title
     */
    private String ctTitle;

    /**
     * 处突id
     */
    private Integer ctId;

    /**
     * 人员名称
     */
    private String personName;

    /**
     * 人员名称
     */
    private String jqInfo;

    /**
     * 人员名称
     */
    private String warningInfo;

    /**
     * 创建人员id
     */
    private Long createUserId;

    /**
     * 创建部门id
     */
    private Long createDeptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 操作
     */
    private Integer operation;

    /**
     * 操作名称
     */
    private String recordType;


    /**
     * 转换vo
     *
     * @param record 记录
     * @return vo
     */
    public static PlanRecordVO entityToVO(PlanRecordEntity record){
        PlanRecordVO vo = new PlanRecordVO();
        vo.setStartTime(DateTimeFormatter.ofPattern("yyyy.MM.dd HH:mm:ss").format(record.getCreateTime()));
        vo.setTime(DateTimeFormatter.ofPattern("yyyy.MM.dd").format(record.getCreateTime()));
        vo.setContent(record.getContent());
        vo.setAttachments(record.getAttachments());
        vo.setRecordType(PlanOperationEnum.codeOf(record.getOperation()).getName());
        vo.setOperation(record.getOperation());
        return vo;
    }
}
