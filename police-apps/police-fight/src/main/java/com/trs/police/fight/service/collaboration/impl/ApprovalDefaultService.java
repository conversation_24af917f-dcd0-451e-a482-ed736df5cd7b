package com.trs.police.fight.service.collaboration.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.trs.police.common.core.constant.enums.DeptTypeEnum;
import com.trs.police.common.core.constant.log.OperateModule;
import com.trs.police.common.core.constant.log.Operation;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.entity.Collaboration;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.utils.AreaUtils;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.vo.permission.SimpleUserVO;
import com.trs.police.common.core.vo.permission.UserDeptVO;
import com.trs.police.common.openfeign.starter.service.ApprovalService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.common.openfeign.starter.vo.ApprovalActionVO;
import com.trs.police.common.openfeign.starter.vo.ApprovalRequest;
import com.trs.police.fight.domain.vo.Collaboration.ApprovalInfo;
import com.trs.police.fight.service.collaboration.CollaborationApprovalService;
import com.trs.police.fight.service.collaboration.action.ApprovalAction;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.trs.police.common.core.constant.enums.DeptTypeEnum.codeOf;

/**
 * v1版审批配置选择（除省厅外 都是用的这个版本）
 *
 * <AUTHOR>
 */
@Component
@ConditionalOnProperty(name = "ys.fight.collaboration.approval.version", havingValue = "v1", matchIfMissing = true)
public class ApprovalDefaultService implements CollaborationApprovalService {

    @Autowired
    private ApprovalService approvalService;

    @Resource
    private PermissionService permissionService;

    @Autowired
    private ApprovalAction approvalAction;

    @Override
    public String configNameByType(CurrentUser currentUser, Collaboration collaboration) {
        Long type = collaboration.getCollaborationType();
        return findConfigByType(currentUser, type);
    }

    @Override
    public ApprovalInfo firstApprovalUser(CurrentUser currentUser, Long collaborationType, Long targetDeptId) {
        if (Boolean.FALSE.equals(approvalAction.needApproval(collaborationType))) {
            return new ApprovalInfo(Boolean.FALSE, new ArrayList<>());
        }
        Boolean enable = BeanFactoryHolder.getEnv().getProperty(String.format("approval.select.%s", Operation.COLLABORATION_APPROVAL.getCode()), Boolean.class, Boolean.FALSE);
        if (Boolean.FALSE.equals(enable)) {
            return new ApprovalInfo(enable, new ArrayList<>());
        }
        ApprovalActionVO actionVO = new ApprovalActionVO();
        actionVO.setService(OperateModule.COMPOSITE);
        actionVO.setAction(Operation.COLLABORATION_APPROVAL);
        ApprovalRequest request = new ApprovalRequest();
        request.setApprovalActionVO(actionVO);
        String configNameByType = findConfigByType(currentUser, collaborationType);
        request.setApprovalConfigName(configNameByType);
        request.setUser(UserDeptVO.of(AuthHelper.getCurrentUser()));
        // 目标部门随便填写一个 因为通常目标部门不是第一个审批节点 (是的话就需要前端调用接口把目标部门也传进来)
        UserDeptVO userDeptVO = new UserDeptVO();
        userDeptVO.setDeptId(targetDeptId);
        request.setTargetUser(userDeptVO);
        String s = approvalService.fistApproverV2(request);
        JSONObject jsonObject = JSON.parseObject(s);
        if (jsonObject.getInteger("code").equals(500)) {
            throw new TRSException(jsonObject.getString("message"));
        }
        List<UserDeptVO> userDeptVOList = jsonObject.getJSONArray("data")
                .stream()
                .map(obj -> JSON.parseObject(obj.toString(), UserDeptVO.class))
                .collect(Collectors.toList());
        List<SimpleUserVO> result = userDeptVOList.stream()
                .map(vo -> permissionService.findSimpleUser(vo.getUserId(), vo.getDeptId()))
                .collect(Collectors.toList());
        return new ApprovalInfo(enable, result);
    }

    private String findConfigByType(CurrentUser currentUser, Long type) {
        // 部门级别
        AreaUtils.Level level = AreaUtils.level(currentUser.getDept().getDistrictCode());
        // 部门基本类型
        Optional<DeptDto> collaborateDeptType = DeptTypeEnum.findKeyTypeDept(currentUser.getDept(), permissionService::getDeptById);
        Long deptType = collaborateDeptType.isPresent()
                ? codeOf(Integer.valueOf(collaborateDeptType.get().getType().intValue())).getCode().longValue()
                : null;
        // 业务部门类型
        Long childType = collaborateDeptType.isPresent() ? collaborateDeptType.get().getChildType() : null;
        StringBuilder keyBuilder = new StringBuilder()
                // 级别编码 （省 市 区）
                .append(level.getCode())
                // 是否是特殊的部门（实战服务中心）
                .append("_").append(isSpecialDeptType(deptType) ? deptType : "")
                // 是否是特殊的协作类别
                .append("_").append(isSpecialCollaborationType(type) ? type : "");
        // 如果是特俗业务部门，具体的业务类型
        if (isSpecialChildDeptType(childType)) {
            keyBuilder.append("_").append(childType);
        }
        String key = keyBuilder.toString();
        String approvalConfig = BeanFactoryHolder.getEnv().getProperty("ys.fight.collaboration.approval.config");
        Map<String, String> configMapping = JSON.parseObject(approvalConfig, HashMap.class);
        if (configMapping.containsKey(key)) {
            return configMapping.get(key);
        }
        throw new TRSException(String.format("类型：%s未配置审批节点", key));
    }

    private Boolean isSpecialDeptType(Long deptType) {
        if (Objects.isNull(deptType)) {
            return false;
        }
        String property = BeanFactoryHolder.getEnv().getProperty("ys.fight.collaboration.approval.special.dept.type");
        return Stream.of(property.split(",|;"))
                .map(Long::valueOf)
                .anyMatch(dp -> Objects.equals(dp, deptType));
    }

    private Boolean isSpecialChildDeptType(Long deptType) {
        if (Objects.isNull(deptType)) {
            return false;
        }
        String property = BeanFactoryHolder.getEnv().getProperty("ys.fight.collaboration.approval.special.childDept.type");
        return Stream.of(property.split(",|;"))
                .map(Long::valueOf)
                .anyMatch(dp -> Objects.equals(dp, deptType));
    }

    private Boolean isSpecialCollaborationType(Long type) {
        String property = BeanFactoryHolder.getEnv().getProperty("ys.fight.collaboration.approval.special.collaboration.type");
        return Stream.of(property.split(",|;"))
                .map(Long::valueOf)
                .anyMatch(dp -> Objects.equals(dp, type));
    }
}
