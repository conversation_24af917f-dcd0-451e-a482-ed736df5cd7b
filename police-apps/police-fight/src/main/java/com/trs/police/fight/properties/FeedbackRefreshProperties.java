package com.trs.police.fight.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @ClassName CluePoolProperties
 * @Description 反馈刷新相关配置项
 * <AUTHOR>
 * @Date 2024/3/21 11:51
 **/
@Data
@Component
@ConfigurationProperties(prefix = "com.trs.collaboration.feedback.refresh")
public class FeedbackRefreshProperties {

    private String key;

    private String accessToken;

    private String preUrl;

    private String inputDataPath;

    private String noticeRunModelPath;

    private String pdfFiles;

    // systemInfoJson
    private String systemCode = "510300ZHLD";

    private String systemName = "智慧龙都";

    private String ip = "***********";

    private String port = "443";

    // userInfoJson
    private String userIp;

    // caseInfoJson
    private String caseId;

    private String caseName;

    private String caseType;

    private String typeName;

    private String lastSpr;

    // xmlInfoJson
    private String jobId = "ZG_JQ_FN_ZDH_LICHENG_JOB";

    private String police = "QZ";






}
