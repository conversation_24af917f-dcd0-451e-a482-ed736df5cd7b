package com.trs.police.fight.service.impl.CluePoolAnalysis;

import com.alibaba.fastjson.JSONObject;
import com.trs.police.fight.service.ClueAnalysisTaskService;
import com.trs.police.fight.task.analysis.impl.CombatUnitClueAnalysis;
import com.trs.police.fight.task.analysis.impl.MainInvestigationClueAnalysis;
import com.trs.police.fight.task.analysis.impl.OtherPoliceCategoryAnalysis;
import com.trs.police.fight.task.analysis.impl.PersonalClueAnalysis;
import com.trs.police.fight.task.context.ClueAnalysisContext;
import com.trs.web.builder.base.RestfulResultsV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: dingkeyu
 * @date: 2024/06/11
 * @description:
 */
@Slf4j
@Service
public class ClueAnalysisTaskServiceImpl implements ClueAnalysisTaskService {
    @Autowired
    private PersonalClueAnalysis personalClueAnalysis;

    @Autowired
    private OtherPoliceCategoryAnalysis otherPoliceCategoryAnalysis;

    @Autowired
    private MainInvestigationClueAnalysis mainInvestigationClueAnalysis;

    @Autowired
    private CombatUnitClueAnalysis combatUnitClueAnalysis;

    /**
     * doClueAnalysisTask
     *
     * @param days days
     * @param moduleType moduleType
     * @return {@link RestfulResultsV2}<{@link String}>
     */
    @Override
    public RestfulResultsV2<String> doClueAnalysisTask(Integer days, String moduleType) {
        try {
            ClueAnalysisContext context = new ClueAnalysisContext();
            JSONObject obj = new JSONObject();
            obj.put("days", days);
            context.setDetailInfo(obj);
            switch (moduleType) {
                case "1":
                    personalClueAnalysis.analyzeScene(context);
                    break;
                case "2":
                    otherPoliceCategoryAnalysis.analyzeScene(context);
                    break;
                case "3":
                    mainInvestigationClueAnalysis.analyzeScene(context);
                    break;
                case "4":
                    combatUnitClueAnalysis.analyzeScene(context);
                    break;
                default:
                    log.error("未知的moduleType！");
            }
            return RestfulResultsV2.ok("success");
        } catch (Exception e) {
            log.error("线索池统计异常：", e);
            return RestfulResultsV2.error("failed");
        }
    }
}
