package com.trs.police.fight.task;

import com.trs.police.fight.task.analysis.impl.MainInvestigationClueAnalysis;
import com.trs.police.fight.task.context.ClueAnalysisContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * @author: dingkeyu
 * @date: 2024/03/26
 * @description: 主侦警种线索统计定时任务
 */
@Slf4j
@Component
public class MainInvestigationClueTask {

    @Autowired
    private MainInvestigationClueAnalysis mainInvestigationClueAnalysis;

    /**
     *  主侦警种线索统计
     */
    @Scheduled(cron = "${com.trs.schedule.mainInvestigationClue.task.cron:0 0/30 * * * ?}")
    public void run() {
        try {
            mainInvestigationClueAnalysis.analyzeScene(new ClueAnalysisContext());
        } catch (Exception e) {
            log.error("主侦警种线索统计失败", e);
        }
    }
}
