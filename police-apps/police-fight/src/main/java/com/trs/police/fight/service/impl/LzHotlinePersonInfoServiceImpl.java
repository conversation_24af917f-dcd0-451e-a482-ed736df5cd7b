package com.trs.police.fight.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.entity.User;
import com.trs.police.common.core.mapper.GlobalUserMapper;
import com.trs.police.fight.domain.vo.HotLineUserInfoVO;
import com.trs.police.fight.service.HotlinePersonInfoService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;


/**
 * 泸州
 *
 * <AUTHOR>
 */
@Service
@ConditionalOnProperty(name = "ys.fight.hotline.version", havingValue = "lz", matchIfMissing = false)
public class LzHotlinePersonInfoServiceImpl implements HotlinePersonInfoService {

    @Resource
    private GlobalUserMapper userMapper;

    @Override
    public HotLineUserInfoVO findHotlineInfoByTel(String tel) {
        User user = userMapper.selectOne(new QueryWrapper<User>().eq("mobile", tel));
        if (Objects.isNull(user) || StringUtils.isEmpty(user.getIdNumber())) {
            return null;
        }
        HotLineUserInfoVO vo = new HotLineUserInfoVO();
        vo.setTel(user.getTelephone());
        vo.setName(user.getRealName());
        vo.setIdNumber(user.getIdNumber());
        vo.setPoliceCode(user.getPoliceCode());
        return vo;
    }
}
