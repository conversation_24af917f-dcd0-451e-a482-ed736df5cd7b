package com.trs.police.fight.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合成作战和预警关联表
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "t_fight_composite_warning_relation")
public class FightCompositeWarnDO extends AbstractBaseEntity {

    /**
     * 关联的预警主键
     */
    @TableField(value = "warn_id")
    private Long warnId;


    /**
     * 合成作战主键
     */
    @TableField(value = "composite_id")
    private Long compositeId;

    public FightCompositeWarnDO(Long warnId, Long compositeId) {
        this.warnId = warnId;
        this.compositeId = compositeId;
    }
}