package com.trs.police.fight.controller;

import com.trs.police.common.core.vo.message.SystemMessage;
import com.trs.police.common.core.vo.message.WebsocketMessageVO;
import com.trs.police.fight.listener.RabbitMessageListener;
import com.trs.police.fight.message.SystemMessageService;
import javax.annotation.Resource;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
public class MessageExchangeController {

    @Resource
    private SystemMessageService systemMessageService;
    @Resource
    private RabbitMessageListener rabbitMessageListener;


    /**
     * 接收用户消息并处理
     *
     * @param message {@link WebsocketMessageVO}
     */
    @PostMapping(value = "/message/receive/user", consumes = MediaType.APPLICATION_JSON_VALUE)
    public void receiveUserMessage(@RequestBody WebsocketMessageVO message) {
        rabbitMessageListener.processMessage(message);
    }

    /**
     * 接收系统消息并处理
     *
     * @param message {@link SystemMessage}
     */
    @PostMapping(value = "/message/receive/system", consumes = MediaType.APPLICATION_JSON_VALUE)
    public void receiveSystemMessage(@RequestBody SystemMessage message) {
        systemMessageService.processSystemMessage(message);
    }
}
