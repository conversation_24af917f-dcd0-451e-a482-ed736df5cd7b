package com.trs.police.fight.domain.vo;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 嫌疑人vo
 *
 * <AUTHOR>
 * @since 2022/4/15 10:57
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CriminalVO {

    private String name;

    private String avatar;

    private Integer sex;

    private String type;
    /**
     * 民族
     */
    private String nation;
    /**
     * 轨迹列表
     */
    private String recentTrack;
    /**
     * 布控状态
     */
    private String status;

    /**
     * 标签
     */
    private List<String> tags;


}
