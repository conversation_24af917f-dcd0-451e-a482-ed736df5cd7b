package com.trs.police.fight.domain.vo;

import java.time.LocalDateTime;
import lombok.Data;

/**
 * 我的合成作战
 *
 * <AUTHOR>
 */
@Data
public class RecordVO {
    /**
     * id
     */
    private String id;
    /**
     * 标题
     */
    private String title;
    /**
     * 合成类型
     */
    private String type;
    /**
     * 最后回复内容
     */
    private String lastReply;
    /**
     * 成员数量
     */
    private Integer memberCount;
    /**
     * 最后回复时间
     */
    private String lastReplyTime;
    /**
     * LocalDateTime类型时间（用于排序）
     */
    private LocalDateTime originTime;
}
