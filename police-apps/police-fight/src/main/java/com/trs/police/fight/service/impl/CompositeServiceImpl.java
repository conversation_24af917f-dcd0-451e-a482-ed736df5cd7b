package com.trs.police.fight.service.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.*;
import com.deepoove.poi.data.style.BorderStyle;
import com.google.common.base.Preconditions;
import com.trs.common.base.PreConditionCheck;
import com.trs.police.common.core.constant.ExceptionMessageConstant;
import com.trs.police.common.core.constant.enums.*;
import com.trs.police.common.core.constant.log.OperateModule;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.dto.UserDto;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.entity.FightComposite;
import com.trs.police.common.core.entity.FightCompositeUserRelation;
import com.trs.police.common.core.excpetion.ParamValidationException;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.utils.DateUtil;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.core.vo.CaseTagVO;
import com.trs.police.common.core.vo.GeometryVO;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.fight.CompositeListVO;
import com.trs.police.common.core.vo.message.NoticeVO;
import com.trs.police.common.core.vo.message.PhoneMessageVO;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import com.trs.police.common.core.vo.permission.SimpleUserVO;
import com.trs.police.common.core.vo.permission.UserDeptVO;
import com.trs.police.common.notice.starter.enums.SystemNoticeEnum;
import com.trs.police.common.notice.starter.utils.NoticeUtil;
import com.trs.police.common.openfeign.starter.service.*;
import com.trs.police.common.openfeign.starter.vo.ApprovalActionVO;
import com.trs.police.fight.builder.CompositeDetailBuilder;
import com.trs.police.fight.constant.CompositeConstant;
import com.trs.police.fight.constant.FightOperationConstant;
import com.trs.police.fight.constant.enums.CompositeJoinEnum;
import com.trs.police.fight.constant.enums.CompositePartnerWay;
import com.trs.police.fight.constant.enums.CompositeType;
import com.trs.police.fight.constant.enums.PlanSchedulingMeasureEnum;
import com.trs.police.fight.domain.dto.ClassicCaseDto;
import com.trs.police.fight.domain.dto.CompositePartnerDTO;
import com.trs.police.fight.domain.entity.*;
import com.trs.police.fight.domain.params.ExportCompositeParam;
import com.trs.police.fight.domain.vo.*;
import com.trs.police.fight.helper.DutyUserHelper;
import com.trs.police.fight.helper.FightSendDwdMessageHelper;
import com.trs.police.fight.listener.RabbitMessageListener;
import com.trs.police.fight.mapper.*;
import com.trs.police.fight.message.FightMessageHelper;
import com.trs.police.fight.message.SystemMessageAnnotation;
import com.trs.police.fight.service.*;
import com.trs.police.fight.service.compositePartner.CompositePartnerFactory;
import com.trs.police.fight.service.compositePartner.ICompositePartnerService;
import com.trs.police.fight.service.impl.relation.UserRelationServiceImpl;
import com.trs.police.fight.sync.bean.Message;
import com.trs.police.fight.sync.constant.DataSource;
import com.trs.police.fight.sync.service.MsgService;
import com.trs.police.fight.sync.service.YsPermissionService;
import com.trs.police.fight.sync.service.impl.builder.CompositeBuilder;
import com.trs.web.builder.base.RestfulResultsV2;
import com.trs.web.builder.util.BeanFactoryHolder;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.trs.police.common.core.constant.DwdServiceCodeConstant.DWD_HECHENG_INFO;
import static com.trs.police.common.core.constant.GovWxConstant.*;
import static com.trs.police.common.core.constant.SystemDefaultRoleConstant.COMPOSITE_DEFAULT_ORGANIZER;
import static com.trs.police.common.notice.starter.enums.SystemNoticeEnum.COMPOSITE_APPROVED;
import static com.trs.police.common.notice.starter.enums.SystemNoticeEnum.COMPOSITE_REJECTED;

/**
 * 合成作战服务层实现
 *
 * <AUTHOR>
 * @since 2022/4/1 17:26
 **/
@Service
@Slf4j
public class CompositeServiceImpl implements CompositeService {

    @Resource
    private FightCompositeMapper fightCompositeMapper;
    @Resource
    private FightCaseEventRelationService fightCaseEventRelationService;
    @Resource
    private FightCompositeCaseEventRelationMapper fightCompositeCaseEventRelationMapper;
    @Resource
    private FightCaseTagServiceRelationImpl fightCaseTagServiceRelation;
    @Resource
    private FightCompositeUserRelationService fightCompositeUserRelationService;
    @Resource
    private FightCompositeUserRelationMapper fightCompositeUserRelationMapper;
    @Resource
    private FightCaseTagRelationService fightCaseTagRelationService;
    @Resource
    private PermissionService permissionService;
    @Resource
    private DictService dictService;

    @Resource
    private OssService ossService;
    @Resource
    private FightCompositeApplyMapper fightCompositeApplyMapper;
    @Resource
    private ApprovalService approvalService;
    @Resource
    private FightCompositeCommentRelationMapper fightCompositeCommentRelationMapper;
    @Resource
    private RelationSubjectService relationSubjectService;
    @Resource
    private FightCompositeService fightCompositeService;
    @Resource
    private FightCompositeCommentService fightCompositeCommentService;

    @Resource
    private FightCompositeClueRelationService fightCompositeClueRelationService;

    @Resource
    private UserRelationServiceImpl userRelationService;

    @Resource
    private MyCompositeServiceImpl myCompositeService;

    @Resource
    private RabbitMessageListener rabbitMessageListener;

    @Resource
    private MessageService messageService;

    @Resource
    private PlanRecordMapper planRecordMapper;

    @Resource
    private FightSendDwdMessageHelper fightSendDwdMessageHelper;

    @Resource
    private FightCompositePersonRelationServiceImpl fightCompositePersonRelationService;

    @Resource
    private CompositeApprovalService compositeApprovalService;

    @Autowired
    private PlanMapper planMapper;

    @Autowired
    private PlanService planService;

    @Resource
    private FightCompositeWorkSceneRelationMapper fightCompositeWorkSceneRelationMapper;

    @Resource
    private PlanSchedulingMeasureRelationMapper planSchedulingMeasureRelationMapper;

    @Autowired
    private FightMessageHelper fightMessageHelper;

    @Autowired
    private YsPermissionService ysPermissionService;

    @Autowired
    private MsgService msgService;

    @Autowired
    private CompositeBuilder compositeBuilder;

    @Autowired
    private CompositePartnerFactory compositePartnerFactory;
    @Autowired
    private PlanTaskRelationMapper planTaskRelationMapper;
    @Autowired
    private BigScreenDutyUserMapper bigScreenDutyUserMapper;
    @Autowired
    private DutyUserHelper dutyUserHelper;

    @Resource
    private FightMessageModuleMapper messageModuleMapper;

    /**
     * 导出
     *
     * @param id       id 主键
     * @param response response 响应
     */
    @Override
    public void exportComposite(Long id, HttpServletResponse response) {
        try {
            CompositeDetailVO compositeDetail = getCompositeDetail(id);

            // 查询处突状态码表
            DictDto fightCompositeStatus = dictService.getDictByTypeAndCode("fight_composite_status", compositeDetail.getStatus());
            if (fightCompositeStatus == null) {
                throw new RuntimeException("fight_composite_status 未找到此状态码 " + compositeDetail.getStatus());
            }
            // 获取指挥长信息
            List<CompositeUserInfo> partners = compositeDetail.getPartners();


            // 构造参与人员
            Map<String, List<CompositeUserInfo>> collect = partners.stream().collect(Collectors.groupingBy(CompositeUserInfo::getDeptName));
            StringBuilder partnerContent = new StringBuilder();
            for (Map.Entry<String, List<CompositeUserInfo>> entry : collect.entrySet()) {
                partnerContent.append(entry.getKey()).append("（").append(entry.getValue().size()).append("）:");
                List<CompositeUserInfo> value = entry.getValue();
                value.forEach(item -> partnerContent.append(" ").append(item.getUserName()));
                partnerContent.append("\n");
            }

            // 构造关联预警信息
            // 表格数据
            RowRenderData warnHeader = Rows.of("关联预警信息",null,null,null,null,null,null,null,null).create();
            List<RowRenderData> warnRows = new ArrayList<>();
            warnRows.add(warnHeader);
            warnRows.add(Rows.of("序号", "预警人员姓名", "预警级别", "预警对象", "管控类型", "模型", "命中专题", "命中场景", "预警时间").create());

            // 解析预警信息
            if (StringUtils.isNotEmpty(compositeDetail.getWarningInfo())) {
                try {
                    JSONArray warningArray = JSON.parseArray(compositeDetail.getWarningInfo());
                    for (int i = 0; i < warningArray.size(); i++) {
                        JSONObject warning = warningArray.getJSONObject(i);
                        List<String> activityPersons = new ArrayList<>();
                        if (warning.containsKey("activityPerson")) {
                            activityPersons = warning.getJSONArray("activityPerson").toJavaList(String.class);
                        } else if (warning.containsKey("newActivityPerson")) {
                            activityPersons = warning.getJSONArray("newActivityPerson").stream()
                                    .map(obj -> ((JSONObject) obj).getString("name"))
                                    .collect(Collectors.toList());
                        }

                        String activityPerson = String.join(",", activityPersons);
                        String warningLevel = warning.containsKey("warningLevel")
                                ? warning.getJSONObject("warningLevel").getString("name") : "";
                        String tag = warning.getString("tag");
                        // 默认布控
                        String controlType = "布控";

                        List<String> models = new ArrayList<>();
                        if (warning.containsKey("model")) {
                            models = warning.getJSONArray("model").toJavaList(String.class);
                        }
                        String model = String.join(",", models);

                        String hitSubject = warning.containsKey("hitSubject")
                                ? warning.getJSONObject("hitSubject").getString("name") : "";
                        String hitSubjectScene = warning.containsKey("hitSubjectScene")
                                ? warning.getJSONObject("hitSubjectScene").getString("name") : "";
                        String warningTime = warning.getString("warningTime");

                        warnRows.add(Rows.of(
                                String.valueOf(i + 1),
                                activityPerson,
                                warningLevel,
                                tag,
                                controlType,
                                model,
                                hitSubject,
                                hitSubjectScene,
                                warningTime
                        ).create());
                    }
                } catch (Exception e) {
                    log.error("解析预警信息失败", e);
                }
            }

            // 构造关联警情信息
            RowRenderData policeHeader = Rows.of("关联警情信息", null, null, null, null).create();
            List<RowRenderData> policeRows = new ArrayList<>();
            policeRows.add(policeHeader);
            policeRows.add(Rows.of("序号", "警情内容", "警情类别", "处置单位", "预警时间").create());

            // 解析警情信息
            if (StringUtils.isNotEmpty(compositeDetail.getPoliceIntelligenceInfo())) {
                try {
                    JSONArray policeArray;
                    String jsonStr = compositeDetail.getPoliceIntelligenceInfo();
                    policeArray = JSON.parseArray(jsonStr);

                    for (int i = 0; i < policeArray.size(); i++) {
                        JSONObject police = policeArray.getJSONObject(i);
                        String content = police.getString("content");
                        String jqlbmc = police.getString("jqlbmc");
                        String gxdwmc = police.getString("gxdwmc");
                        String alarmTime = police.getString("alarmTime");

                        policeRows.add(Rows.of(
                                String.valueOf(i + 1),
                                content,
                                jqlbmc,
                                gxdwmc,
                                alarmTime
                        ).create());
                    }
                } catch (Exception e) {
                    log.error("解析警情信息失败", e);
                }
            }

            // 创建表格合并规则
            MergeCellRule.MergeCellRuleBuilder warnMergeRuleBuilder = MergeCellRule.builder();
            warnMergeRuleBuilder.map(MergeCellRule.Grid.of(0, 0), MergeCellRule.Grid.of(0, 8)); // 合并第一行的9列

            MergeCellRule.MergeCellRuleBuilder policeMergeRuleBuilder = MergeCellRule.builder();
            policeMergeRuleBuilder.map(MergeCellRule.Grid.of(0, 0), MergeCellRule.Grid.of(0, 4)); // 合并第一行的5列

            // 创建表格
            TableRenderData warnTable = Tables.of(warnRows.toArray(new RowRenderData[0]))
                    .mergeRule(warnMergeRuleBuilder.build())
                    .border(BorderStyle.builder().withSize(5).build())
                    .create();

            TableRenderData policeTable = Tables.of(policeRows.toArray(new RowRenderData[0]))
                    .mergeRule(policeMergeRuleBuilder.build())
                    .border(BorderStyle.builder().withSize(5).build())
                    .create();
            // 获取指挥长信息
            Optional<CompositeUserInfo> commandersOptional = partners.stream()
                    .filter(item -> CompositeRoleEnum.ORGANIZER.getCode().equals(item.getRole()))
                    .findFirst();
            CompositeUserInfo compositeUserInfo = commandersOptional.orElse(new CompositeUserInfo());
            // 读取模板文件
            InputStream inputStream = getClass().getClassLoader()
                    .getResourceAsStream("/fight-export-template.docx");
            if (inputStream == null) {
                throw new FileNotFoundException("/fight-export-template.docx 资源未找到");
            }
            // 构造关联警情信息
            XWPFTemplate template = XWPFTemplate.compile(inputStream).render(
                    new HashMap<String, Object>(){{
                        put("title", compositeDetail.getTitle());
                        put("createTime", compositeDetail.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                        put("status", fightCompositeStatus.getName());
                        put("leaderUserName", compositeUserInfo.getUserName());
                        put("leaderDeptName", compositeUserInfo.getDeptName());
                        put("eventPlaceName", compositeDetail.getEventPlace());
                        put("require", compositeDetail.getRequire());
                        put("partnerContent", partnerContent.toString());
                        put("warningTable", warnTable);
                        put("policeTable", policeTable);
                    }});


            String docName = compositeDetail.getTitle() + ".docx";
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(docName, StandardCharsets.UTF_8));
            ServletOutputStream outputStream = response.getOutputStream();
            template.writeAndClose(outputStream);
            outputStream.flush();
            response.setContentType("application/octet-stream");

            log.info("导出合成作战数据成功！");
        } catch (Exception e) {
            log.error("导出合成作战数据失败！", e);
        }
    }

    /**
     * 清除掉非启用状态的预案id
     *
     * @param data      数据id
     * @param getPlanId 获取预案id
     * @param findPlan  根据预案id获取预案
     * @param setPlanId 设置预案id
     * @param getId     唯一值
     * @param <T>       t
     * @param <ID>      id
     */
    public static <T, ID> void clearBanedPlanId(List<T> data, Function<T, Long> getPlanId, Function<List<Long>, List<PlanEntity>> findPlan, BiConsumer<T, Long> setPlanId, Function<T, ID> getId) {
        if (null == data || data.isEmpty()) {
            return;
        }
        List<Long> ids = data.stream()
                .map(getPlanId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        // 启用状态的id
        Set<Long> enableSet = findPlan.apply(ids).stream().map(PlanEntity::getId).collect(Collectors.toSet());
        // 失效的id
        Set<Long> banedSet = ids.stream()
                .filter(id -> !enableSet.contains(id))
                .collect(Collectors.toSet());
        // 遍历数据 失效的给清理掉
        data.forEach(d -> {
            Long planId = getPlanId.apply(d);
            if (Objects.nonNull(planId) && banedSet.contains(planId)) {
                setPlanId.accept(d, null);
            }
        });
    }


    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    @SystemMessageAnnotation(conversationId = "id", type = MessageTypeEnum.CREATE, defaultReadStatus = true)
    public FightComposite createComposite(CreateCompositeVO createCompositeVO) {
        // 设置作战类别或者使用默认值
        CompositeType type = CompositeType.ofCode(createCompositeVO.getCompositeType()).orElse(CompositeType.COMPOSITE);
        // 构造作战实体
        FightComposite fightComposite = CreateCompositeVO.fightComposite(createCompositeVO, compositeApprovalService.getDefaultStatue(type));
        fightCompositeMapper.insert(fightComposite);
        // 更新预案等级
        updatePlanLevel(createCompositeVO);
        // 更新作战全局唯一id
        Optional<DataSource> dataSource = ysPermissionService.tryGetLocalDataSource();
        if (dataSource.isPresent()) {
            DataSource localDataSource = ysPermissionService.getLocalDataSource();
            fightComposite.setDataSource(localDataSource.getCode());
            fightComposite.setSourcePrimaryKey(fightComposite.getId().toString());
            fightCompositeMapper.updateById(fightComposite);
        }
        // 拿到锁，阻塞其它获取锁的行为（比如其它模块mq消费消息时 如果这个方法没执行完，会获取到空的作战）
        log.info(
                "创建合成获取锁的结果是：{}",
                Optional.ofNullable(fightCompositeMapper.selectForUpdate(fightComposite.getId())).map(FightComposite::getId).orElse(null)
        );
        // 建立关联关系
        setRelation(createCompositeVO, fightComposite);
        // 创建审批（如果需要）
        compositeApprovalService.createCompositeApproval(fightComposite.getId(), fightComposite, type);
        return fightComposite;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    @SystemMessageAnnotation(conversationId = "id", type = MessageTypeEnum.CREATE, defaultReadStatus = true)
    public FightComposite createCompositeNotApproval(CreateCompositeVO createCompositeVO) {
        FightComposite fightComposite = CreateCompositeVO.fightComposite(createCompositeVO, CompositeStatusEnum.IN_PROGRESS);
        fightCompositeMapper.insert(fightComposite);
        setRelation(createCompositeVO, fightComposite);
        return fightComposite;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FightComposite mergeComposite(MergeCompositeVO mergeCompositeVO) {
        FightComposite mainComposite = fightCompositeMapper.selectById(mergeCompositeVO.getMainCompositeId());
        // 构造新的作战
        FightComposite mergedComposite = buildFightComposite(mainComposite, mergeCompositeVO.getMergeIds());
        fightCompositeMapper.insert(mergedComposite);
        // 生成快照（暂不实现）

        // 关联关系修改到新的作战（合成）上
        relationSubjectService.rebuildRelation(mergeCompositeVO, mainComposite, mergedComposite);
        // 删除掉旧的作战（合成）
        fightCompositeMapper.deleteBatchIds(mergeCompositeVO.getMergeIds());
        return mergedComposite;
    }

    /**
     * 构造作战do
     *
     * @param mainComposite 主要的作战
     * @param mergeIds      全部的合并的id
     * @return 作战
     */
    private FightComposite buildFightComposite(FightComposite mainComposite, List<Long> mergeIds) {
        FightComposite mergedComposite = new FightComposite();
        BeanUtil.copyPropertiesIgnoreNull(mainComposite, mergedComposite, "id");
        mergedComposite.setStatus(mainComposite.getStatus());
        // 设置文件vo
        List<FileInfoVO> attachments = fightCompositeMapper.selectBatchIds(mergeIds)
                .stream()
                .map(FightComposite::getAttachments)
                .flatMap(List::stream)
                .collect(Collectors.toList());
        mergedComposite.setAttachments(attachments);
        return mergedComposite;
    }

    /**
     * 设置关系
     *
     * @param createCompositeVO 新增参数
     * @param fightComposite    合成作战
     */
    private void setRelation(CreateCompositeVO createCompositeVO, FightComposite fightComposite) {
        relationSubjectService.createRelation(createCompositeVO, fightComposite);
    }

    @Override
    public PageResult<CompositeListVO> getCompositeList(ListParamsRequest compositeParams) {
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        if (Objects.isNull(currentUser)) {
            throw new TRSException(ExceptionMessageConstant.CANT_FIND_CURRENT_USER);
        }
        PageParams pageParams = compositeParams.getPageParams();
        Page<CompositeListVO> page = fightCompositeMapper.getPage(compositeParams, currentUser, pageParams.toPage());
        List<Long> ids = page.getRecords().stream().map(CompositeListVO::getId).collect(Collectors.toList());
        Map<Long, List<CaseTagVO>> caseTagMap = getCaseTagName(ids);
        Map<Long, String> planCategory = dictService.getDictListByType("plan_category").stream()
                .collect(Collectors.toMap(DictDto::getCode, DictDto::getName));
        for (CompositeListVO compositeListVO : page.getRecords()) {
            compositeListVO.setCaseTag(caseTagMap.get(compositeListVO.getId()));
            if (StringUtils.isNotEmpty(compositeListVO.getUrgentTypeId())) {
                List<Long> urgentTypeCode = JSONArray.parseArray(compositeListVO.getUrgentTypeId(), Long.class);
                List<String> names = urgentTypeCode.stream()
                        .map(code -> Optional.ofNullable(planCategory.get(code)).orElse(null))
                        .filter(StringUtils::isNotEmpty)
                        .collect(Collectors.toList());
                String name = names.isEmpty() ? null : names.get(names.size() - 1);
                compositeListVO.setUrgentTypeName(name);
            }
        }
        List<CompositeListVO> records = page.getRecords();
        // 如果有预案被禁用了 清理掉预案id
        CompositeServiceImpl.clearBanedPlanId(records, CompositeListVO::getPlanId, planService::findEnablePlanById, CompositeListVO::setPlanId, CompositeListVO::getId);
        // 添加预案对应的距离
        List<Long> planIds = records.stream()
                .map(CompositeListVO::getPlanId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        List<PlanSchedulingMeasureRelation> mrList = CollectionUtils.isEmpty(planIds)
                ? new ArrayList<>()
                : planSchedulingMeasureRelationMapper.selectList(
                Wrappers.lambdaQuery(PlanSchedulingMeasureRelation.class)
                        .in(PlanSchedulingMeasureRelation::getPlanId, planIds
                        )
        );
        for (PlanSchedulingMeasureRelation mr : mrList) {
            records.stream()
                    .filter(re -> Objects.equals(re.getPlanId(), mr.getPlanId()))
                    .forEach(re -> setRangeValue(mr, re));
        }
        buildEventPlaceGeoRange(records);
        return PageResult.of(page.getRecords(), pageParams.getPageNumber(), page.getTotal(), pageParams.getPageSize());
    }

    /**
     * 构建发生地点最大范围
     *
     * @param records 处突
     */
    private void buildEventPlaceGeoRange(List<CompositeListVO> records) {
        records.stream()
                //只对具有预案的处突做处理
                .filter((record) -> record.getPlanId() != null)
                .forEach(this::buildEventPlaceGeoRange);
    }

    /**
     * 构建发生地点最大范围
     *
     * @param record 处突
     */
    private void buildEventPlaceGeoRange(CompositeListVO record) {
        if (!StringUtils.isNotEmpty(record.getEventPlace())) {
            return;
        }
        try {
            double jinYuanRange = record.getJinCheRange() == null ? 0D : record.getJinCheRange();
            double jinCheRange = record.getJinCheRange() == null ? 0D : record.getJinCheRange();
            double sourceRange = record.getSourceRange() == null ? 0D : record.getSourceRange();
            double range = Math.max(jinYuanRange, jinCheRange);
            range = Math.max(range, sourceRange);
            //画圈
            GeometryVO geo = new GeometryVO();
            geo.setType("circle");
            JSONObject jsonObject = JSON.parseObject(record.getEventPlace());
            geo.setGeometry(String.format("POINT(%s %s)", jsonObject.getString("long"), jsonObject.getString("lat")));
            var p = new GeometryVO.Properties();
            p.setRadius(range);
            geo.setProperties(p);
            record.setGeometries(List.of(geo));
        } catch (Exception e) {
            log.error("构建事件发生地点范围发生异常", e);
        }
    }

    private void setRangeValue(PlanSchedulingMeasureRelation mr, CompositeListVO vo) {
        PlanSchedulingMeasureEnum measureEnum = PlanSchedulingMeasureEnum.codeOf(mr.getMeasureType().intValue());
        if (Objects.isNull(measureEnum)) {
            return;
        }
        switch (measureEnum) {
            case POLICE:
                vo.setJinYuanRange(mr.getMeasureRange());
                break;
            case UNIT_DEPT:
                break;
            case CAMERA:
                vo.setSourceRange(mr.getMeasureRange());
                break;
            case POLICE_CAR:
                vo.setJinCheRange(mr.getMeasureRange());
                break;
            default:
                break;
        }
    }

    /**
     * 获取案件标签名称
     *
     * @param ids 合成id
     * @return {@link CaseTagVO}
     */
    private Map<Long, List<CaseTagVO>> getCaseTagName(List<Long> ids) {
        Map<Long, List<CaseTagVO>> caseTagMap = new HashMap<>();
        if (CollectionUtils.isEmpty(ids)) {
            return caseTagMap;
        }
        List<FightCaseTagRelation> caseTagRelations = fightCaseTagServiceRelation.list(
                new QueryWrapper<FightCaseTagRelation>().in("composite_id", ids));
        for (FightCaseTagRelation caseTagRelation : caseTagRelations) {
            CaseTagVO vo = new CaseTagVO();
            vo.setName(caseTagRelation.getCaseTagName());
            caseTagMap.computeIfAbsent(caseTagRelation.getCompositeId(), k -> new ArrayList<>()).add(vo);
        }
        return caseTagMap;
    }

    @Override
    public CompositeDetailVO getCompositeDetail(Long id) {
        FightComposite fightComposite = fightCompositeMapper.selectById(id);
        if (Objects.isNull(fightComposite)) {
            return new CompositeDetailVO();
        }
        // 清理掉禁用的作战
        CompositeServiceImpl.clearBanedPlanId(Arrays.asList(fightComposite), FightComposite::getPlanId, planService::findEnablePlanById, FightComposite::setPlanId, FightComposite::getId);
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        assert currentUser != null;
        CompositeUserInfo organizer = fightCompositeUserRelationMapper.findByCompositeIdAndRole(
                        fightComposite.getId(), CompositeRoleEnum.ORGANIZER.getCode())
                .stream()
                .map(CompositeDetailBuilder.builder().permissionService(permissionService).build()
                        .getFightCompositeUserRelationUserInfoFunction())
                .findAny()
                .orElse(new CompositeUserInfo());
        List<PlanSchedulingMeasureRelation> pr = Objects.isNull(fightComposite.getPlanId())
                ? new ArrayList<>()
                : planSchedulingMeasureRelationMapper.selectList(
                Wrappers.lambdaQuery(PlanSchedulingMeasureRelation.class)
                        .eq(PlanSchedulingMeasureRelation::getPlanId, fightComposite.getPlanId()
                        )
        );
        CompositeDetailBuilder build = CompositeDetailBuilder.builder()
                .ossService(ossService)
                .fightCompositeCaseEventRelationMapper(fightCompositeCaseEventRelationMapper)
                .permissionService(permissionService)
                .dictService(dictService)
                .fightCompositeUserRelationService(fightCompositeUserRelationService)
                .fightComposite(fightComposite)
                .currentUser(currentUser)
                .fightCompositeApplyMapper(fightCompositeApplyMapper)
                .fightCaseTagServiceRelation(fightCaseTagServiceRelation)
                .fightCompositeClueRelationService(fightCompositeClueRelationService)
                .fightCompositePersonRelationService(fightCompositePersonRelationService)
                .fightCompositeWorkSceneRelationMapper(fightCompositeWorkSceneRelationMapper)
                .planEntity(getPlan(fightComposite.getPlanId()))
                .createUser(permissionService.findCurrentUser(fightComposite.getCreateUserId(), fightComposite.getCreateDeptId()))
                .organization(organizer)
                .measureRelationList(pr)
                .planTaskRelationMapper(planTaskRelationMapper)
                .dutyUserHelper(dutyUserHelper)
                .build();
        CompositeDetailVO compositeDetailVO = build.of();
        build.setRelation(compositeDetailVO);
        //处理历史数据，把警情录音赋值回去
        buildJjlyh(compositeDetailVO);
        Map<Long, String> planCategory = dictService.getDictListByType("plan_category").stream()
                .collect(Collectors.toMap(DictDto::getCode, DictDto::getName));
        List<Long> urgentTypeCode = StringUtils.isNotEmpty(compositeDetailVO.getUrgentTypeId())
                ? JSONArray.parseArray(compositeDetailVO.getUrgentTypeId(), Long.class)
                : new ArrayList<>();
        List<String> names = urgentTypeCode.stream()
                .map(code -> Optional.ofNullable(planCategory.get(code)).orElse(null))
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());
        String name = names.isEmpty() ? null : names.get(names.size() - 1);
        compositeDetailVO.setUrgentTypeName(name);
        return compositeDetailVO;
    }

    private void buildJjlyh(CompositeDetailVO collect) {
        if (com.trs.common.utils.StringUtils.isNotEmpty(collect.getPoliceIntelligenceInfo())) {
            String jsonStr = collect.getPoliceIntelligenceInfo();
            JSONArray jsonArray;
            // 判断是数组还是对象
            if (jsonStr.startsWith("[")) {
                // 直接是数组形式
                jsonArray = JSON.parseArray(jsonStr);
            } else {
                // 是对象形式，检查是否有 data 字段
                JSONObject jsonObject = JSON.parseObject(jsonStr);
                jsonArray = jsonObject.containsKey("data")
                        ? jsonObject.getJSONArray("data")
                        : new JSONArray();
            }
            if (!jsonArray.isEmpty()) {
                jsonArray.forEach(item -> {
                    JSONObject jsonItem = (JSONObject) item;
                    if (!jsonItem.containsKey("jjlyh")) {
                        Long id = jsonItem.getLong("id");
                        // 根据id去t_profile_sthy表中查询jjlyh字段
                        String jjlyh = fightCompositeMapper.getjjlyh(id);
                        if (com.trs.common.utils.StringUtils.isNotEmpty(jjlyh)) {
                            String env = BeanFactoryHolder.getEnv().getProperty("fight.forward.url", "");
                            String newUrl = env + jjlyh;
                            jsonItem.put("jjlyh", newUrl);
                        }
                    }
                });
            }
            collect.setPoliceIntelligenceInfo(jsonArray.toJSONString());
        }
    }

    @Override
    public List<CompositeJoinStatus> getJoinStatus() {
        return Arrays.stream(CompositeJoinEnum.values())
                .map(e -> new CompositeJoinStatus(String.valueOf(e.getId()), e.getName())).collect(Collectors.toList());
    }

    @Override
    public void exportCompositeList(HttpServletResponse httpServletResponse,
                                    ExportCompositeParam exportCompositeParam) {
        if (exportCompositeParam.getCompositeIds().isEmpty()) {
            throw new ParamValidationException("需要导出的id为空");
        }
        List<FightComposite> fightComposites = fightCompositeMapper.selectBatchIds(
                exportCompositeParam.getCompositeIds());
        String fileName = String.format("合成列表-%s.xlsx",
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        httpServletResponse.setContentType("application/vnd.ms-excel");
        httpServletResponse.setCharacterEncoding("utf-8");
        httpServletResponse.setHeader("Content-disposition", URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        List<CompositeExportVO> exportVOList = fightComposites.stream().map(CompositeExportVO::of)
                .collect(Collectors.toList());
        try {
            EasyExcelFactory.write(httpServletResponse.getOutputStream(), CompositeExportVO.class).sheet()
                    .doWrite(exportVOList);
        } catch (IOException e) {
            log.error("合成列表导出失败！", e);
        }

    }

    @Override
    public List<SimpleUserVO> getCompositeDefaultOrganizer() {
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        if (Objects.isNull(currentUser)) {
            throw new TRSException(ExceptionMessageConstant.CANT_FIND_CURRENT_USER);
        }

        return permissionService.getUserListByRoleId(COMPOSITE_DEFAULT_ORGANIZER)
                .stream()
                .filter(item -> !AuthHelper.getNotNullUser().getId().equals(item.getUserId()))
                .filter(item -> currentUser.getDept().getDistrictCode().equals(item.getDistrictCode()))
                .collect(Collectors.toList());
    }


    /**
     * 完成审批
     *
     * @param action 事件
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void finishApproval(ApprovalActionVO action) {
        FightComposite lock = fightCompositeMapper.selectForUpdate(action.getId());
        log.info(
                "创建合成获取锁的结果是：{}",
                Optional.ofNullable(lock).map(FightComposite::getId).orElse(null)
        );
        FightComposite task = fightCompositeMapper.selectById(action.getId());
        task.setStatus(CompositeStatusEnum.approvalStatusToComposite(action.getApprovalStatus()));
        fightCompositeMapper.updateById(task);
        // 发送通知消息
        sendApprovalNotice(task, action);
        log.info("合成状态更新成功！审批信息：{}", action);
        // 发送业务档案消息
        try {
            if (OperateModule.COMPOSITE.equals(action.getService()) && ApprovalStatusEnum.PASSED.equals(action.getApprovalStatus())) {
                FightComposite fightComposite = fightCompositeMapper.getFightCompositeById(action.getId());
                task.setEventRelation(fightComposite.getEventRelation());
                task.setUserRelation(fightComposite.getUserRelation());
                task.setCompositeClueRelation(fightComposite.getCompositeClueRelation());
                task.setCaseTagRelation(fightComposite.getCaseTagRelation());
                fightSendDwdMessageHelper.sendDwdMessage(task, DWD_HECHENG_INFO, "insert", "composite_id",
                        (map) -> {
                            map.put("status", task.getStatus().getCode());
                            map.put("attachments", JSON.toJSONString(task.getAttachments()));
                        });
            }
        } catch (Exception e) {
            log.error("推送业务档案消息失败", e);
        }
        // 推送作战到其它环境
        Try.run(() -> {
            Boolean enabled = BeanFactoryHolder.getEnv().getProperty("ys.fight.push.fight", Boolean.class, false);
            if (enabled && CompositeType.COMPOSITE.equals(CompositeType.ofCode(task.getCompositeType()).orElse(null))) {
                log.info("推送作战：id:{}, title:{}", task.getId(), task.getTitle());
                Message message = compositeBuilder.buildMsg(task);
                msgService.sendMsg(message);
            }
        }).onFailure(e -> log.error(String.format("推送作战：%s失败", task.getId()), e));
    }


    /**
     * 完成添加人员
     *
     * @param action 事件
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void finishAddCompositePerson(ApprovalActionVO action) {
        boolean update = fightCompositeUserRelationService.lambdaUpdate()
                .eq(FightCompositeUserRelation::getCompositeId, action.getId())
                .eq(FightCompositeUserRelation::getApprovalId, action.getApprovalId())
                .set(FightCompositeUserRelation::getStatus, action.getApprovalStatus().getCode())
                .update();
        sendAddPersonSuccessMsg(action);
        log.info("邀请作战人员成功！审批信息：{}", action);
    }

    private void sendAddPersonSuccessMsg(ApprovalActionVO action) {
        try {
            if (!ApprovalStatusEnum.PASSED.equals(action.getApprovalStatus())) {
                return;
            }
            CurrentUser requestUser = permissionService.findCurrentUser(action.getRequestUserId(), action.getRequestUserDeptId());

            // 系统消息
            List<UserDeptVO> addMembersVO = fightCompositeUserRelationService.lambdaQuery()
                    .eq(FightCompositeUserRelation::getCompositeId, action.getId())
                    .eq(FightCompositeUserRelation::getApprovalId, action.getApprovalId())
                    .list()
                    .stream()
                    .map(re -> new UserDeptVO(re.getUserId(), re.getDeptId()))
                    .collect(Collectors.toList());
            myCompositeService.generateMsg(action.getId(), action, requestUser, addMembersVO);
            // 短信通知
            List<CurrentUser> users = addMembersVO.stream()
                    .map(u -> permissionService.findCurrentUser(u.getUserId(), u.getDeptId()))
                    .collect(Collectors.toList());
            String[] tel = users.stream()
                    .map(CurrentUser::getMobile)
                    .filter(Objects::nonNull)
                    .distinct()
                    .toArray(String[]::new);
            FightComposite fightComposite = fightCompositeMapper.selectById(action.getId());
            PhoneMessageVO message = new PhoneMessageVO(
                    tel,
                    String.format(String.format("%s邀请你加入作战：%s", requestUser.getRealName(), fightComposite.getTitle())),
                    null,
                    action.getId()
            );
            log.info("短信通知：{}", JSON.toJSONString(message));
            sendFightSmsIfCould(message);
        } catch (Exception e) {
            log.error(String.format("发送添加用户成功的消息失败:【%s】", JsonUtil.toJsonString(action)), e);
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateComposite(CreateCompositeVO createCompositeVO) {
        createCompositeVO.setScene(2);
        final Long compositeId = createCompositeVO.getId();
        final FightComposite fightComposite = fightCompositeMapper.selectById(compositeId);
        final Long planId = fightComposite.getPlanId();
        fightComposite.setTitle(createCompositeVO.getTitle());
        fightComposite.setIsClassified(createCompositeVO.getIsClassified());
        fightComposite.setRequire(createCompositeVO.getRequire());
        fightComposite.setAttachments(createCompositeVO.getFileListId().stream().map(id -> FileInfoVO.idNumberToImg(ossService.getFileInfo(id))).collect(Collectors.toList()));
        if (Objects.nonNull(createCompositeVO.getTypeId())) {
            fightComposite.setType(createCompositeVO.getTypeId());
        }
        if (Objects.nonNull(createCompositeVO.getSubtypeId())) {
            fightComposite.setSubType(createCompositeVO.getSubtypeId());
        }
        if (Objects.nonNull(createCompositeVO.getUrgentTypeId())) {
            fightComposite.setUrgentTypeId(createCompositeVO.getUrgentTypeId());
        }
        if (Objects.nonNull(createCompositeVO.getPlanId())) {
            fightComposite.setPlanId(createCompositeVO.getPlanId());
        }
        fightComposite.setWarningInfo(createCompositeVO.getWarningInfo());
        fightComposite.setJqInfo(createCompositeVO.getPoliceIntelligenceInfo());
        fightComposite.setCreateDeptId(createCompositeVO.getCreateDeptId());
        fightComposite.setEventPlace(createCompositeVO.getEventPlace());
        fightComposite.setJqbh(createCompositeVO.getJqbh());
        fightComposite.setJqlxdm(createCompositeVO.getJqlxdm());
        fightComposite.setJqContent(createCompositeVO.getJqContent());
        fightComposite.setTarget(createCompositeVO.getTarget());
        fightComposite.setElement(createCompositeVO.getElement());
        fightComposite.setIsNeedSms(createCompositeVO.getIsNeedSms());
        fightCompositeMapper.updateById(fightComposite);
        // 更新预案等级
        updatePlanLevel(createCompositeVO);
        // 移除关联关系
        fightCompositeCaseEventRelationMapper.deleteAllByCompositeId(compositeId);
        // 设置关联关系
        setRelation(createCompositeVO, fightComposite);
        // 对变动的人员发起审批
        List<FightCompositeUserRelation> addUser = userRelationService.editUser(createCompositeVO, fightComposite, planId);
        // 发起审批
        List<UserDeptVO> vos = addUser.stream().map(user -> {
            UserDeptVO vo = new UserDeptVO(user.getUserId(), user.getDeptId());
            vo.setRole(Objects.nonNull(user.getRole()) ? user.getRole().getCode() : CompositeRoleEnum.PARTNER.getCode());
            return vo;
        }).collect(Collectors.toList());
        CompositeType type = CompositeType.ofCode(fightComposite.getCompositeType()).orElse(CompositeType.COMPOSITE);
        myCompositeService.addMembers(
                compositeApprovalService.needApproval(type),
                compositeApprovalService.getPersonDefaultStatue(type).getCode(),
                compositeId,
                vos
        );
        // 发送业务档案消息
        FightComposite fightComposite1 = fightCompositeMapper.getFightCompositeById(compositeId);
        fightComposite.setCompositeClueRelation(fightComposite1.getCompositeClueRelation());
        fightComposite.setEventRelation(fightComposite1.getEventRelation());
        fightComposite.setUserRelation(fightComposite1.getUserRelation());
        fightComposite.setCaseTagRelation(fightComposite1.getCaseTagRelation());
        fightSendDwdMessageHelper.sendDwdMessage(fightComposite, DWD_HECHENG_INFO, "update", "composite_id",
                (map) -> {
                    map.put("status", fightComposite.getStatus().getCode());
                    map.put("attachments", JSON.toJSONString(fightComposite.getAttachments()));
                });
        // 如果预案id改了，发送启动预案的消息
        if (!Objects.equals(planId, createCompositeVO.getPlanId()) && Objects.nonNull(createCompositeVO.getPlanId())) {
            fightMessageHelper.changePlan(createCompositeVO.getId(), createCompositeVO, AuthHelper.getCurrentUser());
        }
    }

    @Override
    public List<String> getCompositeOperation(Long id) {
        final CurrentUser currentUser = AuthHelper.getCurrentUser();
        if (Objects.isNull(currentUser)) {
            throw new TRSException(ExceptionMessageConstant.CANT_FIND_CURRENT_USER);
        }
        final FightCompositeUserRelation userRole = fightCompositeUserRelationMapper.findByCompositeIdAndUserIdAndDeptId(id, currentUser.getId(), currentUser.getDeptId());
        final FightComposite fightComposite = fightCompositeMapper.selectById(id);
        return Objects.nonNull(userRole)
                ? FightOperationConstant.getOperationByCompositeStatusAndRole(fightComposite.getStatus(), userRole.getRole())
                : Collections.emptyList();
    }


    @Override
    public void initiateComposite(Long id) {
        FightComposite fightComposite = fightCompositeMapper.selectById(id);
        if (Objects.isNull(fightComposite)) {
            throw new TRSException(String.format("合成:%s,不存在！", id));
        }
        CompositeType type = CompositeType.ofCode(fightComposite.getCompositeType()).orElse(CompositeType.COMPOSITE);
        CompositeStatusEnum status = fightComposite.getStatus();
        if (status != CompositeStatusEnum.REJECTED && status != CompositeStatusEnum.CANCELED) {
            throw new TRSException(String.format("合成:%s,不能提交！", id));
        }
        fightComposite.setStatus(compositeApprovalService.getDefaultStatue(type));
        fightCompositeMapper.updateById(fightComposite);
        //新建审批
        compositeApprovalService.createCompositeApproval(id, fightComposite, type);
    }

    @Override
    public CompositeEditInfoVO compositeEditValue(Long id) {
        CompositeDetailVO compositeDetail = getCompositeDetail(id);
        CompositeEditInfoVO result = new CompositeEditInfoVO();
        BeanUtil.copyPropertiesIgnoreNull(compositeDetail, result);
        FightComposite fightComposite = fightCompositeMapper.selectById(id);
        result.setType(fightComposite.getType());
        result.setSubType(fightComposite.getSubType());
        result.setCreateDeptId(fightComposite.getCreateDeptId());
        result.setIsBuildGovernmentChatGroup(Objects.nonNull(fightComposite.getGovWxGroupChatId())
                ? CompositeConstant.YES : CompositeConstant.NO);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setClassicCase(ClassicCaseDto classicCase) {
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        if (Objects.isNull(currentUser)) {
            throw new TRSException(ExceptionMessageConstant.CANT_FIND_CURRENT_USER);
        }
        Preconditions.checkArgument(StringUtils.isNotEmpty(classicCase.getIds()), "合成id不能为空");
        List<String> compositeIds = Arrays.stream(classicCase.getIds().split(",")).collect(Collectors.toList());
        List<FightComposite> fightComposites = fightCompositeMapper.selectBatchIds(compositeIds);
        if (CollectionUtils.isEmpty(fightComposites)) {
            throw new TRSException(String.format("合成作战不存在！"));
        }
        Map<Boolean, List<FightComposite>> map = fightComposites.stream().collect(Collectors.groupingBy(FightComposite::getIsClassicCase));
        // 先更新合成作战表经典案例状态
        fightComposites.forEach(fight -> fight.setIsClassicCase(classicCase.getIsClassicCase()));
        fightCompositeService.updateBatchById(fightComposites);
        // 设置经典案例状态
        if (classicCase.getIsClassicCase()) {
            for (Boolean aBoolean : map.keySet()) {
                List<FightComposite> fightCompositeList = map.get(aBoolean);
                List<Long> compositeIdList = fightCompositeList.stream().map(FightComposite::getId).collect(Collectors.toList());
                if (aBoolean) {
                    // 已经是经典案例，需要更新关联表
                    QueryWrapper<FightCompositeCommentRelation> queryWrapper = new QueryWrapper<FightCompositeCommentRelation>().in("composite_id", compositeIdList);
                    List<FightCompositeCommentRelation> updateList = fightCompositeCommentRelationMapper.selectList(queryWrapper);
                    updateList.forEach(e -> e.setRecommendedComment(classicCase.getComment()));
                    fightCompositeCommentService.updateBatchById(updateList);
                } else {
                    // 不是经典案例,需要插入关联表
                    List<FightCompositeCommentRelation> insertList = compositeIdList.stream().map(id -> {
                        FightCompositeCommentRelation commentRelation = new FightCompositeCommentRelation();
                        commentRelation.fillAuditFields(currentUser);
                        commentRelation.setCompositeId(id);
                        commentRelation.setRecommendedComment(classicCase.getComment());
                        return commentRelation;
                    }).collect(Collectors.toList());
                    fightCompositeCommentService.saveBatch(insertList);
                }
            }
        } else {
            // 取消经典案例状态,删除关联表数据
            QueryWrapper<FightCompositeCommentRelation> queryWrapper = new QueryWrapper<FightCompositeCommentRelation>().in("composite_id", compositeIds);
            fightCompositeCommentRelationMapper.delete(queryWrapper);
        }

    }

    @Override
    public List<ClassicCaseTypeVO> getClassicCaseTypes(Long id) {
        Preconditions.checkArgument(Objects.nonNull(id), "合成id不能为空");
        return fightCompositeMapper.getClassicCaseTypeByCompositeId(id);
    }

    @Override
    public PageResult<ClassicCaseCompositeListVO> getClassicCases(String caseType, PageParams pageParams) {
        final ProfileService profileService = BeanUtil.getBean(ProfileService.class);
        List<Long> caseEventIds = profileService.getCaseEventIdsByCaseType(caseType);
        if (CollectionUtils.isEmpty(caseEventIds)) {
            return PageResult.empty(pageParams.getPageNumber(), pageParams.getPageSize());
        }
        Page<FightComposite> page = fightCompositeMapper.getClassicCaseByCaesEventIds(caseEventIds, pageParams.toPage());
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return PageResult.empty(pageParams.getPageNumber(), pageParams.getPageSize());
        }
        List<ClassicCaseCompositeListVO> compositeListVos = page.getRecords().stream().map(fightComposite -> {
            final ClassicCaseCompositeListVO vo = new ClassicCaseCompositeListVO();
            BeanUtil.copyPropertiesIgnoreNull(fightComposite, vo);
            final CompositeInfoVO compositeInfoVO = CompositeInfoVO.toVO(fightComposite);
            BeanUtil.copyPropertiesIgnoreNull(compositeInfoVO, vo);
            return vo;
        }).collect(Collectors.toList());
        // 设置推荐评语
        List<Long> compositeIds = compositeListVos.stream().map(ClassicCaseCompositeListVO::getId).collect(Collectors.toList());
        QueryWrapper<FightCompositeCommentRelation> queryWrapper = new QueryWrapper<FightCompositeCommentRelation>()
                .in("composite_id", compositeIds);
        List<FightCompositeCommentRelation> fightCompositeCommentRelations = fightCompositeCommentRelationMapper.selectList(queryWrapper);
        Map<Long, List<FightCompositeCommentRelation>> map = fightCompositeCommentRelations.stream()
                .collect(Collectors.groupingBy(FightCompositeCommentRelation::getCompositeId));
        compositeListVos.forEach(vo -> {
            List<FightCompositeCommentRelation> commentRelationList = map.get(vo.getId());
            if (!CollectionUtils.isEmpty(commentRelationList)) {
                commentRelationList = commentRelationList.stream()
                        .sorted(Comparator.comparing(AbstractBaseEntity::getCreateTime).reversed())
                        .collect(Collectors.toList());
                vo.setRecommendedComment(commentRelationList.get(0).getRecommendedComment());
            }
            // 设置办案时间(作战创建时间到归档时间)
            List<String> timeList = new ArrayList<>();
            timeList.add(vo.getFightCreateTime());
            if (vo.getStatus().equals(CompositeStatusEnum.ARCHIVED.getCode())) {
                timeList.add(vo.getFightUpdateTime());
            }
            vo.setHandleCaseTime(timeList);
        });
        return PageResult.of(compositeListVos, pageParams.getPageNumber(), page.getTotal(), pageParams.getPageSize());
    }

    /**
     * 获取线索档案或线索池的相关协同列表
     *
     * @param clueId     合成
     * @param dataSource 分页参数
     * @param pageParams 分页参数
     * @return {@link PageResult}<{@link ClassicCaseCompositeListVO}>
     */
    @Override
    public PageResult<ClassicCaseCompositeListVO> getClueCompositeList(Long clueId, Integer dataSource, PageParams pageParams) {
        Preconditions.checkNotNull(clueId, "线索id不能为空");
        List<Long> compositeIds = fightCompositeClueRelationService.getRelationListByClueId(clueId, dataSource);
        if (CollectionUtils.isEmpty(compositeIds)) {
            return PageResult.of(new ArrayList<>(), 1, 0L, pageParams.getPageSize());
        }
        Page<FightComposite> page = fightCompositeMapper.selectPage(pageParams.toPage(), new QueryWrapper<FightComposite>()
                .in("id", compositeIds)
                .orderByDesc("create_time"));
        List<ClassicCaseCompositeListVO> listVoList = page.getRecords().stream()
                .map(data -> {
                    ClassicCaseCompositeListVO vo = new ClassicCaseCompositeListVO();
                    vo.setTitle(data.getTitle());
                    vo.setId(data.getId());
                    vo.setFightCreateTime(DateUtil.dateTimeToString(data.getCreateTime()));
                    return vo;
                }).collect(Collectors.toList());
        return PageResult.of(listVoList, (int) page.getPages(), page.getTotal(), (int) page.getSize());
    }

    @Override
    public FightComposite getFightCompositeById(Long compositeId) {
        PreConditionCheck.checkArgument(Objects.nonNull(compositeId), "合成id不能为空");
        return fightCompositeMapper.getFightCompositeById(compositeId);
    }

    @Override
    public void checkDisplayMapStatus(Long id, Boolean isDisplayMap) {
        PreConditionCheck.checkArgument(Objects.nonNull(id), "处突id不能为空");
        FightComposite fightComposite = fightCompositeMapper.selectById(id);
        if (Objects.nonNull(fightComposite)) {
            fightComposite.setIsDisplayMap(isDisplayMap);
            fightCompositeMapper.updateById(fightComposite);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(String compositeId) {
        if (com.trs.common.utils.StringUtils.isEmpty(compositeId)) {
            return;
        }
        List<Long> ids = Stream.of(compositeId.split(",|;"))
                .filter(com.trs.common.utils.StringUtils::isNotEmpty)
                .map(Long::valueOf)
                .distinct()
                .collect(Collectors.toList());
        ids.forEach(id -> {
            FightComposite byId = fightCompositeService.getById(id);
            CompositeStatusEnum status = byId.getStatus();
            if (CompositeStatusEnum.CANCELED.equals(status)) {
                fightCompositeService.lambdaUpdate()
                        .eq(FightComposite::getId, id)
                        .set(FightComposite::getIsDel, 1)
                        .update();
            } else {
                throw new TRSException(String.format("状态：%s无法删除", status.getName()));
            }
        });
    }

    @Override
    public List<CompositeUserInfo> getCompositePartner(CompositePartnerDTO dto) {
        // 通过指定类型获取作战人员
        if (Objects.nonNull(dto.getType())) {
            ICompositePartnerService service = compositePartnerFactory.getCompositePartnerService(CompositePartnerWay.codeOf(dto.getType()));
            PreConditionCheck.checkArgument(Objects.nonNull(service), "该方式还未配置");
            List<CompositeUserInfo> userInfos = service.getCompositePartner(dto);
            // 添加默认主理人
            addDefaultOrganizer(userInfos);
            // 进行排重
            List<CompositeUserInfo> results = userInfos.stream()
                    .collect(Collectors.toMap(
                            CompositeUserInfo::getUserId,
                            Function.identity(),
                            (v1, v2) -> CompositeRoleEnum.ORGANIZER.getCode().equals(v1.getRole()) ? v1 : v2
                    ))
                    .values()
                    .stream().collect(Collectors.toList());
            // 设置其他信息
            setOtherInfo(results, dto.getCompositeId());
            return results;
        }
        // 获取默认的全部作战人员
        List<CompositeUserInfo> compositeUserInfos = new ArrayList<>();
        for (ICompositePartnerService service : compositePartnerFactory.getAllCompositePartnerService()) {
            List<CompositeUserInfo> compositePartners = service.getCompositePartner(dto);
            compositeUserInfos.addAll(compositePartners);
        }
        // 增加默认主理人
        addDefaultOrganizer(compositeUserInfos);
        // 设置唯一的主理人
        setUniqueOrganizer(compositeUserInfos);
        // 进行排重
        List<CompositeUserInfo> results = compositeUserInfos.stream()
                .collect(Collectors.toMap(
                        CompositeUserInfo::getUserId,
                        Function.identity(),
                        (v1, v2) -> CompositeRoleEnum.ORGANIZER.getCode().equals(v1.getRole()) ? v1 : v2
                ))
                .values()
                .stream().collect(Collectors.toList());
        // 设置其他信息
        setOtherInfo(results, dto.getCompositeId());
        return results;
    }

    @Override
    public void updateCompositeField(Long id, String key, String value) {
        FightComposite fightComposite = fightCompositeMapper.selectById(id);
        PreConditionCheck.checkArgument(Objects.nonNull(fightComposite), String.format("id=%s的作战不存在!", id));
        try {
            Field field = fightComposite.getClass().getDeclaredField(key);
            field.setAccessible(true);
            field.set(fightComposite, value);
            fightCompositeMapper.updateById(fightComposite);
        } catch (Exception e) {
            log.error("更新id={}的作战{}字段失败：", id, key, e);
        }
    }

    @Override
    public RestfulResultsV2 dealCompositeInvite(Integer compositeId, Integer operate) {
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        if (Objects.isNull(currentUser)) {
            return RestfulResultsV2.error("当前登录用户不存在");
        }
        List<FightCompositeUserRelation> list = fightCompositeUserRelationMapper
                .selectList(new QueryWrapper<FightCompositeUserRelation>()
                        .eq("composite_id", compositeId).eq("user_id", currentUser.getId())
                        .eq("dept_id", currentUser.getDeptId()));
        if (CollectionUtils.isEmpty(list)) {
            return RestfulResultsV2.error("该作战参与人员不包含当前用户");
        }
        FightCompositeUserRelation fightCompositeUserRelation = list.get(0);
        fightCompositeUserRelation.setReceiveStatus(operate);
        fightCompositeUserRelation.setUpdateTime(LocalDateTime.now());
        fightCompositeUserRelation.setUpdateUserId(currentUser.getId());
        fightCompositeUserRelation.setUpdateDeptId(currentUser.getDeptId());
        fightCompositeUserRelationMapper.updateById(fightCompositeUserRelation);
        return RestfulResultsV2.ok("操作成功");
    }

    private PlanEntity getPlan(Long planId) {
        if (Objects.isNull(planId)) {
            return new PlanEntity();
        }
        PlanEntity planEntity = planMapper.selectById(planId);
        return Objects.isNull(planEntity) ? new PlanEntity() : planEntity;
    }

    private void sendApprovalNotice(FightComposite fightComposite, ApprovalActionVO action) {
        try {
            final FightMessageModule module = messageModuleMapper.selectOne(new QueryWrapper<FightMessageModule>().eq("table_name", "t_fight_composite"));
            final Long moduleId = Optional.ofNullable(module).map(FightMessageModule::getId).orElse(null);
            SimpleUserVO sender = permissionService.findSimpleUser(action.getOperateUserId(), action.getOperateUserDeptId());
            SimpleUserVO receiver = permissionService.findSimpleUser(fightComposite.getCreateUserId(), fightComposite.getCreateDeptId());
            final Boolean sendSms = BeanFactoryHolder.getEnv().getProperty("ys.fight.fight.sendsms", Boolean.class, false);
            log.info("作战短信开关：{}", sendSms);
            // 审批通过的消息
            NoticeVO noticeVO = NoticeUtil.generateNoticeVO(
                    sender,
                    receiver,
                    ApprovalStatusEnum.REJECTED.equals(action.getApprovalStatus()) ? COMPOSITE_REJECTED : COMPOSITE_APPROVED,
                    LocalDateTime.now(),
                    fightComposite.getId(),
                    sender.getUserName(), sender.getDeptName(), fightComposite.getTitle()
            );
            JSONObject object = new JSONObject();
            object.put(REDIRECT_MODULE, APPROVAL_DETAIL);
            object.put(APPROVAL_ID, action.getApprovalId());
            noticeVO.setOtherMsg(object.toJSONString());
            noticeVO.setSendSms(sendSms);
            noticeVO.setSmsModuleId(moduleId);
            messageService.sendNotice(Arrays.asList(noticeVO));
            // 下个节点的审批者的消息
            if (Objects.isNull(action.getNextApprover()) || action.getNextApprover().isEmpty()) {
                return;
            }
            SimpleUserVO user = permissionService.findSimpleUser(fightComposite.getCreateUserId(), fightComposite.getCreateDeptId());
            List<NoticeVO> approvalNotice = action.getNextApprover()
                    .stream()
                    .map(approvalUser -> permissionService.findSimpleUser(approvalUser.getUserId(), approvalUser.getDeptId()))
                    .map(approvalUser -> {
                                NoticeVO vo = NoticeUtil.generateNoticeVO(
                                        user,
                                        approvalUser,
                                        SystemNoticeEnum.COMPOSITE_INITIATE,
                                        LocalDateTime.now(),
                                        fightComposite.getId(),
                                        user.getUserName(), user.getDeptName(), fightComposite.getTitle());
                                vo.setSendSms(sendSms);
                                noticeVO.setSmsModuleId(moduleId);
                                return vo;
                            }
                    )
                    .collect(Collectors.toList());
            messageService.sendNotice(approvalNotice);
        } catch (Exception e) {
            log.error(String.format("作战：%s发送审批结果用户通知消息失败", fightComposite.getId()), e);
        }
    }

    private void updatePlanLevel(CreateCompositeVO vo) {
        if (Objects.nonNull(vo.getPlanId()) && Objects.nonNull(vo.getPlanLevel())) {
            PlanEntity planEntity = planMapper.selectById(vo.getPlanId());
            PreConditionCheck.checkArgument(Objects.nonNull(planEntity), String.format("id=%s的预案不存在", vo.getPlanId()));
            planEntity.setLevel(vo.getPlanLevel());
            planMapper.updateById(planEntity);
        }
    }

    private void addDefaultOrganizer(List<CompositeUserInfo> userInfoList) {
        final List<CompositeUserInfo> organizers = userInfoList.stream()
                .filter(user -> user.getRole().equals(CompositeRoleEnum.ORGANIZER.getCode()))
                .collect(Collectors.toList());
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        CompositeUserInfo userInfo = new CompositeUserInfo();
        userInfo.setUserId(currentUser.getId());
        userInfo.setUserName(currentUser.getRealName());
        userInfo.setDeptId(currentUser.getDeptId());
        userInfo.setDeptCode(currentUser.getDeptCode());
        userInfo.setDeptName(currentUser.getDept().getName());
        userInfo.setDeptShortName(currentUser.getDept().getShortName());
        if (CollectionUtils.isEmpty(organizers)) {
            userInfo.setRole(CompositeRoleEnum.ORGANIZER.getCode());
        } else {
            userInfo.setRole(CompositeRoleEnum.ASSISTANT.getCode());
        }
        userInfoList.add(userInfo);
    }

    private void setUniqueOrganizer(List<CompositeUserInfo> userInfoList) {
        // 设置唯一的主理人 如果有多个主理人 则以作战类型获取的主理人为主
        List<CompositeUserInfo> organizers = userInfoList.stream().filter(user -> user.getRole().equals(CompositeRoleEnum.ORGANIZER.getCode()))
                .collect(Collectors.toList());
        if (organizers.size() > 1) {
            for (CompositeUserInfo organizer : organizers) {
                if (!"type".equals(organizer.getSource())) {
                    organizer.setRole(CompositeRoleEnum.ASSISTANT.getCode());
                }
            }
        }

    }

    private void setOtherInfo(List<CompositeUserInfo> userInfoList, Long compositeId) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(userInfoList)) {
            return;
        }
        Map<Long, DictDto> postMap = dictService.getDictByType("post_type").stream()
                .collect(Collectors.toMap(DictDto::getCode, Function.identity(), (v1, v2) -> v1));
        List<Long> userIds = userInfoList.stream()
                .map(CompositeUserInfo::getUserId)
                .collect(Collectors.toList());
        Map<Long, UserDto> userMap = permissionService.getUserListById(userIds).stream()
                .collect(Collectors.toMap(UserDto::getId, Function.identity(), (v1, v2) -> v1));
        List<String> todayDutyUsers = dutyUserHelper.getTodayDutyUser(postMap);
        // 查询作战已参与人员
        List<FightCompositeUserRelation> fightCompositeUserRelations = fightCompositeUserRelationMapper.selectList(new QueryWrapper<FightCompositeUserRelation>()
                .eq("composite_id", compositeId)
                .ne("receive_status", CompositeMembersStatusEnum.REFUSED.getCode()));
        List<String> collect = fightCompositeUserRelations.stream()
                .map(e -> String.format("%s-%s", e.getDeptId(), e.getUserId()))
                .collect(Collectors.toList());

        userInfoList.forEach(userInfo -> {
            Long postCode = userMap.get(userInfo.getUserId()).getPostCode();
            DictDto dictDto = postMap.get(postCode);
            userInfo.setPostName(Objects.isNull(dictDto) ? "" : dictDto.getName());
            if (todayDutyUsers.contains(String.format("%s-%s", userInfo.getDeptId(), userInfo.getUserId()))) {
                userInfo.setZbStatus(1);
            }
            if (collect.contains(String.format("%s-%s", userInfo.getDeptId(), userInfo.getUserId()))) {
                userInfo.setParticipated(1);
            }
        });
    }

    private void sendFightSmsIfCould(PhoneMessageVO message) {
        Boolean sendSms = BeanFactoryHolder.getEnv().getProperty("ys.fight.fight.sendsms", Boolean.class, false);
        if (Boolean.TRUE.equals(sendSms)) {
            FightMessageModule module = messageModuleMapper.selectOne(new QueryWrapper<FightMessageModule>().eq("table_name", "t_fight_composite"));
            message.setModuleId(module.getId());
            if (null != message.getPhoneNumbers() && message.getPhoneNumbers().length > 0) {
                messageService.sendPhoneMessage(message);
            }
        }
    }
}
