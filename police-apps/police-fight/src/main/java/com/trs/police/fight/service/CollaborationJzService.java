package com.trs.police.fight.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.entity.Collaboration;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.utils.RemoteAddrUtil;
import com.trs.police.common.core.vo.profile.JqCommonVO;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.common.openfeign.starter.service.ProfileService;
import com.trs.police.fight.domain.bean.JqAssistancePerson;
import com.trs.police.fight.domain.entity.CollaborationJq;
import com.trs.police.fight.mapper.CollaborationJqMapper;
import com.trs.police.fight.mapper.CollaborationMapper;
import com.trs.police.fight.properties.FeedbackRefreshProperties;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

/**
 * 协作jz服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class CollaborationJzService {

    @Autowired
    private FeedbackRefreshProperties feedbackRefreshProperties;

    @Autowired
    private CollaborationTimeAxisService collaborationTimeAxisService;

    @Resource
    private CollaborationMapper collaborationMapper;

    @Resource
    private PermissionService permissionService;

    @Autowired
    private CollaborationSceneRelationService collaborationSceneRelationService;

    @Autowired
    private ProfileService profileService;

    @Resource
    private CollaborationJqMapper collaborationJqMapper;

    /**
     * 构造参数
     *
     * @param collaborationId 协作id
     * @param sjpc 数据批次
     * @param cxdh 查询电话
     * @return 请求jz的参数
     */
    public Map<String, Object> buildParamsMap(Long collaborationId, String sjpc, String cxdh) {
        final Map<String, Object> bodyMap = new HashMap<>();
        // 查询系统信息 systemInfoJson
        JSONObject systemInfo = new JSONObject();
        systemInfo.put("systemCode", feedbackRefreshProperties.getSystemCode());
        systemInfo.put("systemName", feedbackRefreshProperties.getSystemName());
        systemInfo.put("ip", feedbackRefreshProperties.getIp());
        systemInfo.put("port", feedbackRefreshProperties.getPort());
        bodyMap.put("systemInfoJson", systemInfo);
        // 查询人员信息 userInfoJson
        JSONObject userInfo = new JSONObject();
        CurrentUser user = getUserInfo(collaborationId);
        userInfo.put("userID", user.getIdNumber());
        userInfo.put("userName", user.getRealName());
        String userIp = Try.of(() -> RemoteAddrUtil.getIp())
                .onFailure(e -> log.error("获取ip失败", e))
                .getOrElse(feedbackRefreshProperties.getIp());
        userIp = StringUtils.isEmpty(userIp) ? feedbackRefreshProperties.getIp() : userIp;
        userInfo.put("userIp", StringUtils.isEmpty(userIp) ? feedbackRefreshProperties.getUserIp() : userIp);
        userInfo.put("orgID", user.getDeptCode());
        userInfo.put("orgName", user.getDept().getShortName());
        userInfo.put("lastSpr", lastSpr(collaborationId).orElse("null"));
        bodyMap.put("userInfoJson", userInfo);
        // 查询关联aj信息 caseInfoJson
        JSONObject caseInfo = new JSONObject();
        bodyMap.put("caseInfoJson", caseInfo);
        // 获取aj信息
        Collaboration collaboration = collaborationMapper.selectById(collaborationId);
        CollaborationJq collaborationJq = collaborationJqMapper.selectOne(
                Wrappers.lambdaQuery(CollaborationJq.class)
                        .eq(CollaborationJq::getCollaborationId, collaborationId)
        );
        JqCommonVO jq = profileService.findByBh(collaborationJq.getJqbh());
        caseInfo.put("caseID", jq.getJjdbh());
        caseInfo.put("caseName", collaboration.getTitle());
        caseInfo.put("caseType", feedbackRefreshProperties.getCaseType());
        caseInfo.put("typeName", feedbackRefreshProperties.getTypeName());
        caseInfo.put("lastSpr", lastSpr(collaborationId).orElse("null"));
        bodyMap.put("caseInfoJson", caseInfo);
        // 提请表相关信息 tqspInfoJson
        String tqspInfoXml = buildTqspInfoXml(collaborationId, cxdh);
        bodyMap.put("tqspInfoJson", tqspInfoXml);
        // 自动调用内容 xmlContent
        JSONObject xmlInfo = new JSONObject();
        xmlInfo.put("missionId", UUID.randomUUID().toString());
        xmlInfo.put("jobId", feedbackRefreshProperties.getJobId());
        xmlInfo.put("sjpc", sjpc);
        xmlInfo.put("police", feedbackRefreshProperties.getPolice());
        bodyMap.put("xmlContent", xmlInfo);
        return bodyMap;
    }

    private CurrentUser getUserInfo(Long collaborationId) {
        // 获取协作发起人信息
        Collaboration collaboration = collaborationMapper.selectById(collaborationId);
        CurrentUser currentUser = permissionService.findCurrentUser(collaboration.getCreateUserId(), collaboration.getCreateDeptId());
        return currentUser;
    }

    /**
     * 构造提请审批信息的XML格式内容
     *
     * @param collaborationId 协作id
     * @param cxdh 查询电话
     * @return XML格式的提请审批信息
     */
    private String buildTqspInfoXml(Long collaborationId, String cxdh) {
        Collaboration collaboration = collaborationMapper.selectById(collaborationId);
        try (InputStream resourceAsStream = this.getClass().getClassLoader().getResourceAsStream("jzNotice.xml")) {
            String xmlString = IOUtils.toString(resourceAsStream);
            // wsbh ga机关有关单位填写TQ+提请单位ga机关代码前六位+年份+从000001开始的六位流水编号，如TQ1100002025000002
            xmlString = xmlString.replaceAll("p_wsbh", collaboration.getFormNumber());
            // 区域id
            CurrentUser createUser = permissionService.findCurrentUser(collaboration.getCreateUserId(), collaboration.getCreateDeptId());
            xmlString = xmlString.replace("p_areaid", createUser.getDept().getDistrictCode());
            // 提请单位+提请部门 例如:成都市ga局jd大队
            xmlString = xmlString.replace("p_cxdwmc", createUser.getDept().getName());
            // 提请人姓名
            xmlString = xmlString.replace("p_lxr", createUser.getRealName());
            // 提请人联系电话
            xmlString = xmlString.replace("p_lxdh", createUser.getMobile());
            // 提请人身份证
            xmlString = xmlString.replace("p_zjhm", createUser.getIdNumber());
            // 填表日期 yyyy-MM-dd
            LocalDateTime tbrq = collaboration.getCreateTime();
            xmlString = xmlString.replace("p_tbrq", tbrq.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            // 获取aj信息
            CollaborationJq collaborationJq = collaborationJqMapper.selectOne(
                    Wrappers.lambdaQuery(CollaborationJq.class)
                            .eq(CollaborationJq::getCollaborationId, collaboration.getId())
            );
            JqCommonVO jq = profileService.findByBh(collaborationJq.getJqbh());
            // aj编号
            xmlString = xmlString.replace("p_ajbh", jq.getJjdbh());
            // 填表单位id
            xmlString = xmlString.replace("p_tbdwid", createUser.getDeptCode());
            // 填表单位名称
            xmlString = xmlString.replace("p_tbdwmc", createUser.getDept().getName());
            // 填表人id
            xmlString = xmlString.replace("p_writemanid", createUser.getIdNumber());
            // 填表人 提请人姓名+(jh)  例如:张三(NO12132)
            xmlString = xmlString.replace("p_writeman", createUser.getRealName() + "(" + createUser.getPoliceCode() + ")");
            // 填写日期 yyyy-MM-dd hh24:mi:ss
            xmlString = xmlString.replace("p_writedate", tbrq.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            // 查询名称(asj名称)
            xmlString = xmlString.replace("p_cxmc", collaboration.getTitle());
            // asj名称
            xmlString = xmlString.replace("p_ajmc", collaboration.getTitle());
            // 填表人身份证号码
            xmlString = xmlString.replace("p_writemanidcard", createUser.getIdNumber());
            // 最后审批人姓名
            xmlString = xmlString.replace("p_lastspr", lastSpr(collaborationId).orElse("null"));

            // 查询号码
            xmlString = xmlString.replace("p_cxdh", cxdh);
            // 查询对象
            String s = JSONArray.parseArray(collaborationJq.getAssistancePerson(), JqAssistancePerson.class)
                    .stream()
                    .filter(p -> p.getTel().equals(cxdh))
                    .findFirst()
                    .map(JqAssistancePerson::getName)
                    .orElse("");
            xmlString = xmlString.replace("p_cxdx", s);
            // 开始时间
            xmlString = xmlString.replaceAll("p_kssj", collaboration.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            // 结束时间
            xmlString = xmlString.replaceAll("p_jssj", collaboration.getCreateTime().plusDays(3).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            return xmlString;
        } catch (Exception e) {
            log.error("构造请求xml失败", e);
            throw new TRSException("构造请求xml失败");
        }
    }

    private Optional<String> lastSpr(Long collaborationId) {
        return Optional.ofNullable(collaborationTimeAxisService.getLastApprovalUser(collaborationId));
    }
}
