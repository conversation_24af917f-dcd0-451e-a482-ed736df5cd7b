package com.trs.police.fight.task.analysis.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.DistrictDto;
import com.trs.police.common.core.dto.UserDto;
import com.trs.police.common.core.entity.FightCompositeUserRelation;
import com.trs.police.common.core.vo.Dict2VO;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.fight.constant.enums.FightResultRelatedType;
import com.trs.police.fight.domain.entity.ClueEntity;
import com.trs.police.fight.domain.entity.CluePoolAnalysisResult;
import com.trs.police.fight.domain.entity.FightCompositeClueRelation;
import com.trs.police.fight.domain.entity.FightResult;
import com.trs.police.fight.mapper.ClueEntityMapper;
import com.trs.police.fight.mapper.FightCompositeClueRelationMapper;
import com.trs.police.fight.service.FightCompositeUserRelationService;
import com.trs.police.fight.service.impl.FightResultMpServiceImpl;
import com.trs.police.fight.task.analysis.AbsClueAnalysis;
import com.trs.police.fight.task.analysis.IClueItemAnalysis;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: dingkeyu
 * @date: 2024/03/26
 * @description: 个人统计
 */
@Component
public class PersonalClueAnalysis extends AbsClueAnalysis<JSONObject> {

    @Autowired
    private List<IClueItemAnalysis> iClueItemAnalyses;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private DictService dictService;

    @Autowired
    private FightCompositeClueRelationMapper fightCompositeClueRelationMapper;

    @Autowired
    private FightCompositeUserRelationService fightCompositeUserRelationService;

    @Autowired
    private FightResultMpServiceImpl fightResultMpService;

    @Autowired
    private ClueEntityMapper clueEntityMapper;

    @Override
    public Function<ClueEntity, JSONObject> dimensionFunc() {
        return (ClueEntity clueEntity) -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("userName", clueEntity.getReportUserTrueName());
            jsonObject.put("deptId", clueEntity.getReportDeptId());
            return jsonObject;
        };
    }

    @Override
    public List<ClueEntity> filterClueEntities(List<ClueEntity> clueEntities, String date, JSONObject dimension) {
        return clueEntities.stream()
                .filter((clue) -> Objects.equals(clue.getCollectUser(), dimension.getString("userName"))
                        && date.equals(clue.getReportTime().format(DateTimeFormatter.ofPattern("yyyyMMdd"))))
                .collect(Collectors.toList());
    }

    @Override
    public List<JSONObject> dimensions(List<ClueEntity> clueEntities) {
        // 过滤掉采集人为空的历史数据
        return clueEntities.stream().filter(e -> Objects.nonNull(e.getCollectDeptId())).map(e -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("userName", e.getCollectUser());
            jsonObject.put("realName", e.getCollectUserTrueName());
            jsonObject.put("deptId", e.getCollectDeptId());
            return jsonObject;
        }).distinct().collect(Collectors.toList());
    }

    @Override
    public CluePoolAnalysisResult createCluePoolAnalysisResult(JSONObject dimension) {
        CluePoolAnalysisResult cluePoolAnalysisResult = new CluePoolAnalysisResult();
        cluePoolAnalysisResult.setUserName(dimension.getString("realName"));
        cluePoolAnalysisResult.setUName(dimension.getString("userName"));
        cluePoolAnalysisResult.setDeptId(dimension.getLong("deptId"));
        cluePoolAnalysisResult.setSceneType(getSceneType());
        return cluePoolAnalysisResult;
    }

    @Override
    public List<IClueItemAnalysis> clueItemAnalyses() {
        return iClueItemAnalyses;
    }

    @Override
    public String uniqueKey(CluePoolAnalysisResult cluePoolAnalysisResult) {
        // cluePersonalAnalysis_20240613_1860_104 唯一值再加上部门，存在同一个人有多个部门的情况
        return String.format("%s_%s_%s_%s",
                getSceneType(),
                TimeUtils.dateToString(cluePoolAnalysisResult.getServiceTime(), TimeUtils.YYYYMMDD5),
                cluePoolAnalysisResult.getUserId(),
                cluePoolAnalysisResult.getDeptId());
    }

    @Override
    public void completeCluePoolAnalysisResult(List<CluePoolAnalysisResult> cluePoolAnalysisResults) {
        if (!CollectionUtils.isEmpty(cluePoolAnalysisResults)) {
            // 反查user
            List<String> usernames = cluePoolAnalysisResults.stream()
                    .map(CluePoolAnalysisResult::getUName)
                    .distinct()
                    .collect(Collectors.toList());
            List<UserDto> userDtos = CollectionUtils.isEmpty(usernames) ? new ArrayList<>() : permissionService.getUserByUserNames(usernames);
            Map<String, UserDto> userDtoMap = userDtos.stream().collect(Collectors.toMap(UserDto::getUsername, e -> e, (v1, v2) -> v2));
            // 反查dept
            List<Long> deptIds = cluePoolAnalysisResults.stream().map(CluePoolAnalysisResult::getDeptId).collect(Collectors.toList());
            List<DeptDto> deptDtos = CollectionUtils.isEmpty(deptIds) ? new ArrayList<>() : permissionService.getDeptByIds(deptIds);
            Map<Long, DeptDto> deptDtoMap = deptDtos.stream().collect(Collectors.toMap(DeptDto::getId, e -> e, (v1, v2) -> v2));
            // 反查area
            List<Object> districtCodes = deptDtos.stream()
                    .map(DeptDto::getDistrictCode)
                    .map(str -> (Object) str)
                    .collect(Collectors.toList());
            List<DistrictDto> districtDtos = CollectionUtils.isEmpty(districtCodes) ? new ArrayList<>() : dictService.getByDistrictCodes(districtCodes);
            Map<String, DistrictDto> districtDtoMap = districtDtos.stream().collect(Collectors.toMap(DistrictDto::getCode, e -> e, (v1, v2) -> v2));
            // 反查policeKind
            List<Dict2VO> policeKinds = dictService.commonSearch("police_kind", null, null,null);
            Map<Long, String> policeKindMap = policeKinds.stream().collect(Collectors.toMap(Dict2VO::getCode, Dict2VO::getName));
            cluePoolAnalysisResults.forEach(e -> {
                UserDto userDto = userDtoMap.get(e.getUName());
                if (Objects.nonNull(userDto)) {
                    e.setUserId(Math.toIntExact(userDto.getId()));
                }
                DeptDto deptDto = deptDtoMap.get(e.getDeptId());
                if (Objects.nonNull(deptDto)) {
                    e.setDeptName(deptDto.getName());
                    e.setAreaCode(deptDto.getDistrictCode());
                    e.setPoliceKind(deptDto.getPoliceKind());
                    e.setPoliceKindDesc(policeKindMap.get(e.getPoliceKind()));
                    e.setDeptType(deptDto.getType());
                    e.setDeptCode(deptDto.getCode());
                }
                DistrictDto districtDto = districtDtoMap.get(e.getAreaCode());
                if (Objects.nonNull(districtDto)) {
                    e.setAreaCodeDesc(districtDto.getName());
                }
                // 构建唯一key
                e.setUniqueKey(uniqueKey(e));
            });
        }

    }

    @Override
    public List<Long> getFightResultIdsByClue(List<Long> clueIds, JSONObject dimension) {
        if (CollectionUtils.isEmpty(clueIds)) {
            return new ArrayList<>();
        }
        QueryWrapper<FightCompositeClueRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("clue_id", clueIds);
        List<Long> compositeIds = fightCompositeClueRelationMapper.selectList(queryWrapper).stream()
                .map(FightCompositeClueRelation::getCompositeId)
                .collect(Collectors.toList());
        List<Long> idList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(compositeIds)) {
            List<UserDto> userDtos = permissionService.getUserByUserNames(Arrays.asList(dimension.getString("userName")));
            List<Long> dimensionCompositeIds = fightCompositeUserRelationService.lambdaQuery()
                    .in(FightCompositeUserRelation::getCompositeId, compositeIds)
                    .eq(FightCompositeUserRelation::getUserId, userDtos.get(0).getId())
                    .list().stream().map(FightCompositeUserRelation::getCompositeId)
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(dimensionCompositeIds)) {
                // 根据合成id查询战果id
                List<Long> fightResultIds = fightResultMpService.lambdaQuery()
                        .eq(FightResult::getRelatedType, FightResultRelatedType.COMPOSITE.getCode())
                        .in(FightResult::getCompositeId, dimensionCompositeIds)
                        .select(FightResult::getId)
                        .list()
                        .stream().map(FightResult::getId).collect(Collectors.toList());
                idList.addAll(fightResultIds);
            }
        }
        // 根据线索id查询战果id
        List<Long> dimensionClueIds = clueEntityMapper.selectList(new QueryWrapper<ClueEntity>()
                .eq("collect_user", dimension.getString("userName"))
                .in("id", clueIds)).stream().map(ClueEntity::getId)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(dimensionClueIds)) {
            List<Long> fightResultIds = fightResultMpService.lambdaQuery()
                    .eq(FightResult::getRelatedType, FightResultRelatedType.CLUE.getCode())
                    .in(FightResult::getCompositeId, dimensionClueIds)
                    .select(FightResult::getId)
                    .list()
                    .stream().map(FightResult::getId).collect(Collectors.toList());
            idList.addAll(fightResultIds);
        }

        return idList;
    }

    @Override
    public String getSceneType() {
        return "cluePersonalAnalysis";
    }

    @Override
    public String key() {
        return "clue_personal_analysis";
    }

    @Override
    public String desc() {
        return "个人统计";
    }
}
