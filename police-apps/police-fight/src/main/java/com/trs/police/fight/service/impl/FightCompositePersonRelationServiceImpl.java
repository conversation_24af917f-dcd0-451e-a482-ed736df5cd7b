package com.trs.police.fight.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trs.police.common.core.vo.profile.PersonCaseVO;
import com.trs.police.fight.domain.entity.FightCompositePersonRelation;
import com.trs.police.fight.mapper.FightCompositePersonRelationMapper;
import com.trs.police.fight.service.FightCompositePersonRelationService;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/5/29
 */
@Service
public class FightCompositePersonRelationServiceImpl extends ServiceImpl<FightCompositePersonRelationMapper, FightCompositePersonRelation> implements FightCompositePersonRelationService {
    @Override
    public List<PersonCaseVO> getRelationList(Long compositeId) {
        return baseMapper.getRelationList(compositeId);
    }
}
