package com.trs.police.fight.service.impl.CluePoolAnalysis;

import com.trs.police.fight.constant.enums.CluePoolAnalysisOperateEnum;
import com.trs.police.fight.service.AbsCluePoolAnalysisService;
import com.trs.police.fight.utils.ExcelExportUtils;
import org.springframework.stereotype.Service;

/**
 * @author: dingkeyu
 * @date: 2024/03/26
 * @description: 作战单元统计
 */
@Service
public class CombatUnitAnalysis extends AbsCluePoolAnalysisService {

    @Override
    public String getGroupField() {
        return "combatUnitAnalysis";
    }

    @Override
    public CluePoolAnalysisOperateEnum getSceneType() {
        return CluePoolAnalysisOperateEnum.COMBAT_UNIT_ANALYSIS;
    }

    @Override
    protected String getTemplateName() {
        return ExcelExportUtils.TEMPLATE_FIGHT_UNIT_NAME;
    }

    @Override
    protected String getExportFileName() {
        return ExcelExportUtils.TEMPLATE_FIGHT_UNIT_EXPORT_FILENAME;
    }
}
