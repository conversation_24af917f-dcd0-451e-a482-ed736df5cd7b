package com.trs.police.fight.service.impl.CluePoolAnalysis;

import com.trs.police.fight.constant.enums.CluePoolAnalysisOperateEnum;
import com.trs.police.fight.domain.vo.CluePoolAnalysisResultVO;
import com.trs.police.fight.service.AbsPersonalCluePoolAnalysisService;
import com.trs.police.fight.utils.ExcelExportUtils;
import org.springframework.stereotype.Service;

/**
 * @author: dingkeyu
 * @date: 2024/03/26
 * @description: 个人统计
 */
@Service
public class PersonalAnalysisServiceImpl extends AbsPersonalCluePoolAnalysisService {


    @Override
    public String getGroupField() {
        return "user_id";
    }

    @Override
    public CluePoolAnalysisOperateEnum getSceneType() {
        return CluePoolAnalysisOperateEnum.PERSON;
    }

    @Override
    protected String getFilterType(CluePoolAnalysisResultVO data) {
        return data.getUserId().toString();
    }

    @Override
    protected String getExportFileName() {
        return ExcelExportUtils.PERSONAL_STATISTICS_EXPORT_FILENAME;
    }
}
