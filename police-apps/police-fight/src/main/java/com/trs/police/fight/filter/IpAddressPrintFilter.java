package com.trs.police.fight.filter;

import com.trs.police.common.core.utils.RemoteAddrUtil;
import java.io.IOException;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/08/19
 */
@Component
@Order(1)
@Slf4j
public class IpAddressPrintFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
        throws IOException, ServletException {
        HttpServletRequest req = (HttpServletRequest) request;
        String address = RemoteAddrUtil.getRemoteAddress(req);
//        log.info(String.format("remote address: [%s] is querying url: [%s] method is: [%s] header is: [%s]", address,
//            req.getRequestURI(), req.getMethod(), ((HttpServletRequest) request).getHeader("Authorization")));

        chain.doFilter(request, response);
    }
}
