package com.trs.police.fight.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import com.trs.police.common.core.handler.typehandler.JsonToFileInfoHandler;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: dingkeyu
 * @date: 2024/02/28
 * @description: 合成战果表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "t_fight_result",autoResultMap = true)
@AllArgsConstructor
@NoArgsConstructor
public class FightResult extends AbstractBaseEntity {

    /**
     * 合成id
     */
    @TableField(value = "composite_id")
    private Long compositeId;

    /**
     * 抓捕人员
     */
    @TableField(value = "suspect")
    private String suspect;

    /**
     * 案件结案
     */
    @TableField(value = "case_event")
    private String caseEvent;

    /**
     * 附件
     */
    @TableField(typeHandler = JsonToFileInfoHandler.class)
    private List<FileInfoVO> attachments;

    /**
     * 关联的案件
     */
    @TableField(exist = false)
    private String relatedCase;

    /**
     * 关联的其他信息
     */
    @TableField(exist = false)
    private String relatedOtherinfo;

    /**
     * 关联的人
     */
    @TableField(exist = false)
    private String relatedUser;

    /**
     * 关联类型：0-作战，1-线索
     */
    @TableField("related_type")
    private Integer relatedType;
}
