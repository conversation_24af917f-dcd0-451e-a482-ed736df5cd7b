package com.trs.police.fight.service.impl.collaborationTimeAxis;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.police.common.core.constant.ExceptionMessageConstant;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.UserDto;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.fight.constant.enums.CollaborationOperateEnum;
import com.trs.police.fight.converter.CollaborationTimeAxisConvert;
import com.trs.police.fight.domain.entity.CollaborationTimeAxis;
import com.trs.police.fight.domain.vo.CollaborationTimeAxisVO;
import com.trs.police.fight.mapper.CollaborationTimeAxisMapper;
import com.trs.police.fight.service.CollaborationTimeAxisDetailService;
import com.trs.police.fight.service.CollaborationTimeAxisService;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/3/8
 */
@Service
public class CollaborationTimeAxisServiceImpl implements CollaborationTimeAxisService {
    @Resource
    private CollaborationTimeAxisMapper timeAxisMapper;

    @Resource(name = "collaborationTimeAxisExecutor")
    private Executor executor;

    @Resource
    private PermissionService permissionService;

    private Map<Integer, CollaborationTimeAxisDetailService> detailServiceMap = new HashMap<>();

    public CollaborationTimeAxisServiceImpl(List<CollaborationTimeAxisDetailService> detailServiceList) {
        detailServiceList.forEach(service -> detailServiceMap.put(service.operateType(), service));
    }

    @Override
    public List<CollaborationTimeAxisVO> listTimeAxis(Long collaborationId) {
        List<CollaborationTimeAxisVO> timeAxisVos = timeAxisMapper.selectList(new QueryWrapper<CollaborationTimeAxis>()
                .eq("collaboration_id", collaborationId)
                .orderByAsc("create_time"))
                .stream().map(CollaborationTimeAxisConvert.CONVERTER::doToVO).collect(Collectors.toList());
        // 根据操作类型批量更新详情
        List<CompletableFuture<Void>> completableFutures = timeAxisVos.stream()
                .collect(Collectors.groupingBy(CollaborationTimeAxisVO::getOperateType))
                .entrySet().stream()
                .map(entry -> CompletableFuture.completedFuture(timeAxisVos).thenAcceptAsync(t -> {
                            CollaborationTimeAxisDetailService service = detailServiceMap.get(entry.getKey());
                            if (service != null) {
                                List<CollaborationTimeAxisVO> timeAxisVOList = entry.getValue();
                                List<Long> relatedIdList = timeAxisVOList.stream()
                                        .filter(vo -> vo.getRelatedId() != null)
                                        .map(CollaborationTimeAxisVO::getRelatedId).collect(Collectors.toList());
                                String nodeIds = timeAxisVOList.stream()
                                        .filter(vo -> vo.getNodeId() != null)
                                        .map(vo -> String.valueOf(vo.getNodeId())).collect(Collectors.joining(","));
                                Pair<Map, Map> detail = service.detail(relatedIdList, nodeIds);
                                timeAxisVOList.stream()
                                        .filter(vo -> vo.getRelatedId() != null)
                                        .forEach(vo -> vo.setDetail(detail.getLeft().get(vo.getRelatedId())));
                                timeAxisVOList.stream()
                                        .filter(vo -> vo.getNodeId() != null)
                                        .forEach(vo -> vo.setDetail(detail.getRight().get(vo.getNodeId() + "-" + vo.getCreateUserId() + "-" + vo.getCreateDeptId())));
                            }
                        }, executor)
                ).collect(Collectors.toList());
        CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0])).join();
        return timeAxisVos;
    }

    @Override
    public void addTimeAxis(Long collaborationId, CollaborationOperateEnum operateType, Long relatedId) {
        addTimeAxis(collaborationId, operateType, relatedId, AuthHelper.getCurrentUser());
    }

    @Override
    public void addTimeAxis(Long collaborationId, CollaborationOperateEnum operateType, Long relatedId, CurrentUser user) {
        CollaborationTimeAxis timeAxis = new CollaborationTimeAxis();
        timeAxis.setCollaborationId(collaborationId);
        timeAxis.setOperateType(operateType);
        timeAxis.setRelatedId(relatedId);
        if (Objects.isNull(user)) {
            throw new TRSException(ExceptionMessageConstant.CANT_FIND_CURRENT_USER);
        }
        timeAxis.fillAuditFields(user);
        timeAxis.setCreateRealName(user.getRealName());
        timeAxis.setCreateAvator(user.getAvatar());
        timeAxis.setCreateDeptName(user.getDept().getName());
        timeAxisMapper.insert(timeAxis);
    }

    @Override
    public void addTimeAxis(Long collaborationId, CollaborationOperateEnum operateType, Long nodeId, Long userId, Long deptId) {
        if (Objects.isNull(userId) || Objects.isNull(deptId)) {
            return;
        }
        CollaborationTimeAxis timeAxis = new CollaborationTimeAxis();
        timeAxis.setCollaborationId(collaborationId);
        timeAxis.setOperateType(operateType);
        timeAxis.setNodeId(nodeId);
        UserDto user = permissionService.getUserById(userId);
        DeptDto dept = permissionService.getDeptById(deptId);
        timeAxis.setCreateUserId(user.getId());
        timeAxis.setCreateDeptId(dept.getId());
        timeAxis.setCreateTime(LocalDateTime.now());
        timeAxis.setUpdateUserId(user.getId());
        timeAxis.setUpdateDeptId(dept.getId());
        timeAxis.setUpdateTime(LocalDateTime.now());
        timeAxis.setCreateRealName(user.getRealName());
        timeAxis.setCreateAvator(user.getAvatar());
        timeAxis.setCreateDeptName(dept.getName());
        timeAxisMapper.insert(timeAxis);
    }

    @Override
    public String getLastApprovalUser(Long collaborationId) {
        List<CollaborationTimeAxis> collaborationTimeAxes = timeAxisMapper.selectList(new QueryWrapper<CollaborationTimeAxis>()
                .eq("collaboration_id", collaborationId)
                .eq("operate_type", CollaborationOperateEnum.APPROVE_PASS.getCode())
                .orderByDesc("create_time"));
        if (!CollectionUtils.isEmpty(collaborationTimeAxes)) {
            CollaborationTimeAxis collaborationTimeAxis = collaborationTimeAxes.get(0);
            return collaborationTimeAxis.getCreateRealName();
        }
        return null;
    }
}
