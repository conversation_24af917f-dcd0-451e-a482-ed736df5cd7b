package com.trs.police.fight.statistic.mapper;

import com.trs.police.common.core.dto.DictDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * deptMapper
 */
@Mapper
public interface DictMapper{


    /**
     * xx
     *
     * @param type xx
     * @return xx
     */
    @Select("select id,name,code from t_dict where type = #{type} and status = 1")
    List<DictDto> getDictListByType(String type);
}
