package com.trs.police.fight.converter;

import com.trs.police.fight.domain.dto.FightResultDto;
import com.trs.police.fight.domain.entity.FightResultCaseRelation;
import com.trs.police.fight.domain.entity.FightResultOtherInfoRelation;
import com.trs.police.fight.domain.entity.FightResultUserRelation;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * @author: dingkeyu
 * @date: 2024/03/21
 * @description:
 */
@Mapper
public interface FightResultConvert {

    FightResultConvert CONVERTER = Mappers.getMapper(FightResultConvert.class);


    /**
     * do转dto
     *
     * @param relation relation
     * @return {@link FightResultDto.OtherInfo}
     */
    FightResultDto.OtherInfo otherInfo2Dto(FightResultOtherInfoRelation relation);

    /**
     * do转dto
     *
     * @param relation relation
     * @return {@link FightResultDto.SuspectInfo}
     */
    @Mappings(
            @Mapping(target = "suspectName", source = "userName")
    )
    FightResultDto.SuspectInfo suspect2Dto(FightResultUserRelation relation);

    /**
     * do转dto
     *
     * @param relation relation
     * @return {@link FightResultDto.CaseEventInfo}
     */
    @Mappings({
            @Mapping(target = "caseId", source = "caseEventId"),
            @Mapping(target = "caseCode", source = "caseEventCode"),
            @Mapping(target = "caseTitle", source = "caseName")
    })
    FightResultDto.CaseEventInfo caseEvent2Dto(FightResultCaseRelation relation);
}
