package com.trs.police.fight.controller;

import com.trs.police.fight.service.CollaborationMpService;
import com.trs.police.fight.service.collaboration.action.ReviewEndAction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 后门
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping({"/public/backdoor"})
public class BackdoorController {

    @Autowired
    private ReviewEndAction reviewEndAction;

    @Autowired
    private CollaborationMpService collaborationMpService;

    /**
     * 刷新反馈
     *
     * @param id id
     */
    @GetMapping("jzRefresh")
    public void testJzRefresh(Integer id) {
        reviewEndAction.refreshFeedback(collaborationMpService.getById(id));
    }
}
