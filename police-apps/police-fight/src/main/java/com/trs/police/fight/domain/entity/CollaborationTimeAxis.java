package com.trs.police.fight.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import com.trs.police.fight.constant.enums.CollaborationOperateEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 协同作战时间轴
 *
 * <AUTHOR>
 * @date 2024/3/8
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "t_collaboration_time_axis")
@AllArgsConstructor
@NoArgsConstructor
public class CollaborationTimeAxis extends AbstractBaseEntity {
    /**
     * 协作id
     */
    @TableField("collaboration_id")
    private Long collaborationId;

    /**
     * 操作类型
     */
    @TableField("operate_type")
    private CollaborationOperateEnum operateType;

    /**
     * 关联id
     */
    @TableField("related_id")
    private Long relatedId;

    /**
     * 节点id
     */
    @TableField("node_id")
    private Long nodeId;

    /**
     * 操作人姓名
     */
    @TableField("create_real_name")
    private String createRealName;

    /**
     * 操作人头像
     */
    @TableField("create_avator")
    private String createAvator;

    /**
     * 操作人部门
     */
    @TableField("create_dept_name")
    private String createDeptName;
}
