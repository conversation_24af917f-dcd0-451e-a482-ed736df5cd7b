package com.trs.police.fight.statistic.DTO;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 公共检索参数
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class CommonDTO implements Serializable {

    /**
     * 场景类型 {@link com.trs.police.fight.statistic.constant.SceneType#getCode}
     */
    private Integer sceneType;

    /**
     * 开始时间
     */
    private String startTime;


    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 每页条数
     */
    private Integer pageSize = 10;

    /**
     * 地域
     */
    private String areaCode;

    /**
     * 类型
     */
    private String type = "";

    /**
     * 部门代码
     */
    private String deptId;
}
