package com.trs.police.fight.controller;

import com.trs.police.common.core.dto.NodeCreateStrategyDTO;
import com.trs.police.common.core.vo.approval.NodeCreateStrategyResult;
import com.trs.police.fight.domain.vo.EsMessageEntity;
import com.trs.police.fight.service.CompositeMessageService;
import com.trs.police.fight.service.EsService;
import com.trs.police.fight.service.collaboration.CollaborationConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Set;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/public")
public class PublicController {

    @Resource
    private EsService esService;
    @Resource
    private CompositeMessageService compositeMessageService;

    @Autowired
    private CollaborationConfigService collaborationConfigService;


    /**
     * 测试连接
     *
     * @return 结果
     * @throws IOException io异常
     */
    @GetMapping("/es/test")
    public Set<String> test() throws IOException {
        return esService.test();
    }

    /**
     * 测试添加
     *
     * @param vo es消息
     * @throws IOException io异常
     */
    @PostMapping("/es/add")
    public void add(@RequestBody EsMessageEntity vo) throws IOException {
        vo.setCompositeId(1L);
        vo.setSendTime(LocalDateTime.now());
        esService.saveCompositeMessage(vo);
    }

    /**
     * 更新是否为关键信息
     *
     * @param compositeId 合成id
     * @param messageId   消息id
     * @param isKey       是否关键信息
     */
    @RequestMapping("/is-key-message")
    public void update(Long compositeId, Long messageId, Boolean isKey) {
        compositeMessageService.updateIsImportant(compositeId, messageId, isKey, null, null);
    }

    /**
     * 作战的审批策略
     *
     * @param dto dto
     * @return 策略
     */
    @PostMapping("nodeCreateStrategyResult")
    public NodeCreateStrategyResult nodeCreateStrategyResult(@RequestBody NodeCreateStrategyDTO dto) {
        return collaborationConfigService.nodeCreateStrategyResult(dto);
    }

}
