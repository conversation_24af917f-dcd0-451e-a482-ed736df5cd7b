package com.trs.police.fight.service.compositePartner.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.constant.enums.CompositeRoleEnum;
import com.trs.police.common.core.constant.enums.PostKindEnum;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.fight.constant.enums.CompositePartnerWay;
import com.trs.police.fight.domain.dto.CompositePartnerDTO;
import com.trs.police.fight.domain.entity.BigScreenDutyUserEntity;
import com.trs.police.fight.domain.vo.CompositeUserInfo;
import com.trs.police.fight.helper.DutyUserHelper;
import com.trs.police.fight.mapper.BigScreenDutyUserMapper;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.trs.common.utils.TimeUtils.YYYYMMDD;

/**
 * @author: dingkeyu
 * @date: 2024/12/23
 * @description: 根据岗位获取参与人员
 */
@Component
@ConditionalOnProperty(prefix = "ys.fight.composite.partner.post", name = "enabled", havingValue = "true")
public class PostCompositePartnerImpl extends AbsCompositePartnerImpl {

    @Resource
    private BigScreenDutyUserMapper bigScreenDutyUserMapper;

    @Resource
    private DictService dictService;

    @Resource
    private PermissionService permissionService;

    @Resource
    private DutyUserHelper dutyUserHelper;

    /**
     * 根据岗位获取参与人员
     *
     * @param dto dto
     * @return {@link List}<{@link CompositeUserInfo}>
     */
    @Override
    public List<CompositeUserInfo> getCompositePartner(CompositePartnerDTO dto) {
        if (StringUtils.isEmpty(dto.getPostCodes())) {
            return new ArrayList<>();
        }
        CurrentUser currentUser = AuthHelper.getNotNullUser();
        Map<Long, DictDto> postMap = dictService.getDictByType("post_type").stream().collect(Collectors.toMap(DictDto::getCode, Function.identity()));
        List<Integer> postCodes = Arrays.stream(dto.getPostCodes().split(",")).map(Integer::valueOf).collect(Collectors.toList());

        List<String> deptAndUserIdList = new ArrayList<>();
        // 选择岗位后仅拉取本单位的所选岗位中的当日值班人员
        List<BigScreenDutyUserEntity> dutyUsers = new LambdaQueryChainWrapper<>(bigScreenDutyUserMapper)
                .eq(BigScreenDutyUserEntity::getDutyTime, TimeUtils.getCurrentDate(YYYYMMDD) + " 00:00:00")
                .eq(BigScreenDutyUserEntity::getDistrictCode, currentUser.getDept().getDistrictCode())
                .list();
        if (CollectionUtils.isEmpty(dutyUsers)) {
            return new ArrayList<>();
        }
        return getOtherPostPerson(dutyUsers, postCodes, postMap);
    }

    /**
     * way
     *
     * @return {@link CompositePartnerWay}
     */
    @Override
    public CompositePartnerWay way() {
        return CompositePartnerWay.POST;
    }

    private List<CompositeUserInfo> getOtherPostPerson(List<BigScreenDutyUserEntity> dutyUsers, List<Integer> postCodes, Map<Long, DictDto> postMap) {
        List<String> deptAndUserIdList = new ArrayList<>();
        if (postCodes.contains(PostKindEnum.TLD.getCode())) {
            List<String> list = dutyUsers.stream()
                    .filter(user -> "值班领导".equals(user.getNature()) && "510000".equals(user.getDistrictCode()))
                    .map(BigScreenDutyUserEntity::getUniqueSign)
                    .map(e -> e + "-" + CompositeRoleEnum.ASSISTANT.getCode())
                    .collect(Collectors.toList());
            deptAndUserIdList.addAll(list);
        }
        if (postCodes.contains(PostKindEnum.JLD.getCode())) {
            List<String> list = dutyUsers.stream()
                    .filter(user -> "值班领导".equals(user.getNature()) && !"510000".equals(user.getDistrictCode()))
                    .map(BigScreenDutyUserEntity::getUniqueSign)
                    .map(e -> e + "-" + CompositeRoleEnum.ASSISTANT.getCode())
                    .collect(Collectors.toList());
            deptAndUserIdList.addAll(list);
        }
        if (postCodes.contains(PostKindEnum.ZHZ.getCode())) {
            List<String> list = dutyUsers.stream()
                    .filter(user -> "指挥长".equals(user.getNature()))
                    .map(BigScreenDutyUserEntity::getUniqueSign)
                    .map(e -> e + "-" + CompositeRoleEnum.ASSISTANT.getCode())
                    .collect(Collectors.toList());
            deptAndUserIdList.addAll(list);
        }
        List<Integer> list = Arrays.asList(PostKindEnum.TLD.getCode(), PostKindEnum.JLD.getCode(), PostKindEnum.ZHZ.getCode());
        List<Integer> postCodeList = postCodes.stream().filter(code -> !list.contains(code)).collect(Collectors.toList());
        BigScreenDutyUserEntity dutyUserEntity = dutyUsers.get(0);
        for (Integer postCode : postCodeList) {
            DictDto dto = postMap.get(Long.valueOf(postCode));
            if (StringUtils.isEmpty(dto.getDictDesc())) {
                continue;
            }
            try {
                Field field = dutyUserEntity.getClass().getDeclaredField(dto.getDictDesc());
                field.setAccessible(true);
                String value = (String) field.get(dutyUserEntity);
                if (!StringUtils.isEmpty(value)) {
                    List<String> collect = Stream.of(value.split(","))
                            .map(e -> e + "-" + CompositeRoleEnum.ASSISTANT.getCode())
                            .collect(Collectors.toList());
                    deptAndUserIdList.addAll(collect);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return dutyUserHelper.buildCompositePartner(deptAndUserIdList, "post");
    }

}
