package com.trs.police.fight.service;

import org.apache.commons.lang3.tuple.Pair;

import java.util.List;
import java.util.Map;

/**
 * @author: zuo.kaiyuan
 * @date: 2024/03/11
 * @description: 协同作战时间轴详细数据
 * @param <T> T
 */
public interface CollaborationTimeAxisDetailService<T> {
    /**
     * 操作类型
     *
     * @return {@link Integer}
     */
    Integer operateType();

    /**
     * 详细数据，备注、附件等
     *
     * @param relateIds 关联id
     * @param nodeIds   节点id
     * @return {@link T}
     */
    Pair<Map<Long, T>, Map<String, T>> detail(List<String> relateIds, String nodeIds);
}
