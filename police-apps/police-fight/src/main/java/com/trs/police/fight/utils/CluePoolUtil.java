package com.trs.police.fight.utils;

import com.trs.common.utils.TimeUtils;

/**
 * @ClassName CluePoolUtil
 * @Description 线索池工具类
 * <AUTHOR>
 * @Date 2024/3/20 18:15
 **/
public class CluePoolUtil {


    /**
     * 数字左侧补0
     *
     * @param number 数字
     * @param length 限定长度
     * @return 结果
     */
    public static String padLeft(int number, int length) {
        String numStr = String.valueOf(number);
        int numLength = numStr.length();
        if (numLength >= length) {
            return numStr;
        } else {
            StringBuilder paddedNum = new StringBuilder();
            for (int i = 0; i < length - numLength; i++) {
                paddedNum.append('0');
            }
            paddedNum.append(numStr);
            return paddedNum.toString();
        }
    }

    /**
     * 获取一天的开始时间
     *
     * @param time 时间
     * @return 结果
     */
    public static String getStartTime(String time) {
        return TimeUtils.stringToString(time, TimeUtils.YYYYMMDD) + " 00:00:00";
    }

    /**
     * 获取一天的结束时间
     *
     * @param time 时间
     * @return 结果
     */
    public static String getEndTime(String time) {
        return TimeUtils.stringToString(time, TimeUtils.YYYYMMDD) + " 23:59:59";
    }
}
