package com.trs.police.fight.constant.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 协作的表单类型
 *
 * <AUTHOR>
 */
public enum CollaborationFormType {

    COMMON_FROM(0, "默认表单"),
    // 表单一 目前技侦在使用
    ONE(1, "表单1"),
    TWO(2, "表单2"),
    TREE(3, "表单3"),
    FOUR(4, "表单4"),
    // 表单五 目前建模服务在使用
    FIVE(5, "表单5"),
    // 目前紧急警情使用的这个表单
    SIX(6, "表单6");

    /**
     * 表单编号
     */
    @Getter
    private Integer code;

    /**
     * 表单名称
     */
    private String name;

    CollaborationFormType(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据名字匹配类型
     *
     * @param name 名字
     * @return 类型
     */
    public static CollaborationFormType findByNameOrDefault(String name) {
        for (CollaborationFormType value : CollaborationFormType.values()) {
            if (Objects.equals(name, value.name)) {
                return value;
            }
        }
        return COMMON_FROM;
    }
}
