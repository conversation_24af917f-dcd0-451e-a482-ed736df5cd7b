package com.trs.police.fight.service.collaboration;

import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.entity.Collaboration;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 协作工具类
 *
 * <AUTHOR>
 */
@Component
public class CollaborationHelper {

    /**
     * 是否审核流程结束了
     *
     * @param collaboration 协作
     * @return boolean
     */
    public Boolean isEndView(Collaboration collaboration) {
        // 已经是最后一位了 也不会继续扭转
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        if (Objects.equals(currentUser.getDeptId(), collaboration.getCollaborationDeptId())) {
            return true;
        }
        return false;
    }
}
