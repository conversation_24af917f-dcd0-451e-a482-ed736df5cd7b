<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.subject.common.mapper.SwClueExcavateMapper">
    <resultMap id="errorPersonResultMap" type="com.trs.police.subject.domain.vo.ErrorPersonExcavateVO">
        <result column="photos" property="photos"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToStringListHandler"/>
    </resultMap>

    <select id="getClueExcavateList" resultType="com.trs.police.subject.domain.vo.ErrorPersonExcavateVO">
        select
        tsp.xm as realName,
        tsp.zjxy as religion,
        tsp.zjhm as idCard,
        COUNT(*) as personCount,
        MAX(twt.activity_time) as finalActivityTime,
        IFNULL((SELECT status FROM tb_fx_task_process WHERE task_id = tsp.zjhm AND subject_type = #{dto.subjectType}
        AND clue_excavate_type = 7), 1) as warningStatus,
        (SELECT COUNT(1) FROM tb_fx_feedback WHERE task_id = tsp.zjhm AND subject_type = #{dto.subjectType} AND
        clue_excavate_type = 7) as feedbackCount
        from tb_sw_person tsp
        join t_warning_track twt on tsp.person_id = twt.person_id
        join t_warning tw on twt.warning_id = tw.id
        <where>
            <if test="personLabels != null and personLabels.size() > 0">
                AND JSON_OVERLAPS(tsp.person_label,
                <foreach collection="personLabels" item="labelId" open="JSON_ARRAY(" close=")" separator=",">
                    #{labelId}
                </foreach>
                )
            </if>
            <if test="dto.worth != null">
                AND tsp.zjhm IN (select distinct task_id from tb_fx_feedback where JSON_EXTRACT(feedback, '$.worth')
                = #{dto.worth} and subject_type = #{dto.subjectType} and clue_excavate_type = 7)
            </if>
            <if test="dto.warningStatus != null and dto.warningStatus != ''">
                AND (
                <if test="@com.trs.police.common.core.utils.StringUtil@convertToIntegerList(dto.warningStatus).contains(5)">
                    <!-- 未反馈: 状态为已签收或者已完结且没有反馈记录 -->
                    (tsp.zjhm IN (select distinct task_id from tb_fx_task_process t1 where t1.subject_type =
                    #{dto.subjectType} and t1.clue_excavate_type = 7 and
                    (t1.status = 2 or (t1.status = 4 and not exists(select 1 from tb_fx_feedback t2 where t2.task_id =
                    tsp.zjhm and t2.subject_type = #{dto.subjectType} and t2.clue_excavate_type = 7))))
                    ) OR
                </if>
                <if test="@com.trs.police.common.core.utils.StringUtil@convertToIntegerList(dto.warningStatus).contains(1)">
                    <!-- 未签收 -->
                    (tsp.zjhm NOT IN (select distinct task_id from tb_fx_task_process t1 where t1.subject_type =
                    #{dto.subjectType} and t1.clue_excavate_type = 7)) OR
                </if>
                <if test="@com.trs.police.common.core.utils.StringUtil@convertToIntegerList(dto.warningStatus).contains(2)">
                    <!-- 已签收包括（已签收、未反馈、已反馈） -->
                    (tsp.zjhm IN (select distinct task_id from tb_fx_task_process t1 where t1.subject_type =
                    #{dto.subjectType} and t1.clue_excavate_type = 7 AND t1.status IN (2,3))) OR
                </if>
                tsp.zjhm IN (select distinct task_id from tb_fx_task_process t1 where t1.subject_type =
                #{dto.subjectType} and t1.clue_excavate_type =7 and t1.status in
                <foreach collection="dto.warningStatus.split(',')" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>)
                )
            </if>
            <if test="dto.activityStartTime != null and dto.activityStartTime != ''">
                AND twt.activity_time &gt;= #{dto.activityStartTime}
            </if>
            <if test="dto.activityEndTime != null and dto.activityEndTime != ''">
                AND twt.activity_time &lt;= #{dto.activityEndTime}
            </if>
            <if test="dto.searchField != null and dto.searchField != '' and dto.searchValue != null and dto.searchValue != ''">
                <choose>
                    <when test="dto.searchField == 'idCard'">
                        AND tsp.zjhm LIKE CONCAT('%', #{dto.searchValue}, '%')
                    </when>
                    <when test="dto.searchField == 'realName'">
                        AND tsp.xm LIKE CONCAT('%', #{dto.searchValue}, '%')
                    </when>
                    <when test="dto.searchField = 'fullText'">
                        AND (
                        tsp.zjhm LIKE CONCAT('%', #{dto.searchValue}, '%')
                        OR tsp.xm LIKE CONCAT('%', #{dto.searchValue}, '%')
                        )
                    </when>
                </choose>
            </if>
        </where>
        GROUP BY tsp.zjhm
        ORDER BY twt.activity_time desc
    </select>
</mapper>