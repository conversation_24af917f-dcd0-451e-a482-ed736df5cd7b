<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.subject.common.mapper.FxGroupExcavateMapper">

    <select id="groupClueList" resultType="com.trs.police.subject.domain.vo.GroupClueExcavateListVO">
        SELECT
        *,
        id as groupId,
        IFNULL((SELECT status FROM tb_fx_task_process WHERE task_id = tfq.id AND subject_type = #{dto.subjectType} AND
        clue_excavate_type = 2), 1) as warningStatus,
        (SELECT COUNT(1) FROM tb_fx_feedback WHERE task_id = tfq.id AND subject_type = #{dto.subjectType} AND
        clue_excavate_type = 2) as feedbackCount
        FROM tb_fx_qtxswj tfq
        <where>
            <if test="dto.subjectType != null and dto.subjectType != ''">
                AND tfq.subject_type = #{dto.subjectType}
            </if>
            <if test="dto.worth != null">
                AND tfq.id IN (select distinct task_id from tb_fx_feedback where JSON_EXTRACT(feedback, '$.worth') =
                #{dto.worth}
                and subject_type = #{dto.subjectType} and clue_excavate_type = 2)
            </if>
            <if test="dto.warningStatus != null and dto.warningStatus != ''">
                AND (
                <if test="@com.trs.police.common.core.utils.StringUtil@convertToIntegerList(dto.warningStatus).contains(5)">
                    <!-- 未反馈: 状态为已签收或者已完结且没有反馈记录 -->
                    (tfq.id IN (select distinct task_id from tb_fx_task_process t1 where t1.subject_type =
                    #{dto.subjectType} and t1.clue_excavate_type = 2 and
                    (t1.status = 2 or (t1.status = 4 and not exists(select 1 from tb_fx_feedback t2 where t2.task_id =
                    tfq.id and t2.subject_type = #{dto.subjectType} and t2.clue_excavate_type = 2))))
                    ) OR
                </if>
                <if test="@com.trs.police.common.core.utils.StringUtil@convertToIntegerList(dto.warningStatus).contains(1)">
                    <!-- 未签收 -->
                    (tfq.id NOT IN (select distinct task_id from tb_fx_task_process t1 where t1.subject_type =
                    #{dto.subjectType} and t1.clue_excavate_type = 2)) OR
                </if>
                <if test="@com.trs.police.common.core.utils.StringUtil@convertToIntegerList(dto.warningStatus).contains(2)">
                    <!-- 已签收包括（已签收、未反馈、已反馈） -->
                    (tfq.id IN (select distinct task_id from tb_fx_task_process t1 where t1.subject_type =
                    #{dto.subjectType} and t1.clue_excavate_type = 2 AND t1.status IN (2,3))) OR
                </if>
                tfq.id IN (select distinct task_id from tb_fx_task_process t1 where t1.subject_type = #{dto.subjectType}
                and t1.clue_excavate_type = 2 and t1.status in
                <foreach collection="dto.warningStatus.split(',')" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>)
                )
            </if>
        </where>
    </select>
    <select id="swGroupClueList" resultType="com.trs.police.subject.domain.vo.GroupClueExcavateListVO">
        SELECT
        gr.group_id as groupId,
        COUNT(DISTINCT p.id) as groupSize
        FROM
        t_profile_person_group_relation gr
        JOIN t_profile_person p ON gr.person_id = p.id
        group by gr.group_id
    </select>
    <select id="selectGroupNameByGroupIds" resultType="com.trs.police.subject.domain.vo.GroupClueExcavateListVO">
        select
        g.id as groupId,
        g.risk_score as riskScore,
        g.name as groupName
        from t_profile_group g
        <where>
            <if test="groupIds != null and groupIds.size() > 0">
                g.id in
                <foreach collection="groupIds" item="groupId" open="(" close=")" separator=",">
                    #{groupId}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectPersonIdCardByGroupIds" resultType="java.lang.String">
        select
        distinct
        p.id_number
        from t_profile_person_group_relation pgr
        join t_profile_person p on pgr.person_id = p.id
        <where>
            pgr.group_id = #{groupId}
        </where>
    </select>
</mapper>