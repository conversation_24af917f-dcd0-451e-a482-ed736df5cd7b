<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.subject.common.mapper.WarningProcessMapper">

    <select id="getUserProcessByWarningId" resultType="com.trs.police.common.core.entity.WarningProcessEntity">
        SELECT *
        FROM t_warning_process twp
        LEFT JOIN t_warning_notify twn ON twp.id = twn.process_id
        <where>
            <if test="warningIds != null and warningIds.size() != 0">
                AND twn.warning_id IN
                <foreach collection="warningIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            AND twn.user_id = #{userId}
            AND twn.dept_id = #{deptId}
        </where>
    </select>

    <select id="getProcessByWarningId" resultType="com.trs.police.common.core.entity.WarningProcessEntity">
        SELECT *
        FROM t_warning_process twp
        <where>
            <if test="warningIds != null and warningIds.size() != 0">
                AND twp.warning_id IN
                <foreach collection="warningIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY twp.warning_id
    </select>

</mapper>