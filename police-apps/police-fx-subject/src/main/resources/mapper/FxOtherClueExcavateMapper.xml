<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.subject.common.mapper.FxOtherClueMapper">


    <resultMap id="swPersonMap" type="com.trs.police.subject.domain.vo.OtherClueExcavateVO">
        <result column="idCard" property="idCard"/>
        <result column="tel" property="tel"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToStringListHandler"/>
    </resultMap>

    <select id="otherClueList" resultType="com.trs.police.subject.domain.vo.OtherClueExcavateVO">
        SELECT
            tfo.zjhm as idCard,
            tfo.sjhm as tel,
            tfo.sjhm as bjdh,
            tfo.jjdbh as jqCode,
            tfo.xm as bj<PERSON>eporter,
            tfo.sj as bjsj,
            tfo.jqjy as jq<PERSON><PERSON>mary,
            CASE WHEN tfo.type = 'jq' THEN '警综警情' ELSE '12345' END AS sourceFrom,
            IFNULL((SELECT status FROM tb_fx_task_process WHERE task_id = tfo.zjhm AND subject_type = #{dto.subjectType} AND clue_excavate_type = 8), 1) as warningStatus,
            (SELECT COUNT(1) FROM tb_fx_feedback WHERE task_id = tfo.zjhm AND subject_type = #{dto.subjectType} AND clue_excavate_type = 8) as feedbackCount
        FROM tb_fx_ocwj tfo
        <where>
            <if test="dto.worth != null">
                AND tfo.zjhm IN (select distinct task_id from tb_fx_feedback where JSON_EXTRACT(feedback, '$.worth') = #{dto.worth}
                    and subject_type = #{dto.subjectType} and clue_excavate_type = 8)
            </if>
            <if test="dto.warningStatus != null and dto.warningStatus != ''">
                AND (
                <if test="@com.trs.police.common.core.utils.StringUtil@convertToIntegerList(dto.warningStatus).contains(5)">
                    <!-- 未反馈: 状态为已签收或者已完结且没有反馈记录 -->
                    (tfo.zjhm IN (select distinct task_id from tb_fx_task_process t1 where t1.subject_type = #{dto.subjectType} and t1.clue_excavate_type = 8 and
                    (t1.status = 2 or (t1.status = 4 and not exists(select 1 from tb_fx_feedback t2 where t2.task_id = tfo.zjhm and t2.subject_type = #{dto.subjectType} and t2.clue_excavate_type = 8))))
                    ) OR
                </if>
                <if test="@com.trs.police.common.core.utils.StringUtil@convertToIntegerList(dto.warningStatus).contains(1)">
                    <!-- 未签收 -->
                    (tfo.zjhm NOT IN (select distinct task_id from tb_fx_task_process t1 where t1.subject_type = #{dto.subjectType} and t1.clue_excavate_type = 8)) OR
                </if>
                <if test="@com.trs.police.common.core.utils.StringUtil@convertToIntegerList(dto.warningStatus).contains(2)">
                    <!-- 已签收包括（已签收、未反馈、已反馈） -->
                    (tfo.zjhm IN (select distinct task_id from tb_fx_task_process t1 where t1.subject_type = #{dto.subjectType} and t1.clue_excavate_type = 8 AND t1.status IN (2,3))) OR
                </if>
                tfo.zjhm IN (select distinct task_id from tb_fx_task_process t1 where t1.subject_type = #{dto.subjectType} and t1.clue_excavate_type = 8 and t1.status in
                <foreach collection="dto.warningStatus.split(',')" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>)
                )
            </if>
            <if test="dto.searchField != null and dto.searchField != '' and dto.searchValue != null and dto.searchValue != ''">
                <choose>
                    <when test="dto.searchField == 'fullText'">
                        AND (
                            xm LIKE CONCAT('%',#{dto.searchValue},'%') OR
                            sjhm LIKE CONCAT('%',#{dto.searchValue},'%') OR
                            jqjy LIKE CONCAT('%',#{dto.searchValue},'%')
                        )
                    </when>
                    <otherwise>
                        AND ${dto.searchField} LIKE CONCAT('%',#{dto.searchValue},'%')
                    </otherwise>
                </choose>
            </if>
        </where>
    </select>
    <select id="swOtherClueList" resultType="com.trs.police.subject.domain.vo.OtherClueExcavateVO">
        SELECT * FROM (
        SELECT
        DISTINCT
        <include refid="otherClueListSelect"></include>
        from t_profile_sthy tps where
        tps.BJRZJHM in (
        select
        tpp.id_number
        from
        t_profile_person tpp
        <include refid="otherClueListFilterCondition"></include>
        )
        UNION ALL
        SELECT
        DISTINCT
        <include refid="otherClueListSelect"></include>
        from t_profile_sthy tps where
        tps.BJDH in (
        select
        j.tel_number
        from
        t_profile_person tpp
        join JSON_TABLE(tpp.tel,'$[*]' COLUMNS(tel_number varchar(255) path '$')) as j on true
        <include refid="otherClueListFilterCondition"></include>
        )
        ) AS results
        ORDER BY bjsj DESC
    </select>

    <sql id="otherClueListSelect">
        tps.jjdbh as jqCode,
        tps.BJDHYHM as bjReporter,
        tps.BJRZJHM as idCard,
        tps.bjdh as bjdh,
        tps.bjsj as bjsj,
        tps.bjnr as jqSummary,
        CASE WHEN tps.source_type = '2' THEN '警综警情' ELSE '12345' END AS sourceFrom
    </sql>
    <sql id="otherClueListFilterCondition">
        <where>
            <if test="dto.searchField != null and dto.searchField != '' and dto.searchValue != null and dto.searchValue != ''">
                <choose>
                    <when test="dto.searchField == 'fullText'">
                        AND (
                        tps.bjdh LIKE CONCAT('%',#{dto.searchValue},'%') OR
                        tps.BJDHYHM LIKE CONCAT('%',#{dto.searchValue},'%') OR
                        tps.jjdbh LIKE CONCAT('%',#{dto.searchValue},'%')
                        )
                    </when>
                    <otherwise>
                        AND ${dto.searchField} LIKE CONCAT('%',#{dto.searchValue},'%')
                    </otherwise>
                </choose>
            </if>
            <if test="dto.bjStartTime != null and dto.bjStartTime != ''">
                AND tps.bjsj >= #{dto.bjStartTime}
            </if>
            <if test="dto.bjEndTime != null and dto.bjEndTime != ''">
                AND tps.bjsj &lt;= #{dto.bjEndTime}
            </if>
            <if test="personLabels != null and personLabels.size() > 0">
                AND JSON_OVERLAPS(tpp.person_label,
                <foreach collection="personLabels" item="labelId" open="JSON_ARRAY(" close=")" separator=",">
                    #{labelId}
                </foreach>
                )
            </if>
        </where>
    </sql>
</mapper>