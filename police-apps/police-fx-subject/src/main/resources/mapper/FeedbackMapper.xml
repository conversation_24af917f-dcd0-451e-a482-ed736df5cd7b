<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.subject.common.mapper.FeedbackMapper">

    <select id="feedbackRecords" resultType="com.trs.police.subject.domain.vo.FeedbackRecordVO">
        SELECT
            u.real_name AS fkUser,
            d.short_name AS fkDept,
            f.feedback_time AS fkTime,
            JSON_UNQUOTE(JSON_EXTRACT(feedback, '$.worth')) AS worth,
            JSON_UNQUOTE(JSON_EXTRACT(feedback, '$.content')) AS content,
            JSON_UNQUOTE(JSON_EXTRACT(feedback, '$.attachments')) AS attachments
        FROM tb_fx_feedback f
        LEFT JOIN t_user u ON f.user_id = u.id
        LEFT JOIN t_dept d ON f.dept_id = d.id
        <where>
            AND f.subject_type = #{dto.subjectType}
            AND f.clue_excavate_type = #{dto.clueExcavateType}
            AND f.task_id = #{dto.taskId}
        </where>
        ORDER BY f.feedback_time DESC
    </select>
</mapper>