package com.trs.police.subject.fx.service.taskProcess;

import com.trs.police.subject.domain.dto.TaskProcessDTO;

/**
 * @author: dingkeyu
 * @date: 2024/10/22
 * @description:
 */
public interface ITaskProcessService {

    /**
     * 签收
     *
     * @param taskId taskId
     * @param dto dto
     */
    void sign(String taskId, TaskProcessDTO dto);

    /**
     * 反馈
     *
     * @param taskId     taskId
     * @param dto dto
     */
    void feedback(String taskId, TaskProcessDTO dto);

    /**
     * 完结
     *
     * @param taskId taskId
     * @param dto dto
     */
    void done(String taskId, TaskProcessDTO dto);
}
