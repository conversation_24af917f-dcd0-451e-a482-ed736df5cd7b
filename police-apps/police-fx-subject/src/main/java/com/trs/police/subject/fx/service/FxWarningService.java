package com.trs.police.subject.fx.service;

import com.trs.police.subject.domain.dto.MonitorIdCardRelationDTO;
import com.trs.police.subject.domain.dto.StatisticsDTO;
import com.trs.police.subject.domain.dto.WarningSearchDTO;
import com.trs.police.subject.domain.vo.FxModelWarningListVO;
import com.trs.police.subject.domain.vo.FxMonitorWarningListVO;
import com.trs.police.subject.domain.vo.PersonalStatisticsVO;
import com.trs.web.builder.base.RestfulResultsV2;

import javax.servlet.http.HttpServletResponse;

/**
 * 模型预警service
 *
 * <AUTHOR>
 * @date 2024/04/23
 */
public interface FxWarningService {

    /**
     * 获取模型预警列表
     *
     * @param dto 请求参数
     * @return 列表数据
     */
    RestfulResultsV2<FxModelWarningListVO> getWarningModelList(WarningSearchDTO dto);

    /**
     * 模型预警签收操作
     *
     * @param id 模型预警id
     * @return 签收是否成功信息
     */
    RestfulResultsV2<Boolean> warningModelReceipted(Long id);

    /**
     * 模型预警-预警状态更新
     *
     * @param id             模型预警id
     * @param warningStatus  预警状态 code值
     * @param isModelWarning 是否是从模型预警列表进入
     * @return 更新是否成功
     */
    Boolean updateWarningStatus(Long id, Integer warningStatus, Boolean isModelWarning);

    /**
     * 获取布控预警列表
     *
     * @param dto 请求参数
     * @return 列表数据
     */
    RestfulResultsV2<FxMonitorWarningListVO> getWarningMonitorList(WarningSearchDTO dto);

    /**
     * 模型预警导出
     *
     * @param dto      请求参数
     * @param response response
     */
    void exportWarningModelList(WarningSearchDTO dto, HttpServletResponse response);

    /**
     * 模型预警-布控与人员关联
     *
     * @param dto 请求参数
     * @return 关联表主键id
     */
    RestfulResultsV2<Long> createMonitorPersonRelation(MonitorIdCardRelationDTO dto);

    /**
     * 获取模型预警id
     *
     * @param monitorId 布控id
     * @return 模型预警id
     */
    RestfulResultsV2<Long> getModelWarningId(Long monitorId);


    /**
     * 预警统计
     *
     * @param dto dto
     * @return {@link RestfulResultsV2}<{@link PersonalStatisticsVO}>
     */
    RestfulResultsV2<PersonalStatisticsVO> warningStatistics(StatisticsDTO dto);

    /**
     * 布控预警列表导出
     *
     * @param dto      列表查询参数
     * @param response 响应
     */
    void exportWarningMonitorList(WarningSearchDTO dto, HttpServletResponse response);
}
