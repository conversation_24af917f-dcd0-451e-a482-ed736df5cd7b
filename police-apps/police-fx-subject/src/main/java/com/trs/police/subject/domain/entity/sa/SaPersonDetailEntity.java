package com.trs.police.subject.domain.entity.sa;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @author: duzhaoyang
 * @date: 2024/07/10
 * @description: sa人员详情实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_sa_person_detail")
public class SaPersonDetailEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 卡片ID
     */
    private String cardId;

    /**
     * 卡片编号
     */
    private String cardNumber;

    /**
     * 卡片状态
     */
    private String cardStatus;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 性别
     */
    private String gender;

    /**
     * 工作单位
     */
    private String workUnit;

    /**
     * 联系电话
     */
    private String tel;

    /**
     * 病人属于
     */
    private String brsy;

    /**
     * 现住详细地址
     */
    private String address;

    /**
     * 人群分类
     */
    private String personType;

    /**
     * 发病日期
     */
    private LocalDateTime fbrq;

    /**
     * 诊断时间
     */
    private LocalDateTime zdsj;

    /**
     * 填卡医生
     */
    private String tkys;

    /**
     * 医生填卡日期
     */
    private LocalDateTime ystkrq;

    /**
     * 报告单位
     */
    private String bgdw;

    /**
     * 报告卡录入时间
     */
    private LocalDateTime bgklrsj;

    /**
     * 录卡用户
     */
    private String lkyh;

    /**
     * 录卡用户所属单位
     */
    private String lkyhssdw;

    /**
     * 区县审核时间
     */
    private LocalDateTime qxshsj;

    /**
     * 审核状态
     */
    private String shzt;

    /**
     * 订正终审时间
     */
    private String dzzssj;

    /**
     * 备注
     */
    private String bz;

    /**
     * 婚姻
     */
    private String hy;

    /**
     * 民族
     */
    private String nation;

    /**
     * 文化程度
     */
    private String whcd;

    /**
     * 户籍地类型
     */
    private String hjdlx;

    /**
     * 户籍地址编码
     */
    private String hjdzbm;

    /**
     * 户籍地址
     */
    private String hjdz;

    /**
     * 接触史
     */
    private String jcs;

    /**
     * 性病史
     */
    private String xbs;

    /**
     * 最有可能感染途径
     */
    private String zykngrtj;

    /**
     * 样本来源
     */
    private String ybly;

    /**
     * 实验室检测结论
     */
    private String sysjcjl;

    /**
     * 确认（替代策略、核酸）检测单位
     */
    private String qrjcdw;

    /**
     * 确认（替代策略、核酸）检测阳性日期
     */
    private LocalDateTime qrjcyxrq;

    /**
     * 标签
     */
    private String tag;

    /**
     * 管控地派出所名称
     */
    private String gkdpcsmc;

    /**
     * 管控责任派出所民警
     */
    private String gkzrpcsmj;
}
