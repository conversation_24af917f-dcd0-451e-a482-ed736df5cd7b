package com.trs.police.subject.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.trs.police.common.core.constant.enums.ControlTypeEnum;
import com.trs.police.common.core.constant.enums.MonitorLevelEnum;
import com.trs.police.common.core.handler.typehandler.JsonToLongListHandler;
import com.trs.police.common.core.handler.typehandler.JsonToStringListHandler;
import com.trs.police.subject.common.handler.JsonToFxTagsHandler;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 预警表(Warning)数据访问类-FX专题预警
 *
 * <AUTHOR>
 * @since 2022-08-11 14:04:37
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "t_warning_fxryyj", autoResultMap = true)
public class FxWarningEntity implements Serializable {

    private static final long serialVersionUID = -8569267883626844169L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT, value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新用户id
     */
    @TableField(fill = FieldFill.UPDATE, value = "update_user_id")
    private Long updateUserId;

    /**
     * 更新部门id
     */
    @TableField(fill = FieldFill.UPDATE, value = "update_dept_id")
    private Long updateDeptId;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE, value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 预警类型
     */
    private String warningType;

    /**
     * 预警级别
     */
    private MonitorLevelEnum warningLevel;

    /**
     * 预警详情
     */
    private String content;

    /**
     * 预警时间
     */
    private LocalDateTime warningTime;

    /**
     * 布控id
     */
    private Long monitorId;

    /**
     * 模型id
     */
    @TableField(typeHandler = JsonToLongListHandler.class)
    private List<Long> modelId;
    /**
     * 群体预警群体id
     */
    private Long groupId;

    /**
     * 预警积分
     */
    private double warningScore;

    /**
     * 预警人员idCard
     */
    @TableField("id_card")
    private String idCard;

    /**
     * 管控类型 1=布控 2=常控
     */
    private ControlTypeEnum controlType;

    /**
     * 活动时间
     */
    private LocalDateTime activityTime;
    /**
     * 活动地点
     */
    private String activityAddress;
    /**
     * 预警状态
     * 1 待签收 2 已签收 3 已布控 4 已研判
     */
    private Integer warningStatus;

    /**
     * 预警关联的人的标签，用于统计
     */
    @TableField(typeHandler = JsonToLongListHandler.class)
    private List<Long> personLabel;
    /**
     * 命中的区域id
     */
    @TableField(typeHandler = JsonToLongListHandler.class)
    private List<Long> areaId;

    /**
     * 命中的场所code
     */
    @TableField(typeHandler = JsonToLongListHandler.class)
    private List<Long> placeCode;

    /**
     * 预警标签
     */
    @TableField(typeHandler = JsonToStringListHandler.class)
    private List<String> warningTags = new ArrayList<>();

    /**
     * fx标签
     */
    @TableField(value = "fx_tags", typeHandler = JsonToFxTagsHandler.class)
    private List<FxSubjectLabelEsDO.FxTag> fxTags = new ArrayList<>();
}