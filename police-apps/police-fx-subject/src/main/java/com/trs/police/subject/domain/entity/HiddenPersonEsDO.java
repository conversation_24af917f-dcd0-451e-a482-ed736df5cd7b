package com.trs.police.subject.domain.entity;

import com.trs.db.sdk.annotations.TableField;
import com.trs.db.sdk.annotations.TableName;
import com.trs.db.sdk.pojo.BaseRecordDO;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: dingkeyu
 * @date: 2024/06/19
 * @description: 隐性人员挖掘
 */
@Data
@NoArgsConstructor
@TableName("${fx.excavate.hiddenPerson:fx_xswj_yxrywj}")
public class HiddenPersonEsDO extends BaseRecordDO {

    private static final long serialVersionUID = 5610377127178182446L;

    /**
     * 证件号码
     */
    @TableField(value = "zjhm")
    private String zjhm;

    /**
     * 手机号码
     */
    @TableField(value = "sjhm")
    private String sjhm;

    /**
     * IMSI
     */
    @TableField(value = "imsi")
    private String imsi;

    /**
     * IMEI
     */
    @TableField(value = "imei")
    private String imei;

    /**
     * 对于敏感app，存具体App名称，对于在册人员同行，存同行人证件号码
     */
    @TableField(value = "reason")
    private String reason;

    /**
     * 出现地地域编码
     */
    @TableField(value = "cxd_dybm")
    private String cxdDybm;

    /**
     * 隐形人员姓名
     */
    @TableField(value = "xm")
    private String xm;

}
