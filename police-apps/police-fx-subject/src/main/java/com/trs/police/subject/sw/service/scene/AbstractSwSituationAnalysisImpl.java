package com.trs.police.subject.sw.service.scene;

import com.trs.police.subject.common.service.scene.ISubjectStatisticScene;
import com.trs.police.subject.domain.dto.StatisticsDTO;
import com.trs.police.subject.domain.dto.SubjectSceneContext;

import java.util.List;

/**
 * sw专题-态势感知-基础统计抽象类
 *
 * @param <T> 搜索结果类型
 * <AUTHOR>
 * @date 2025/4/16
 */
public abstract class AbstractSwSituationAnalysisImpl<T> implements ISubjectStatisticScene<T, StatisticsDTO> {

    /**
     * 总数
     */
    protected Long total = 0L;

    @Override
    public List<T> search(SubjectSceneContext<StatisticsDTO> context) {
        StatisticsDTO dto = context.getDto();
        if (dto == null) {
            dto = new StatisticsDTO();
            context.setDto(dto);
        }
        dto.setEndTime(context.getEndTime());
        dto.setStartTime(context.getStartTime());
        return doSearch(context);
    }

    /**
     * 获取总数
     *
     * @return 总数
     */
    public Long getTotal() {
        return total;
    }


    /**
     * 执行搜索
     *
     * @param context 上下文
     * @return 搜索结果
     */
    protected abstract List<T> doSearch(SubjectSceneContext<StatisticsDTO> context);
}
