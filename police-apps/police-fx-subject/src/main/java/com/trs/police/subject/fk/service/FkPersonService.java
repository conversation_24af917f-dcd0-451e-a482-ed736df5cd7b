package com.trs.police.subject.fk.service;

import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.vo.control.AreaListVO;
import com.trs.police.subject.domain.dto.StatisticsDTO;
import com.trs.police.subject.domain.vo.NewestSensitivePersonVO;
import com.trs.police.subject.domain.vo.PersonalStatisticsVO;
import com.trs.police.subject.domain.vo.WarningModelStatisticsVO;
import com.trs.web.builder.base.RestfulResultsV2;

/**
 * 高新反恐人员服务
 *
 * <AUTHOR>
 * @date 2024/06/26
 */
public interface FkPersonService {


    /**
     * 人员区域分布
     *
     * @param dto dto
     * @return {@link RestfulResultsV2}<{@link PersonalStatisticsVO}>
     */
    RestfulResultsV2<PersonalStatisticsVO> fkArealDistribution(StatisticsDTO dto);

    /**
     * 敏感人员
     *
     * @param dto dto
     * @return {@link RestfulResultsV2}<{@link PersonalStatisticsVO}>
     */
    RestfulResultsV2<PersonalStatisticsVO> fkSensitivenessPerson(StatisticsDTO dto);

    /**
     * fk-管控区域
     *
     * @param dto dto
     * @param pageParams pageParams
     * @return {@link RestfulResultsV2}<{@link AreaListVO}>
     */
    RestfulResultsV2<AreaListVO> fkControlArea(StatisticsDTO dto, PageParams pageParams);

    /**
     * 根据标签获取idCards
     *
     * @param tag tag
     * @return {@link String}
     */
    String getIdCardsByTag(String tag);

    /**
     * FK专题大屏-人员预警模型
     *
     * @param dto 请求参数
     * @return {@link RestfulResultsV2}<{@link WarningModelStatisticsVO}>
     */
    RestfulResultsV2<WarningModelStatisticsVO> fkPersonWarningModel(StatisticsDTO dto);

    /**
     * FK专题大屏-人员预警统计
     *
     * @param dto 请求参数
     * @return {@link RestfulResultsV2}<{@link PersonalStatisticsVO}>
     */
    RestfulResultsV2<PersonalStatisticsVO> fkPersonWarningStatistics(StatisticsDTO dto);

    /**
     * FK专题大屏-杆体预警统计（感知源）
     *
     * @param dto 请求参数
     * @param pageParams 分页参数
     * @return {@link RestfulResultsV2}<{@link PersonalStatisticsVO}>
     */
    RestfulResultsV2<PersonalStatisticsVO> fkPersonWarningSourceStatistics(StatisticsDTO dto, PageParams pageParams);

    /**
     * FK专题大屏-预警地图统计
     *
     * @param dto 请求参数
     * @return {@link RestfulResultsV2}<{@link PersonalStatisticsVO}>
     */
    RestfulResultsV2<PersonalStatisticsVO> fkPersonWarningMapStatistics(StatisticsDTO dto);

    /**
     * FK专题大屏-最新敏感人员
     *
     * @param dto 请求参数
     * @param pageParams 分页参数
     * @return {@link RestfulResultsV2}<{@link NewestSensitivePersonVO}>
     */
    RestfulResultsV2<NewestSensitivePersonVO> fkPersonNewestSensitivePersonList(StatisticsDTO dto, PageParams pageParams);
}
