package com.trs.police.subject.sh.controller;

import com.alibaba.fastjson.JSONObject;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.vo.control.AreaListVO;
import com.trs.police.subject.domain.dto.PersonDTO;
import com.trs.police.subject.domain.dto.StatisticsDTO;
import com.trs.police.subject.domain.vo.PersonTrackVO;
import com.trs.police.subject.domain.vo.PersonTypeStatisticsVO;
import com.trs.police.subject.domain.vo.PersonVO;
import com.trs.police.subject.domain.vo.PersonalStatisticsVO;
import com.trs.police.subject.sh.service.ShPersonService;
import com.trs.web.builder.base.RestfulResultsV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: dingkeyu
 * @date: 2024/11/08
 * @description: 涉黄大屏-态势分析
 */
@Slf4j
@RestController
@RequestMapping("/sh/person")
public class ShPersonController {

    @Autowired
    private ShPersonService shPersonService;

    /**
     * 人员区域分布
     *
     * @param dto dto
     * @return {@link RestfulResultsV2}<{@link PersonalStatisticsVO}>
     */
    @GetMapping("/arealDistribution")
    public RestfulResultsV2<PersonalStatisticsVO> arealDistribution(StatisticsDTO dto) {
        return shPersonService.arealDistribution(dto);
    }

    /**
     * 人员类别分布
     *
     * @param dto dto
     * @return {@link RestfulResultsV2}<{@link PersonTypeStatisticsVO}>
     */
    @GetMapping("/categoricalDistribution")
    public RestfulResultsV2<PersonTypeStatisticsVO> categoricalDistribution(StatisticsDTO dto) {
        return shPersonService.categoricalDistribution(dto);
    }

    /**
     * 顶部统计
     *
     * @param dto dto
     * @return {@link RestfulResultsV2}<{@link PersonalStatisticsVO}>
     */
    @GetMapping("/topStatistics")
    public RestfulResultsV2<PersonalStatisticsVO> topStatistics(StatisticsDTO dto) {
        return shPersonService.topStatistics(dto);
    }

    /**
     * 活跃数分析
     *
     * @param dto dto
     * @return {@link RestfulResultsV2}<{@link JSONObject}>
     */
    @GetMapping("/activeStatistic")
    public RestfulResultsV2<JSONObject> activeStatistic(StatisticsDTO dto) {
        return shPersonService.activeStatistic(dto);
    }

    /**
     * 管控区域
     *
     * @param dto        dto
     * @param pageParams pageParams
     * @return {@link RestfulResultsV2}<{@link AreaListVO}>
     */
    @GetMapping("/controlArea")
    public RestfulResultsV2<AreaListVO> controlArea(StatisticsDTO dto, PageParams pageParams) {
        return shPersonService.controlArea(dto, pageParams);
    }

    /**
     * 线索挖掘
     *
     * @param dto        dto
     * @param pageParams pageParams
     * @return {@link RestfulResultsV2}<{@link JSONObject}>
     */
    @GetMapping("/clueExcavate")
    public RestfulResultsV2<JSONObject> clueExcavate(StatisticsDTO dto, PageParams pageParams) {
        return shPersonService.clueExcavate(dto, pageParams);
    }

    /**
     * 预警统计
     *
     * @param dto dto
     * @return {@link RestfulResultsV2}<{@link PersonalStatisticsVO}>
     */
    @GetMapping("/warningStatistics")
    public RestfulResultsV2<PersonalStatisticsVO> warningStatistics(StatisticsDTO dto) {
        return shPersonService.warningStatistics(dto);
    }

    /**
     * 人员列表
     *
     * @param dto dto
     * @return {@link RestfulResultsV2}<{@link PersonVO}>
     */
    @GetMapping("/list")
    public RestfulResultsV2<PersonVO> personList(PersonDTO dto) {
        return shPersonService.personList(dto);
    }

    /**
     * 人员轨迹列表
     *
     * @param dto dto
     * @return {@link RestfulResultsV2}<{@link PersonTrackVO}>
     */
    @GetMapping("/track/list")
    public RestfulResultsV2<PersonTrackVO> trackList(PersonDTO dto) {
        return shPersonService.trackList(dto);
    }
}
