package com.trs.police.subject.domain.entity.task;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.trs.police.common.core.vo.control.WarningFeedbackDetailVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @author: dingkeyu
 * @date: 2024/10/22
 * @description:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_fx_feedback")
public class FxFeedback {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 任务id
     */
    @TableField("task_id")
    private String taskId;

    /**
     * 反馈人id
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 反馈部门id
     */
    @TableField("dept_id")
    private Long deptId;

    /**
     * 反馈内容
     */
    @TableField(typeHandler = JacksonTypeHandler.class, updateStrategy = FieldStrategy.IGNORED)
    private WarningFeedbackDetailVO feedback;

    /**
     * 反馈时间
     */
    @TableField("feedback_time")
    private LocalDateTime feedbackTime;

    /**
     * 专题类型
     */
    @TableField("subject_type")
    private String subjectType;

    /**
     * 类型
     */
    @TableField("clue_excavate_type")
    private Integer clueExcavateType;
}
