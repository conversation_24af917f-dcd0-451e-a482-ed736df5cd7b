package com.trs.police.subject.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.fx.entity.Person;
import com.trs.police.subject.domain.dto.ClueExcavateDTO;
import com.trs.police.subject.domain.entity.FxGroupExcavateEntity;
import com.trs.police.subject.domain.vo.GroupClueExcavateListVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: dingkeyu
 * @date: 2024/07/04
 * @description:
 */
@Mapper
public interface FxGroupExcavateMapper extends BaseMapper<FxGroupExcavateEntity> {

    /**
     * 群体线索挖掘列表
     *
     * @param dto dto
     * @param page page
     * @return {@link Page}<{@link GroupClueExcavateListVO}>
     */
    Page<GroupClueExcavateListVO> groupClueList(@Param("dto") ClueExcavateDTO dto, @Param("page") Page page);

    /**
     * sw专题-群体线索挖掘列表
     *
     * @param dto dto
     * @param objectPage objectPage
     * @return {@link Page}<{@link GroupClueExcavateListVO}>
     */
    Page<GroupClueExcavateListVO> swGroupClueList(@Param("dto") ClueExcavateDTO dto,
                                                  Page<Object> objectPage);

    /**
     * 根据groupId查询groupName
     *
     * @param groupIds groupIds
     * @return {@link List}<{@link Person}>
     */
    List<GroupClueExcavateListVO> selectGroupNameByGroupIds(@Param("groupIds") List<Long> groupIds);

    /**
     * 根据groupId查询idCard
     *
     * @param groupId groupId
     * @return {@link List}<{@link Long}>
     */
    List<String> selectPersonIdCardByGroupIds(@Param("groupId") Integer groupId);

}
