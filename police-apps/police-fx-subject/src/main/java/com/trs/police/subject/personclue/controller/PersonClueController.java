package com.trs.police.subject.personclue.controller;

import com.trs.police.subject.personclue.service.PersonClueService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 人员线索控制层
 *
 * <AUTHOR>
 * @date 2024/1-/22
 */
@Slf4j
@RestController
@RequestMapping("/personClue")
public class PersonClueController {

    @Autowired
    private PersonClueService personClueService;

    /**
     * 执行es轨迹同步 剽窃、卖淫嫖娼、盗窃三车
     *
     * @param dwdType dwdType
     * @return 是否成功
     */
    @GetMapping("/synClueFromGjxx")
    public Boolean synClueFromGjxx(String dwdType) {
        return personClueService.synClueFromGjxx(dwdType);
    }

}
