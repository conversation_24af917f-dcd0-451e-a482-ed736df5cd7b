package com.trs.police.subject.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.subject.domain.entity.FxActionExcavateDict;
import com.trs.police.subject.domain.vo.FxActionExcavateDictVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;


/**
 * @author: dingkeyu
 * @date: 2024/05/20
 * @description:
 */
@Mapper
public interface FxActionExcavateDictMapper extends BaseMapper<FxActionExcavateDict> {

    /**
     * 搜索反邪标签
     *
     * @param tag 标签名称
     * @return FxActionExcavateDictVO>
     */
    @Select("select * from tb_fx_action_excavate_dict where name = #{tag} ")
    FxActionExcavateDictVO selectByName(String tag);
}
