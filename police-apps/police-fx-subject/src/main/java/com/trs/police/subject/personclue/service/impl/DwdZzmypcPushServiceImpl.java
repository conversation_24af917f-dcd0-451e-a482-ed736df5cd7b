package com.trs.police.subject.personclue.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.police.common.core.entity.dwd.BaseDwdEntity;
import com.trs.police.common.core.entity.dwd.DwdZzmypcEntity;
import com.trs.police.common.core.vo.control.CareMonitorVO;
import com.trs.police.subject.personclue.config.PersonClueCareMonitorConfig;
import com.trs.police.subject.common.mapper.DwdZzmypcMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 卖淫嫖娼组织者数据推送服务层
 *
 * <AUTHOR>
 * @date 2024/10/17
 */
@Component
public class DwdZzmypcPushServiceImpl extends BasePersonCluePushService<DwdZzmypcEntity> {

    @Autowired
    private DwdZzmypcMapper dwdZzmypcMapper;

    @Resource
    private PersonClueCareMonitorConfig config;

    @Override
    protected List<DwdZzmypcEntity> getPushData(CareMonitorVO careMonitorVO) {
        QueryWrapper<DwdZzmypcEntity> wrapper = new QueryWrapper<DwdZzmypcEntity>()
                .isNotNull("zjhm");
        return dwdZzmypcMapper.selectList(wrapper);
    }

    @Override
    protected List<DwdZzmypcEntity> filterData(List<DwdZzmypcEntity> list) {
        return list;
    }


    @Override
    protected Map<String, BaseDwdEntity> getGroupData(List<DwdZzmypcEntity> pushData) {
        Map<String, BaseDwdEntity> map = pushData.stream().collect(Collectors.toMap(BaseDwdEntity::getZjhm, b -> b, (a, b) -> a));
        return map;
    }


    /**
     * 获取需要命中的区域id
     *
     * @return 区域id
     */
    @Override
    protected List<Long> getHitAreaId(){
        return config.getDwdZzmypcAreaId();
    }

    @Override
    protected String getClueTypeCode() {
        return "";
    }

    /**
     * 获取线索类型
     *
     * @return 线索类型
     */
    @Override
    protected Integer getClueType(){
        // 不入线索池
        return null;
    }

    @Override
    public String key() {
        return "dwd_zzmypc";
    }

    @Override
    public String desc() {
        return "组织卖淫嫖娼";
    }
}
