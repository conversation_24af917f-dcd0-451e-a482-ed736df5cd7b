package com.trs.police.subject.sw.task;

import com.trs.police.subject.sw.service.personLabel.SwLcdgryLabelSyncServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * @author: dingkeyu
 * @date: 2024/07/05
 * @description: 同步sw流串到广安标签定时任务
 */
@Slf4j
@Component
@ConditionalOnProperty(prefix = "com.trs.schedule.lcdgry.task", name = "enable", havingValue = "true")
public class SyncSwLcdgryTask {

    @Autowired
    private SwLcdgryLabelSyncServiceImpl swLcdgryLabelSyncService;

    /**
     *  sw流串到广安标签同步
     */
    @Scheduled(cron = "${com.trs.schedule.lcdgry.task.cron:0 0 0 * * ?}")
    public void run() {
        try {
            log.info("开始同步sw流串到广安标签");
            swLcdgryLabelSyncService.syncPersonLabel();
        } catch (Exception e) {
            log.error("sw流串到广安标签同步失败", e);
        }
    }
}
