package com.trs.police.subject.domain.dto;

import com.trs.police.common.core.constant.enums.MonitorBaseTypeEnum;
import lombok.Data;

/**
 * 预警检索参数
 *
 * <AUTHOR>
 * @date 2024/04/23
 */
@Data
public class WarningSearchDTO {

    /**
     * 分页参数
     */
    private Integer pageNum;

    /**
     * 分页参数
     */
    private Integer pageSize;

    /**
     * 排序字段
     */
    private String orderField;

    /**
     * 排序方式
     */
    private String orderValue;

    /**
     * 搜索字段
     */
    private String searchField;

    /**
     * 搜索值
     */
    private String searchValue;

    /**
     * 预警开始时间
     */
    private String warningBeginTime;

    /**
     * 预警结束时间
     */
    private String warningEndTime;

    /**
     * 预警状态
     */
    private String warningStatus;

    /**
     * 预警级别
     */
    private String warningLevel;

    /**
     * 预警模型
     */
    private String warningModel;

    /**
     * 预警最小分数
     */
    private Integer minScore;

    /**
     * 预警最大分数
     */
    private Integer maxScore;

    /**
     * 导出大小
     */
    private Integer exportSize;

    /**
     * id列表
     */
    private String ids;

    /**
     * 导出的字段
     */
    private String exportFields;

    /**
     * 人员类型
     */
    private String personType;

    /**
     * 人员状态
     */
    private String personStatus;

    /**
     * 所属区县
     */
    private String areaCode;

    /**
     * 预警类型-英文名
     * {@link MonitorBaseTypeEnum}
     */
    private String warningType;

}
