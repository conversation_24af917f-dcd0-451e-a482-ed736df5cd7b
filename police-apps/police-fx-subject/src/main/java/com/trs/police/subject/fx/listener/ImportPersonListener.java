package com.trs.police.subject.fx.listener;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.common.base.PreConditionCheck;
import com.trs.common.exception.ServiceException;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.constant.enums.IdentifierTypeEnum;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.vo.control.CareMonitorVO;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import com.trs.police.common.core.vo.profile.PersonVO;
import com.trs.police.common.openfeign.starter.service.ControlService;
import com.trs.police.common.openfeign.starter.service.OssService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.subject.fx.converter.FxPersonConverter;
import com.trs.police.subject.domain.dto.ImportPersonDTO;
import com.trs.police.common.core.fx.entity.Person;
import com.trs.police.subject.domain.vo.ImportPersonVO;
import com.trs.police.subject.common.mapper.PersonMapper;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/24
 */
@Slf4j
public class ImportPersonListener extends AnalysisEventListener<ImportPersonDTO> {
    private PersonMapper personMapper;

    private PermissionService permissionService;

    private ImportPersonVO importPersonVO;

    private List<ImportPersonDTO> failData;

    private OssService ossService;

    private ControlService controlService;

    private Map<String, DeptDto> deptMap;

    private Environment environment;

    public ImportPersonListener(PersonMapper personMapper,
                                PermissionService permissionService,
                                OssService ossService,
                                ControlService controlService,
                                ImportPersonVO importPersonVO,
                                Environment environment) {
        this.personMapper = personMapper;
        this.permissionService = permissionService;
        this.ossService = ossService;
        this.controlService = controlService;
        this.importPersonVO = importPersonVO;
        this.environment = environment;
        failData = new ArrayList<>();
    }

    @Override
    public void invoke(ImportPersonDTO data, AnalysisContext context) {
        try {
            Person person = toPerson(data);
            savePerson(person);
            importPersonVO.setSuccessNum(importPersonVO.getSuccessNum()+1);
            // 调用管控
            try {
                Boolean warn = BeanFactoryHolder.getEnv().getProperty("ys.control.fx.warn.enable", Boolean.class, Boolean.TRUE);
                if (Boolean.TRUE.equals(warn)) {
                    CareMonitorVO vo = buildByPerson(person);
                    controlService.initiateCareMonitor(vo);
                }
            } catch (Exception e) {
                log.error("关注预警失败", e);
            }
        } catch (Exception e){
            failData.add(data);
            importPersonVO.getFailReasons().add(new ImportPersonVO.ImportFailVO(null, data.getAreaName(), e.getMessage()));
            importPersonVO.setFailNum(importPersonVO.getFailNum()+1);
            log.error("导入人员[{}]出错：[{}]", data.getRealName(), e.getMessage(), e);
        } finally {
            importPersonVO.setTotalNum(importPersonVO.getTotalNum()+1);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        saveFailData();
        log.info("所有数据解析完成！");
    }

    /**
     * 保存人员信息
     *
     * @param person person
     */
    private void savePerson(Person person) {
        CurrentUser currentUser = permissionService.getCurrentUserWithRole();
        if (person.getId() != null) {
            person.setUpdateUserId(currentUser.getId());
            person.setUpdateDeptId(currentUser.getDeptId());
            person.setUpdateTime(LocalDateTime.now());
            personMapper.updateById(person);
        } else {
            person.fillAuditFields(currentUser);
            personMapper.insert(person);
        }
    }

    /**
     * 保存导入失败数据
     *
     */
    private void saveFailData() {
        if(failData.isEmpty()){
            return;
        }
        try {
            String filePath = System.getProperty("java.io.tmpdir") + "/" + "temp.xlsx";
            EasyExcel.write(filePath).head(ImportPersonDTO.class).sheet("Sheet1").registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).doWrite(failData);
            File file = new File(filePath);
            FileInputStream input = new FileInputStream(file);
            MultipartFile multipartFile = new MockMultipartFile("file", file.getName(), "application/vnd.ms-excel",
                    input);
            file.delete();
            FileInfoVO fileInfoVO = ossService.upload(multipartFile, false);
            importPersonVO.setFile(fileInfoVO);
        } catch (Exception e) {
            log.error("保存导入失败数据出错：{}", e.getMessage(), e);
        }
    }

    private Person toPerson(ImportPersonDTO data) throws ServiceException {
        data.isValid();
        Person person = FxPersonConverter.CONVERTER.dtoToDo(data);
        // 构建证件照
        buildPhoto(person);
        // 构建管控区域
        buildControlArea(person);

        return person;
    }

    private void buildPhoto(Person person) {
        Person oldPerson = personMapper.selectOne(new QueryWrapper<Person>().eq("id_card", person.getIdCard()));
        if (oldPerson == null || CollectionUtils.isEmpty(oldPerson.getPhoto()) || StringUtils.isEmpty(oldPerson.getAvatar())) {
            List<PersonVO> personVOList = controlService.getPersonBackfill(person.getIdCard(), Long.valueOf(IdentifierTypeEnum.ID_NUMBER.getCode()));
            if (!CollectionUtils.isEmpty(personVOList)) {
                personVOList.stream().filter(p -> Objects.equals(p.getCertificateNumber(), person.getIdCard())).findFirst().ifPresent(p -> {
                    person.setPhoto(p.getImgs());
                    if(!CollectionUtils.isEmpty(person.getPhoto())){
                        person.setAvatar(person.getPhoto().get(0).getUrl());
                    }
                });
            }
        }
        if(oldPerson != null){
            person.setId(oldPerson.getId());
        }
    }

    private void buildControlArea(Person person) {
        if(deptMap==null){
            List<DeptDto> deptAll = permissionService.getDeptAll();
            deptMap = deptAll.stream().collect(Collectors.toMap(DeptDto::getName, deptDto -> deptDto, (v1,v2)->v1));
        }
        if(StringUtils.isNotEmpty(person.getControlUnit())){
            DeptDto deptDto = deptMap.get(person.getControlUnit());
            PreConditionCheck.checkNotNull(deptDto, "管控单位不存在");
            person.setControlAreaCode(deptDto.getDistrictCode());
        }
    }

    private CareMonitorVO buildByPerson(Person person) {
        CareMonitorVO vo = new CareMonitorVO();
        vo.setCertificateType(IdentifierTypeEnum.ID_NUMBER.getCode());
        vo.setCertificateValue(person.getIdCard());
        Long id = BeanFactoryHolder.getEnv().getProperty("ys.control.fx.default.config", Long.class);
        Objects.requireNonNull(id, "未能配置fx预警配置");
        vo.setMonitorConfigId(id);
        vo.setModuleCareMonitorHandlerClassName("com.trs.police.control.handler.care.impl.FxCareMonitorHandler");
        CurrentUser user = AuthHelper.getCurrentUser();
        vo.setCreateUserId(user.getId().toString());
        vo.setCreateDeptId(user.getDeptId().toString());
        return vo;
    }
}
