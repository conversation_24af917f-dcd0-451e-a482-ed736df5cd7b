package com.trs.police.subject.sw.service.scene;

import com.trs.police.common.core.dto.DistrictDto;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.subject.common.mapper.FxMonitorPersonMapper;
import com.trs.police.subject.common.mapper.PersonMapper;
import com.trs.police.subject.domain.dto.PersonDTO;
import com.trs.police.subject.domain.dto.SubjectSceneContext;
import com.trs.police.subject.domain.vo.PersonalStatisticsVO;
import com.trs.police.subject.helper.DistrictHelper;
import com.trs.web.builder.base.RestfulResultsV2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 人员管控-人员区域分布实现类
 *
 * <AUTHOR>
 * @date 2025/4/16
 */
@Component
public class SwPersonAreaDistributionImpl extends AbstractPersonControlAnalysisImpl<PersonalStatisticsVO> {

    @Autowired
    private PersonMapper personMapper;

    @Autowired
    private FxMonitorPersonMapper fxMonitorPersonMapper;

    @Autowired
    private DictService dictService;

    @Autowired
    private DistrictHelper districtHelper;

    @Autowired
    private SwSituationAnalysisMidAreaStatisticsImpl swSituationAnalysisMidAreaStatistics;


    @Override
    protected RestfulResultsV2<PersonalStatisticsVO> doSearch(SubjectSceneContext<PersonDTO> context) {
        final List<Long> personLabels = swSituationAnalysisMidAreaStatistics.getSwPersonLabels();
        final String parentAreaCode = swSituationAnalysisMidAreaStatistics.getParentAreaCode();
        final List<Long> modelIds = swSituationAnalysisMidAreaStatistics.getModelIds();
        Map<String, Long> areaPersonCountMap = swSituationAnalysisMidAreaStatistics.getSwPersonDeptCodes()
                .stream()
                .filter(vo -> Objects.nonNull(vo.getValue()))
                .collect(Collectors.groupingBy(vo -> {
                            if (vo.getValue().length() >= 6) {
                                return vo.getValue().substring(0, 6);
                            } else {
                                return vo.getValue();
                            }
                        },
                        Collectors.counting()));
        Map<String, PersonalStatisticsVO> voMap = fxMonitorPersonMapper.swPersonAreaDistribution(context.getDto(),
                        personLabels,
                        modelIds)
                .stream()
                .collect(Collectors.toMap(PersonalStatisticsVO::getAreaCode, Function.identity()));
        List<DistrictDto> dtos = dictService.getDistrictTree(parentAreaCode).stream()
                .flatMap(d -> {
                    List<DistrictDto> districtDtos = d.getChildren();
                    districtDtos.add(d);
                    return districtDtos.stream();
                }).sorted(Comparator.comparing(DistrictDto::getCode)).collect(Collectors.toList());
        List<PersonalStatisticsVO> statisticsVoList = dtos.stream().map(districtDto -> {
            PersonalStatisticsVO vo = voMap.getOrDefault(districtDto.getCode(), new PersonalStatisticsVO());
            vo.setAreaName(districtDto.getShortName());
            vo.setAreaShowName(districtDto.getShortName());
            vo.setAreaCode(districtDto.getCode());
            //人数
            vo.setTotalCount(areaPersonCountMap.getOrDefault(districtDto.getCode(), 0L));
            return vo;
        }).filter(vo ->!vo.getAreaCode().equals(parentAreaCode)).collect(Collectors.toList());

        return RestfulResultsV2.ok(statisticsVoList);
    }


    @Override
    public String key() {
        return "sw-personAreaDistribution";
    }

    @Override
    public String desc() {
        return "人员区域分布";
    }
}
