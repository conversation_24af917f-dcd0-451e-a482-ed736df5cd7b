package com.trs.police.subject.fx.task;

import com.trs.police.subject.fx.service.otherClue.IOtherClueSync;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * @author: dingkeyu
 * @date: 2024/07/05
 * @description: 其他线索挖掘定时任务
 */
@Slf4j
@Component
@ConditionalOnProperty(prefix = "com.trs.schedule.otherClue.task", name = "enable", havingValue = "true")
public class FxOtherClueTask {

    @Autowired
    private IOtherClueSync otherClueSync;

    /**
     *  其他线索挖掘同步
     */
    @Scheduled(cron = "${com.trs.schedule.otherClue.task.cron:0 0/30 * * * ?}")
    public void run() {
        try {
            otherClueSync.syncFxOtherClue();
        } catch (Exception e) {
            log.error("其他线索挖掘同步失败", e);
        }
    }
}
