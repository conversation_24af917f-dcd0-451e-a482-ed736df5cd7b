package com.trs.police.subject.jz.service.scene;



import com.trs.police.common.core.params.TimeParams;
import com.trs.police.common.core.utils.DateUtil;
import com.trs.police.subject.domain.dto.SubjectJqtsDTO;
import com.trs.police.subject.domain.dto.SubjectSceneContext;
import com.trs.police.subject.domain.vo.PoliceSubjectStatisticsVO;
import com.trs.police.common.core.vo.RatioItem;
import com.trs.police.subject.common.mapper.JqtsAnalyseMapper;
import com.trs.police.subject.common.service.scene.ISubjectStatisticScene;
import com.trs.police.common.core.utils.RatioCalculationUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Period;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 强制措施service实现类
 *
 * <AUTHOR>
 * @date 2024/07/10
 */
@Service
public class JzCompulsionAnalyseImpl implements ISubjectStatisticScene<PoliceSubjectStatisticsVO, SubjectJqtsDTO> {

    @Autowired
    private JqtsAnalyseMapper jqtsAnalyseMapper;

    /**
     * 数据处理
     *
     * @param context 上下文
     * @return RestfulResultsV2
     */
    @Override
    public List<PoliceSubjectStatisticsVO> search(SubjectSceneContext<SubjectJqtsDTO> context) {
        List<PoliceSubjectStatisticsVO> listChildren = jqtsAnalyseMapper.selectQzcsCount(context,context.getTopCondition());
        PoliceSubjectStatisticsVO policeQzcsVO = new PoliceSubjectStatisticsVO();
        // 检索嫌疑人时，标记检索的强制措施
        policeQzcsVO.setKey("-1");
        getAllQzcsCount(policeQzcsVO,listChildren);
        //执行计算
        Map<String, RatioItem> resultMap = doCalculateRatio(policeQzcsVO,context, listChildren);
        //构造返回结果
        if(CollectionUtils.isEmpty(listChildren)){
            listChildren = resultMap.values().stream().map(vo->new PoliceSubjectStatisticsVO(vo.getKey(), vo.getName(), null)).collect(Collectors.toList());
        }
        for (PoliceSubjectStatisticsVO policeSubjectStatisticsVO : listChildren) {
            RatioItem ratioItem = resultMap.get(policeSubjectStatisticsVO.getKey());
            if (ratioItem != null) {
                policeSubjectStatisticsVO.setCount(ratioItem.getCount());
                policeSubjectStatisticsVO.setYoy(ratioItem.getYoy());
                policeSubjectStatisticsVO.setRatio(ratioItem.getRatio());
                policeSubjectStatisticsVO.setRecentCount(ratioItem.getRecentCount());
                policeSubjectStatisticsVO.setLastYearCount(ratioItem.getLastYearCount());
            }
        }
        policeQzcsVO.setChildren(listChildren);
        List<PoliceSubjectStatisticsVO> list = new ArrayList<>();
        list.add(policeQzcsVO);
        return list;
    }


    /**
     * 执行计算
     *
     * @param list 参数
     * @param context 上下文
     * @param policeVO 总数居
     * @return Map
     */
    private Map<String, RatioItem> doCalculateRatio(PoliceSubjectStatisticsVO policeVO,SubjectSceneContext<SubjectJqtsDTO> context,List<PoliceSubjectStatisticsVO> list) {
        TimeParams timeParams = context.getTimeParams();
        //检索上一周期的数据
        TimeParams momParam = timeParams.backtrack(timeParams.generatePeriod());
        SubjectSceneContext<SubjectJqtsDTO> lastWeekSubject = sceneContext(momParam, context);
        List<PoliceSubjectStatisticsVO> recentDatas = jqtsAnalyseMapper.selectQzcsCount(lastWeekSubject,lastWeekSubject.getTopCondition());
        PoliceSubjectStatisticsVO recentVO = new PoliceSubjectStatisticsVO();
        //获取上周数据总数居，计算同比
        getAllQzcsCount(recentVO,recentDatas);
        policeVO.setRatio(RatioCalculationUtils.computeRatio(policeVO.getCount(),recentVO.getCount()));
        //检索上一年同周期的数据
        TimeParams yoyParam = timeParams.backtrack(Period.ofYears(1));
        SubjectSceneContext<SubjectJqtsDTO> lastYearSubject = sceneContext(yoyParam, context);
        List<PoliceSubjectStatisticsVO> yoyDatas = jqtsAnalyseMapper.selectQzcsCount(lastYearSubject,lastYearSubject.getTopCondition());
        PoliceSubjectStatisticsVO yoyVO = new PoliceSubjectStatisticsVO();
        //获取上一年同周期的总数居，计算环比
        getAllQzcsCount(yoyVO,yoyDatas);
        policeVO.setYoy(RatioCalculationUtils.computeRatio(policeVO.getCount(),yoyVO.getCount()));
        return RatioCalculationUtils.calculateRatio2Map(list, recentDatas, yoyDatas, PoliceSubjectStatisticsVO::getKey, PoliceSubjectStatisticsVO::getName, PoliceSubjectStatisticsVO::getCount, timeParams);
    }

    /**
     * 构造一个新的SubjectSceneContext
     *
     * @param timeParams 时间信息
     * @param context    上下文参数
     * @return SubjectSceneContext
     */
    private SubjectSceneContext<SubjectJqtsDTO> sceneContext(TimeParams timeParams, SubjectSceneContext<SubjectJqtsDTO> context) {
        SubjectSceneContext<SubjectJqtsDTO> sceneContext = new SubjectSceneContext<>();
        sceneContext.setStartTime(DateUtil.dateTimeToString(timeParams.getBeginTime()));
        sceneContext.setEndTime(DateUtil.dateTimeToString(timeParams.getEndTime()));
        context.getDto().setSearchQzcs(true);
        sceneContext.setDto(context.getDto());
        sceneContext.setSubjectType(context.getSubjectType());
        sceneContext.setTopCondition(context.getTopCondition());
        return sceneContext;
    }

    /**
     * 获取所有的异常信息
     *
     * @param policeVO 所有数据
     * @param children children
     * */
    public void getAllQzcsCount(PoliceSubjectStatisticsVO policeVO,List<PoliceSubjectStatisticsVO> children){
        Long count = 0L;
        for (PoliceSubjectStatisticsVO child : children) {
            count+=child.getCount();
        }
        policeVO.setCount(count);
    }
    @Override
    public String key() {
        return "jz-compulsion-analyse";
    }

    @Override
    public String desc() {
        return "强制措施分析";
    }

}
