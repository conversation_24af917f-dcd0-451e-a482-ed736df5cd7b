package com.trs.police.subject.domain.dto;

import com.trs.common.exception.ServiceException;
import com.trs.police.common.core.dto.BaseListDTO;
import lombok.Data;

/**
 * 假币高危人员预警dto
 *
 * <AUTHOR>
 */
@Data
public class JbGwryyjDTO extends BaseListDTO {

    private Integer minZf;

    private Integer maxZf;

    private String xyrxm;

    private String xyrcyzjzjhm;

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL> 创建时间：2020-09-10 15:49
     */
    @Override
    protected boolean checkParams() throws ServiceException {
        return false;
    }
}
