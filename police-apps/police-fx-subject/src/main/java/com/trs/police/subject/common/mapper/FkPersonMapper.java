package com.trs.police.subject.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.vo.KeyValueVO;
import com.trs.police.statistic.domain.bean.CountItem;
import com.trs.police.subject.domain.dto.FkTsDto;
import com.trs.police.subject.domain.dto.PersonDTO;
import com.trs.police.subject.domain.dto.StatisticsDTO;
import com.trs.police.subject.domain.dto.SubjectJqtsDTO;
import com.trs.police.subject.domain.dto.SubjectSceneContext;
import com.trs.police.subject.domain.entity.fkrxyj.ProfilePerson;
import com.trs.police.subject.domain.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;

/**
 * @author: dingkeyu
 * @date: 2024/06/26
 * @description:
 */
@Mapper
public interface FkPersonMapper extends BaseMapper<ProfilePerson> {

    /**
     * fk-人员区域分布
     *
     * @param dto dto
     * @return {@link List}<{@link PersonalStatisticsVO}>
     */
    List<PersonalStatisticsVO> fkArealDistribution(@Param("dto") StatisticsDTO dto);

    /**
     * fk-敏感人员
     *
     * @param dto dto
     * @return {@link List}<{@link PersonalStatisticsVO}>
     */
    List<PersonalStatisticsVO> fkSensitivenessPerson(@Param("dto") StatisticsDTO dto);

    /**
     * fk-敏感人员(群体)
     *
     * @param dto dto
     * @return {@link List}<{@link PersonalStatisticsVO}>
     */
    List<PersonalStatisticsVO> fkSensitivenessGroup(@Param("dto") StatisticsDTO dto);

    /**
     * FK专题大屏-人员预警模型
     *
     * @param dto              请求参数
     * @param labelIdJsonArray 标签id的json数组
     * @return {@link List}<{@link WarningModelStatisticsVO}>
     */
    List<WarningModelStatisticsVO> fkPersonWarningModel(@Param("dto") StatisticsDTO dto, @Param("labelIdJsonArray") String labelIdJsonArray);

    /**
     * 根据id获取预警模型名称
     *
     * @param modelIds 预警模型id
     * @return 预警模型名称
     */
    List<KeyValueVO> getWarningModelNameByIds(@Param("modelIds") Collection<String> modelIds);

    /**
     * FK专题大屏-人员预警统计
     *
     * @param dto      请求参数
     * @param labelIds 人员标签
     * @return {@link List}<{@link PersonalStatisticsVO}>
     */
    List<PersonalStatisticsVO> fkPersonWarningStatistics(@Param("dto") StatisticsDTO dto, @Param("labelIds") Collection<Long> labelIds);

    /**
     * FK专题大屏-杆体预警统计（感知源）
     *
     * @param dto  请求参数
     * @param page 分页参数
     * @return {@link List}<{@link PersonalStatisticsVO}>
     */
    Page<PersonalStatisticsVO> fkPersonWarningSourceStatistics(@Param("dto") StatisticsDTO dto, Page<PersonalStatisticsVO> page);

    /**
     * 根据人员标签查询所有下属子标签
     *
     * @param labelId 标签id
     * @return 人员标签查询和所有下属子标签
     */
    @Select("SELECT id FROM t_profile_label WHERE CONCAT(path, id, '-') LIKE CONCAT('%-', #{labelId}, '-%')")
    List<Long> getAllFkPersonLabels(@Param("labelId") Long labelId);

    /**
     * 根据人员标签查询人员档案id
     *
     * @param labelId 标签id
     * @return 人员档案id
     */
    @Select("SELECT id FROM t_profile_person WHERE " +
            "JSON_OVERLAPS(person_label, (SELECT JSON_ARRAYAGG(l.id) FROM t_profile_label l WHERE CONCAT(l.path, l.id, '-') LIKE CONCAT('%-', #{labelId}, '-%')))")
    List<Long> getFkPersonIds(@Param("labelId") Long labelId);

    /**
     * 根据人员标签查询对应人员档案的责任单位
     *
     * @param labelId 标签id
     * @return 责任单位代码
     */
    @Select("SELECT pc.person_id as `key`, pc.control_station as `value` FROM t_profile_person_police_control pc " +
            "LEFT JOIN t_profile_person p ON pc.person_id = p.id WHERE " +
            "JSON_OVERLAPS(p.person_label, (SELECT JSON_ARRAYAGG(l.id) FROM t_profile_label l WHERE CONCAT(l.path, l.id, '-') LIKE CONCAT('%-', #{labelId}, '-%')))")
    List<KeyValueVO> getFkPersonControlStations(@Param("labelId") Long labelId);

    /**
     * 根据人员标签查询对应人员档案的责任单位
     *
     * @param labelId 标签id
     * @return 责任单位代码
     */
    List<KeyValueVO> getFkPersonControlStationsByLabelIds(@Param("personLabelList") List<Long> labelId);

    /**
     * 根据人员标签查询人员标签名称
     *
     * @param labelIds 标签id
     * @return 人员标签名称
     */
    List<KeyValueVO> getPersonLabelName(@Param("labelIds") Collection<Long> labelIds);

    /**
     * 根据模型id查询预警模型名称
     *
     * @param modelIds 预警模型id
     * @return 预警模型名称
     */
    List<KeyValueVO> getModelName(@Param("modelIds") Collection<Long> modelIds);

    /**
     * 根据人员标签查询活跃数和预警数
     *
     * @param dto      请求参数
     * @param labelIds 标签id
     * @return 活跃人数
     */
    List<PersonalStatisticsVO> getActiveCountAndWarningCountByFkPersonLabels(@Param("dto") StatisticsDTO dto, @Param("labelIds") Collection<Long> labelIds);

    /**
     * 查询最新敏感人员
     *
     * @param dto       请求参数
     * @param personIds 指定人员id
     * @param page      分页参数
     * @return {@link List}<{@link NewestSensitivePersonVO}>
     */
    Page<NewestSensitivePersonVO> fkPersonNewestSensitivePersonList(@Param("dto") StatisticsDTO dto, @Param("personIds") Collection<Long> personIds, Page<NewestSensitivePersonVO> page);

    /**
     * FK专题大屏-人员预警概况统计
     *
     * @param context 请求参数
     * @return <{@link FkPersonYjgkVO}>
     */
    Long fkPersonYjgkStatisticsJd(@Param("context") SubjectSceneContext<SubjectJqtsDTO> context);

    /**
     * FK专题大屏-人员预警概况统计
     *
     * @param context 请求参数
     * @return <{@link FkPersonYjgkVO}>
     */
    Long fkPersonYjgkStatisticsYj(@Param("context") SubjectSceneContext<SubjectJqtsDTO> context);

    /**
     * FK专题大屏-人员预警概况统计
     *
     * @param context 请求参数
     * @return <{@link FkPersonYjgkVO}>
     */
    Long fkPersonYjgkStatisticsJq(@Param("context") SubjectSceneContext<SubjectJqtsDTO> context);

    /**
     * FK专题大屏-人员预警区域统计
     *
     * @param context 请求参数
     * @return {@link List}<{@link PoliceSubjectStatisticsVO}>
     */
    List<PoliceSubjectStatisticsVO> fkPersonAreaStatistics(@Param("context") SubjectSceneContext<SubjectJqtsDTO> context);

    /**
     * FK专题大屏-人员预警标签统计
     *
     * @param context 请求参数
     * @return {@link List}<{@link PoliceSubjectStatisticsVO}>
     */
    List<PoliceSubjectStatisticsVO> fkPersonLabelStatistics(@Param("context") SubjectSceneContext<SubjectJqtsDTO> context);


    /**
     * 获取活跃人数,districtList
     *
     * @param dto dto
     * @param districtList 区域list
     * @param isPcs 是否派出所
     * @param type active：活跃，wdlr：外地流入
     * @return 结果
     */
    List<CountItem> getFkPersonWarningCount(@Param("dto") FkTsDto dto, @Param("districtList") List<KeyValueVO> districtList,
                                   @Param("isPcs") Boolean isPcs, @Param("type") String type);

    /**
     * 获取反恐人员，类型统计
     *
     * @param dto dto
     * @return 结果
     */
    List<ProfilePerson> selectCountByPersonType(@Param("dto") FkTsDto dto);

    /**
     * 获取反恐人员身份证
     *
     * @param dto dto
     * @param onRecord onRecord
     * @return 结果
     */
    List<String> selectPersonIdCard(@Param("dto") FkTsDto dto,@Param("onRecord") Integer onRecord);

    /**
     * 分组统计命中模型的人员数量
     *
     * @param dto dto
     * @param idCards idcards
     * @param isPcs 是否派出所
     * @return 结果
     */
    List<CountItem> getModelHitCount(@Param("dto") FkTsDto dto, @Param("idCards") List<String> idCards,
                                     @Param("isPcs") Boolean isPcs);

    /**
     * 分组预警数量
     *
     * @param dto dto
     * @param idCards idcards
     * @param isPcs 是否派出所
     * @return 结果
     */
    List<CountItem> selectFkPerson(@Param("dto") FkTsDto dto, @Param("idCards") List<String> idCards,
                                   @Param("isPcs") Boolean isPcs);

    /**
     * FK专题大屏-区域人员轨迹
     *
     * @param dto  请求参数
     * @param page 分页参数
     * @return {@link List}<{@link PersonTrackVO}>
     */
    Page<PersonTrackVO> fkAreaPersonTrackList(@Param("dto") PersonDTO dto, Page<Object> page);

    /**
     * FK专题大屏-所有人员基本信息
     *
     * @param dto 请求参数
     * @param page 分页参数
     * @return {@link List}<{@link PersonTrackVO}>
     */
    Page<PersonTrackVO> fkPersonTrackList(@Param("dto") PersonDTO dto,
                                     Page<PersonTrackVO> page);

    /**
     * FK专题大屏-根据人员id查询人员最后研判的标签
     *
     * @param id 人员id
     * @return 人员最后研判的标签
     */
    String getLastJudgedLabel(@Param("id") Long id);

    /**
     * FK专题大屏-人员异常行为人员基本信息
     *
     * @param dto 请求参数
     * @param page 分页参数
     * @return {@link List}<{@link PersonTrackVO}>
     */
    Page<PersonTrackVO> fkPersonTrackListV2(@Param("dto") PersonDTO dto, Page<PersonTrackVO> page);

    /**
     * FK专题大屏-人员异常行为人员基本信息(查询预警)
     *
     * @param dto 请求参数
     * @param page 分页参数
     * @return {@link List}<{@link PersonTrackVO}>
     */
    Page<PersonTrackVO> fkPersonTrackListByYj(@Param("dto") PersonDTO dto, Page<PersonTrackVO> page);

    /**
     * 获取关联警情数量
     *
     * @param dto dto
     * @param onRecord 建档状态，0：未建档；1：已建档
     * @param isPcs 是否派出所
     * @return 结果
     */
    List<CountItem> getRelatedJqCount(@Param("dto") FkTsDto dto,@Param("onRecord") int onRecord,
                                      @Param("isPcs") Boolean isPcs);

    /**
     * 根据身份证获取fk人员信息
     *
     * @param idCardList idCardList
     * @return 结果
     */
    List<PersonTrackVO> findFkPersonByIdCard(@Param("idCardList") List<String> idCardList);

}
