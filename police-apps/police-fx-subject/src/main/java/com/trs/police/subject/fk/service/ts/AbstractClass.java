package com.trs.police.subject.fk.service.ts;

import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.DistrictDto;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.entity.DeptDistrict;
import com.trs.police.common.core.utils.AreaUtils;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.vo.KeyValueVO;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.subject.common.service.scene.ISubjectStatisticScene;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 抽象类
 *
 * @param <T> t
 * @param <DTO> dto
 */
@Service
public abstract class AbstractClass<T,DTO> implements ISubjectStatisticScene<T,DTO> {

    @Autowired
    private DictService dictService;

    @Autowired
    private PermissionService permissionService;

    /**
     * 获取区域前缀
     *
     * @return areaCodePrefix
     */
    public String areaCodePrefix(){
        CurrentUser currentUser = AuthHelper.getNotNullUser();
        if (currentUser == null){
            return "";
        }
        DeptDto dept = currentUser.getDept();
        if (dept == null){
            return "";
        }
        String districtCode = dept.getDistrictCode();
        return AreaUtils.areaPrefix(districtCode);
    }

    /**
     * 获取区域信息
     *
     * @param areaCode code
     * @return districtList
     */
    public List<KeyValueVO> getDistrictList(String areaCode){
        List<DistrictDto> oneLayerDistrictList;
        oneLayerDistrictList = dictService.getOneLayerDistrictList(areaCode);
        oneLayerDistrictList = CollectionUtils.isEmpty(oneLayerDistrictList) ? new ArrayList<>() : oneLayerDistrictList;
        Object areaObj = areaCode;
        List<DistrictDto> byDistrictCodes = dictService.getByDistrictCodes(Arrays.asList(areaObj));
        if (!CollectionUtils.isEmpty(byDistrictCodes)){
            oneLayerDistrictList.add(byDistrictCodes.get(0));
        }
        List<KeyValueVO> reslutList = oneLayerDistrictList.stream().map(e -> {
            KeyValueVO vo = new KeyValueVO();
            vo.setKey(e.getCode());
            vo.setValue(e.getShortName());
            return vo;
        }).collect(Collectors.toList());
        return reslutList;
    }

    /**
     * 获取区域派出所
     *
     * @param code code
     * @param isDeptId 是否要部门id
     * @return 结果
     */
    public List<KeyValueVO> gePcstList(String code, Boolean isDeptId){
        List<DeptDistrict> pcsList = permissionService.getDeptDistrictByCode(code);
        List<KeyValueVO> reslutList = pcsList.stream().map(e -> {
            KeyValueVO vo = new KeyValueVO();
            vo.setKey(isDeptId ? String.valueOf(e.getId()) : e.getCode());
            vo.setValue(e.getShortName());
            return vo;
        }).collect(Collectors.toList());
        return reslutList;
    }

}
