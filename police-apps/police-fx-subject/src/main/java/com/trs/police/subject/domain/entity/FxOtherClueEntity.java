package com.trs.police.subject.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;


/**
 * @author: dingkeyu
 * @date: 2024/07/04
 * @description: 其他线索挖掘
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "tb_fx_ocwj")
public class FxOtherClueEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 证件号码
     */
    @TableField(value = "zjhm")
    private String zjhm;

    /**
     * 手机号码
     */
    @TableField(value = "sjhm")
    private String sjhm;

    /**
     * 警情编号
     */
    @TableField(value = "jjdbh")
    private String jjdbh;

    /**
     * 报案人
     */
    @TableField(value = "xm")
    private String xm;

    /**
     * 报警时间
     */
    @TableField(value ="sj")
    private LocalDateTime sj;

    /**
     * 警情简要
     */
    @TableField(value = "jqjy")
    private String jqjy;

    /**
     * 行政区划代码
     */
    @TableField(value = "xzqhdm")
    private String xzqhdm;

    /**
     * 来源表
     */
    @TableField(value = "trs_source_from")
    private String trsSourceFrom;

    /**
     * jq/12345
     */
    @TableField(value = "type")
    private String type;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT, value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE, value = "update_time")
    private LocalDateTime updateTime;
}