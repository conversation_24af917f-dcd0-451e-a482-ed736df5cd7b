package com.trs.police.subject.domain.dto;

import com.trs.common.base.PreConditionCheck;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 布控人员管来奶dto
 * @author: gao.yuan
 */
@Data
public class MonitorIdCardRelationDTO implements Serializable {

    private static final long serialVersionUID = -7325717093554292188L;

    /**
     * 布控主键id
     */
    private Long monitorId;

    /**
     * 预警id
     */
    private Long warningId;

    /**
     * 身份证集合
     */
    private List<String> idCard;

    /**
     * 校验参数
     */
    public void checkParams() {
        PreConditionCheck.checkNotNull(monitorId, "布控id");
        PreConditionCheck.checkNotNull(idCard, "被布控fx人员身份证号");
    }
}
