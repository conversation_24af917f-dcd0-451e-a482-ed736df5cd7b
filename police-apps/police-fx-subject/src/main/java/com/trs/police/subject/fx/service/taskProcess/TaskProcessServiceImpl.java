package com.trs.police.subject.fx.service.taskProcess;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.police.common.core.constant.enums.TaskStatusEnum;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.vo.OperateVO;
import com.trs.police.common.core.vo.control.WarningDoneVO;
import com.trs.police.common.core.vo.permission.SimpleUserVO;
import com.trs.police.subject.domain.dto.TaskProcessDTO;
import com.trs.police.subject.domain.entity.task.FxFeedback;
import com.trs.police.subject.domain.entity.task.FxTaskProcess;
import com.trs.police.subject.helper.DistrictHelper;
import com.trs.police.subject.common.mapper.FeedbackMapper;
import com.trs.police.subject.common.mapper.PersonMapper;
import com.trs.police.subject.common.mapper.TaskProcessMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * @author: dingkeyu
 * @date: 2024/10/22
 * @description:
 */
@Service
public class TaskProcessServiceImpl implements ITaskProcessService {

    @Autowired
    private TaskProcessMapper taskProcessMapper;

    @Autowired
    private FeedbackMapper feedbackMapper;

    @Autowired
    private PersonMapper personMapper;

    @Autowired
    private DistrictHelper districtHelper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sign(String taskId, TaskProcessDTO dto) {
        // 增加签收记录
        FxTaskProcess taskProcess = new FxTaskProcess();
        taskProcess.setTaskId(taskId);
        taskProcess.setSubjectType(districtHelper.configSubjectType(null));
        taskProcess.setClueExcavateType(dto.getClueExcavateType());
        taskProcess.setStatus(TaskStatusEnum.SIGNED_OFF.getCode());
        OperateVO operateVO = OperateVO.newInstance();
        taskProcess.setSign(operateVO);
        taskProcessMapper.insert(taskProcess);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void feedback(String taskId, TaskProcessDTO dto) {
        String subjectType = districtHelper.configSubjectType(null);
        QueryWrapper<FxTaskProcess> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("subject_type", subjectType);
        queryWrapper.eq("clue_excavate_type", dto.getClueExcavateType());
        queryWrapper.eq("task_id", taskId);
        FxTaskProcess fxTaskProcess = taskProcessMapper.selectOne(queryWrapper);
        if (!TaskStatusEnum.FEEDBACK_PROVIDED.equals(fxTaskProcess.getStatus())) {
            fxTaskProcess.setStatus(TaskStatusEnum.FEEDBACK_PROVIDED.getCode());
            taskProcessMapper.updateById(fxTaskProcess);
        }

        // 添加反馈记录
        SimpleUserVO currentUser = AuthHelper.getNotNullSimpleUser();
        FxFeedback feedback = new FxFeedback();
        feedback.setTaskId(taskId);
        feedback.setUserId(currentUser.getUserId());
        feedback.setDeptId(currentUser.getDeptId());
        feedback.setFeedback(TaskProcessDTO.dto2WarningFeedbackDetailVO(dto));
        feedback.setFeedbackTime(LocalDateTime.now());
        feedback.setSubjectType(subjectType);
        feedback.setClueExcavateType(dto.getClueExcavateType());
        feedbackMapper.insert(feedback);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void done(String taskId, TaskProcessDTO dto) {
        String subjectType = districtHelper.configSubjectType(null);
        QueryWrapper<FxTaskProcess> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("subject_type", subjectType);
        queryWrapper.eq("clue_excavate_type", dto.getClueExcavateType());
        queryWrapper.eq("task_id", taskId);
        FxTaskProcess fxTaskProcess = taskProcessMapper.selectOne(queryWrapper);
        if (!TaskStatusEnum.COMPLETED.equals(fxTaskProcess.getStatus())) {
            // 添加完结记录
            fxTaskProcess.setStatus(TaskStatusEnum.COMPLETED.getCode());
            WarningDoneVO warningDoneVO = TaskProcessDTO.dto2WarningDoneVO(dto);
            fxTaskProcess.setDone(warningDoneVO);
            taskProcessMapper.updateById(fxTaskProcess);
        }
    }
}
