package com.trs.police.subject.fx.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trs.common.base.PreConditionCheck;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.entity.WarningFeedbackEntity;
import com.trs.police.common.core.entity.WarningProcessEntity;
import com.trs.police.common.core.vo.AreaStatisticsVO;
import com.trs.police.subject.domain.dto.MonitorIdCardRelationDTO;
import com.trs.police.subject.domain.dto.StatisticsDTO;
import com.trs.police.subject.domain.dto.WarningSearchDTO;
import com.trs.police.subject.domain.entity.FxWarningEntity;
import com.trs.police.subject.domain.vo.FxModelWarningListVO;
import com.trs.police.subject.domain.vo.FxMonitorWarningListVO;
import com.trs.police.subject.domain.vo.PersonalStatisticsVO;
import com.trs.police.subject.helper.DistrictHelper;
import com.trs.police.subject.fx.manager.FxWarningManager;
import com.trs.police.subject.common.mapper.FxMonitorPersonMapper;
import com.trs.police.subject.common.mapper.FxWarningMapper;
import com.trs.police.subject.common.mapper.SubjectWarningFeedbackMapper;
import com.trs.police.subject.common.mapper.WarningProcessMapper;
import com.trs.police.subject.fx.service.FxWarningService;
import com.trs.web.builder.base.RestfulResultsV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 模型预警service实现
 *
 * <AUTHOR>
 * @date 2024/04/23
 */
@Slf4j
@Service
public class FxWarningServiceImpl extends ServiceImpl<FxWarningMapper, FxWarningEntity> implements FxWarningService {

    @Resource
    private FxWarningManager fxWarningManager;
    @Autowired
    private FxMonitorPersonMapper fxMonitorPersonMapper;
    @Autowired
    private SubjectWarningFeedbackMapper subjectWarningFeedbackMapper;
    @Autowired
    private WarningProcessMapper warningProcessMapper;
    @Autowired
    private DistrictHelper districtHelper;

    /**
     * 获取模型预警列表
     *
     * @param dto 请求参数
     * @return 列表数据
     */
    @Override
    public RestfulResultsV2<FxModelWarningListVO> getWarningModelList(WarningSearchDTO dto) {
        try {
            return fxWarningManager.getWarningModelList(dto);
        } catch (Exception e) {
            log.error("获取模型预警失败", e);
            return RestfulResultsV2.error("获取模型预警失败" + e.getMessage());
        }
    }

    /**
     * 模型预警签收操作
     *
     * @param id 模型预警id
     * @return 结果
     */
    @Override
    public RestfulResultsV2<Boolean> warningModelReceipted(Long id) {
        try {
            return fxWarningManager.warningModelReceipted(id);
        } catch (Exception e) {
            log.error("签收失败", e);
            return RestfulResultsV2.error("签收失败" + e.getMessage());
        }
    }

    /**
     * 模型预警-预警状态更新
     *
     * @param id             模型预警id
     * @param isModelWarning 是否是来自模型预警的更新
     * @return 更新是否成功
     */
    @Override
    public Boolean updateWarningStatus(Long id, Integer warningStatus, Boolean isModelWarning) {
        try {
            return fxWarningManager.updateWarningStatus(id, warningStatus, isModelWarning);
        } catch (Exception e) {
            log.error("预警状态更新失败", e);
            return false;
        }
    }

    @Override
    public RestfulResultsV2<FxMonitorWarningListVO> getWarningMonitorList(WarningSearchDTO dto) {
        try {
            return fxWarningManager.getWarningMonitorList(dto);
        } catch (Exception e) {
            log.error("获取布控预警列表数据失败", e);
            return RestfulResultsV2.error("获取布控预警列表数据失败" + e.getMessage());
        }
    }

    @Override
    public void exportWarningModelList(WarningSearchDTO dto, HttpServletResponse response) {
        try {
            fxWarningManager.exportWarningModelList(dto, response);
        } catch (Exception e) {
            log.error("导出模型预警列表失败", e);
        }
    }

    @Override
    public RestfulResultsV2<Long> createMonitorPersonRelation(MonitorIdCardRelationDTO dto) {
        try {
            dto.checkParams();
            return fxWarningManager.createMonitorPersonRelation(dto);
        } catch (Exception e) {
            log.error("添加fx布控-人员关联关系失败", e);
            return RestfulResultsV2.error("添加fx布控-人员关联关系失败" + e.getMessage());
        }
    }

    @Override
    public RestfulResultsV2<Long> getModelWarningId(Long monitorId) {
        try {
            PreConditionCheck.checkNotNull(monitorId, "布控id不能为空");
            return fxWarningManager.getModelWarningId(monitorId);
        } catch (Exception e) {
            log.error("获取模型预警id失败", e);
            return RestfulResultsV2.error("获取模型预警id失败" + e.getMessage());
        }
    }

    @Override
    public RestfulResultsV2<PersonalStatisticsVO> warningStatistics(StatisticsDTO dto) {
        // 默认专题类型为fx
        dto.setSubjectType(districtHelper.configSubjectType(dto.getSubjectType()));
        List<AreaStatisticsVO> bkWarningStatistics = fxMonitorPersonMapper.bkWarningStatistics(dto);

        List<PersonalStatisticsVO> vos = bkWarningStatistics.stream().map(e -> {
            PersonalStatisticsVO vo = PersonalStatisticsVO.of(e);
            if (!StringUtils.isEmpty(e.getIds())) {
                // 签收数统计
                Long signCount = warningProcessMapper.selectCount(new QueryWrapper<WarningProcessEntity>()
                        .in("warning_id", e.getIds().split(",")));
                // 反馈数统计
                Long feedbackCount = subjectWarningFeedbackMapper.selectCount(new QueryWrapper<WarningFeedbackEntity>()
                        .in("warning_id", e.getIds().split(",")));

                vo.setFeedbackCount(feedbackCount);
                vo.setSignCount(signCount);
            }
            return vo;
        }).collect(Collectors.toList());
        Map<String, PersonalStatisticsVO> map = vos.stream().collect(Collectors.toMap(PersonalStatisticsVO::getAreaCode, Function.identity()));
        //获取市级下所有区县信息,默认泸州市
        List<Map.Entry<String, String>> categoryList = districtHelper.categoryList(dto.getSubjectType());
        List<PersonalStatisticsVO> voList = new ArrayList<>();
       for(Map.Entry<String, String> entity : categoryList) {
           PersonalStatisticsVO statisticsVO = map.get(entity.getValue());
           if (Objects.nonNull(statisticsVO)) {
               statisticsVO.setAreaName("sw".equals(dto.getSubjectType()) ? entity.getKey().replace("派出所", "") : entity.getKey());
               statisticsVO.setAreaShowName(entity.getKey());
               voList.add(statisticsVO);
           } else {
               PersonalStatisticsVO vo = new PersonalStatisticsVO();
               vo.setAreaCode(entity.getValue());
               vo.setAreaName("sw".equals(dto.getSubjectType()) ? entity.getKey().replace("派出所", "") : entity.getKey());
               vo.setAreaShowName(entity.getKey());
               voList.add(vo);
           }
       }
        return RestfulResultsV2.ok(voList);
    }

    @Override
    public void exportWarningMonitorList(WarningSearchDTO dto, HttpServletResponse response) {
        try {
            fxWarningManager.exportWarningMonitorList(dto, response);
        } catch (Exception e) {
            log.error("导出布控预警失败", e);
        }
    }
}
