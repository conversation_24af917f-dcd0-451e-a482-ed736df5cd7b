package com.trs.police.subject.test;

import com.trs.police.subject.FxSubjectApp;
import com.trs.police.subject.domain.dto.WarningSearchDTO;
import com.trs.police.subject.domain.vo.FxModelWarningListVO;
import com.trs.police.subject.fx.service.FxWarningService;
import com.trs.web.builder.base.RestfulResultsV2;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest(classes = FxSubjectApp.class)
public class FxWarnServiceTest {

    @Resource
    private FxWarningService fxWarningService;

    @Test
    public void warnListTest() {
        WarningSearchDTO dto = new WarningSearchDTO();
        RestfulResultsV2<FxModelWarningListVO> modelList = fxWarningService.getWarningModelList(dto);
        System.out.println("成功");;
    }

}
