package com.trs.police.subject.test;

import com.alibaba.fastjson.JSONObject;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.subject.FxSubjectApp;
import com.trs.police.subject.domain.dto.PersonDTO;
import com.trs.police.subject.domain.dto.StatisticsDTO;
import com.trs.police.subject.domain.dto.SubjectJqtsDTO;
import com.trs.police.subject.domain.dto.SubjectSceneContext;
import com.trs.police.subject.domain.vo.PersonDetailVO;
import com.trs.police.subject.domain.vo.PersonTypeStatisticsVO;
import com.trs.police.subject.domain.vo.PersonVO;
import com.trs.police.subject.domain.vo.PersonalStatisticsVO;
import com.trs.police.subject.fx.service.FxPersonService;
import com.trs.police.subject.common.service.scene.ISubjectStatisticScene;
import com.trs.police.subject.common.service.scene.SubjectSceneSearchFactory;
import com.trs.web.builder.base.RestfulResultsV2;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

@SpringBootTest(classes = FxSubjectApp.class)
public class FxPersonServiceTest {

    @Resource
    private FxPersonService fxPersonService;

    @Test
    public void arealDistributionTest() {
        StatisticsDTO dto = new StatisticsDTO();
        dto.setStartTime("2024-01-01 00:00:00");
        dto.setEndTime("2024-12-31 23:59:59");
        List<PersonalStatisticsVO> datas = fxPersonService.arealDistribution(dto).getDatas();
        System.out.println();
    }

    @Test
    public void categoricalDistributionTest() {
        StatisticsDTO dto = new StatisticsDTO();
        dto.setStartTime("2024-04-25 00:00:00");
        dto.setEndTime("2024-04-25 23:59:59");
        dto.setSubjectType("sw");
        List<PersonTypeStatisticsVO> datas = fxPersonService.categoricalDistribution(dto).getDatas();
        System.out.println();
    }

    @Test
    public void stateDistributionTest() {
        StatisticsDTO dto = new StatisticsDTO();
        dto.setStartTime("2024-04-25 00:00:00");
        dto.setEndTime("2024-04-25 23:59:59");
        List<PersonalStatisticsVO> datas = fxPersonService.stateDistribution(dto).getDatas();
        System.out.println();
    }

    @Test
    public void topStatisticsTest() {
        StatisticsDTO dto = new StatisticsDTO();
        dto.setStartTime("2024-01-01 00:00:00");
        dto.setEndTime("2024-12-31 23:59:59");
        List<PersonalStatisticsVO> datas = fxPersonService.topStatistics(dto).getDatas();
        System.out.println();
    }

    @Test
    public void activeStatisticTest() {
        StatisticsDTO dto = new StatisticsDTO();
        dto.setStartTime("2024-01-01 00:00:00");
        dto.setEndTime("2024-12-31 23:59:59");
        List<JSONObject> datas = fxPersonService.activeStatistic(dto).getDatas();
        System.out.println();
    }

    @Test
    public void exceptionStatisticTest() {
        StatisticsDTO dto = new StatisticsDTO();
        dto.setStartTime("2024-01-01 00:00:00");
        dto.setEndTime("2024-12-31 23:59:59");
        List<JSONObject> datas = fxPersonService.exceptionBehaviorStatistics(dto).getDatas();
        System.out.println();
    }

    @Test
    public void testList() {
        PersonDTO personDTO = new PersonDTO();
        PageResult<PersonVO> pageResult = fxPersonService.personList(personDTO);
        System.out.println(pageResult);
    }

    @Test
    public void lkPersonListTest() {
        PersonDTO dto = new PersonDTO();
        dto.setStartTime("2024-01-01 00:00:00");
        dto.setEndTime("2024-12-31 23:59:59");
        dto.setTopCondition("jmryCount");
        dto.setSubjectType("jz");
        List<PersonVO> datas = fxPersonService.bkPersonList(dto).getDatas();
        System.out.println();
    }

    @Test
    public void personDetailTest() {
        RestfulResultsV2<PersonDetailVO> jz = fxPersonService.personDetail("jz", "918007198705219081");
        System.out.println(jz);
    }

    @Test
    public void yjgkTest() {
        SubjectSceneContext<SubjectJqtsDTO> context = new SubjectSceneContext<>();
        context.setStartTime("2023-01-01 00:00:00");
        context.setEndTime("2025-01-01 00:00:00");
        context.setDto(new SubjectJqtsDTO());
        ISubjectStatisticScene subjectSceneSearchImpl = SubjectSceneSearchFactory.getSubjectSceneSearchImpl("fk-personYjgkStatistic");
        List search = subjectSceneSearchImpl.search(context);
        System.out.println();
    }

    @Test
    public void AreaStatisticTest() {
        SubjectSceneContext<SubjectJqtsDTO> context = new SubjectSceneContext<>();
        context.setDto(new SubjectJqtsDTO());
        ISubjectStatisticScene subjectSceneSearchImpl = SubjectSceneSearchFactory.getSubjectSceneSearchImpl("fk-personAreaStatistic");
        List search = subjectSceneSearchImpl.search(context);
        System.out.println();
    }

    @Test
    public void personLabelStatisticTest() {
        SubjectSceneContext<SubjectJqtsDTO> context = new SubjectSceneContext<>();
        context.setDto(new SubjectJqtsDTO());
        ISubjectStatisticScene subjectSceneSearchImpl = SubjectSceneSearchFactory.getSubjectSceneSearchImpl("fk-personLabelStatistic");
        List search = subjectSceneSearchImpl.search(context);
        System.out.println();
    }

}
