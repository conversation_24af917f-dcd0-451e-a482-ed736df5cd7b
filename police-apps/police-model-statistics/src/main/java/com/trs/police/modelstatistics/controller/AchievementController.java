package com.trs.police.modelstatistics.controller;

import com.trs.police.modelstatistics.domain.dto.AchievementDTO;
import com.trs.police.modelstatistics.domain.vo.AchievementVO;
import com.trs.police.modelstatistics.service.IAchievementService;
import com.trs.web.builder.base.RestfulResultsV2;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Description:
 *
 * @author: lv.bo
 * @create: 2024-07-31 16:39
 */
@RestController
@Api(value = "成果", tags = "成果")
@RequestMapping("/achievement")
@Slf4j
public class AchievementController {


    @Autowired
    private List<IAchievementService> achievementServiceList;

    /**
     *  查询成果列表
     *
     * @param dto dto
     * @return 列表
     */
    @PostMapping("/queryAchievementList")
    @ApiOperation(value = "查询成果列表", notes = "查询成果列表")
    public RestfulResultsV2<AchievementVO> queryAchievementList(@RequestBody @Validated AchievementDTO dto) {
        for (IAchievementService achievementService : achievementServiceList) {
            if (achievementService.support(dto.getType())) {
                return achievementService.queryAchievementList(dto);
            }
        }
        throw new RuntimeException("没有找到对应类型的成果查询服务");
    }
}
