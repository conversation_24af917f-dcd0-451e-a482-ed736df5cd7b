package com.trs.police.modelstatistics.common.util;

import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.constant.enums.TimeRangeEnum;
import io.vavr.Tuple2;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoField;
import java.time.temporal.TemporalAdjusters;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

/**
 * Description: 时间工具类
 *
 * @author: lv.bo
 * @create: 2023-12-12 15:36
 */
public class TimeUtil {

    public static final LocalDateTime DEFAULT_BEGIN_TIME = LocalDateTime.parse("2000-01-01T00:00:00");

    public static final String YYYYMMDD = "yyyy-MM-dd";

    public static final String YYYYMMDDHHMMSS = "yyyy-MM-dd HH:mm:ss";

    /**
     * 获取当前自然周的第一天和当前天
     *
     * @return 返回值
     */
    public static Tuple2<LocalDateTime, LocalDateTime> getCurrWeekStartAndEndTime() {
        // 获取当前日期
        Calendar calendar = Calendar.getInstance();
        Date currentDate = calendar.getTime();

        // 获取当前日期所在周的第一天（周一）的00:00:00
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        calendar.setTime(currentDate);
        calendar.set(Calendar.DAY_OF_WEEK, calendar.getFirstDayOfWeek());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Instant instant = Instant.ofEpochMilli(calendar.getTimeInMillis());
        final LocalDateTime firstDay = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());

        // 获取当前日期的23:59:59
        calendar.setTime(currentDate);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        Instant instant1 = Instant.ofEpochMilli(calendar.getTimeInMillis());
        final LocalDateTime currDay = LocalDateTime.ofInstant(instant1, ZoneId.systemDefault());

        return new Tuple2<>(firstDay, currDay);
    }

    /**
     * 获取当前自然周的第一天和当前天
     *
     * @return 返回值
     */
    public static Tuple2<String, String> getCurrWeekStartAndEndTimeStr() {
        // 获取当前日期
        Calendar calendar = Calendar.getInstance();
        Date currentDate = calendar.getTime();

        // 获取当前日期所在周的第一天（周一）的00:00:00
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        calendar.setTime(currentDate);
        calendar.set(Calendar.DAY_OF_WEEK, calendar.getFirstDayOfWeek());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        final String startTime = TimeUtils.dateToString(calendar.getTime(), TimeUtils.YYYYMMDD_HHMMSS);

        // 获取当前日期的23:59:59
        calendar.setTime(currentDate);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        final String currTime = TimeUtils.dateToString(calendar.getTime(), TimeUtils.YYYYMMDD_HHMMSS);

        return new Tuple2<>(startTime, currTime);
    }



    /**
     * 根据秒或毫秒的时间戳获取时间
     *
     * @param time 时间戳
     * @return LocalDateTime
     */
    public static LocalDateTime getLocalDateTime(Long time) {
        if (time == null) {
            return null;
        }
        Instant instant = null;
        // 毫秒秒
        if (13 == String.valueOf(time).length()) {
            instant = Instant.ofEpochMilli(time);
        } else {
            // 秒
            instant = Instant.ofEpochSecond(time);
        }
        return LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
    }

    /**
     * 生成简洁格式时间
     *
     * @param time 时间
     * @return 字符串
     */
    public static String getSimpleTime(LocalDateTime time) {
        LocalDateTime now = LocalDateTime.now();
        LocalDate today = LocalDate.now();
        if (time == null) {
            return "";
        }
        if (time.isBefore(now)) {
            //本世纪前
            if (time.isBefore(getBeginTime(TimeRangeEnum.ALL, today))) {
                return time.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
                //本世纪-今年
            } else if (time.isBefore(getBeginTime(TimeRangeEnum.CURRENT_YEAR, today))) {
                return time.format(DateTimeFormatter.ofPattern("yy-MM-dd HH:mm"));
                //今年-昨天
            } else if (time.isBefore(getBeginTime(TimeRangeEnum.YESTERDAY, today))) {
                return time.format(DateTimeFormatter.ofPattern("MM-dd HH:mm"));
                //昨天-今天
            } else if (time.isBefore(getBeginTime(TimeRangeEnum.TODAY, today))) {
                return time.format(DateTimeFormatter.ofPattern("昨天 HH:mm"));
                //今天-1小时前
            } else if (time.isBefore(now.minusHours(1))) {
                return time.format(DateTimeFormatter.ofPattern("HH:mm"));
                //1小时前-现在
            } else {
                Duration duration = Duration.between(time, now);
                return durationToString(duration) + "前";
            }
        } else {
            return time.format(DateTimeFormatter.ofPattern("MM-dd HH:mm"));
        }
    }

    /**
     * 按照时间范围返回开始时间
     *
     * @param rangeEnum 时间范围
     * @param today     时间
     * @return 时间
     */
    public static LocalDateTime getBeginTime(TimeRangeEnum rangeEnum, LocalDate today) {
        int season = (int) Math.ceil((double) today.getMonthValue() / 3);
        switch (rangeEnum) {
            case ALL:
                return DEFAULT_BEGIN_TIME;
            case TODAY:
                return today.atStartOfDay();
            case CURRENT_WEEK:
                return today.with(DayOfWeek.MONDAY).atStartOfDay();
            case CURRENT_MONTH:
                return today.with(ChronoField.DAY_OF_MONTH, 1).atStartOfDay();
            case CURRENT_SEASON:
                return today.withMonth((season - 1) * 3 + 1).withDayOfMonth(1).atStartOfDay();
            case CURRENT_YEAR:
                return today.with(ChronoField.DAY_OF_YEAR, 1).atStartOfDay();
            case RECENT_DAY:
                return LocalDateTime.now().minusHours(24);
            case RECENT_THREE_DAYS:
                return today.minusDays(2).atStartOfDay();
            case RECENT_WEEK:
                return today.minusDays(6).atStartOfDay();
            case RECENT_MONTH:
                return today.minusMonths(1).atStartOfDay();
            case RECENT_SEASON:
                return today.minusMonths(3).atStartOfDay();
            case RECENT_YEAR:
                return today.minusYears(1).atStartOfDay();
            case YESTERDAY:
                return today.minusDays(1).atStartOfDay();
            case LAST_WEEK:
                return today.minusWeeks(1).with(DayOfWeek.MONDAY).atStartOfDay();
            case LAST_MONTH:
                return today.minusMonths(1).with(ChronoField.DAY_OF_MONTH, 1).atStartOfDay();
            case LAST_SEASON:
                return today.withMonth((season - 1) * 3 + 1).minusMonths(3).withDayOfMonth(1).atStartOfDay();
            case LAST_HALF_YEAR:
                return today.minusMonths(6).withDayOfMonth(1).atStartOfDay();
            case LAST_YEAR:
                return today.minusYears(1).withDayOfYear(1).atStartOfDay();
            case TOMORROW:
                return today.plusDays(1).atStartOfDay();
            default:
                return LocalDateTime.now();
        }
    }

    /**
     * 时间段转换字符串 > 48h:      xx天 > 1h <=48h: xx小时 <=60min:    xx分
     *
     * @param duration {@link Duration}
     * @return 字符串
     */
    public static String durationToString(Duration duration) {
        Duration twoDay = Duration.ofDays(2);
        Duration anHour = Duration.ofHours(1);
        if (duration.compareTo(twoDay) > 0) {
            return duration.toDays() + "天";
        } else if (duration.compareTo(anHour) > 0) {
            return duration.toHours() + "小时";
        } else if (!duration.isNegative()) {
            return duration.toMinutes() + "分钟";
        } else {
            return "0分钟";
        }
    }

    /**
     * 生成简洁格式日期
     *
     * @param time 时间
     * @return 字符串
     */
    public static String getSimpleDate(LocalDateTime time) {
        LocalDate today = LocalDate.now();
        LocalDateTime todayMax = LocalDateTime.of(today, LocalTime.MAX);
        if (time == null) {
            return "";
        }
        if (time.isBefore(todayMax)) {
            //本世纪前
            if (time.isBefore(getBeginTime(TimeRangeEnum.ALL, today))) {
                return time.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                //本世纪-今年
            } else if (time.isBefore(getBeginTime(TimeRangeEnum.CURRENT_YEAR, today))) {
                return time.format(DateTimeFormatter.ofPattern("yy-MM-dd"));
                //今年-昨天
            } else if (time.isBefore(getBeginTime(TimeRangeEnum.YESTERDAY, today))) {
                return time.format(DateTimeFormatter.ofPattern("MM-dd"));
                //昨天-今天
            } else if (time.isBefore(getBeginTime(TimeRangeEnum.TODAY, today))) {
                return "昨天";
                //今天-1小时前
            } else {
                return "今天";
            }
        } else {
            return time.format(DateTimeFormatter.ofPattern("MM-dd"));
        }
    }

    /**
     * 获取指定月份后的时间
     *
     * @param timeStr     时间字符串
     * @param inputRegex  输入格式
     * @param month       指定几个月
     * @param returnRegex 返回格式
     * @return 返回指定格式的时间字符串
     */
    public static String dateBefOrAftMonth(String timeStr, String inputRegex, int month, String returnRegex) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(inputRegex);
        if(timeStr.length() > 10){
            LocalDateTime date = LocalDateTime.parse(timeStr, formatter);
            LocalDateTime lastMonthToday = date.plusMonths(month);
            DateTimeFormatter returnFormatter = DateTimeFormatter.ofPattern(returnRegex);
            return lastMonthToday.format(returnFormatter);
        }
        LocalDate date = LocalDate.parse(timeStr, formatter);
        LocalDate lastMonthToday = date.plusMonths(month);
        DateTimeFormatter returnFormatter = DateTimeFormatter.ofPattern(returnRegex);
        return lastMonthToday.format(returnFormatter);
    }

    /**
     * 获取指定年份后的时间
     *
     * @param timeStr     时间字符串
     * @param inputRegex  输入格式
     * @param year        指定几年
     * @param returnRegex 返回格式
     * @return 返回指定格式的时间字符串
     */
    public static String dateBefOrAftYear(String timeStr, String inputRegex, int year, String returnRegex) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(inputRegex);
        if(timeStr.length() > 10){
            LocalDateTime date = LocalDateTime.parse(timeStr, formatter);
            LocalDateTime lastMonthToday = date.plusYears(year);
            DateTimeFormatter returnFormatter = DateTimeFormatter.ofPattern(returnRegex);
            return lastMonthToday.format(returnFormatter);
        }
        LocalDate date = LocalDate.parse(timeStr, formatter);
        LocalDate lastMonthToday = date.plusYears(year);
        DateTimeFormatter returnFormatter = DateTimeFormatter.ofPattern(returnRegex);
        return lastMonthToday.format(returnFormatter);
    }

    /**
     * 获取上周的时间范围
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 时间范围
     */
    public static Tuple2<String, String> getLastWeekTimeRange(String startTime, String endTime) {
        if (StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime)) {
            return new Tuple2<>(startTime, endTime);
        }
        String lastWeekStartTime = TimeUtils.dateBefOrAft(startTime, -7, TimeUtil.YYYYMMDDHHMMSS);
        String lastWeekEndTime = TimeUtils.dateBefOrAft(endTime, -7, TimeUtil.YYYYMMDDHHMMSS);
        return new Tuple2<>(lastWeekStartTime, lastWeekEndTime);
    }

    /**
     *  获取指定时间的上一周的时间范围
     *
     * @param now 指定时间
     * @return 上一周的时间范围
     */
    public static Tuple2<String, String> getLastWeekTimeRange(LocalDateTime now){

        LocalDateTime startTime = now.minusWeeks(1).with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY)).withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endTime = now.minusWeeks(1).with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY)).withHour(23).withMinute(59).withSecond(59).withNano(999_999_999);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YYYYMMDDHHMMSS);
        String startTimeStr = startTime.format(formatter);
        String endTimeStr = endTime.format(formatter);
        return new Tuple2<>(startTimeStr, endTimeStr);
    }

    /**
     * 获取环比时间范围
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 时间范围
     */
    public static Tuple2<String, String> getLastMonthTimeRange(String startTime, String endTime) {
        if (StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime)) {
            return new Tuple2<>(startTime, endTime);
        }
        String lastMonthStartTime;
        String lastMonthEndTime;
        if(startTime.length() > 10){
            lastMonthStartTime = TimeUtil.dateBefOrAftMonth(startTime, TimeUtil.YYYYMMDDHHMMSS, -1, TimeUtil.YYYYMMDDHHMMSS);
            lastMonthEndTime = TimeUtil.dateBefOrAftMonth(endTime, TimeUtil.YYYYMMDDHHMMSS, -1, TimeUtil.YYYYMMDDHHMMSS);
            return new Tuple2<>(lastMonthStartTime, lastMonthEndTime);
        }
        lastMonthStartTime = TimeUtil.dateBefOrAftMonth(startTime, TimeUtil.YYYYMMDD, -1, TimeUtil.YYYYMMDD);
        lastMonthEndTime = TimeUtil.dateBefOrAftMonth(endTime, TimeUtil.YYYYMMDD, -1, TimeUtil.YYYYMMDD);
        return new Tuple2<>(lastMonthStartTime, lastMonthEndTime);
    }

    /**
     * 获取同比时间范围
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 时间范围
     */
    public static Tuple2<String, String> getLastYearTimeRange(String startTime, String endTime) {
        if (StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime)) {
            return new Tuple2<>(startTime, endTime);
        }
        String lastYearStartTime;
        String lastYearEndTime;
        if(startTime.length() > 10){
            lastYearStartTime = TimeUtil.dateBefOrAftYear(startTime, TimeUtil.YYYYMMDDHHMMSS, -1, TimeUtil.YYYYMMDDHHMMSS);
            lastYearEndTime = TimeUtil.dateBefOrAftYear(endTime, TimeUtil.YYYYMMDDHHMMSS, -1, TimeUtil.YYYYMMDDHHMMSS);
            return new Tuple2<>(lastYearStartTime, lastYearEndTime);
        }
        lastYearStartTime = TimeUtil.dateBefOrAftYear(startTime, TimeUtil.YYYYMMDD, -1, TimeUtil.YYYYMMDD);
        lastYearEndTime = TimeUtil.dateBefOrAftYear(endTime, TimeUtil.YYYYMMDD, -1, TimeUtil.YYYYMMDD);
        return new Tuple2<>(lastYearStartTime, lastYearEndTime);
    }

    /**
     *  获取指定localDateTime的字符串日期
     *
     * @param localDateTime localDateTime
     * @return 字符串日期
     */
    public static String dateToString(LocalDateTime localDateTime){
        return dateToString(localDateTime, YYYYMMDDHHMMSS);
    }


    /**
     *  获取指定localDateTime的字符串日期
     *
     * @param localDateTime localDateTime
     * @param format 格式化字符串
     * @return 字符串日期
     */
    public static String dateToString(LocalDateTime localDateTime, String format){
        if(Objects.isNull(localDateTime)){
            return "";
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return localDateTime.format(formatter);
    }
}
