package com.trs.police.modelstatistics.service;

import com.trs.police.common.core.vo.control.WarningStatisticsVO;
import com.trs.police.modelstatistics.domain.dto.ModelDto;
import com.trs.police.modelstatistics.domain.dto.MonitorWarningModelListDto;
import com.trs.police.modelstatistics.domain.entity.WarningModel;
import com.trs.police.modelstatistics.domain.vo.CountVO;
import com.trs.police.modelstatistics.domain.vo.ModelUseVO;
import com.trs.police.modelstatistics.domain.vo.MonitorWarningModelVO;
import com.trs.police.modelstatistics.domain.vo.WarningModelVO;
import com.trs.web.builder.base.RestfulResultsV2;

import java.util.List;

/**
 * Description:
 *
 * @author: lv.bo
 * @create: 2024-07-30 17:30
 */
public interface WarningModelService extends BaseService<WarningModel, WarningModelVO> {
    /**
     *获取模型总使用情况
     *
     * @param dto 参数
     * @return 数量
     */
    CountVO getTotalDataAbility(ModelDto dto);

    /**
     *  获取预警模型
     *
     * @param vo vo
     * @param type 类型
     * @return 预警模型
     */
    List<WarningModel> getWarningModel(WarningStatisticsVO vo, int type);

    /**
     * 获取模型使用趋势
     *
     * @param dto 参数
     * @return 模型使用趋势
     */
    List<CountVO> moduleTrend(ModelDto dto);

    /**
     * 获取模型使用情况
     *
     * @param dto 参数
     * @return 模型使用情况
     */
    RestfulResultsV2<ModelUseVO> modelUsedInfo(ModelDto dto);

    /**
     * 获取模型介绍
     *
     * @param id 模型id
     * @return 模型信息
     */
    WarningModelVO modelIntroduction(Integer id);

    /**
     * 获取模型列表
     *
     * @param dto 入参
     * @return 获取模型列表
     */
    RestfulResultsV2<MonitorWarningModelVO> getMonitorWarningModelList(MonitorWarningModelListDto dto);

    /**
     * 同步数据
     */
    void tbData();
}
