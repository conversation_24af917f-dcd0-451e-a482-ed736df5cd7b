package com.trs.police.modelstatistics.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.trs.police.modelstatistics.domain.dto.MonitorWarningModelListDto;
import com.trs.police.modelstatistics.domain.entity.WarningModel;
import com.trs.police.modelstatistics.domain.vo.ModelDailyStatisticsVO;
import com.trs.police.modelstatistics.domain.vo.MonitorWarningModelVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Description:
 *
* @author: lv.bo
* @create: 2024-07-30 17:04
*
*/
@Mapper
public interface WarningModelMapper extends BaseMapper<WarningModel> {
    /**
     * 初始化所有模型信息
     *
     * @return 所有模型信息
     */
    List<ModelDailyStatisticsVO> selectAllInfo();

    /**
     *  查询监控模型列表
     *
     * @param page 分页参数
     * @param dto dto
     * @return 列表
     */
    IPage<MonitorWarningModelVO> selectMonitorWarningModelList(IPage<MonitorWarningModelVO> page,@Param("dto") MonitorWarningModelListDto dto);
}
