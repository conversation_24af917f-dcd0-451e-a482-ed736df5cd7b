package com.trs.police.task.tracing.listener.portal;

import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.task.tracing.domain.entity.TaskTracingReply;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * Description:
 *
 * <AUTHOR>
 * @date: 2024/3/21 19:59
 */
@Getter
public class ReplyMessageEvent extends ApplicationEvent {

    private static final long serialVersionUID = 5923934508411760016L;

    private CurrentUser currentUser;

    private TaskTracingReply taskTracingReply;

    public ReplyMessageEvent(Object source, CurrentUser currentUser, TaskTracingReply taskTracingReply) {
        super(source);
        this.currentUser = currentUser;
        this.taskTracingReply = taskTracingReply;
    }
}
