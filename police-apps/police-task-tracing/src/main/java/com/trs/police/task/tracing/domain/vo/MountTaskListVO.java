package com.trs.police.task.tracing.domain.vo;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.trs.police.task.tracing.constant.enums.TaskStatusEnum;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/2/9 15:37
 */
@Data
public class MountTaskListVO implements Serializable {

    private static final long serialVersionUID = -652936823524302359L;
    private Long id;
    /**
     * 状态
     */
    private String status;

    /**
     * 挂账类型
     */
    private Long dbType;


    /**
     * 是否逾期
     */
    @JsonIgnore
    private Boolean isOverdue;

    /**
     * 挂账名称
     */
    private String title;

    /**
     * 挂账相关部门
     */
    private List<MountTaskDeptVO> depts;

    /**
     * 获取挂账状态
     *
     * @return 状态
     */
    public String getStatus() {
        return Boolean.TRUE.equals(this.isOverdue) ? TaskStatusEnum.OVERDUE.getName() : status;
    }
}
