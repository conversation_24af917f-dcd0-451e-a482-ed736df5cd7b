package com.trs.police.task.tracing.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.task.tracing.domain.entity.TaskTracingProcess;
import java.util.List;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Select;

/**
 * t_task_tracing_process 表数据库访问接口
 *
 * <AUTHOR>
 */
@Mapper
public interface TaskTracingProcessMapper extends BaseMapper<TaskTracingProcess> {

    /**
     * 根据挂帐id 和部门id 查询办理流程
     *
     * @param taskId 挂帐id
     * @param deptId 部门id
     * @return 办理流程
     */
    @ResultMap("mybatis-plus_TaskTracingProcess")
    @Select("select * from t_task_tracing_process p where p.dept_id=#{deptId} and p.task_id = #{taskId}")
    TaskTracingProcess selectByTaskIdAndDeptId(@Param("taskId") Long taskId, @Param("deptId") Long deptId);

    /**
     * 根据挂帐id查询办理流程
     *
     * @param taskId 挂帐id
     * @return 办理流程
     */
    @ResultMap("mybatis-plus_TaskTracingProcess")
    @Select("select * from t_task_tracing_process p where p.task_id = #{taskId}")
    List<TaskTracingProcess> selectByTaskId(@Param("taskId") Long taskId);

    /**
     * 根据挂账id删除
     *
     * @param taskId 挂账id
     */
    @Delete("delete from t_task_tracing_process where task_id = #{taskId}")
    void deleteByTaskId(@Param("taskId") Long taskId);
}