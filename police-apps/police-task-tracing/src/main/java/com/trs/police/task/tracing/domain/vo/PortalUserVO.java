package com.trs.police.task.tracing.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 反馈消息视图
 * @date 2023/11/13 15:18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PortalUserVO implements Serializable {

    private static final long serialVersionUID = 986752325542943105L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 用户名
     */
    private String loginName;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 性别
     */
    private String sex;

    private String idEntityCard;

    private String inDustRialId;

    /**
     * 单位code
     */
    private String orgCode;

    private String orgName;

    /**
     * 职务
     */
    private String positionName;

    /**
     * 电话
     */
    private String mobile;

    private String scope;

    /**
     * 阅读状态：0-未读，1-已读
     */
    private Integer userStatus;

    private String requestTime;
}
