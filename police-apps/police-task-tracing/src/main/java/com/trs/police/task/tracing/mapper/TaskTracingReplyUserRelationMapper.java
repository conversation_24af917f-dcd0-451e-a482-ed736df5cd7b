package com.trs.police.task.tracing.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.task.tracing.domain.entity.TaskTracingReplyUserRelation;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * t_task_tracing_reply_user_relation 表数据库访问接口
 *
 * <AUTHOR>
 */
@Mapper
public interface TaskTracingReplyUserRelationMapper extends BaseMapper<TaskTracingReplyUserRelation> {


    /**
     * 根据用户和挂帐查询所有已读回复记录
     *
     * @param taskId 挂帐id
     * @param userId 用户id
     * @param deptId 部门id
     * @return 已读记录
     */
    @Select("select count(*) from t_task_tracing_reply_user_relation r where r.task_id=#{taskId} and r.user_id=#{userId} and r.dept_id=#{deptId} and r.is_read = 0 ")
    Integer countUnreadByUserIdAndTaskId(@Param("taskId") Long taskId,
        @Param("userId") Long userId, @Param("deptId") Long deptId);

    /**
     * 删除回复关联关系
     *
     * @param replyId 回复id
     */
    @Delete("delete from t_task_tracing_reply_user_relation where reply_id = #{replyId} ")
    void deleteByReplyId(@Param("replyId") Long replyId);

    /**
     * 删除回复关联关系
     *
     * @param taskId 挂账id
     */
    @Delete("delete from t_task_tracing_reply_user_relation where task_id = #{taskId} ")
    void deleteByTaskId(@Param("taskId") Long taskId);

    /**
     * 统计未读消息数
     *
     * @param userId 用户id
     * @param deptId 部门id
     * @return 数量
     */
    @Select("select count(*) from t_task_tracing_reply_user_relation r where r.user_id=#{userId} and r.dept_id=#{deptId} and is_read = 0 ")
    Integer countUnreadReply(@Param("userId") Long userId, @Param("deptId") Long deptId);

    /**
     * 更新用户和挂账所有回复的已读状态
     *
     * @param taskId  挂账id
     * @param userId  用户id
     * @param deptId  部门id
     */
    @Update("update t_task_tracing_reply_user_relation r set r.is_read = 1 where r.user_id=#{userId} and r.dept_id=#{deptId} and task_id = #{taskId}")
    void readReply(@Param("taskId") Long taskId, @Param("userId") Long userId, @Param("deptId") Long deptId);

    /**
     * 是否已读
     *
     * @param replyId 回复id
     * @param userId  用户id
     * @param deptId  部门id
     * @return 结果
     */
    @Select("select r.is_read from t_task_tracing_reply_user_relation r where r.user_id=#{userId} and r.dept_id=#{deptId} and reply_id = #{replyId} ")
    Boolean checkReplyRead(@Param("replyId") Long replyId, @Param("userId") Long userId, @Param("deptId") Long deptId);
}
