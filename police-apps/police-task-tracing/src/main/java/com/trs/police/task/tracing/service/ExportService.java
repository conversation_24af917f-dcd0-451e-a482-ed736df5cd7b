package com.trs.police.task.tracing.service;

import com.trs.police.common.core.params.ExportParams;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 */
public interface ExportService {

    /**
     * 布控列表导出
     *
     * @param response response
     * @param vo       导出列表vo
     * @throws IOException io异常
     **/
    void exportList(HttpServletResponse response, ExportParams vo) throws IOException;

    /**
     * 挂账列表批量导出
     *
     * @param response response
     * @param params params
     * @throws IOException io异常
     */
    void exportTracingList(HttpServletResponse response, ExportParams params);
}
