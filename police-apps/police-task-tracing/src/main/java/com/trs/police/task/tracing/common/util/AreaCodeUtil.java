package com.trs.police.task.tracing.common.util;

import com.trs.police.common.core.entity.District;

/**
 * Description:
 *
 * <AUTHOR>
 * @date: 2024/3/21 22:01
 */
public class AreaCodeUtil {


    /**
     * 根据district获取区域代码前缀
     *
     * @param district district
     * @param code code
     * @return 区域代码前缀
     */
    public static String getAreaCodePre(District district,String code) {
        if (code.equals("")){
            return code;
        }
        switch (district.getLevel()){
            case 1:
                return code.substring(0,2);
            case 2:
                return code.substring(0,4);
            case 3:
            default:
                return code;
        }
    }
}
