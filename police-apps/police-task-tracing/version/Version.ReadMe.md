# 挂账
# 17.4
- XMKFB-8597 挂账详情页顶部的类型信息显示为了数字


## 2025-4-23:rc
XMKFB-8041 【德阳】旌阳情指发布挂帐后，旌阳所签收异常

## 16.2
- XMKFB-7754 要情、指令挂账报错

## 15.3
- XMKFB-7012 后-【云哨】- 挂账，挂账数据在审批不通过时显示的盯办时限有误

## RC20250318
- XMKFB-7291 后-【省厅协作】- 线索、协作模块短信平台对接

## 15.2
- 后-【德阳】- 挂帐模块优化

##15.1
- 后-【云哨】- 挂账，挂账数据在审批不通过时显示的盯办时限有误

## 14.4
-  XMKFB-6996 - 后-【自贡】- 挂账盯办—上网追逃报错
-  XMKFB-7010 - 【德阳】挂账，新建重点警情、涉稳事件类型的挂账报错

## v14.3
- XMKFB-6886 合-【广安】- 挂帐盯办增加导出功能
- XMKFB-6920 后-【自贡】- 挂账列表发起单位优化
- XMKFB-6801 后-【德阳】- 挂帐模块优化

## v143.1
- XMKFB-6591 【广安】挂账统计，市局、区县各单位挂账发布和办结次数统计图中的单位数据有误
- XMKFB-6593 【广安】挂账统计，切换任意子级地区后各警种挂账发布和办结次数中均显示为了全部分局的数据

## v13.4
-XMKFB-6610 - 后-挂账统计详情列表返回优化

## v13.2
-XMKFB-6236 挂账详情增加下钻接口

## v13.1
- XMKFB-6192 - 后-【广安】- 挂账统计接口（mount-list）总数需要加上已办结的数量

## v12.4
- XMKFB-5755 后-【广安】- 挂账统计功能开发


## v12.3
- XMKFB-5780[XMKFB-5363] 后-【省厅】合成作战大屏开发

## v11.4
- XMKFB-5321  后-【广安】- 挂账盯办浮窗后端接口支持
- XMKFB-5279 - 同警种、指定警种本单位及下级单位或全部单位数据权限用户查看我办结的挂账、全部挂账、报送我的挂账报系统错误
- XMKFB-5754 后-【广安】- 挂账盯办调整

## v11.3
- XMKFB-5247 合-【广安】- 挂账盯办增加盯办类别

## v9.4
- STAPP-29 前-【省厅App】审批-线索详情页，审批通过后，状态显示不正确

## v1.5 发版日志
>XMKFB-1191、挂账列表按照发起单位筛选得到的筛选结果无效   
## v1.4 发版日志
>XMKFB-764、后 - 【高新】发布挂账接口需要增加参数 
>XMKFB-625、【高新】挂账支持自动产生 
## v1.3 发版日志
>XMKFB-555、南充GA-挂账盯办业务的待审数据，需增加消息中心提醒

# nacos配置更新
```
#系统消息需要配置超管的信息
com:
  trs:
    task:
      tracing:
        adminUserId: 2214
        adminDeptId: 2
```
## 8.1
- XMKFB-2876 后-【省厅】-发送政务微信消息覆盖整个系统模块