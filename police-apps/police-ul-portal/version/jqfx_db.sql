-- ----------------------------
-- Table structure for gx_jgdw_xq
-- ----------------------------
CREATE TABLE `gx_jgdw_xq` (
  `bmbh` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '部门编码',
  `bmmc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '部门名称',
  `dz` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '地址名称',
  `zdmj` text COMMENT '占地面积',
  `rksl` int DEFAULT NULL COMMENT '辖区人口数'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for t_portal_area_info
-- ----------------------------
CREATE TABLE `t_portal_area_info` (
  `id` int NOT NULL AUTO_INCREMENT,
  `area_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `area` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=146 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;


-- ----------------------------
-- Table structure for t_portal_boundary
-- ----------------------------
CREATE TABLE `t_portal_boundary` (
  `id` int NOT NULL AUTO_INCREMENT,
  `type` int DEFAULT NULL,
  `bmbh` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `bmmc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `area` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=97 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;


-- ----------------------------
-- Table structure for t_portal_company
-- ----------------------------
CREATE TABLE `t_portal_company` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `company_name` varchar(255) DEFAULT NULL COMMENT '公司名称',
  `company_aliases` text COMMENT '公司别名(多个逗号分割)',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1846732111119839235 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;




-- ----------------------------
-- Table structure for t_portal_company_excluded
-- ----------------------------
CREATE TABLE `t_portal_company_excluded` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `excluded_name` varchar(255) DEFAULT NULL COMMENT '排除词名称',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;