<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.ulportal.mapper.RoleMapper">
    <select id="getRoleListByUserId" resultType="com.trs.police.ulportal.domain.entity.Role">
        SELECT
            r.*
        FROM
            t_portal_user_role ur,t_portal_role r
        where
            ur.user_id=#{userId}
          and
            ur.role_id=r.id
    </select>

    <select id="countUserAdminRole" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            t_portal_user_role ur,t_portal_role r
        where
            ur.user_id=#{userId}
          and
            ur.role_id=r.id and r.sys_defined=1
    </select>

</mapper>