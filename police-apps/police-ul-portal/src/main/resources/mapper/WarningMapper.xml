<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.ulportal.mapper.ys.WarningMapper">
    <select id="getCluelInfo" resultType="com.trs.police.ulportal.domain.vo.ys.CluelInfoVO">
        select tw.content as title , IFNULL(twp.status, 1) as warningStatus, tw.warning_time as warningTime
        from t_warning tw
            left join (select warning_id, status from t_warning_process twp where id in (
                select max(id) from t_warning_process twp group by warning_id)) twp
            on tw.id = twp.warning_id
        where tw.model_id like CONCAT('%[', #{id} ,']%')
           or tw.model_id like CONCAT('%[', #{id} ,',%')
           or tw.model_id like CONCAT('%,', #{id} ,',%')
           or tw.model_id like CONCAT('%,', #{id} ,']%')
    </select>
</mapper>