<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.ulportal.mapper.ExtDeptMapper">


    <select id="getDeptMjNumber"
            resultType="com.trs.police.ulportal.domain.vo.policesituationtopic.PerCjAnalysisResultVO">
        select
            d.org_name as bmmc,
            d.org_code as bmbh,
            sum(1) as mjNumber
        from
            t_portal_ext_user as u
            left join t_portal_ext_user_dept as ud on u.id = ud.user_id
            left join t_portal_ext_dept as d on d.id = ud.dept_id
        where 1 = 1
        <if test="deptList != null and deptList.size > 0">
            and org_code in
            <foreach collection="deptList" item="item" open="(" separator="," close=")">
                #{item.code}
            </foreach>
        </if>
        group by org_code;
    </select>
    <select id="getParentExtDeptList" resultType="com.trs.police.ulportal.domain.vo.ParentDeptVO">
        select a.org_code,a.org_jc,a.org_name,GROUP_CONCAT(b.org_code) as childOrgCodes
        from
            (SELECT org_code,org_name,org_jc,root_path FROM `t_portal_ext_dept` where org_level in ('31','40'))
                a,
            t_portal_ext_dept b where b.root_path like CONCAT(a.root_path,'\_%') or b.root_path=a.root_path
        GROUP BY a.org_code
    </select>
    <select id="getDeptByUserId" resultType="com.trs.police.ulportal.domain.entity.PortalDept">
        SELECT
        *
        FROM
        t_portal_ext_dept d left join t_portal_ext_user_dept ud on d.id = ud.dept_id
        WHERE
        ud.user_id = #{userId}
        LIMIT 1
    </select>
</mapper>