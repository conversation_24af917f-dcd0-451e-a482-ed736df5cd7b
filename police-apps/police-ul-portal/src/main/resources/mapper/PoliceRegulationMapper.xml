<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.ulportal.mapper.policesituationtopic.PoliceRegulationMapper">


    <select id="statisticsCjLess60"
            resultType="com.trs.police.ulportal.domain.vo.policesituationtopic.PoliceRegulationsCountVO">
        SELECT
            bmbh,
            bmmc,
            sum( cjzs60 ) AS less60Count
        from dwd_cjgftj
        WHERE 1 = 1
        <if test="dto.regionCode != null">
            and bmbh like concat(#{dto.regionCode,jdbcType=VARCHAR}, '%')
        </if>
        <if test="dto.startTime != null">
            and rq >= #{dto.startTime}
        </if>
        <if test="dto.endTime != null">
            and rq &lt;= #{dto.endTime}
        </if>
        group by bmbh;
    </select>
    <select id="statisticsNoSigned"
            resultType="com.trs.police.ulportal.domain.vo.policesituationtopic.PoliceRegulationsCountVO">
        SELECT
            bmbh,
            bmmc,
            sum(wqs) AS noSignedCount
        from dwd_cjgftj
        WHERE 1 = 1
        <if test="dto.regionCode != null">
            and bmbh like concat(#{dto.regionCode,jdbcType=VARCHAR}, '%')
        </if>
        <if test="dto.startTime != null">
            and rq >= #{dto.startTime}
        </if>
        <if test="dto.endTime != null">
            and rq &lt;= #{dto.endTime}
        </if>
        group by bmbh;
    </select>
    <select id="statisticsDeptCjCount"
            resultType="com.trs.police.ulportal.domain.vo.policesituationtopic.DeptCjCountVO">
        SELECT
        bmbh,
        bmmc,
        sum(cjzs) AS cjNumber
        from dwd_cjgftj
        WHERE 1 = 1
        <if test="dto.regionCode != null">
            and bmbh like concat(#{dto.regionCode,jdbcType=VARCHAR}, '%')
        </if>
        <if test="deptList != null and deptList.size > 0">
            and bmbh in
            <foreach collection="deptList" item="item" open="(" separator="," close=")">
                #{item.code}
            </foreach>
        </if>
        <if test="dto.startTime != null">
            and rq >= #{dto.startTime}
        </if>
        <if test="dto.endTime != null">
            and rq &lt;= #{dto.endTime}
        </if>
        group by bmbh;
    </select>
</mapper>