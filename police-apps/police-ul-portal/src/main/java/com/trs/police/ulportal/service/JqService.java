package com.trs.police.ulportal.service;

import com.alibaba.fastjson.JSONObject;
import com.trs.police.ulportal.domain.dto.JqDTO;
import com.trs.police.ulportal.domain.entity.JQ;
import com.trs.police.ulportal.domain.vo.*;
import com.trs.web.builder.base.RestfulResultsV2;


/**
 * 警情服务
 *
 * <AUTHOR>
 * @date 2024/3/28
 */
public interface JqService extends BaseService<JQ, JqVO> {

    /**
     * 警情统计
     *
     * @param dto dto
     * @return 结果
     */
    RestfulResultsV2<JqStatisticsVO> jqStatistics(JqDTO dto);

    /**
     * 重复报警列表
     *
     * @param dto dto
     * @return 结果
     */
    RestfulResultsV2<BjRepeatVO> bjRepeatList(JqDTO dto);


    /**
     * 人员出警考核列表
     *
     * @param dto dto
     * @return 结果
     */
    RestfulResultsV2<CjAppraiseVO> cjAppraiseList(JqDTO dto);

    /**
     * 报警列表（重复）
     *
     * @param dto dto
     * @return 结果
     */
    RestfulResultsV2<JqVO> bjList(JqDTO dto);

    /**
     * 报警列表
     *
     * @param dto dto
     * @return 结果
     */
    RestfulResultsV2<JqVO> bjListV2(JqDTO dto);

    /**
     * 出警列表
     *
     * @param dto dto
     * @return 结果
     */
    RestfulResultsV2<JqVO> cjList(JqDTO dto);

    /**
     * 派出所警情排名
     *
     * @param dto dto
     * @return {@link RestfulResultsV2}<{@link JqAnalysisVO}>
     */
    RestfulResultsV2<JqAnalysisVO> policeStationRank(JqDTO dto);

    /**
     * 高发时段出现警情排名
     *
     * @param dto dto
     * @return {@link RestfulResultsV2}<{@link JqAnalysisVO}>
     */
    RestfulResultsV2<JqAnalysisVO> jqPeakPeriodRank(JqDTO dto);

    /**
     * 出警时长与出警次数
     *
     * @param dto dto
     * @return {@link RestfulResultsV2}<{@link JSONObject}>
     */
    RestfulResultsV2<JSONObject> cjTimeAndCountStatistics(JqDTO dto);

    /**
     * 人员警情排名
     *
     * @param dto dto
     * @return {@link RestfulResultsV2}<{@link JqAnalysisVO}>
     */
    RestfulResultsV2<JqAnalysisVO> jqPersonRank(JqDTO dto);

    /**
     * 人员弹窗
     *
     * @param dto dto
     * @return {@link RestfulResultsV2}<{@link JqAnalysisVO}>
     */
    RestfulResultsV2<JqAnalysisVO> personalDetail(JqDTO dto);

    /**
     * 警情弹窗
     *
     * @param dto dto
     * @return {@link RestfulResultsV2}<{@link JqInfoVO}>
     */
    RestfulResultsV2<JqInfoVO> jqDetail(JqDTO dto);

    /**
     * 警情总数统计
     *
     * @param dto dto
     * @return 结果
     */
    RestfulResultsV2<JqStatisticsVO> jqTotalStatistics(JqDTO dto);

    /**
     * 每日警情数量趋势
     *
     * @param dto dto
     * @return 结果
     */
    RestfulResultsV2<JqStatisticsVO> jqNumberTrend(JqDTO dto);

    /**
     * 警情分类统计
     *
     * @param dto dto
     * @return 结果
     */
    RestfulResultsV2<JqStatisticsVO> jqClassStatistics(JqDTO dto);

    /**
     * 出警统计
     *
     * @param dto dto
     * @return 结果
     */
    RestfulResultsV2<AlarmStatisticsVO> alarmStatistics(JqDTO dto);

    /**
     * 出警情况趋势
     *
     * @param dto dto
     * @return 结果
     */
    RestfulResultsV2<AlarmStatisticsVO> alarmTrend(JqDTO dto);

    /**
     * 出警警情弹窗
     *
     * @param dto dto
     * @return {@link RestfulResultsV2}<{@link JqInfoVO}>
     */
    RestfulResultsV2<JqInfoVO> cjJqDetail(JqDTO dto);

    /**
     * 今日指数
     *
     * @return {@link RestfulResultsV2}<{@link JqIndexVO}>
     */
    RestfulResultsV2<JqIndexVO> todayIndex();

    /**
     * 今日指数详情
     *
     * @param dto dto
     * @return {@link RestfulResultsV2}<{@link JqIndexDetailVO}>
     */
    RestfulResultsV2<JqIndexDetailVO> todayIndexDetail(JqDTO dto);
}
