package com.trs.police.ulportal.service.impl.scoreDwd;

import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.entity.dwd.BaseDwdEntity;
import com.trs.police.ulportal.common.constants.PersonClueScoreRecordConstant;
import com.trs.police.ulportal.domain.entity.PersonClueScoreRecord;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 *
 *
 * @param <T> 泛型
 */
public abstract class DwdService<T> {
    /**
     * 计算基础分数
     *
     * @param t 类型
     * @return jieguo
     */
    public abstract double countBasicScore(T t);

    /**
     * 获取类型
     *
     * @param t 参数
     * @return 结果
     */
    public abstract boolean getDwdType(T t);

    /**
     * 计算基础的分
     *
     * @param time 时间
     * @param entity x
     * @param record x
     * @return 基础分数
     */
    public double countBasicScoreByYear(String time, PersonClueScoreRecord record, BaseDwdEntity entity) {
        if (StringUtils.isEmpty(time)){
            return 0;
        }
        LocalDate occurTime = LocalDate.parse(time, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        LocalDate currentTime = LocalDate.now();
        Long years = (long) (currentTime.getYear()-occurTime.getYear());
        if ((occurTime.getMonthValue() == currentTime.getMonthValue()
                && occurTime.getDayOfMonth() < currentTime.getDayOfMonth())
                || occurTime.getMonthValue() < currentTime.getMonthValue()){
            years++;
        }
        switch (years.intValue()){
            case PersonClueScoreRecordConstant.ONE_YEAR:
                record.setScoreItems("近一年有案发"+ (StringUtils.isNotEmpty(entity.getLastAfmc()) ? "(" + entity.getLastAfmc() +")" : ""));
                return PersonClueScoreRecordConstant.FIFTY_SCORE;
            case PersonClueScoreRecordConstant.TWO_YEAR:
                record.setScoreItems("近两年有案发"+ (StringUtils.isNotEmpty(entity.getLastAfmc()) ? "(" + entity.getLastAfmc() +")" : ""));
                return PersonClueScoreRecordConstant.THIRTY_SCORE;
            case PersonClueScoreRecordConstant.THREE_YEAR:
            default:
                record.setScoreItems("近三年或以上有案发"+ (StringUtils.isNotEmpty(entity.getLastAfmc()) ? "(" + entity.getLastAfmc() +")" : ""));
                return PersonClueScoreRecordConstant.TWENTY_SCORE;
        }
    }
}
