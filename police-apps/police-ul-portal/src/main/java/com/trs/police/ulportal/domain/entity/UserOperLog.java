package com.trs.police.ulportal.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * Description:
 *
 * @author: lv.bo
 * @create: 2024-05-22 10:42
 */
@Data
@TableName(value = "t_portal_user_oper_log")
public class UserOperLog extends BaseDo implements Serializable {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     *  用户名
     */
    @TableField(value = "user_name")
    private String userName;

    /**
     *  身份证号
     */
    @TableField(value = "user_id_card")
    private String userIdCard;
}
