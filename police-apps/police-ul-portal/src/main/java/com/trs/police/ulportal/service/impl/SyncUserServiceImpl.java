package com.trs.police.ulportal.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.ulportal.common.constants.CommonConstants;
import com.trs.police.ulportal.converter.PortalDeptConverter;
import com.trs.police.ulportal.converter.PortalUserConverter;
import com.trs.police.ulportal.converter.PortalUserDeptConverter;
import com.trs.police.ulportal.domain.entity.*;
import com.trs.police.ulportal.mapper.DeptMapper;
import com.trs.police.ulportal.mapper.UserDeptMapper;
import com.trs.police.ulportal.mapper.UserMapper;
import com.trs.police.ulportal.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * @author: lv.bo
 * @create: 2023-12-20 14:26
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SyncUserServiceImpl implements SyncUserService {


    private final UserMapper userMapper;

    private final ExtUserService extUserService;

    private final PortalUserConverter portalUserConverter;

    private final DeptMapper deptMapper;

    private final ExtDeptService extDeptService;

    private final PortalDeptConverter portalDeptConverter;

    private final UserDeptMapper userDeptMapper;

    private final ExtUserDeptService extUserDeptService;

    private final PortalUserDeptConverter portalUserDeptConverter;

    @Override
//    @Transactional
    public void syncThirdUserInfo() throws Exception {
        syncUser();
        syncDept();
        syncUserDept();
    }

    @Override
    public void syncUser() throws Exception {
        commonSync(userMapper, this::syncPartUser);
    }

    /**
     *  同步一批次的用户
     *
     * @param pageNum 当前页
     * @param pageSize 页面大小
     */
    private void syncPartUser(int pageNum, long pageSize){
        Page<PortalUser> page = Page.of(pageNum, pageSize);
        userMapper.selectPage(page, new QueryWrapper<>());
        List<PortalUser> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)){
            return;
        }
        List<Long> idList = records.stream().map(PortalUser::getId).collect(Collectors.toList());
        List<ExtUser> extUsers = extUserService.listByIds(idList);
        if (!CollectionUtils.isEmpty(extUsers)){
            Map<Long, PortalUser> map = records.stream().collect(Collectors.toMap(PortalUser::getId, e -> e));
            // 排除没变的实体
            for (ExtUser extUser : extUsers){
                PortalUser portalUser = map.get(extUser.getId());
                if (portalUser == null){
                    continue;
                }
                if (isUserSameValue(extUser, portalUser)){
                    records.remove(portalUser);
                }
            }
        }
        if (CollectionUtils.isEmpty(records)){
            return;
        }
        List<ExtUser> extUserList = portalUserConverter.toExtUserList(records);
        extUserService.saveOrUpdateBatch(extUserList);
    }

    /**
     *  判断实体是否改变
     *
     * @param ext 本地实体
     * @param portal 第三方实体
     * @return true/false
     */
    private boolean isUserSameValue(ExtUser ext, PortalUser portal) {
        return Objects.equals(ext.getLoginName(), portal.getLoginName())
                && Objects.equals(ext.getUserName(), portal.getUserName())
                && Objects.equals(ext.getUserType(), portal.getUserType())
                && Objects.equals(ext.getEmail(), portal.getEmail())
                && Objects.equals(ext.getPassword(), portal.getPassword())
                && Objects.equals(ext.getSex(), portal.getSex())
                && Objects.equals(ext.getPositionId(), portal.getPositionId())
                && Objects.equals(ext.getPositionName(), portal.getPositionName())
                && Objects.equals(ext.getIdEntityCard(), portal.getIdEntityCard())
                && Objects.equals(ext.getMobile(), portal.getMobile())
                && Objects.equals(ext.getTypeName(), portal.getTypeName())
                && Objects.equals(ext.getInDustRialId(), portal.getInDustRialId())
                && Objects.equals(ext.getSalt(), portal.getSalt());
    }

    /**
     * 抽取出来的公共的部分
     *
     * @param baseMapper baseMapper
     * @param consumer consumer
     * @param <T> 泛型
     */
    private <T> void commonSync(BaseMapper<T> baseMapper, BiConsumer<Integer, Long> consumer){
        long totalCount = baseMapper.selectCount(Wrappers.emptyWrapper());
        if (totalCount <= 0){
            return;
        }
        // 分批进行
        if (CommonConstants.SYNC_USER_PAGE_SIZE <= totalCount){
            long count = (totalCount + CommonConstants.SYNC_USER_PAGE_SIZE -1) / CommonConstants.SYNC_USER_PAGE_SIZE;
            for (int i = 1; i <= count; i++) {
                consumer.accept(i, CommonConstants.SYNC_USER_PAGE_SIZE);
            }
            return;
        }
        // 一次处理完
        consumer.accept(1, CommonConstants.SYNC_USER_PAGE_SIZE);
    }

    @Override
    public void syncDept() throws Exception {
        commonSync(deptMapper, this::syncPartDept);
    }

    /**
     *  同步一批次的部门
     *
     * @param pageNum 当前页
     * @param pageSize 页面大小
     */
    private void syncPartDept(int pageNum, long pageSize){
        Page<PortalDept> page = Page.of(pageNum, pageSize);
        deptMapper.selectPage(page, new QueryWrapper<>());
        List<PortalDept> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)){
            return;
        }
        List<Long> idList = records.stream().map(PortalDept::getId).collect(Collectors.toList());
        List<ExtDept> extDeptList = extDeptService.listByIds(idList);
        if (!CollectionUtils.isEmpty(extDeptList)){
            Map<Long, PortalDept> map = records.stream().collect(Collectors.toMap(PortalDept::getId, e -> e));
            // 排除没变的实体
            for (ExtDept extDept : extDeptList){
                PortalDept portalDept = map.get(extDept.getId());
                if (portalDept == null){
                    continue;
                }
                if (isDeptSameValue(extDept, portalDept)){
                    records.remove(portalDept);
                }
            }
        }
        if (CollectionUtils.isEmpty(records)){
            return;
        }
        // 如果部门变了需要清空部门的本地缓存map，重新去数据库拿
        extDeptService.setDeptMapToNull();
        List<ExtDept> finalExtDeptList = portalDeptConverter.toExtDeptList(records);
        extDeptService.saveOrUpdateBatch(finalExtDeptList);
    }

    /**
     *  判断实体是否改变
     *
     * @param ext 本地实体
     * @param portal 第三方实体
     * @return true/false
     */
    private boolean isDeptSameValue(ExtDept ext, PortalDept portal) {
        return Objects.equals(ext.getOrgCode(), portal.getOrgCode())
                && Objects.equals(ext.getOrgName(), portal.getOrgName())
                && Objects.equals(ext.getRootPath(), portal.getRootPath())
                && Objects.equals(ext.getOrgType(), portal.getOrgType())
                && Objects.equals(ext.getOrgTypeName(), portal.getOrgTypeName())
                && Objects.equals(ext.getOrgLevel(), portal.getOrgLevel())
                && Objects.equals(ext.getOrgBizType(), portal.getOrgBizType())
                && Objects.equals(ext.getOrgNo(), portal.getOrgNo())
                && Objects.equals(ext.getParentId(), portal.getParentId())
                && Objects.equals(ext.getBmpyszm(), portal.getBmpyszm())
                && Objects.equals(ext.getOrgJc(), portal.getOrgJc())
                && Objects.equals(ext.getOrgQc(), portal.getOrgQc())
                && Objects.equals(ext.getAddress(), portal.getAddress())
                && Objects.equals(ext.getLinkTel(), portal.getLinkTel())
                && Objects.equals(ext.getLinkMan(), portal.getLinkMan())
                && Objects.equals(ext.getLinkManTel(), portal.getLinkManTel())
                && Objects.equals(ext.getSfgp(), portal.getSfgp());
    }

    @Override
    public void syncUserDept() throws Exception {
        // 删除之前的用户部门关系，但排除admin用户
        extUserDeptService.remove(new QueryWrapper<ExtUserDept>()
                .notInSql("user_id", "SELECT id FROM yh_user WHERE login_name = 'admin'"));
        commonSync(userDeptMapper, this::syncPartUserDept);
    }

    /**
     *  同步一批次的用户部门关系
     *
     * @param pageNum 当前页
     * @param pageSize 页面大小
     */
    private void syncPartUserDept(int pageNum, long pageSize){
        Page<PortalUserDept> page = Page.of(pageNum, pageSize);
        userDeptMapper.selectPage(page, new QueryWrapper<>());
        List<PortalUserDept> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)){
            return;
        }
        List<ExtUserDept> finalExtDeptList = portalUserDeptConverter.toExtUserDeptList(records);
        extUserDeptService.saveOrUpdateBatch(finalExtDeptList);
    }
}
