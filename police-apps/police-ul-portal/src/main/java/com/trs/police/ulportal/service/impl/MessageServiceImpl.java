package com.trs.police.ulportal.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.grt.condify.exception.CondifyException;
import com.grt.condify.parser.MybatisSearchParser;
import com.trs.common.utils.StringUtils;
import com.trs.police.ulportal.common.constants.MessageConstants;
import com.trs.police.ulportal.common.handler.PortalException;
import com.trs.police.ulportal.common.util.ResultHelper;
import com.trs.police.ulportal.common.util.TimeUtil;
import com.trs.police.ulportal.converter.MessageConverter;
import com.trs.police.ulportal.domain.dto.CurrUserUnReadMessagesDto;
import com.trs.police.ulportal.domain.dto.MessageDto;
import com.trs.police.ulportal.domain.dto.MessageListDto;
import com.trs.police.ulportal.domain.dto.ReadMessageDto;
import com.trs.police.ulportal.domain.entity.ExtUser;
import com.trs.police.ulportal.domain.entity.Message;
import com.trs.police.ulportal.domain.vo.MessageVO;
import com.trs.police.ulportal.mapper.MessageMapper;
import com.trs.police.ulportal.service.ExtUserService;
import com.trs.police.ulportal.service.MessageService;
import com.trs.web.builder.base.RestfulResultsV2;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * @author: lv.bo
 * @create: 2023-12-07 16:26
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MessageServiceImpl extends ServiceImpl<MessageMapper, Message> implements MessageService {

    private final MessageMapper messageMapper;

    private final MessageConverter messageConverter;

    private final ExtUserService extUserService;

    @Override
    public RestfulResultsV2<MessageVO> queryForPage(IPage<Message> page, QueryWrapper<Message> queryWrapper) {
        IPage<Message> result = messageMapper.selectPage(page, queryWrapper);
        return ResultHelper.getIPageConverter().convert(messageConverter.toPageVo(result));

    }

    @Override
    public RestfulResultsV2 addMessage(MessageDto messageDto) {
        Message message = messageConverter.toEntity(messageDto);
        messageMapper.insert(message);
        return RestfulResultsV2.ok("接入消息成功！");
    }

    @Override
    public RestfulResultsV2 read(ReadMessageDto readMessageDto) {
        List<Long> messageIdList = Arrays.stream(readMessageDto.getMessageIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
        List<Message> messages = messageMapper.selectBatchIds(messageIdList);
        if (!CollectionUtils.isEmpty(messages)){
            messages.forEach(e -> {
                e.setReadStatus(MessageConstants.MESSAGE_STATUS_READ);
            });
            updateBatchById(messages);
        }
        return RestfulResultsV2.ok("设置消息已读成功！");
    }

    @Override
    public RestfulResultsV2<MessageVO> queryList(MessageListDto messageListDto) throws CondifyException {
        IPage<Message> page = MybatisSearchParser.buildPage(messageListDto);

        QueryWrapper<Message> queryWrapper =
                MybatisSearchParser.buildQueryWrapper(messageListDto);

        RestfulResultsV2<MessageVO> result = queryForPage(page, queryWrapper);
        result.getDatas().stream().forEach(e->e.setLogNoticeTime(e.getNoticeTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
        return result;
    }

    @Override
    public RestfulResultsV2<MessageVO> queryCurrUserUnReadMessages(CurrUserUnReadMessagesDto currUserUnReadMessagesDto) throws PortalException, CondifyException {
        ExtUser extUser = extUserService.getLoginExtUser();
        currUserUnReadMessagesDto.setUserId(extUser.getId() + "");
        IPage<Message> page = MybatisSearchParser.buildPage(currUserUnReadMessagesDto);

        QueryWrapper<Message> queryWrapper =
                MybatisSearchParser.buildQueryWrapper(currUserUnReadMessagesDto);

        return queryForPage(page, queryWrapper);
    }

    @Override
    public RestfulResultsV2 allRead() throws PortalException {
        ExtUser extUser = extUserService.getLoginExtUser();
        String userId = extUser.getId() + "";
        messageMapper.allRead(userId, MessageConstants.MESSAGE_STATUS_READ, MessageConstants.MESSAGE_STATUS_NOT_READ);
        return RestfulResultsV2.ok("设置全部已读成功！");
    }

    @Override
    public void syncPortalMessage(JSONObject jsonObject) throws Exception {
        log.info("消费第三方消息为："+jsonObject.toString());
        if (CollectionUtils.isEmpty(jsonObject)){
            throw new Exception("消费的第三方消息为空！");
        }
        Message message = Message.builder()
                .thirdPartyName(jsonObject.getString("thirdPartyName"))
                .messageId(jsonObject.getString("messageId"))
                .userId(jsonObject.getString("userId"))
                .userName(jsonObject.getString("userName"))
                .userIdCard(jsonObject.getString("userIdCard"))
                .noticeTime(TimeUtil.getLocalDateTime(jsonObject.getLongValue("noticeTime")))
                .sourceOrganCode(jsonObject.getString("sourceOrganCode"))
                .sourceOrganName(jsonObject.getString("sourceOrganName"))
                .organCode(jsonObject.getString("organCode"))
                .organName(jsonObject.getString("organName"))
                .content(jsonObject.getString("content"))
                .readStatus(jsonObject.getIntValue("readStatus"))
                .remark1(jsonObject.getString("remark1"))
                .remark2(jsonObject.getString("remark2"))
                .remark3(jsonObject.getString("remark3"))
                .build();
        if (!checkPortalMessage(message)){
            throw new Exception("第三方消息必填字段为空！");
        }
        messageMapper.insert(message);
    }

    /**
     *  校验第三方消息的必填字段
     *
     * @param message 消息
     * @return true/false
     */
    private boolean checkPortalMessage(Message message) {
        return !StringUtils.isEmpty(message.getThirdPartyName()) && !StringUtils.isEmpty(message.getMessageId()) && !StringUtils.isEmpty(message.getUserName())
                && message.getNoticeTime() != null && !StringUtils.isEmpty(message.getSourceOrganCode()) && !StringUtils.isEmpty(message.getSourceOrganName())
                && !StringUtils.isEmpty(message.getOrganCode()) && !StringUtils.isEmpty(message.getOrganName()) && !StringUtils.isEmpty(message.getContent())
                && message.getReadStatus() != null;
    }
}
