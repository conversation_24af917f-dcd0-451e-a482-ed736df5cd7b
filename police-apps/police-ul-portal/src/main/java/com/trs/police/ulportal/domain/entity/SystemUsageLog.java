package com.trs.police.ulportal.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 系统使用情况表
 * @date 2023/12/05 21:32
 */

@Data
@TableName(value = "t_portal_system_usage_log")
@Slf4j
public class SystemUsageLog implements Serializable {

    private static final long serialVersionUID = 43589282009439655L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 应用id
     */
    @TableField(value = "app_id")
    private Long appId;

    /**
     * 应用名
     */
    @TableField(value = "app_name")
    private String appName;

    /**
     * 登录用户身份证
     */
    @TableField(value = "id_entity_card")
    private String idEntityCard;

    /**
     * 登录用户姓名
     */
    @TableField(value = "user_name")
    private String userName;

    /**
     * 登录用户所属部门code
     */
    @TableField(value = "org_code")
    private String orgCode;

    /**
     * 登录用户所属部门名称
     */
    @TableField(value = "org_name")
    private String orgName;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", jdbcType = JdbcType.TIMESTAMP, fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    LocalDateTime createTime;
}
