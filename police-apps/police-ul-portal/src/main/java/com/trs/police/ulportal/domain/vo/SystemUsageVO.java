package com.trs.police.ulportal.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 反馈消息视图
 * @date 2023/11/13 15:18
 */
@Data
public class SystemUsageVO implements Serializable {

    private static final long serialVersionUID = 986752378902943105L;

    /**
     * 应用名称
     */
    @ApiModelProperty("应用名称")
    private String appName;

    /**
     * 总登录次数
     */
    @ApiModelProperty("登录次数")
    private Long loginTimes;

    /**
     * 登录人数
     */
    @ApiModelProperty("登录人数")
    private Long loginUserNum;
}
