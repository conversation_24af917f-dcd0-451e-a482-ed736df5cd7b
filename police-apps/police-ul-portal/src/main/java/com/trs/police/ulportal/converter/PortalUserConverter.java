package com.trs.police.ulportal.converter;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.ulportal.domain.entity.ExtUser;
import com.trs.police.ulportal.domain.entity.PortalUser;
import com.trs.police.ulportal.domain.vo.PortalUserVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @description 反馈消息转换器
 * @date 2023/11/13 15:36
 */
@Mapper(componentModel = "spring")
public interface PortalUserConverter {

    /**
     * 实体vo
     *
     * @param user 实体
     * @return vo
     */
    PortalUserVO toVo(PortalUser user);

    /**
     * 实体集合转vo集合
     *
     * @param userList 实体集合
     * @return vo集合
     */
    List<PortalUserVO> toVoList(List<PortalUser> userList);

    /**
     * 实体分页转vo分页
     *
     * @param page page
     * @return vo
     */
    Page<PortalUserVO> toPageVo(IPage<PortalUser> page);

    /**
     *  第三方用户实体集合转成统一门户的用户实体集合
     *
     * @param userList 第三方用户实体集合
     * @return extUserList
     */
    List<ExtUser> toExtUserList(List<PortalUser> userList);

    /**
     * 转换成统一门户用户实体
     *
     * @param extUser 第三方用户实体
     * @return PortalUser
     */
    PortalUser toPortalUser(ExtUser extUser);
}
