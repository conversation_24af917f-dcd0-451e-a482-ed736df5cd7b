package com.trs.police.ulportal.common.handler;

import com.grt.condify.exception.CondifyException;
import com.trs.police.ulportal.common.constants.ExceptionEnum;
import com.trs.web.builder.base.RestfulResultsV2;
import com.trs.web.restful.REQUEST_STATUS_CODE;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * Created by recivejt on 2021/1/10.
 */
@Order(Ordered.HIGHEST_PRECEDENCE)
@ControllerAdvice
@ResponseBody
@Slf4j
public class GlobalExceptionHandler {

    /**
     * 门户异常拦截器-强制要求本工厂所有往外抛的异常都为PortalException，禁止向用户侧显示程序异常明文
     *
     * @param e e
     * @return 处理后结果
     */
    @ExceptionHandler(value = PortalException.class)
    public RestfulResultsV2 portalErrorHandler(PortalException e) {
        log.error("系统出错啦！！！", e);

        //token有问题需要返回特殊的标识
        if (e.errCode.equalsIgnoreCase(ExceptionEnum.EX_SSL_CONTEXT_ERROR.errCode)
                || e.errCode.equalsIgnoreCase(ExceptionEnum.EX_VERIFY_TOKEN_FAILED.errCode)) {
            return RestfulResultsV2.error(REQUEST_STATUS_CODE.UNAUTHORIZED.getCode(), e.getMessage());
        }

        return RestfulResultsV2.error(e.getMessage());
    }

    /**
     * condifyErrorHandler
     *
     * @param e e
     * @return {@link RestfulResultsV2}
     */
    @ExceptionHandler(value = CondifyException.class)
    public RestfulResultsV2 condifyErrorHandler(CondifyException e) {
        log.error("系统出错啦！！！", e);

        return RestfulResultsV2.error(ExceptionEnum.EX_CONDIFY_SQL_ERROR.errMsg);
    }

    /**
     * 全局异常拦截
     *
     * @param exception 异常
     * @return 处理结果
     */
    @ExceptionHandler(value = Exception.class)
    public RestfulResultsV2 defaultErrorHandler(Exception exception) {
        log.error("系统出错啦！！！", exception);
        return RestfulResultsV2.error(ExceptionEnum.EX_NON_COMPLIANT.errMsg);
    }
}

