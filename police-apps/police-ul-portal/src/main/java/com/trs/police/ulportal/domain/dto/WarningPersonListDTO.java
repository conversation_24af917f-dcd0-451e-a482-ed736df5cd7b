package com.trs.police.ulportal.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * Description:
 *
 * @author: lv.bo
 * @create: 2024-05-28 16:01
 */
@Data
public class WarningPersonListDTO extends ExcludeInfoDTO {

    /**
     *  预警类型：1：违规ip，2：异常时间段，3：异常环比
     */
    private Integer type;

    /**
     * 部门代码
     */
    private String organCode;

    /**
     * 部门集合
     */
    private List<String> organList;

    /**
     * 搜索关键词
     */
    private String keyword;
    /**
     * 排序字段
     */
    private String orderType;
}
