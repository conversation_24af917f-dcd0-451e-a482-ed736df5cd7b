package com.trs.police.ulportal.adapter.controller;

import com.trs.police.ulportal.common.handler.PortalException;
import com.trs.police.ulportal.domain.dto.SpecialAnalysisDTO;
import com.trs.police.ulportal.domain.vo.SpecialAnalysisVO;
import com.trs.police.ulportal.service.UserOperLogService;
import com.trs.web.builder.base.RestfulResultsV2;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * Description:
 *
 * @author: lv.bo
 * @create: 2024-05-22 10:51
 */
@RestController
@Api(value = "用户日志", tags = "用户日志")
@RequiredArgsConstructor
@RequestMapping("/userOperLog")
@Slf4j
public class UserOperLogController {

    private final UserOperLogService userOperLogService;

    /**
     *  新增用户操作日志
     *
     * @return 是否成功
     * @throws PortalException 异常
     */
    @PostMapping("/addUserOperLog")
    @ApiOperation(value = "新增用户操作日志", notes = "新增用户操作日志")
    public RestfulResultsV2 addUserOperLog() throws PortalException {
        return userOperLogService.addUserOperLog();
    }

    /**
     *  警情专题分析
     *
     * @param dto dto
     * @return 分析结果
     */
    @PostMapping("/specialAnalysis")
    @ApiOperation(value = "警情专题分析", notes = "警情专题分析")
    public RestfulResultsV2<SpecialAnalysisVO> specialAnalysis(@RequestBody SpecialAnalysisDTO dto){
        return userOperLogService.specialAnalysis(dto);
    }

    /**
     *  多轨分析
     *
     * @param dto dto
     * @return 分析结果
     */
    @PostMapping("/multiAnalysis")
    @ApiOperation(value = "警情专题分析", notes = "警情专题分析")
    public RestfulResultsV2<SpecialAnalysisVO> multiAnalysis(@RequestBody SpecialAnalysisDTO dto){
        return userOperLogService.multiAnalysis(dto);
    }

    /**
     *  顶部分析
     *
     * @param dto dto
     * @return 分析结果
     */
    @PostMapping("/specialTopAnalysis")
    @ApiOperation(value = "警情专题分析", notes = "警情专题分析")
    public RestfulResultsV2<SpecialAnalysisVO> specialTopAnalysis(@RequestBody SpecialAnalysisDTO dto){
        return userOperLogService.specialTopAnalysis(dto);
    }
}
