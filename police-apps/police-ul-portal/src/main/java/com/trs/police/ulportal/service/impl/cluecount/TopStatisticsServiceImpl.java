package com.trs.police.ulportal.service.impl.cluecount;

import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.params.DeptRequestParams;
import com.trs.police.common.core.vo.IdNameCountVO;
import com.trs.police.common.core.vo.IdNameVO;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.ulportal.converter.PersonClueConvert;
import com.trs.police.ulportal.domain.dto.ys.PersonClueNavigationDTO;
import com.trs.police.ulportal.domain.vo.ys.PersonClueNavigationVO;
import com.trs.police.ulportal.mapper.ys.PersonClueMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 门户新线索池根据线索类型统计的实现,顶部接口
 *
 * <AUTHOR>
 * @date 2024/11/05 17:01
 */
@Component
public class TopStatisticsServiceImpl extends AbstractPersonClueCountService<PersonClueNavigationDTO>{

    @Resource
    private PersonClueMapper personClueMapper;

    @Resource
    private DictService dictService;

    @Resource
    private PermissionService permissionService;

    private final PersonClueConvert personClueConvert = PersonClueConvert.CONVERTER;

    @Override
    public String key() {
        return "topStatistics";
    }

    @Override
    public String desc() {
        return "顶部线索统计";
    }

    @Override
    protected boolean needStatistic(PersonClueNavigationDTO dto) {
        return !dto.getPcode().equals("0");
    }

    @Override
    protected List<PersonClueNavigationVO> getRankList(PersonClueNavigationDTO dto) {
        List<DictDto> typeList = dictService.getDictListByTypeList(Collections.singletonList(dto.getType()));
        List<PersonClueNavigationVO> navigationVos = typeList.stream()
                .filter(dict -> dto.getPcode().equals(dict.getPCode().toString()))
                .map(personClueConvert::dictToNavigation)
                .collect(Collectors.toList());
        DeptDto dept = permissionService.getDeptByCode(dto.getPcode());
        // 部门是全部的模型
        if (Objects.nonNull(dept) || "85".equals(dto.getPcode())) {
            navigationVos = typeList.stream()
                    .filter(dict -> dict.getPCode().equals(85L))
                    .map(personClueConvert::dictToNavigation)
                    .collect(Collectors.toList());
        }
        navigationVos.forEach(vo -> {
            List<String> key = typeList.stream()
                    .filter(dict -> {
                        if ("module".equals(vo.getDictDesc())) {
                            return dict.getCode().equals(Long.parseLong(vo.getCode()));
                        } else {
                            return dict.getPCode().equals(Long.parseLong(vo.getCode()));
                        }})
                    .map(DictDto::getFlag)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            vo.setKey(key);
        });
        return navigationVos;
    }

    @Override
    protected Map<String, Integer> executeCountStatistic(PersonClueNavigationDTO dto) {
        if (StringUtils.isEmpty(dto.getDictDesc())){
            dto.setSsbmdm((StringUtils.isEmpty(dto.getPcode()) || "85".equals(dto.getPcode())) ? getAllDeptCode() : Collections.singletonList(dto.getPcode()));
        }
        if (Objects.isNull(dto.getSearchValue())){
            dto.setSearchValue("");
        }
        List<Integer> clueTypes = getClueTypes(dto);
        Map<Integer, Double> clueTypeAndRiskScoreMap = getScoreCondition(clueTypes);
        clueTypes.removeAll(clueTypeAndRiskScoreMap.keySet());
        List<IdNameCountVO> idNameCountVoList = personClueMapper.countByCondition(dto, clueTypes, clueTypeAndRiskScoreMap);
//        Integer clueType = dto.getClueType();
//        if (Objects.nonNull(clueType) && Objects.nonNull(dto.getIsGx())) {
//            if (clueTypeAndRiskScoreMap.containsKey(clueType)) {
//                dto.setClueType(null);
//            }
//            Map<Integer, Double> subScoreCondition = clueTypeAndRiskScoreMap.entrySet().stream()
//                    .filter(entry -> entry.getKey().equals(clueType))
//                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
//            IdNameCountVO idNameCountVo = personClueMapper.countByIsGx(dto, subScoreCondition);
//            idNameCountVoList.forEach(vo -> {
//                if (vo.getUuid().equals(clueType.toString())) {
//                    vo.setCount(idNameCountVo.getCount());
//                }
//            });
//        }
        return idNameCountVoList.stream()
                .collect(Collectors.toMap(IdNameVO::getUuid,
                        vo -> vo.getCount().intValue(),
                        (v1, v2) -> v1));
    }

    private List<String> getAllDeptCode() {
        DeptRequestParams params = new DeptRequestParams();
        params.setType(3);
        return permissionService.getDeptByParams(params)
                .stream().map(DeptDto::getCode)
                .collect(Collectors.toList());
    }

    private List<Integer> getClueTypes(PersonClueNavigationDTO dto) {
        List<DictDto> dictList = Objects.nonNull(dto.getType())
                ? dictService.getDictListByTypeList(Collections.singletonList(dto.getType()))
                : Collections.emptyList();
        if (StringUtils.isEmpty(dto.getDictDesc())){
            boolean allDept = StringUtils.isEmpty(dto.getPcode()) || dto.getPcode().equals("85");
            dto.setSsbmdm(allDept ? getAllDeptCode() : Collections.singletonList(dto.getPcode()));
            return dictList.stream()
                    .filter(dict -> dict.getPCode().equals(85L))
                    .map(d -> Integer.parseInt(d.getFlag()))
                    .collect(Collectors.toList());
        }
        return dictList.stream()
                .filter(dict -> dict.getPCode().equals(Long.parseLong(dto.getPcode())))
                .map(d -> Integer.parseInt(d.getFlag()))
                .collect(Collectors.toList());
    }

}
