package com.trs.police.ulportal.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 用户实体
 * @date 2023/12/1 11:32
 */

@Data
@TableName(value = "yh_dept")
@Slf4j
public class PortalDept implements Serializable {

    private static final long serialVersionUID = 4358928222943105L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 部门code
     */
    @TableField(value = "org_code")
    private String orgCode;

    /**
     * 部门名称
     */
    @TableField(value = "org_name")
    private String orgName;

    /**
     * 根路径开始的id
     */
    @TableField(value = "root_path")
    private String rootPath;

    /**
     * 部门类型
     */
    @TableField(value = "org_type")
    private String orgType;

    /**
     * 部门类型名称
     */
    @TableField(value = "org_type_name")
    private String orgTypeName;

    /**
     * 部门等级
     */
    @TableField(value = "org_level")
    private String orgLevel;

    /**
     * 部门业务类型
     */
    @TableField(value = "org_biz_type")
    private String orgBizType;

    /**
     * 部门顺序号
     */
    @TableField(value = "org_no")
    private String orgNo;

    /**
     * 上级部门主键
     */
    @TableField(value = "parent_id")
    private String parentId;

    /**
     * 部门名字拼音首字母
     */
    @TableField(value = "bmpyszm")
    private String bmpyszm;


    /**
     * 部门简称
     */
    @TableField(value = "org_jc")
    private String orgJc;

    /**
     * 全称
     */
    @TableField(value = "org_qc")
    private String orgQc;

    /**
     * 地址
     */
    @TableField(value = "address")
    private String address;

    /**
     * 联系电话
     */
    @TableField(value = "link_tel")
    private String linkTel;

    /**
     * 联系人
     */
    @TableField(value = "link_man")
    private String linkMan;

    /**
     * 联系人电话
     */
    @TableField(value = "link_man_tel")
    private String linkManTel;

    /**
     * 是否挂牌
     */
    @TableField(value = "sfgp")
    private String sfgp;

}
