package com.trs.police.ulportal.domain.dto;

import com.grt.condify.annotation.SearchType;
import com.grt.condify.common.constant.SearchTypeValue;
import com.grt.condify.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Description:
 *
 * @author: lv.bo
 * @create: 2023-12-05 14:57
 */
@Data
public class IntelligentAppsListDto extends BaseDTO {

    @ApiModelProperty(value = "只能应用名")
    @SearchType(searchField = "app_name", type = SearchTypeValue.EQ)
    private String appName;
}
