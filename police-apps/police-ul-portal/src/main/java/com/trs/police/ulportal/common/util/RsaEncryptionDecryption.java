package com.trs.police.ulportal.common.util;


import com.trs.police.ulportal.common.constants.ExceptionEnum;
import com.trs.police.ulportal.common.handler.PortalException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

/**
 * <AUTHOR>
 * @description RSA工具类
 * @date 2023/11/28 19:42
 */
@Slf4j
@Component
public class RsaEncryptionDecryption {

    private static final String DE_PRIKEY_PATH = "/ul_secret.asc";

    private static final String DE_PUBKEY_PATH = "/ul_public.asc";

    private static final String RSA = "RSA";

    private static final int KEY_LENGTH = 4096;

    private static KeyFactory keyFactory;

    RsaEncryptionDecryption() {
        try {
            keyFactory = KeyFactory.getInstance(RSA);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 读取私钥文件
     *
     * @param path 路径
     * @return 读取的私钥
     * @throws IOException 异常
     */
    public byte[] readPrivateKey(String path) throws IOException {
        InputStream is = this.getClass().getResourceAsStream(StringUtils.isEmpty(path) ? DE_PRIKEY_PATH : path);
        return getBytes(is);
    }


    /**
     * 读取公钥文件
     *
     * @param path 路径
     * @return 读取的公钥
     * @throws IOException 异常
     */
    public byte[] readPublicKey(String path) throws IOException {
        InputStream is = this.getClass().getResourceAsStream(StringUtils.isEmpty(path) ? DE_PUBKEY_PATH : path);
        return getBytes(is);
    }

    /**
     * 文件读取
     *
     * @param is 流
     * @return 字节数组
     * @throws IOException 异常
     */
    @Nullable
    private static byte[] getBytes(InputStream is) throws IOException {
        byte[] bytes = null;
        try (is) {
            bytes = IOUtils.toByteArray(is);
        } catch (Exception e) {
            log.error("读取文件出错！", e);
        }
        return bytes;
    }

    /**
     * 生成私钥文件
     *
     * @param privateKey 私钥文件
     * @param path       路径
     */
    private void writePrivateKey(PrivateKey privateKey, String path) {
        FileOutputStream outputStream = null;
        try {
            outputStream = new FileOutputStream(StringUtils.isEmpty(path) ? DE_PRIKEY_PATH : path);
            outputStream.write(privateKey.getEncoded());
        } catch (IOException e) {
            log.error("生成私钥文件出错！", e);
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("生成私钥文件关闭流出错！", e);
                }
            }
        }
    }

    /**
     * 生成公钥文件
     *
     * @param publicKey 公钥
     * @param path      生成路径
     */
    private void writePublicKey(PublicKey publicKey, String path) {
        FileOutputStream outputStream = null;
        try {
            outputStream = new FileOutputStream(StringUtils.isEmpty(path) ? DE_PUBKEY_PATH : path);
            outputStream.write(publicKey.getEncoded());
        } catch (IOException e) {
            log.error("写公钥文件出错！", e);
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("写公钥文件关闭流出错！", e);
                }
            }
        }
    }

    /**
     * 生产RSA密钥对
     *
     * @return 密钥对
     * @throws NoSuchAlgorithmException 异常
     */
    public KeyPair generateRsaKeyPair() throws NoSuchAlgorithmException {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance(RSA);
        keyPairGenerator.initialize(KEY_LENGTH);
        return keyPairGenerator.generateKeyPair();
    }

    /**
     * 加密字符串：通过RSA加密，再经过base64加密
     *
     * @param sourceStr 原始字符串
     * @return 加密结果
     */
    public String encryptStr(String sourceStr) throws PortalException {
        return encryptStr(sourceStr, "");
    }


    /**
     * 解密字符串：通过RSA加密，再经过base64加密
     *
     * @param sourceStr 加密后的字符串
     * @param keyPath   key路径
     * @return 解密结果
     */
    public String encryptStr(String sourceStr, String keyPath) throws PortalException {

        try {
            X509EncodedKeySpec encodedKeySpec = new X509EncodedKeySpec(readPublicKey(keyPath));
            PublicKey publicKey = keyFactory.generatePublic(encodedKeySpec);
            //公钥对字符串进行加密
            byte[] encryptedBytes = encrypt(sourceStr, publicKey);
            //base64加密成字符串
            String base64Encoding = Base64.getEncoder().encodeToString(encryptedBytes);
            //处理+号问题，再用urlencoding编码一下
            return URLEncoder.encode(base64Encoding, "UTF-8");
        } catch (Exception e) {
            log.error("解密字符【" + sourceStr + "】串出错！", e);
            throw new PortalException(ExceptionEnum.EX_ENCRYPT_ERROR);
        }

    }

    /**
     * 使用公钥进行加密
     *
     * @param data      要加密的字符串
     * @param publicKey 公钥
     * @return 加密后结果
     * @throws Exception 异常
     */
    public byte[] encrypt(String data, PublicKey publicKey) throws Exception {
        Cipher cipher = Cipher.getInstance(RSA);
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        return cipher.doFinal(data.getBytes());
    }

    /**
     * 解密字符串：先经过base64解密再通过RSA解密
     *
     * @param encryptedStr 加密后的字符串
     * @return 解密结果
     */
    public String decryptStr(String encryptedStr) throws Exception {
        return decryptStr(encryptedStr, "");
    }


    /**
     * 解密字符串：先经过base64解密再通过RSA解密
     *
     * @param encryptedStr 加密后的字符串
     * @param keyPath      key路径
     * @return 解密结果
     */
    public String decryptStr(String encryptedStr, String keyPath) throws Exception {
        String decodeEncryptedStr = URLDecoder.decode(encryptedStr, "UTF-8");
        byte[] base64EncryptedBytes = Base64.getDecoder().decode(decodeEncryptedStr);

        byte[] readPrivateKey = readPrivateKey(keyPath);
        PKCS8EncodedKeySpec encodedKeySpec = new PKCS8EncodedKeySpec(readPrivateKey);
        PrivateKey privateKeyObj = keyFactory.generatePrivate(encodedKeySpec);

        return decrypt(base64EncryptedBytes, privateKeyObj);
    }

    /**
     * 使用私钥进行解密
     *
     * @param data       要解密的字节数组
     * @param privateKey 私钥
     * @return 解密后字符串
     * @throws Exception 异常
     */
    public static String decrypt(byte[] data, PrivateKey privateKey) throws Exception {
        Cipher cipher = Cipher.getInstance(RSA);
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        byte[] decryptedBytes = cipher.doFinal(data);
        return new String(decryptedBytes);
    }

    /**
     * 测试入库
     *
     * @param args 入参
     * @throws Exception 异常
     */
    public static void main(String[] args) throws Exception {
        RsaEncryptionDecryption rsaEncryptionDecryption = new RsaEncryptionDecryption();


//         生成RSA密钥对
//        KeyPair keyPair = generateRSAKeyPair();
//        PublicKey publicKey = keyPair.getPublic();
//        PrivateKey privateKey = keyPair.getPrivate();
//        writePublicKey(publicKey);
//        writePrivateKey(privateKey);
        // 要加密的字符串
        String originalString = "userName=张三,deptId=34,sfzh=1232131321";
        log.info("原始字符串： " + originalString);

        // 使用公钥进行加密
        String encryptedString = rsaEncryptionDecryption.encryptStr(originalString);
        log.info("加密后的字节数组： " + encryptedString);

        // 使用私钥进行解密
        String decryptedString = rsaEncryptionDecryption.decryptStr(encryptedString);
        log.info("解密后的字符串： " + decryptedString);
    }

}

