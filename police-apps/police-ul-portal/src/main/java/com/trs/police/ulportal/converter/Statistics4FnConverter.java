package com.trs.police.ulportal.converter;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.ulportal.domain.entity.Statistics4FuNeng;
import com.trs.police.ulportal.domain.vo.Statistics4FuNengVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @description 赋能统计转换器
 * @date 2023/12/04
 */
@Mapper(componentModel = "spring")
public interface Statistics4FnConverter {


    /**
     * 实体vo
     *
     * @param statistics4FuNeng 实体
     * @return vo
     */
    Statistics4FuNengVO toVo(Statistics4FuNeng statistics4FuNeng);

    /**
     * 实体集合转vo集合
     *
     * @param statistics4FuNengs 实体集合
     * @return vo集合
     */
    List<Statistics4FuNengVO> toVoList(List<Statistics4FuNeng> statistics4FuNengs);

    /**
     * 实体分页转vo分页
     *
     * @param page page
     * @return vo
     */
    Page<Statistics4FuNengVO> toPageVo(IPage<Statistics4FuNeng> page);

}
