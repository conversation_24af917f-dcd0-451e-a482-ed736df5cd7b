package com.trs.police.ulportal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.ulportal.domain.entity.Role;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Role查询接口
 *
 * <AUTHOR>
 * @date 2023/12/05 17:59
 */
@Mapper
public interface RoleMapper extends BaseMapper<Role> {

    /**
     *  获取指定用户id的角色列表
     *
     * @param userId 用户id
     * @return 角色列表
     */
    List<Role> getRoleListByUserId(@Param("userId") Long userId);

    /**
     *  统计用户管理员的权限
     *
     * @param userId 用户id
     * @return 校验
     */
    int countUserAdminRole(@Param("userId") Long userId);
}