package com.trs.police.ulportal.adapter.controller;

import com.grt.condify.dto.BaseDTO;
import com.trs.police.ulportal.common.annotation.ExcludeInfo;
import com.trs.police.ulportal.common.handler.PortalException;
import com.trs.police.ulportal.domain.dto.*;
import com.trs.police.ulportal.domain.vo.*;
import com.trs.police.ulportal.service.LogService;
import com.trs.web.builder.base.RestfulResultsV2;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @description 反馈信息相关接口
 * @date 2023/11/13 15:20
 */
@RestController
@Api(value = "日志", tags = "日志")
@RequiredArgsConstructor
@RequestMapping("/log")
@Slf4j
public class LogController {


    private final LogService logService;

    /**
     * 查询日志列表
     *
     * @param logListDto 入参
     * @return 结果
     * @throws Exception 异常
     */
    @PostMapping("/queryList")
    @ApiOperation(value = "查询日志列表", notes = "查询日志列表")
    @ExcludeInfo(needOrganCodes = true)
    public RestfulResultsV2<LogVO> queryList(@Validated @RequestBody LogListDto logListDto) throws Exception {
        return logService.queryList(logListDto);
    }

    /**
     * 保存操作日志
     *
     * @param module 模块名称
     * @param request 请求参数
     * @return 结果
     */
    @GetMapping("/saveOperationLog")
    public RestfulResultsV2 saveOperationLog(@RequestParam(value = "module")String module,HttpServletRequest request) throws PortalException {
       return logService.saveOperationLog(module,request);
    }


    /**
     * 获取检索应用列表
     *
     * @return 获取检索应用列表
     */
    @GetMapping("/queryThirdPartyNameList")
    @ApiOperation(value = "获取检索应用列表", notes = "获取检索应用列表")
    public RestfulResultsV2<LogThirdPartyNameVO> queryThirdPartyNameList() {
        return logService.queryThirdPartyNameList();
    }

    /**
     * 日志导出：和产品沟通确认了，不是查出什么导什么，而是用户在列表选什么导什么
     *
     * @param exportLogDto 入参
     * @param response 入参
     * @throws Exception 异常
     */
    @PostMapping("/export")
    @ApiOperation(value = "日志导出", notes = "日志导出")
    public void export(@RequestBody @Validated ExportLogDto exportLogDto, HttpServletResponse response) throws Exception {
        logService.export(exportLogDto, response);
    }

    /**
     *  获取总使用量
     *
     * @param dto dto
     * @return  使用量
     */
    @PostMapping("/getTotalLogCount")
    @ApiOperation(value = "获取总使用量", notes = "获取总使用量")
    @ExcludeInfo
    public RestfulResultsV2<TotalLogCountVO> getTotalLogCount(@RequestBody TotalLogCountDTO dto) {
        return logService.getTotalLogCount(dto);
    }

    /**
     *  获取日常工作使用量-人员排名前十
     *
     * @param dto dto
     * @return  使用量
     */
    @PostMapping("/getDailyPersonCountTopTen")
    @ApiOperation(value = "获取日常工作使用量-人员排名前十", notes = "获取日常工作使用量-人员排名前十")
    @ExcludeInfo
    public RestfulResultsV2<DailyCountVO> getDailyPersonCountTopTen(@RequestBody DailyCountDTO dto) {
        return logService.getDailyPersonCountTopTen(dto);
    }

    /**
     *  获取日常工作使用量-人员排名前十
     *
     * @param dto dto
     * @return  使用量
     */
    @PostMapping("/getDailyPersonCount")
    @ApiOperation(value = "获取日常工作使用量", notes = "获取日常工作使用量")
    @ExcludeInfo
    public RestfulResultsV2<DailyCountVO> getDailyPersonCount(@RequestBody DailyCountDTO dto) {
        return logService.getDailyPersonCount(dto);
    }

    /**
     *  获取日常工作使用量-按单位
     *
     * @param dto dto
     * @return  使用量
     */
    @PostMapping("/getDailyOrganCount")
    @ApiOperation(value = "获取日常工作使用量-按单位", notes = "获取日常工作使用量-按单位")
    @ExcludeInfo
    public RestfulResultsV2<DailyCountVO> getDailyOrganCount(@RequestBody DailyCountDTO dto) {
        return logService.getDailyOrganCount(dto);
    }

    /**
     *  获取数据能效--获取系统使用情况
     *
     * @param dto 参数
     * @return  使用量
     */
    @PostMapping("/getSystemUse")
    @ApiOperation(value = "获取系统使用情况")
    @ExcludeInfo(needOrganCodes = true)
    public RestfulResultsV2<SystemUseInfoVO> getSystemUse(@RequestBody DataNxDto dto){
         return logService.getSystemUse(dto);
    }

    /**
     *  获取数据能效--获取人员能效评估
     *
     * @param dto 参数
     * @return  人员能效评估
     */
    @PostMapping("/getPersonUseInfo")
    @ApiOperation(value = "获取人员能效评估")
    @ExcludeInfo(needOrganCodes = true)
    public RestfulResultsV2<PeopleUseInfoVO> getPersonUseInfo(@RequestBody DataNxDto dto){
        return logService.getPersonUseInfo(dto);
    }
    /**
     *  获取数据能效--使用总数
     *
     * @param dto 参数
     * @return  人员能效评估
     */
    @PostMapping("/getAllCount")
    @ApiOperation(value = "获取系统总使用情况")
    @ExcludeInfo(needOrganCodes = true)
    public RestfulResultsV2<TotalLogCountVO> getTotalCount(@RequestBody DataNxDto dto){
        return logService.getTotalSystemUseCount(dto);
    }

    /**
     *  获取次数趋势图接口
     *
     * @param dto dto
     * @return  使用量
     */
    @PostMapping("/getStatisticTrend")
    @ApiOperation(value = "获取次数趋势图接口", notes = "获取次数趋势图接口")
    @ExcludeInfo(needOrganCodes = true)
    public RestfulResultsV2<StatisticTrendVO> getStatisticTrend(@RequestBody StatisticTrendDTO dto) {
        return logService.getStatisticTrend(dto);
    }

    /**
     *  获取使用时段次数趋势图接口
     *
     * @param dto dto
     * @return  使用量
     */
    @PostMapping("/getTimeSpanStatisticTrend")
    @ApiOperation(value = "获取使用时段次数趋势图接口", notes = "获取使用时段次数趋势图接口")
    @ExcludeInfo(needOrganCodes = true)
    public RestfulResultsV2<StatisticTrendVO> getTimeSpanStatisticTrend(@RequestBody StatisticTrendDTO dto) {
        return logService.getTimeSpanStatisticTrend(dto);
    }

    /**
     *  获取单位能效评估
     *
     * @param dto dto
     * @return  统计结果
     */
    @PostMapping("/getDeptEvaluate")
    @ApiOperation(value = "获取单位能效评估", notes = "获取单位能效评估")
    @ExcludeInfo
    public RestfulResultsV2<DeptEvaluateVO> getDeptEvaluate(@RequestBody DeptEvaluateDTO dto) {
        return logService.getDeptEvaluate(dto);
    }

    /**
     *  统计能效的总使用量
     *
     * @param dto dto
     * @return  使用量
     */
    @PostMapping("/getEvaluateTotalCount")
    @ApiOperation(value = "获取总使用量", notes = "获取总使用量")
    @ExcludeInfo
    public RestfulResultsV2<EvaluateTotalCountVO> getEvaluateTotalCount(@RequestBody TotalLogCountDTO dto) {
        return logService.getEvaluateTotalCount(dto);
    }

    /**
     *  获取单位使用人员统计
     *
     * @param dto dto
     * @return 统计
     * @throws PortalException 异常
     */
    @PostMapping("/getDeptRankByOrganCode")
    @ApiOperation(value = "获取单位使用人员统计", notes = "获取单位使用人员统计")
    @ExcludeInfo
    public RestfulResultsV2<DeptRankVO> getDeptRankByOrganCode(@Validated @RequestBody DeptRankByOrganCodeDTO dto) throws PortalException {
        return logService.getDeptRankByOrganCode(dto);
    }

    /**
     *  完整性加密历史数据
     *
     * @param dto dto
     */
    @PostMapping("/encryptHistoryMac")
    @ApiOperation(value = "完整性加密历史数据", notes = "完整性加密历史数据")
    public void encryptHistoryMac(@RequestBody BaseDTO dto){
        logService.encryptHistoryMac(dto);
    }
}
