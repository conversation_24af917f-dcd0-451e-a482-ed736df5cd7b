package com.trs.police.ulportal.adapter.schedule;

import com.trs.police.ulportal.service.impl.PersonClueRelatedInfoImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;


/**
 * Description: 同步person_clue中的三方信息
 *
 * @author: duzhaoyang
 * @create: 2023-12-20 14:22
 */
@Component
@Slf4j
@RequiredArgsConstructor
@ConditionalOnProperty(value = "com.trs.schedule.personClueThirdData.taskEnable",havingValue = "true")
public class PersonClueRelatedInfoTask {

   @Autowired
   private PersonClueRelatedInfoImpl synchronous;

    /**
     *  同步第三方视图数据
     */
    @Scheduled(cron = "${com.trs.schedule.personClueThirdData.time:0 2 0 * * ?}")
    public void run(){
        log.info("同步第三方视图数据：" + System.currentTimeMillis());
        try {
            synchronous.personClueThirdInfoSynchronous();
            log.info("同步第三方视图数据成功");
        }catch (Throwable throwable){
            log.error("同步第三方视图数据失败！", throwable);
        }
        log.info("同步第三方视图数据结束：" + System.currentTimeMillis());
    }
}
