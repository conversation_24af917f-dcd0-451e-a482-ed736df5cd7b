package com.trs.police.ulportal.common.config;

import com.trs.police.common.core.constant.PersonClueConstant;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 关注预警-人员线索（剽窃、卖淫嫖娼、盗窃三车）关注预警配置
 * @date 2024/10/17 11:29
 */
@Data
@Component
@ConfigurationProperties(prefix = "com.trs.police.caremonitor")
public class PersonClueCareMonitorConfig {

    public List<Long> dwdPqAreaId = new ArrayList<>();

    public List<Long> dwdMypcAreaId = new ArrayList<>();

    public List<Long> dwdDqscAreaId = new ArrayList<>();

    /**
     * 获取ids
     *
     * @param type 类型
     * @return ids
     */
    public List<Long> getIds(Integer type) {
        switch (type){
            case PersonClueConstant.DQSC_TYPE:
                return this.dwdDqscAreaId;
            case PersonClueConstant.PQ_TYPE:
                return this.dwdPqAreaId;
            case PersonClueConstant.MYPC_TYPE:
                return this.dwdMypcAreaId;
            default:
                return new ArrayList<>();
        }
    }

}
