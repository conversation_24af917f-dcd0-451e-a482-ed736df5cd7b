package com.trs.police.ulportal.domain.vo.policesituationtopic;

import lombok.Data;

/**
 * Description:出警规范vo
 *
 * <AUTHOR>
 * @create 2024-05-06 9:06
 */
@Data
public class PoliceRegulationsVO {
    /**
     * 数据主键（Mysql 推荐使用连续自增的整数）
     */
    private Long id;
    /**
     * 日期
     */
    private String rq;
    /**
     * 部门编号
     */
    private String bmbh;
    /**
     * 部门名称
     */
    private String bmmc;
    /**
     * 出警总数
     */
    private Integer cjzs;
    /**
     * 未签收数
     */
    private Integer wqs;
    /**
     * 出警小于60s数
     */
    private Integer cjzs60;
}
