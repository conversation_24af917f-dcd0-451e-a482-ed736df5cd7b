package com.trs.police.ulportal.domain.dto;

import com.trs.common.utils.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/28
 */
@Data
public class JqDTO {
    @ApiModelProperty("分页大小，默认10")
    private Integer pageSize;

    @ApiModelProperty("分页索引，默认1")
    private Integer pageNum;

    @ApiModelProperty("开始时间")
    private String startTime;

    @ApiModelProperty("结束时间")
    private String endTime;

    @ApiModelProperty("检索字段")
    private String searchField;

    @ApiModelProperty("检索关键词")
    private String searchValue;

    @ApiModelProperty("检索关键词")
    private String indexValue;

    @ApiModelProperty("报警电话")
    private String bjdh;

    @ApiModelProperty("警员编号")
    private String jybh;

    @ApiModelProperty("接警单位代码")
    private String jjdwdm;

    @ApiModelProperty("出警单位代码")
    private String cjdwdm;

    @ApiModelProperty("警情类别")
    private String type;

    @ApiModelProperty("时间段检索值")
    private String timeScreen;

    @ApiModelProperty("是否有效")
    private Boolean isValid;

    @ApiModelProperty("排序字段")
    private String orderField;

    @ApiModelProperty("排序关键字")
    private String orderValue;

    @ApiModelProperty("警情/出警类型,默认值为jq(警情)")
    private String jqCjType = "jq";

    @ApiModelProperty("指数名称")
    private String indexName;

    /**
     * 初始化开始时间
     *
     * @param startTime startTime
     */
    public void setStartTime(String startTime) {
        if(StringUtils.isNotEmpty(startTime) && startTime.strip().length()<=10){
            this.startTime = startTime + " 00:00:00";
        }else {
            this.startTime = startTime;
        }
    }

    /**
     * 初始化结束时间
     *
     * @param endTime endTime
     */
    public void setEndTime(String endTime) {
        if(StringUtils.isNotEmpty(endTime) && endTime.strip().length()<=10){
            this.endTime = endTime + " 23:59:59";
        }else {
            this.endTime = endTime;
        }
    }
}
