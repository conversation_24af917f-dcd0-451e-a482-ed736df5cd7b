package com.trs.police.ulportal.converter;

import com.trs.police.common.core.entity.dwd.WarningPersonClueEntity;
import com.trs.police.ulportal.domain.vo.PersonTrackVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;


/**
 * Description: 消息实体转换器
 *
 * @author: lv.bo
 * @create: 2023-12-07 16:23
 */
@Mapper(componentModel = "spring")
public interface WarningPersonClueConverter {

    /**
     *  toEntity
     *
     * @param entity 入参
     * @return 实体
     */
    @Mappings({
            @Mapping(target = "recordId", source = "idCard"),
            @Mapping(target = "activityTime", expression = "java(localDateTimeToString(entity.getActivityTime()))")
    })
    PersonTrackVO toEntity(WarningPersonClueEntity entity);

    /**
     * 转换时间
     *
     * @param dateTime 时间
     * @return 字符串时间
     */
    default String localDateTimeToString(LocalDateTime dateTime) {
        return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
}
