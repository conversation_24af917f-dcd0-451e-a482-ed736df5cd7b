package com.trs.police.ulportal.common.util;

import org.apache.commons.lang3.StringUtils;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.Message;

/**
 * Description:
 *
 * @author: lv.bo
 * @create: 2023-12-21 10:36
 */
public class KafkaHelperUtil {

    /**
     *  获取kafka消息
     *
     * @param message message
     * @return 返回值
     */
    public static String checkPayLoad(Message<String> message){
        Acknowledgment acknowledgment = message.getHeaders().get(KafkaHeaders.ACKNOWLEDGMENT, Acknowledgment.class);
        String payload = message.getPayload();
        if (StringUtils.isEmpty(payload)){
            assert acknowledgment != null;
            acknowledgment.acknowledge();
        }
        return payload;
    }
}