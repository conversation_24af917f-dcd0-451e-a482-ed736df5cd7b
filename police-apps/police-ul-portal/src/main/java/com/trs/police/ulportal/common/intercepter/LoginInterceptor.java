package com.trs.police.ulportal.common.intercepter;


import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.utils.StringUtil;
import com.trs.police.ulportal.common.config.WebSecurityConfig;
import com.trs.police.ulportal.common.constants.LoginConstants;
import com.trs.police.ulportal.common.handler.PortalException;
import com.trs.police.ulportal.common.util.TokenUtil;
import com.trs.police.ulportal.domain.vo.PortalUserVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

/**
 * <AUTHOR>
 * @description 登录拦截，校验token
 * @date 2021/5/24
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class LoginInterceptor implements HandlerInterceptor {

    private final TokenUtil tokenUtil;

    private final WebSecurityConfig webSecurityConfig;

    /**
     * token过期时间：默认为120，单位分钟
     */
    @Value("${token.expiration.external:120}")
    private Long tokenExpirationExternal;


    /**
     * 登录拦截
     *
     * @param request  request
     * @param response response
     * @param handler  handler
     * @return 结果
     * @throws PortalException 异常
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws PortalException {

        if (!tokenUtil.needCheckToken(request.getRequestURI())) {
            return true;
        }
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        PortalUserVO userInfo;
        if (currentUser != null) {
            userInfo = new PortalUserVO();
            userInfo.setId(currentUser.getId());
            userInfo.setLoginName(currentUser.getUsername());
            userInfo.setUserName(currentUser.getRealName());
            userInfo.setSex(currentUser.getGender());
            userInfo.setIdEntityCard(currentUser.getIdNumber());
            userInfo.setOrgCode(currentUser.getDept().getCode());
            userInfo.setOrgName(currentUser.getDept().getName());
            userInfo.setPositionName(currentUser.getDuty());
            userInfo.setMobile(currentUser.getMobile());
            userInfo.setUserStatus(0);
        } else {
            String unifiedToken = request.getHeader("unifiedToken");
            String ignoreT = request.getHeader("ignoreT");
            unifiedToken = StringUtil.isEmpty(unifiedToken) ? request.getHeader("Authorization") : unifiedToken;
            userInfo = tokenUtil.parseToken(unifiedToken, ignoreT);
        }
        HttpSession session = request.getSession();

        if (null == session) {
            return false;
        }
        if (null == session.getAttribute(LoginConstants.SESSION_USER_KEY)) {
            session.setAttribute(LoginConstants.SESSION_USER_KEY, userInfo);
        }
        //暂时不需要做token的刷新
        //response.setHeader("unifiedToken","newtoken");
        return true;
    }
}
