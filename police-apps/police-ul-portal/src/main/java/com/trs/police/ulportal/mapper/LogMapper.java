package com.trs.police.ulportal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.trs.police.ulportal.domain.dto.*;
import com.trs.police.ulportal.domain.entity.Log;
import com.trs.police.ulportal.domain.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: 日志mapper
 * <AUTHOR>
 * @date 2023/12/04
 */
@Mapper
public interface LogMapper extends BaseMapper<Log> {

    /**
     * 获取检索应用列表
     *
     * @return 检索应用列表
     */
    List<LogThirdPartyNameVO> queryThirdPartyNameList();

    /**
     *  获取个人用户排行榜-登录天数
     *
     * @param idCards 身份证号集合
     * @param limit 限制条数
     * @param dto 排除信息
     * @return 排行榜
     */
    List<UserRankVO> selectUserLoginDays(@Param("idCards") List<String> idCards, @Param("limit") Integer limit, @Param("dto") ExcludeInfoDTO dto);

    /**
     *  获取个人用户排行榜-操作次数
     *
     * @param idCards 身份证号集合
     * @param limit 限制条数
     * @param dto 排除信息
     * @return 排行榜
     */
    List<UserRankVO> selectUserOperCount(@Param("idCards") List<String> idCards, @Param("limit") Integer limit, @Param("dto") ExcludeInfoDTO dto);

    /**
     *  查询部门排行榜
     *
     * @param iPage 分页参数
     * @param dto dto
     * @return 排行榜
     */
    List<DeptRankVO> getDeptRank(IPage<DeptRankVO> iPage, @Param("dto") DeptRankDto dto);

    /**
     *  统计异常时间段的信息
     *
     * @param dto dto
     * @return 统计信息
     */
    List<ErrorTimeWarningVO> getErrorTimeCount(@Param("dto") WarningDTO dto);

    /**
     *  统计人员指定时间段的使用信息
     *
     * @param dto dto
     * @return 统计
     */
    List<ErrorTimeWarningVO> getUserCountByTime(@Param("dto") WarningDTO dto);

    /**
     *  统计日志总使用量
     *
     * @param dto dto
     * @return 统计结果
     */
    Long selectTotalLogCount(@Param("dto") TotalLogCountDTO dto);

    /**
     *  统计日志总使用量
     *
     * @param dto dto
     * @return 统计结果
     */
    Long selectNightLogCount(@Param("dto") TotalLogCountDTO dto);

    /**
     *  获取日常工作使用量-人员排名前十
     *
     * @param dto dto
     * @return 统计
     */
    List<DailyCountVO> getDailyPersonCountTopTen(@Param("dto") DailyCountDTO dto);

    /**
     *  获取日常工作使用量-按单位
     *
     * @param page 分页
     * @param dto dto
     * @return 统计
     */
    IPage<DailyCountVO> getDailyOrganCount(IPage<DailyCountVO> page, @Param("dto") DailyCountDTO dto);

    /**
     *  获取日常工作使用量
     *
     * @param page 分页
     * @param dto dto
     * @return 统计结果
     */
    IPage<DailyCountVO> getDailyPersonCount(IPage<DailyCountVO> page, @Param("dto") DailyCountDTO dto);

    /**
     *  获取统计趋势
     *
     * @param dto dto
     * @return 统计结果
     */
    List<StatisticTrendVO> getStatisticTrend(@Param("dto") StatisticTrendDTO dto);

    /**
     *  获取单位能效评估
     *
     * @param page 分页
     * @param dto dto
     * @return  统计结果
     */
    IPage<DeptEvaluateVO> getDeptEvaluate(IPage<DeptEvaluateVO> page, @Param("dto") DeptEvaluateDTO dto);

    /**
     *  获取部门下面的人数信息
     *
     * @param codeList 部门编码集合
     * @return 部门人数信息
     */
    List<DeptRankVO> getDeptInfo(@Param("codeList") List<String> codeList);

    /**
     *  获取系统使用情况
     *
     * @param page 分页参数
     * @param dto 参数
     * @return 部门人数信息
     */
    IPage<SystemUseInfoVO> getSystemUse(@Param("page") IPage<SystemUseInfoVO> page,@Param("dto") DataNxDto dto);
    /**
     *  获取系统使用情况
     *
     * @param dto 参数
     * @return 部门人数信息
     */
    List<SystemUseInfoVO> getSystemUse(@Param("dto") DataNxDto dto);

    /**
     *  获取人员能效评估
     *
     * @param page 分页参数
     * @param dto 参数
     * @return 部门人数信息
     */
    IPage<PeopleUseInfoVO> getPersonUseInfo(@Param("page") IPage<PeopleUseInfoVO> page,@Param("dto") DataNxDto dto);

    /**
     *  获取使用次数
     *
     * @param dto 参数
     * @return 部门人数信息
     */
    TotalLogCountVO getTotalCount(@Param("dto") DataNxDto dto);

    /**
     *  获取使用次数
     *
     * @param dto dto
     * @return 统计结果
     */
    EvaluateTotalCountVO getEvaluateTotalCount(@Param("dto") TotalLogCountDTO dto);

    /**
     *  获取单位使用人员统计
     *
     * @param dto dto
     * @return 单位使用人员统计
     */
    List<DeptRankVO> getDeptRankByOrganCode(@Param("dto") DeptRankByOrganCodeDTO dto);

    /**
     *  获取使用时段次数趋势图接口
     *
     * @param dto dto
     * @return  使用量
     */
    List<StatisticTrendVO> getTimeSpanStatisticTrend(@Param("dto") StatisticTrendDTO dto);

    /**
     *  获取最近的一条日志
     *
     * @param dto dto
     * @return 日志信息
     */
    Log getRecentLogByErrorTime(@Param("dto") WarningDTO dto);
}
