package com.trs.police.ulportal.adapter.controller;

import com.trs.police.ulportal.service.SyncUserService;
import com.trs.web.builder.base.RestfulResultsV2;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: 提供定时任务的controller方法
 *
 * @author: lv.bo
 * @create: 2023-12-20 15:45
 */
@RestController
@Api(value = "定时任务", tags = "定时任务调用")
@RequiredArgsConstructor
@RequestMapping("/schedule")
@Slf4j
public class ScheduleController {

    private final SyncUserService syncUserService;


    /**
     *  同步用户
     *
     * @return 返回值
     * @throws Exception 异常
     */
    @PostMapping("/syncUser")
    @ApiOperation(value = "同步第三方用户", notes = "同步第三方用户")
    public RestfulResultsV2 syncUser() throws Exception {
        syncUserService.syncThirdUserInfo();
        return RestfulResultsV2.ok("同步用户成功");
    }
}
