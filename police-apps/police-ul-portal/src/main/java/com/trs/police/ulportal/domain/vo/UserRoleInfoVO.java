package com.trs.police.ulportal.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Description:
 *
 * @author: lv.bo
 * @create: 2023-12-15 15:04
 */
@Data
public class UserRoleInfoVO implements Serializable {
    private static final long serialVersionUID = 4189375251839684111L;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("用户有权限的appids")
    private String appIds;

    @ApiModelProperty("用户有权限的角色")
    private String roleIds;
}
