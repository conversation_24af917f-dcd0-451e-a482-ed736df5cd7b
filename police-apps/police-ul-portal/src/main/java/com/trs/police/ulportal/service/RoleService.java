package com.trs.police.ulportal.service;

import com.trs.police.ulportal.domain.entity.Role;
import com.trs.police.ulportal.domain.vo.RoleVO;

import java.util.List;

/**
 * Description:
 *
 * @author: lv.bo
 * @create: 2023-12-06 16:30
 */
public interface RoleService extends BaseService<Role, RoleVO>{

    /**
     *  获取所有正常状态的角色
     *
     * @return 返回值
     */
    List<RoleVO> queryRoleList();

    /**
     *  保存用户角色
     *
     * @param userId extUserId
     * @param roleIds rolerIds
     */
    void saveRight(Long userId, String roleIds);

    /**
     *  获取指定用户id的角色列表
     *
     * @param userId 用户id
     * @return 角色列表
     */
    List<RoleVO> getRoleListByUserId(Long userId);

    /**
     *  校验用户是否是管理员
     *
     * @param userId 用户id
     * @return 是否是管理员
     */
    boolean checkUserIsAdmin(Long userId);

    /**
     *  加密角色历史mac
     */
    void encryptRoleHistoryMac();
}
