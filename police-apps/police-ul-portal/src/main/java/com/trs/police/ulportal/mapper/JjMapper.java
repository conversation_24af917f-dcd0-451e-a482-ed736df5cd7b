package com.trs.police.ulportal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.ulportal.domain.dto.CommonStatisticsDto;
import com.trs.police.ulportal.domain.entity.Jj;
import com.trs.police.ulportal.domain.vo.Statistics4JjCjVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 接警查询接口
 *
 * <AUTHOR>
 * @date 2023/12/08 17:59
 */
@Mapper
public interface JjMapper extends BaseMapper<Jj> {
    /**
     * 接警统计-按辖区
     *
     * @param statisticsDto dto
     * @param gxDwCodeList gxDwCodeList
     * @return 接警统计
     */
    List<Statistics4JjCjVO> statistics4JjByOrgan(@Param("statisticsDto") CommonStatisticsDto statisticsDto, @Param("gxDwCodeList") List<String> gxDwCodeList);

    /**
     * 接警统计-按类型
     *
     * @param gxDwCodeList gxDwCodeList
     * @param statisticsDto dto
     * @return 接警统计
     */
    List<Statistics4JjCjVO> statistics4JjByType(@Param("statisticsDto") CommonStatisticsDto statisticsDto, @Param("gxDwCodeList") List<String> gxDwCodeList);

}