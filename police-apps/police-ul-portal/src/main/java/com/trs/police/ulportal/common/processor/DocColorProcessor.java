package com.trs.police.ulportal.common.processor;
import com.deepoove.poi.data.TextRenderData;
import com.deepoove.poi.policy.AbstractRenderPolicy;
import com.deepoove.poi.render.RenderContext;
import org.apache.poi.xwpf.usermodel.XWPFRun;

import java.awt.*;

/**
 * 文本颜色
 *
 * <AUTHOR>
 * @date 2024/07/29
 */

public class DocColorProcessor extends AbstractRenderPolicy<TextRenderData> {
    private final Color color;

    public DocColorProcessor(Color color) {
        this.color = color;
    }
    @Override
    public void doRender(RenderContext<TextRenderData> context){
        XWPFRun run = context.getRun();
        TextRenderData data = context.getData();
        run.setText(data.getText(), 0);
        run.setColor(String.format("%02x%02x%02x", color.getRed(), color.getGreen(), color.getBlue()));
    }

}
