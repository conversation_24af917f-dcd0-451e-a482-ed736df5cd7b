package com.trs.police.ulportal.converter;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.ulportal.domain.dto.MessageDto;
import com.trs.police.ulportal.domain.entity.Message;
import com.trs.police.ulportal.domain.vo.MessageVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

/**
 * Description: 消息实体转换器
 *
 * @author: lv.bo
 * @create: 2023-12-07 16:23
 */
@Mapper(componentModel = "spring")
public interface MessageConverter {

    /**
     *  toEntity
     *
     * @param messageDto 入参
     * @return 实体
     */
    @Mappings({
            @Mapping(target = "noticeTime", expression = "java(getDate(messageDto.getNoticeTime()))")
    })
    Message toEntity(MessageDto messageDto);

    /**
     *  根据秒的时间戳获取时间
     *
     * @param time 时间戳
     * @return LocalDateTime
     */
    default LocalDateTime getDate(Long time){
        if (time == null){
            return null;
        }
        Instant instant = null;
        // 毫秒秒
        if (13 == String.valueOf(time).length()){
            instant = Instant.ofEpochMilli(time);
        } else {
            // 秒
            instant = Instant.ofEpochSecond(time);
        }
        return LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
    }

    /**
     * 实体vo
     *
     * @param message 实体
     * @return vo
     */
    MessageVO toVo(Message message);

    /**
     * 实体集合转vo集合
     *
     * @param messages 实体集合
     * @return vo集合
     */
    List<MessageVO> toVoList(List<Message> messages);

    /**
     * 实体分页转vo分页
     *
     * @param page page
     * @return vo
     */
    Page<MessageVO> toPageVo(IPage<Message> page);
}
