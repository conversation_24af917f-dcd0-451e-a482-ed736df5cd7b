package com.trs.police.apiforward.controller;

import com.trs.police.apiforward.domain.dto.PersonArchivesDto;
import com.trs.police.apiforward.service.SearchForwardService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @description 检索相关接口转发
 * @date 2024/02/01 15:20
 */
@RestController
@Api(value = "检索相关接口转发", tags = "检索相关接口转发")
@RequestMapping("/searchForward")
@RequiredArgsConstructor
public class SearchForwardController {

    /**
     * 真正的人档服务提供者：根据配置com.trs.personSearchService来进行区分，目前有SearchForwardOfFeign和SearchForwardOfAbility
     */
    @Resource(name = "${com.trs.personSearchService:SearchForwardOfFeign}ServiceImpl")
    private SearchForwardService searchForwardService;

    /**
     * 设置已读
     *
     * @param request request
     * @param dto     dto
     * @return 设置结果
     */
    @GetMapping("personDetail")
    @ApiOperation(value = "根据身份证号获取人员详情", notes = "根据身份证号获取人员详情")
    public String personDetail(HttpServletRequest request, PersonArchivesDto dto) {
        return searchForwardService.personDetail(request, dto);
    }

}
