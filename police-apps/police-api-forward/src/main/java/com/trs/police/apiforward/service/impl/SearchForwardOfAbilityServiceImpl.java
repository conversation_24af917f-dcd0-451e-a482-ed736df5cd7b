package com.trs.police.apiforward.service.impl;

import com.trs.midend.sdk.exception.GatewayException;
import com.trs.midend.sdk.upgrade.APIGatewayClient;
import com.trs.midend.sdk.upgrade.APIRequest;
import com.trs.police.apiforward.common.constant.SearchConstants;
import com.trs.police.apiforward.config.AbilityConfig;
import com.trs.police.apiforward.domain.dto.PersonArchivesDto;
import com.trs.police.apiforward.service.SearchForwardService;
import com.trs.police.common.core.excpetion.TRSException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024年02月04日 11:28
 */
@Service("SearchForwardOfAbilityServiceImpl")
@RequiredArgsConstructor
@Slf4j
public class SearchForwardOfAbilityServiceImpl implements SearchForwardService {

    private APIGatewayClient apiGatewayClient;

    @Autowired
    private AbilityConfig abilityConfig;

    /**
     * 初始化方法
     */
    @PostConstruct
    public void init() {
        log.info("开始执行类【SearchForwardOfAbilityServiceImpl】的init方法！");
        this.apiGatewayClient = new APIGatewayClient(
                abilityConfig.getClientKey(), abilityConfig.getClientName(), abilityConfig.getPublicKey());
    }

    @Override
    public String personDetail(HttpServletRequest request, PersonArchivesDto dto) {
        String authorization = request.getHeader("Authorization").replaceAll(" ","");

        String uuidStr = getUuId(authorization);
        JSONObject uuidJson = JSONObject.fromObject(uuidStr);

        String uuid = uuidJson.getJSONArray("data").getString(0);

        String personDetail = getDetail(authorization, uuid, dto.getRecordId());
        log.info(personDetail);
        return personDetail;
    }

    private String getDetail(String authorization, String uuid, String recordId) {
        Map<String, String> params = new HashMap<>();
        params.put("recordId", recordId);
        params.put("uuid", uuid);
        params.put("archivesType", SearchConstants.Search_archives_Type);
        params.put("fieldName", SearchConstants.Search_fieldName);

        APIRequest apiRequest = APIRequest
                .custom()
                .header("Authorization", authorization)
                .queryParams(params)
                .baseUri(abilityConfig.getBaseUri())
                .path(abilityConfig.getDetailPath());
        try {
            return apiGatewayClient.get(apiRequest, String.class).getBody();
        } catch (GatewayException e) {
            log.error("获取人员档案详情出错！", e);
            throw new TRSException("获取人员档案详情出错！");
        }
    }

    /**
     * 获取uuid
     *
     * @param authorization authorization
     * @return uuid
     */
    private String getUuId(String authorization) {

        APIRequest apiRequest = APIRequest
                .custom()
                .header("Authorization", authorization)
                .baseUri(abilityConfig.getBaseUri())
                .path(abilityConfig.getUuidPath());
        try {
            return apiGatewayClient.get(apiRequest, String.class).getBody();
        } catch (GatewayException e) {
            log.error("获取uuid出错！", e);
            throw new TRSException("获取uuid出错！");
        }
    }
}
