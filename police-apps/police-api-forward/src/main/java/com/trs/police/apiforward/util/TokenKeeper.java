package com.trs.police.apiforward.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.police.apiforward.config.properties.ImageRecognitionProperties;
import com.trs.police.apiforward.domain.dto.AuthorizeDto;
import com.trs.police.apiforward.rest.service.ImageRecognitionRestService;
import com.trs.police.common.core.excpetion.SystemException;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import retrofit2.Call;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

import java.io.IOException;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * token保持器
 */

@Slf4j
@Component
@ConditionalOnProperty(prefix = ImageRecognitionProperties.PREFIX, name = "enabled", havingValue = "true")
public class TokenKeeper {

    private static final Long RENEW_TIME = 43200L;

    private static final String CLIENT_TYPE = "winpc";

    private String token = null;

    /**
     * 获取token
     *
     * @return token
     */
    public String getToken() {
        if (Objects.isNull(token)) {
            refreshToken();
        }
        return token;
    }

    ImageRecognitionRestService restService;

    private final ImageRecognitionProperties properties;

    public TokenKeeper(ImageRecognitionProperties properties) {
        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(properties.getBaseUrl())
                .addConverterFactory(JacksonConverterFactory.create())
                .build();
        this.restService = retrofit.create(ImageRecognitionRestService.class);
        this.properties = properties;
    }

    /**
     * 定时刷新token方法
     */
    @Scheduled(initialDelay = 0, fixedDelay = 10, timeUnit = TimeUnit.HOURS)
    public void refreshToken() {
        log.info("开始刷新token任务");
        //-----------------第一次请求-----------------
        AuthorizeDto firstDto = new AuthorizeDto();
        firstDto.setUserName(properties.getUserName());
        firstDto.setClientType(CLIENT_TYPE);
        Call<JsonNode> firstCall = restService.authorize(firstDto);
        Request firstRequest = firstCall.request();

        // 输出请求详情
        log.info("准备请求第一次获取token, url:{}", firstRequest.url());
        log.info("请求header: {}", firstRequest.header("Content-Type"));
        log.info("请求body对象: {}", SerializeUtil.bodyToString(firstRequest.body()));

        Response<JsonNode> firstRes;
        try {
            firstRes = firstCall.execute();
        } catch (Exception e) {
            log.error("刷新图像识别token时第一次请求发生错误：", e);
            throw new RuntimeException(e);
        }
        log.info("firstRes.code():{}, HttpStatus.UNAUTHORIZED.value():{}, isNotEqual:{}",
                firstRes.code(),
                HttpStatus.UNAUTHORIZED.value(),
                firstRes.code() != HttpStatus.UNAUTHORIZED.value());
        if (firstRes.code() != HttpStatus.UNAUTHORIZED.value()) {
            log.error("刷新图像识别token时第一次请求返回错误：[{}]{}，body:{}", firstRes.code(), firstRes.message(), firstRes.body());
            throw new SystemException("刷新图像识别token时第一次请求返回错误");
        }

        //取到第一次请求的返回，提取必要信息
        final JsonNode firstJson = SerializeUtil.getErrorBody(firstRes);
        final String realm = firstJson.get("realm").textValue();
        final String randomKey = firstJson.get("randomKey").textValue();
        final String encryptType = firstJson.get("encryptType").textValue();
        final String signature = AuthorizeUtil.signature(encryptType,
                realm,
                randomKey,
                properties.getUserName(),
                properties.getPassword());

        //-----------------第二次请求-----------------
        AuthorizeDto secondDto = new AuthorizeDto();
        secondDto.setUserName(properties.getUserName());
        secondDto.setSignature(signature);
        secondDto.setRandomKey(randomKey);
        secondDto.setEncryptType(encryptType);
        secondDto.setClientType(CLIENT_TYPE);
        secondDto.setExpiredTime(RENEW_TIME);
        Call<JsonNode> secondCall = restService.authorize(secondDto);
        Request secondRequest = secondCall.request();
        // 输出请求详情
        log.info("准备请求第二次获取token, url:{}", secondRequest.url());
        log.info("请求headers: {}", secondRequest.headers());
        log.info("请求body对象: {}", SerializeUtil.bodyToString(secondRequest.body()));

        Response<JsonNode> secondRes;
        try {
            secondRes = secondCall.execute();
        } catch (IOException e) {
            log.error("刷新图像识别token时第二次请求发生错误：", e);
            throw new RuntimeException(e);
        }
        if (!secondRes.isSuccessful()
                || secondRes.body() == null
                || secondRes.code() != HttpStatus.OK.value()) {
            log.error("刷新图像识别token时第二次请求返回错误:：[{}]{}", secondRes.code(), secondRes.message());
            throw new SystemException("刷新图像识别token时第二次请求返回错误");
        }
        this.token = secondRes.body().get("token").textValue();
        log.info("刷新token任务结束，token刷新为{}", this.token);
    }
}
