package com.trs.police.apiforward.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024年02月20日 15:49
 */
@Component
@Data
public class AbilityConfig {

    /**
     * 能力中心接口调用的clientKey
     */
    @Value("${com.trs.ability.clientKey:47c8234c2af64}")
    private String clientKey;

    /**
     * 能力中心接口调用的clientName
     */
    @Value("${com.trs.ability.clientName:智慧云控}")
    private String clientName;

    /**
     * 能力中心接口调用的publicKey
     */
    @Value("${com.trs.ability.publicKey:MIGfMA0GCSqGs}")
    private String publicKey;

    /**
     * 能力中心接口调用的baseUri
     */
    @Value("${com.trs.ability.baseUri:80.35.1.210}")
    private String baseUri;

    /**
     * 能力中心接口调用的uuid接口的路径
     */
    @Value("${com.trs.ability.uuidPath:/athena/forward/983423}")
    private String uuidPath;

    /**
     * 能力中心接口调用的档案详情的接口的路径
     */
    @Value("${com.trs.ability.detailPath:/athena/forward/CFE7}")
    private String detailPath;

}
