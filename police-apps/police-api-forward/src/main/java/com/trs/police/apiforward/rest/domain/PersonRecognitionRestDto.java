package com.trs.police.apiforward.rest.domain;

import lombok.Data;

import java.util.List;
import java.util.UUID;

/**
 * 人像识别rest请求参数
 */

@Data
public class PersonRecognitionRestDto {
    public PersonRecognitionRestDto() {
        this.page = 1;
        this.pageSize = 5;
    }

    public PersonRecognitionRestDto(String imageBase64, String startTime, String endTime) {
        this();
        this.condition = new Condition(imageBase64, startTime, endTime);
    }

    /**
     * 页码
     */
    private Integer page;

    /**
     * 期望返回的条数
     */
    private Integer pageSize;

    /**
     * 查询条件
     */
    private Condition condition;

    /**
     * 查询条件对象
     */
    @Data
    public static class Condition {
        public Condition(String imageBase64) {
            this.retrieval = List.of(new Retrieval(imageBase64));
            this.searchType = 0;
            this.threshold = 0;
        }

        public Condition(String imageBase64, String startCapTime, String endCapTime) {
            this(imageBase64);
            this.startCapTime = startCapTime;
            this.endCapTime = endCapTime;
        }

        /**
         * 查询类型 0-实时任务；1-录像任务；2-本地任务
         */
        private Integer searchType;

        /**
         * 开始时间
         */
        private String startCapTime;

        /**
         * 结束时间
         */
        private String endCapTime;

        /**
         * 比对阈值
         */
        private Integer threshold;

        /**
         * 检索条件
         */
        private List<Retrieval> retrieval;

        /**
         * 检索条件对象
         */
        @Data
        public static class Retrieval {
            public Retrieval(String faceImage) {
                this.faceImage = faceImage;
                this.retrievalId = UUID.randomUUID().toString();
            }

            /**
             * 人脸图片数据
             */
            private String faceImage;

            /**
             * 检索号
             */
            private String retrievalId;
        }
    }

    /**
     * 排序字段
     */
    private List<Order> order = List.of(new Order());

    /**
     * 排序字段对象
     */
    @Data
    public static class Order {
        public Order() {
            this.orderBy = "similarity";
            this.order = "desc";
        }

        /**
         * 排序字段
         */
        private String orderBy;

        /**
         * 排序方式
         */
        private String order;
    }

}
