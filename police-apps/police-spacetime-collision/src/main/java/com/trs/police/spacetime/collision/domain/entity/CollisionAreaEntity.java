package com.trs.police.spacetime.collision.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import com.trs.police.common.core.vo.GeometryVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 时空碰撞-区域
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "t_spacetime_collision_area", autoResultMap = true)
public class CollisionAreaEntity extends AbstractBaseEntity {

    private static final long serialVersionUID = -8066812682463437695L;
    /**
     * 碰撞id
     */
    private Long collisionId;
    /**
     * 区域名称
     */
    private String name;
    /**
     * 时间范围
     */
    private String timeRanges;
    /**
     * 区域坐标
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private GeometryVO geometry;
    /**
     * 命中逻辑
     */
    private Integer dataCountType;
}
