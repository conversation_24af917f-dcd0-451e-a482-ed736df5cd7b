package com.trs.police.spacetime.collision.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.trs.police.common.core.json.serializer.SimpleTimeSerializer;
import com.trs.police.common.core.vo.control.WarningSourceVO;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2023/9/13 14:19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CollisionTrackVO {

    private Long id;
    /**
     * 轨迹时间
     */
    @JsonSerialize(using = SimpleTimeSerializer.class,nullsUsing = SimpleTimeSerializer.class)
    private LocalDateTime time;
    /**
     * 地址
     */
    private String address;
    /**
     * 感知源信息
     */
    private WarningSourceVO warningSource;
    /**
     * 区域名称
     */
    private String areaName;
    /**
     * 经度
     */
    private Double longitude;
    /**
     * 纬度
     */
    private Double latitude;
    /**
     * 详情
     */
    private String content;
    /**
     * 标识符信息
     */
    private CollisionResultDetail collisionResult;
    /**
     * 标识符类型
     */
    private String objectType;
    /**
     * 轨迹表名
     */
    private String tableName;
    /**
     * 轨迹id
     */
    private String recordId;
    /**
     * 感知源类型
     */
    private String gzylx;
    /**
     * 是否有照片
     */
    private Boolean havePhoto;
}
