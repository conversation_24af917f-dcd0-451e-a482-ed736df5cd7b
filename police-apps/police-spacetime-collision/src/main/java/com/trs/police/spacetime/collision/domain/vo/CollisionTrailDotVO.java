package com.trs.police.spacetime.collision.domain.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName CollisionTrailDotVO
 * @Description 碰撞轨迹点VO
 * <AUTHOR>
 * @Date 2024/1/17 10:13
 **/
@Data
public class CollisionTrailDotVO implements Serializable {

    /**
     * 精度
     */
    private String longitude;

    /**
     * 维度
     */
    private String latitude;

    /**
     * 感知源名称
     */
    private String gzymc;

    /**
     * 感知源类型
     */
    private String gzylx;

    /**
     * 感知源编号
     */
    private String gzybh;

    /**
     * 活动时间
     */
    private String hdsj;

    /**
     * 活动地址
     */
    private String hddz;

    /**
     * 特征值类型
     */
    private String tzlx;

    /**
     * 特征值号码
     */
    private String tzzhm;

    /**
     * 轨迹类型
     */
    private String gjlx;

}
