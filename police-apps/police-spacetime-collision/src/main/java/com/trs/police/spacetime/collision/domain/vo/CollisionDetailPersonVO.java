package com.trs.police.spacetime.collision.domain.vo;

import com.trs.police.common.core.vo.oss.FileInfoVO;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2023/9/13 10:44
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CollisionDetailPersonVO {

    /**
     * id
     */
    private Long id;
    /**
     * 姓名
     */
    private String name;
    /**
     * 身份证号
     */
    private String idNumber;
    /**
     * 标签
     */
    private List<String> personLabel;
    /**
     * 照片
     */
    private List<FileInfoVO> imgs;
}
