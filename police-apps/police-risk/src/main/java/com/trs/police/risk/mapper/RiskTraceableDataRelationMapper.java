package com.trs.police.risk.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.common.core.vo.permission.DeptVO;
import com.trs.police.risk.constant.enums.TraceableDataTypeEnum;
import com.trs.police.risk.domain.entity.RiskTraceableDataRelation;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/3 17:39
 */
@Mapper
public interface RiskTraceableDataRelationMapper extends BaseMapper<RiskTraceableDataRelation> {


    /**
     * 根据风险溯源数据关系
     *
     * @param riskId     风险id
     * @param code       编号
     * @param sourceType 溯源数据类型
     * @return 溯源数据关系
     */
    @ResultMap("mybatis-plus_RiskTraceableDataRelation")
    @Select("select * from t_risk_traceable_data_relation where risk_id = #{riskId} and code = #{code} and source_type = #{sourceType}")
    RiskTraceableDataRelation getByRiskIdAndCodeAndType(@Param("riskId") Long riskId, @Param("code")String code, @Param("sourceType")Integer sourceType);

    /**
     * 根据风险溯源数据关系
     *
     * @param riskId     风险id
     * @param sourceType 溯源数据类型
     * @return 溯源数据关系
     */
    @ResultMap("mybatis-plus_RiskTraceableDataRelation")
    @Select("select * from t_risk_traceable_data_relation where risk_id = #{riskId} and source_type = #{sourceType}")
    List<RiskTraceableDataRelation> getByRiskIdAndType(@Param("riskId") Long riskId,
        @Param("sourceType") TraceableDataTypeEnum sourceType);

    /**
     * 根据当前风险溯源数据关系
     *
     * @param riskId 风险id
     * @return 溯源数据关系
     */
    @ResultMap("mybatis-plus_RiskTraceableDataRelation")
    @Select("select * from t_risk_traceable_data_relation where risk_id = #{riskId} and is_current = 1")
    RiskTraceableDataRelation getCurrentByRiskId(@Param("riskId") Long riskId);

    /**
     * 根据风险id和溯源数据类型获取溯源数据关系
     *
     * @param riskId     风险id
     * @param sourceType 溯源数据类型
     * @return 溯源数据关系
     */
    @Select("select code from t_risk_traceable_data_relation where risk_id = #{riskId} and source_type = #{sourceType}")
    List<String> getCodesByRiskIdAndType(@Param("riskId") Long riskId, @Param("sourceType") Integer sourceType);

    /**
     * 根据风险id和溯源数据类型获取溯源数据关系
     *
     * @param riskId     风险id
     * @param sourceType 溯源数据类型
     * @param code       溯源数据code
     */
    @Delete("delete from t_risk_traceable_data_relation where source_type = #{sourceType} and code = #{code} and risk_id = #{riskId}")
    void deleteByTypeAndCode(@Param("riskId") Long riskId, @Param("sourceType") Integer sourceType,
        @Param("code") String code);

    /**
     * 获取反馈单位
     *
     * @param deptCode 单位代码
     * @return 反馈单位
     */
    @Select("select id as deptId, name as deptName,short_name as shortName,`code` as deptCode,type as deptType,`level`,path,police_kind,pid,null as children from t_dept where `code` = #{deptCode} and deleted = 0")
    DeptVO getFeedBackDetpVo(@Param("deptCode") String deptCode);

    /**
     * 根据电话或者身份证获取接警单编号和12345code
     *
     * @param zjhm 身份证号码
     * @param phone 电话号码
     * @return 警情编号或者12345编号
     */
    @Select("(SELECT `JJDBH` as `code` FROM t_profile_sthy WHERE BJRZJHM = #{idNumber} OR BJDH = #{phone}) UNION ALL (SELECT `code` FROM t_profile_clue WHERE id_number = #{idNumber} OR related_phone = #{phone})")
    List<String> getCodeFromSthyAndClue(@Param("idNumber") String zjhm, @Param("phone") String phone);

    /**
     * 获取有接警单编号的全部关联
     *
     * @return 关联数据
     */
    @Select("select distinct * from t_risk_traceable_data_relation where source_type = 2 or source_type = 4")
    List<RiskTraceableDataRelation> selectAllByJjdbh();
}
