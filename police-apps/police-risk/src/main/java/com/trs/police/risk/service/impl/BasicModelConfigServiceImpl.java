package com.trs.police.risk.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.risk.domain.dto.ModelStatusDTO;
import com.trs.police.risk.domain.entity.BasicModelConfig;
import com.trs.police.risk.domain.vo.RuleConfigBasicInfoVO;
import com.trs.police.risk.mapper.BasicModelConfigMapper;
import com.trs.police.risk.service.BasicModelConfigService;
import com.trs.web.builder.base.RestfulResultsV2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 模型基础配置服务实现类
 */
@Slf4j
@Service
public class BasicModelConfigServiceImpl implements BasicModelConfigService {

    @Resource
    private BasicModelConfigMapper mapper;


    /**
     * 根据主键ID查询模型配置信息
     *
     * @param code 模型编码
     * @return 对应ID的配置实体，不存在时返回null
     */
    @Override
    public RestfulResultsV2<RuleConfigBasicInfoVO> getBasicInfo(String code) {
        try {
            if (code == null) {
                throw new IllegalArgumentException("code不能为空");
            }
            QueryWrapper<BasicModelConfig> query = new QueryWrapper<>();
            query.eq("code", code);
            BasicModelConfig entity = mapper.selectOne(query);
            if (entity == null) {
                throw new RuntimeException("根据 code " + code + " 不能找到配置信息");
            }
            RuleConfigBasicInfoVO vo = new RuleConfigBasicInfoVO();
            BeanUtil.copyAttributes(entity, vo);
            vo.setId(entity.getId());
            return RestfulResultsV2.ok(vo);
        } catch (Exception e) {
            log.error("不能获取到配置的基础信息, 错误信息为 {}", e.getMessage(), e);
            throw new RuntimeException("不能获取到配置的基础信息", e);
        }
    }

    /**
     * 开关模型状态
     *
     * @param modelStatusDTO 模型状态空DTO
     * @return 打开或关闭是否成功
     */
    @Override
    public RestfulResultsV2<String> switchModel(ModelStatusDTO modelStatusDTO) {
        // code 为模型编码， open 为 true 就修改为开启，为 false 就修改为关闭
        try {
            String code = modelStatusDTO.getCode();
            if (StringUtils.isBlank(code)) {
                return RestfulResultsV2.error("模型编码不能为空");
            }
            if (modelStatusDTO.getOpen() == null && modelStatusDTO.getDataScope() == null) {
                log.info("open 和 dataScope 同时为空, 不做修改");
                return RestfulResultsV2.ok("成功");
            }

            // 直接构建更新条件（避免先select后update）
            BasicModelConfig existing = mapper.selectOne(new QueryWrapper<BasicModelConfig>().eq("code", code));
            if (existing == null) {
                log.warn("模型配置不存在，code: {}", code);
                return RestfulResultsV2.error("找不到对应的模型配置");
            }
            // 假设enable字段为状态字段
            existing.setOpen(modelStatusDTO.getOpen());
            existing.setDataScope(modelStatusDTO.getDataScope());

            QueryWrapper<BasicModelConfig> updateWrapper = new QueryWrapper<>();
            updateWrapper.eq("code", code);
            mapper.update(existing, updateWrapper);
            return RestfulResultsV2.ok("成功");
        } catch (Exception e) {
            log.error("模型状态切换异常，code: {}，open: {}, 错误信息: {}", modelStatusDTO.getCode(), modelStatusDTO.getOpen(), e.getMessage(), e);
            return RestfulResultsV2.error("状态切换异常: " + e.getLocalizedMessage());
        }
    }

    /**
     * 新增或修改模型基础配置信息
     *
     * @param vo 传入的模型基础配置信息
     * @return 模型基础配置实体
     */
    @Override
    public RestfulResultsV2<String> saveOrUpdateBasicInfo(RuleConfigBasicInfoVO vo) {
        try {
            BasicModelConfig entity = new BasicModelConfig();
            BeanUtil.copyAttributes(vo, entity);
            mapper.insert(entity);
            return RestfulResultsV2.ok("成功");
        } catch (Exception e) {
            log.error("新增模型基础配置信息失败, 错误信息为 {}", e.getMessage(), e);
            throw new RuntimeException("新增模型基础配置信息失败, 错误信息为 " + e.getMessage(), e);
        }
    }
}
