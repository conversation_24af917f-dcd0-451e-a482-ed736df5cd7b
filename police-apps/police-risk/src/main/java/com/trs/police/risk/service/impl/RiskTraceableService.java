package com.trs.police.risk.service.impl;

import com.trs.police.risk.domain.entity.RiskTraceableDataRelation;
import com.trs.police.risk.mapper.RiskTraceableDataRelationMapper;
import com.trs.police.risk.service.RiskTraceableDataRelationConsumer;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 风险轨迹服务
 *
 * <AUTHOR>
 */
@Service
public class RiskTraceableService {

    @Autowired
    private RiskTraceableDataRelationMapper riskTraceableDataRelationMapper;

    @Autowired
    private ObjectProvider<RiskTraceableDataRelationConsumer> myOptionalBeanProvider;

    /**
     * 插入数据
     *
     * @param relation 数据
     */
    public void insert(RiskTraceableDataRelation relation) {
        riskTraceableDataRelationMapper.insert(relation);
        // 关联到其它表
        myOptionalBeanProvider.ifAvailable(consumer -> consumer.accept(relation));
    }
}
