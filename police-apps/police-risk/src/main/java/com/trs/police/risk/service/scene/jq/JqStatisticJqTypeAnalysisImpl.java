package com.trs.police.risk.service.scene.jq;

import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.params.DeptRequestParams;
import com.trs.police.common.core.utils.StringUtil;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.risk.domain.dto.JqStatisticAnalysisDTO;
import com.trs.police.risk.domain.dto.SubjectSceneContext;
import com.trs.police.risk.domain.vo.PoliceSubjectStatisticsVO;
import com.trs.police.risk.mapper.RiskMapper;
import com.trs.web.builder.util.BeanFactoryHolder;
import io.vavr.Tuple;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.trs.police.risk.constant.JqStatisticConstant.*;

/**
 * 警情分析-警情类型统计
 *
 * <AUTHOR>
 */
@Component
public class JqStatisticJqTypeAnalysisImpl extends AbstractJqSubjectSceneAnalysisImpl<JqStatisticAnalysisDTO> {

    @Resource
    private RiskMapper riskMapper;

    @Resource
    private DictService dictService;

    @Resource
    private PermissionService permissionService;

    /**
     * 执行检索
     *
     * @param context 上下文信息
     * @return SubjectAreaStatisticsVO
     */
    @Override
    protected List<PoliceSubjectStatisticsVO> doSearch(SubjectSceneContext<JqStatisticAnalysisDTO> context) {
        JqStatisticAnalysisDTO dto = context.getDto();
        String startTime = context.getStartTime();
        String endTime = context.getEndTime();

        // 1 查询各风险下上升派出所，根据警情类型+警情表管辖单位统计
        List<PoliceSubjectStatisticsVO> allList = new ArrayList<>();
        List<PoliceSubjectStatisticsVO> riskTypeAndDeptStatistics = riskMapper.getJqTypeAndDeptStatistics(dto, startTime, endTime);
        riskTypeAndDeptStatistics = riskTypeAndDeptStatistics.stream().filter(vo -> !StringUtil.isEmpty(vo.getKey())).collect(Collectors.toList());
        allList.addAll(riskTypeAndDeptStatistics);
        // 2 计算总数居
        Long allCount = riskTypeAndDeptStatistics.stream().map(PoliceSubjectStatisticsVO::getCount).reduce(0L, Long::sum);
        allList.add(new PoliceSubjectStatisticsVO("all", null, allCount));
        allList.add(new PoliceSubjectStatisticsVO("jfAll", null, allCount));
        // 3 统计派出所维度下总数据
        List<PoliceSubjectStatisticsVO> deptStatisticsVos = riskTypeAndDeptStatistics.stream()
                .collect(Collectors.groupingBy(vo -> vo.getKey().split("-")[1],
                        Collectors.collectingAndThen(Collectors.toList(),
                                list -> list.stream().map(PoliceSubjectStatisticsVO::getCount).reduce(0L, Long::sum))))
                .entrySet().stream()
                .flatMap(entry -> Stream.of(
                        new PoliceSubjectStatisticsVO("all-" + entry.getKey(), null, entry.getValue()),
                        new PoliceSubjectStatisticsVO("jfAll-" + entry.getKey(), null, entry.getValue())))
                .collect(Collectors.toList());
        allList.addAll(deptStatisticsVos);
        // 4 警情类型维度下总数居
        List<PoliceSubjectStatisticsVO> jqTypeStatisticsVos = riskTypeAndDeptStatistics.stream().collect(Collectors.groupingBy(vo -> vo.getKey().split("-")[0],
                        Collectors.collectingAndThen(Collectors.toList(),
                                list -> list.stream().map(PoliceSubjectStatisticsVO::getCount).reduce(0L, Long::sum))))
                .entrySet().stream()
                .map(entry -> new PoliceSubjectStatisticsVO(entry.getKey(), null, entry.getValue()))
                .collect(Collectors.toList());
        allList.addAll(jqTypeStatisticsVos);
        // 5 纠纷类警情上升派出所
//        List<PoliceSubjectStatisticsVO> jqTypeJfStatisticsVos = riskTypeAndDeptStatistics.stream()
//                .filter(vo -> !vo.getKey().split("-")[0].equals(dto.getJqTypeNotJf()))
//                .collect(Collectors.groupingBy(vo -> vo.getKey().split("-")[1],
//                        Collectors.collectingAndThen(Collectors.toList(),
//                                list -> list.stream().map(PoliceSubjectStatisticsVO::getCount).reduce(0L, Long::sum))))
//                .entrySet().stream()
//                .map(entry -> new PoliceSubjectStatisticsVO("jfAll-" + entry.getKey(), null, entry.getValue()))
//                .collect(Collectors.toList());
//        allList.addAll(jqTypeJfStatisticsVos);
        // 6 纠纷类警情总数据
//        Long jqTypeJfAllCount = jqTypeJfStatisticsVos.stream()
//                .map(PoliceSubjectStatisticsVO::getCount)
//                .reduce(0L, Long::sum);
//        PoliceSubjectStatisticsVO jqTypeJfAllStatisticsVO = new PoliceSubjectStatisticsVO("jfAll", null, jqTypeJfAllCount);
//        allList.add(jqTypeJfAllStatisticsVO);
        fillStatisticVo(context, allList);
        return allList;
    }

    private void fillStatisticVo(SubjectSceneContext<JqStatisticAnalysisDTO> context, List<PoliceSubjectStatisticsVO> allList) {
        if (CollectionUtils.isEmpty(context.getStatisticKey())) {
            return;
        }
        List<String> existKey = allList.stream().map(PoliceSubjectStatisticsVO::getKey).collect(Collectors.toList());
        context.getStatisticKey().forEach(key -> {
            if (!existKey.contains(key)) {
                allList.add(new PoliceSubjectStatisticsVO(key, null, 0L));
            }
        });
    }

    /**
     * 获取统计维度
     *
     * @return 统计维度vo
     */
    @Override
    protected List<PoliceSubjectStatisticsVO> getStatisticRank(SubjectSceneContext<JqStatisticAnalysisDTO> context) {
        List<PoliceSubjectStatisticsVO> allStatisticsVos = new ArrayList<>();
        // 纠纷类警情类型维度
        String jqTypeJfString = BeanFactoryHolder.getEnv().getProperty(JQ_STATISTIC_JQ_TYPE_JF_RANK);
        AtomicLong sort = new AtomicLong(1L);
        // 总统计维度
        allStatisticsVos.add(new PoliceSubjectStatisticsVO("all", "易引发“民转刑”警情分析", "易引发“民转刑”警情分析", null, sort.getAndIncrement()));
        // 纠纷类总警情维度
        allStatisticsVos.add(new PoliceSubjectStatisticsVO("jfAll", "“纠纷类”警情总计", "“纠纷类”警情总计", null, sort.getAndIncrement()));
        List<String> jqTypeList = new ArrayList<>();
        if (!StringUtil.isEmpty(jqTypeJfString)) {
            String[] keyValue = jqTypeJfString.split("[,|;]");
            for (String keyValueStr : keyValue) {
                String[] keyValueArr = keyValueStr.split(":");
                if (keyValueArr.length == 2) {
                    String key = keyValueArr[0];
                    allStatisticsVos.add(new PoliceSubjectStatisticsVO(key, keyValueArr[1], keyValueArr[1], null, sort.getAndIncrement()));
                    jqTypeList.add(key);
                }
            }
        }
        // 行政案件-家庭暴力维度
        String jqTypeXzString = BeanFactoryHolder.getEnv().getProperty(JQ_STATISTIC_JQ_TYPE_XZ_RANK);
        if (!StringUtil.isEmpty(jqTypeXzString)) {
            String[] keyValueArr = jqTypeXzString.split(":");
            String key = keyValueArr[0];
            allStatisticsVos.add(new PoliceSubjectStatisticsVO(key, keyValueArr[1], keyValueArr[1], null, sort.getAndIncrement()));
            jqTypeList.add(key);
            context.getDto().setJqTypeNotJf(key);
        }
        List<String> rankList = new ArrayList<>(jqTypeList);
        rankList.add("all");
        rankList.add("jfAll");
        context.getDto().setJqTypeList(jqTypeList);
        // 派出所维度
        DeptRequestParams params = new DeptRequestParams();
        DictDto dict = dictService.getDictByTypeAndName("dept_type", "派出所");
        params.setType(dict.getCode().intValue());
        List<DeptDto> deptDtos = permissionService.getDeptByParams(params);
        Map<String, Long> deptSortMap = getDeptSort();
        List<PoliceSubjectStatisticsVO> deptStatisticsVos = rankList.stream()
                .flatMap(jqTypeCode -> deptDtos.stream()
                        .map(deptDto -> new PoliceSubjectStatisticsVO(
                                jqTypeCode + "-" + deptDto.getCode(),
                                deptDto.getShortName(),
                                deptDto.getShortName(),
                                null,
                                deptSortMap.getOrDefault(deptDto.getCode(), 99999L))))
                .collect(Collectors.toList());
        allStatisticsVos.addAll(deptStatisticsVos);
        context.setStatisticKey(allStatisticsVos.stream().map(PoliceSubjectStatisticsVO::getKey).collect(Collectors.toList()));
        return allStatisticsVos;
    }

    private Map<String, Long> getDeptSort() {
        String deptSortString = BeanFactoryHolder.getEnv().getProperty(JQ_STATISTIC_JQ_TYPE_DEPT_SORT_RANK);
        if (StringUtil.isEmpty(deptSortString)) {
            return new HashMap<>();
        }
        return Stream.of(deptSortString.split("[,|;]"))
                .map(v -> {
                    String[] strings = v.split(":");
                    return Tuple.of(strings[0], Long.valueOf(strings[1]));
                })
                .collect(Collectors.toMap(t -> t._1, t -> t._2, (v1, v2) -> v1));
    }

    /**
     * 对统计结果做后置处理
     *
     * @param result 统计结果
     * @return 处理后的统计结果
     */
    @Override
    protected List<PoliceSubjectStatisticsVO> doAfter(List<PoliceSubjectStatisticsVO> result) {
        List<PoliceSubjectStatisticsVO> upRatioList = result.stream()
                .filter(vo -> vo.getKey().contains("-"))
                .collect(Collectors.toList());
        Map<String, List<PoliceSubjectStatisticsVO>> riskTypeUpRatioMap = getJqTypeStatisticMap(upRatioList);
        Map<String, List<String>> upDeptMap = getRatioUpDeptNameMap(upRatioList);
        return result.stream()
                .filter(vo -> !vo.getKey().contains("-"))
                .peek(vo -> {
                    List<PoliceSubjectStatisticsVO> ratioList = riskTypeUpRatioMap.getOrDefault(vo.getKey(), new ArrayList<>());
                    List<PoliceSubjectStatisticsVO> statisticsVos = ratioList.stream()
                            .sorted(Comparator.comparing(PoliceSubjectStatisticsVO::getSortValue))
                            .collect(Collectors.toList());
                    vo.setChildren(statisticsVos);
                    vo.setRatioUpDeptList(upDeptMap.getOrDefault(vo.getKey(), Collections.emptyList()));
                })
                .sorted(Comparator.comparing(PoliceSubjectStatisticsVO::getSortValue))
                .collect(Collectors.toList());
    }

    private Map<String, List<String>> getRatioUpDeptNameMap(List<PoliceSubjectStatisticsVO> upRatioList) {
        return upRatioList.stream().filter(vo -> vo.getRatio() > 0)
                .collect(Collectors.groupingBy(vo -> vo.getKey().split("-")[0],
                        Collectors.collectingAndThen(Collectors.toList(),
                                list -> list.stream()
                                        .sorted(Comparator.comparing(PoliceSubjectStatisticsVO::getSortValue))
                                        .map(vo -> vo.getName().substring(0, vo.getName().length() - 1))
                                        .collect(Collectors.toList()))));
    }

    private Map<String, List<PoliceSubjectStatisticsVO>> getJqTypeStatisticMap(List<PoliceSubjectStatisticsVO> upRatioList) {
        return upRatioList.stream()
                .collect(Collectors.groupingBy(vo -> vo.getKey().split("-")[0],
                        Collectors.collectingAndThen(Collectors.toList(),
                                list -> list.stream()
                                        .sorted(Comparator.comparing(PoliceSubjectStatisticsVO::getSortValue))
                                        .collect(Collectors.toList()))));
    }

    @Override
    public String key() {
        return JQ_STATISTIC_JQ_TYPE_KEY;
    }

    @Override
    public String desc() {
        return "民转刑-警情分析-警情类型统计";
    }
}
