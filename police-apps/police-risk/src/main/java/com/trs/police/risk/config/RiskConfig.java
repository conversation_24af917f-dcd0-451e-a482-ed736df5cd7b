package com.trs.police.risk.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description 风险的配置类
 * @date 2024/3/21 11:29
 */
@Component
public class RiskConfig {

    /**
     * 是否开启从警综同步相关人员
     */
    @Value("${com.trs.risk.openSyncRelatePersonFromJz:false}")
    public String openSyncRelatePersonFromJz;

    /**
     * 民转刑风险等级配置
     */
    @Value("${com.trs.risk.mzxRiskLevel:1:民转刑高风险;2:民转刑中风险;3:民转刑低风险}")
    public String mzxRiskLevel;

}
