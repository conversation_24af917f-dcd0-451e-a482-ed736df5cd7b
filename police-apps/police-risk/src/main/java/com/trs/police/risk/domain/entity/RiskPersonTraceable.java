package com.trs.police.risk.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 风险预警人员
 *
 * <AUTHOR>
 */
@TableName(value = "t_risk_person_traceable", autoResultMap = true)
@Data
public class RiskPersonTraceable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 人员id
     */
    @TableField("person_id")
    private Long personId;


    /**
     * 风险id
     */
    @TableField("risk_id")
    private Long riskId;

}
