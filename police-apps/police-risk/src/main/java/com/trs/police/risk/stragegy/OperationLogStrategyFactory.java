package com.trs.police.risk.stragegy;

import com.trs.police.common.core.constant.log.Operation;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


/**
 * 创建日志的策略工厂
 *
 * <AUTHOR>
 * @date 2023/10/25
 */
@Component
public class OperationLogStrategyFactory {

    private static Map<Operation, OperateLogStrategy> services = new ConcurrentHashMap<>();

    /**
     * 获取相应的策略
     *
     * @param type 操作类型
     * @return 对应的策略
     */
    public OperateLogStrategy getStrategyByType(Operation type) {
        return services.get(type);
    }

    /**
     * 将策略注册到该工厂中
     *
     * @param operation          类型
     * @param operateLogStrategy 策略
     */
    public static void register(Operation operation, OperateLogStrategy operateLogStrategy) {
        Assert.notNull(operation, "类型不能为空");
        services.put(operation, operateLogStrategy);
    }
}
