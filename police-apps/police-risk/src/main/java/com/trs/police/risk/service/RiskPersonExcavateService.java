package com.trs.police.risk.service;

import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.risk.domain.dto.JudgementDto;
import com.trs.police.risk.domain.dto.PushDto;
import com.trs.police.risk.domain.dto.RiskPersonExcavateDTO;
import com.trs.police.risk.domain.entity.RiskPerson;
import com.trs.police.risk.domain.vo.RiskPersonDetailVO;
import com.trs.police.risk.domain.vo.RiskPersonListVO;
import com.trs.police.risk.domain.vo.RiskScoreTendencyVO;
import com.trs.police.risk.domain.vo.WorkRecordVO;
import com.trs.web.builder.base.RestfulResultsV2;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/9/23 14:09
 */
public interface RiskPersonExcavateService {

    /**
     * 风险人员列表
     *
     * @param dto dto
     * @return RiskPersonListVO
     */
    RestfulResultsV2<RiskPersonListVO> riskPersonList(RiskPersonExcavateDTO dto);

    /**
     * 风险人员详情
     *
     * @param id id
     * @return RiskPersonDetailVO
     */
    RiskPersonDetailVO riskPersonDetail(Long id);

    /**
     * 关注/一键关注
     *
     * @param ids ids
     */
    void focus(String ids);


    /**
     * 取消关注
     *
     * @param ids ids
     */
    void unfollow(String ids);

    /**
     * 风险分数趋势
     *
     * @param id id
     * @return RiskScoreTendencyVO
     */
    RestfulResultsV2<RiskScoreTendencyVO> riskScoreTendency(Long id);

    /**
     * 保存快照
     *
     * @param person person
     */
     void saveSnapshot(RiskPerson person);


    /**
     * 人员信息已读
     *
     * @param id id
     * @param idCard 身份证
     */
    void doReadAction(Long id, String idCard);

    /**
     * 人员信息已读
     *
     * @param oldPerson 旧数据
     * @param newPerson 新数据
     *
     */
    void doUnReadAction(RiskPerson oldPerson, RiskPerson newPerson);

    /**
     * 推送
     *
     * @param dto 参数
     */
    void push(PushDto dto);

    /**
     * 退回
     *
     * @param dto 参数
     */
    void back(PushDto dto);

    /**
     * 未读数
     *
     * @param dto 查询参数
     * @return 未读数
     */
    RestfulResultsV2<Integer> unReadCount(RiskPersonExcavateDTO dto);



    /**
     * 研判
     *
     * @param dto 参数
     * @return 结果信息
     */
    RestfulResultsV2 judgement(JudgementDto dto);

    /**
     * 工作记录查询
     *
     * @param riskPersonId 风险人员
     * @return 工作记录
     */
    List<WorkRecordVO> workRecord(Long riskPersonId);

    /**
     * 保存工作记录
     *
     * @param riskPerson 风险人员
     * @param currentUser 当前使用者
     * @param detail 操作细节
     * @param code 操作代码
     *
     */
    void saveWorkRecord(RiskPerson riskPerson, CurrentUser currentUser, String detail, int code);

    /**
     * 删除标签记录
     *
     * @param dto 参数
     * @return 信息
     */
    RestfulResultsV2 deleteLabel(JudgementDto dto);
}
