package com.trs.police.risk.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import com.trs.police.common.core.handler.typehandler.JsonToStringListHandler;

import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 风险白名单
 *
 * <AUTHOR>
 * @date 2023/9/26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "t_risk_whitelist", autoResultMap = true)
public class Whitelist extends AbstractBaseEntity {

    private static final long serialVersionUID = -5128050949912409559L;
    /**
     * 电话号码
     */
    @TableField(value = "tel", typeHandler = JsonToStringListHandler.class)
    private List<String> tel;

}
