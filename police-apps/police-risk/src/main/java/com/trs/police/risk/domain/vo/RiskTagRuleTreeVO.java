package com.trs.police.risk.domain.vo;

import lombok.Data;

import java.util.List;

/**
 * 标签规则树结构
 *
 * <AUTHOR>
 * @since 2025/2/12 16:46
 */
@Data
public class RiskTagRuleTreeVO {

    /**
     * 标签名称
     */
    private String name;

    /**
     * 是否用作关联 默认是
     */
    private Boolean needRelation;

    /**
     * 标签条件中文描述
     */
    private String conditionDesc;

    /**
     * 标签优先级
     */
    private Integer priority;

    /**
     * 标签得分
     */
    private Float score;

    /**
     * 标签id
     */
    private Long id;

    /**
     * 标签父级id
     */
    private Long pid;

    /**
     * 下级标签
     */
    private List<RiskTagRuleTreeVO> children;

}
