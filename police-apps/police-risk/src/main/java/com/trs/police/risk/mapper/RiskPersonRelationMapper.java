package com.trs.police.risk.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.risk.domain.entity.RiskPersonRelation;
import com.trs.police.risk.domain.vo.RelatedPersonVO;

import java.util.List;
import java.util.Optional;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @date 2023/3/21 14:09
 */
@Mapper
public interface RiskPersonRelationMapper extends BaseMapper<RiskPersonRelation> {

    /**
     * 相关人员编辑回显
     *
     * @param id 关系id
     * @return 编辑回显信息
     */
    RelatedPersonVO getRelatedPersonById(@Param("id") Long id);


    /**
     * 相关人员
     *
     * @param riskId 风险id
     * @param page   分页参数
     * @return 相关人员信息
     */
    Page<RelatedPersonVO> getRelatedPersonByRiskId(@Param("riskId") Long riskId, Page<RelatedPersonVO> page);

    /**
     * 根据人员身份证号获取风险id
     *
     * @param idNumber 身份证号
     * @return 风险id
     */
    @Select("SELECT risk_id FROM t_risk_person_relation WHERE id_number = #{idNumber}")
    List<Long> getRiskIdByIdNumber(@Param("idNumber") String idNumber);

    /**
     * 根据人员身份证号和风险id获取关系id
     *
     * @param idNumber 身份证号
     * @param riskId   风险id
     * @return 关系id
     */
    @Select("SELECT id FROM t_risk_person_relation WHERE id_number = #{idNumber} AND risk_id = #{riskId}")
    Optional<RiskPersonRelation> getByIdNumberAndRiskId(@Param("idNumber") String idNumber,
                                                        @Param("riskId") Long riskId);

    /**
     * 获取相关人员数量
     *
     * @param riskId 风险id
     * @return 数量
     */
    @Select("select count(*) from t_risk_person_relation where risk_id = #{riskId}")
    Long getRelatedPersonCountByRiskId(@Param("riskId") Long riskId);

    /**
     * 不分页获取相关人员
     *
     * @param riskId 风险id
     * @return 相关人员
     */
    List<RelatedPersonVO> getRelatedPersonByRiskIdAll(@Param("riskId") Long riskId);
}
