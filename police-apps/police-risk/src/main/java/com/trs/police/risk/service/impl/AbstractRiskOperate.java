package com.trs.police.risk.service.impl;

import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.vo.permission.SimpleDeptVO;
import com.trs.police.risk.domain.entity.RiskPerson;
import com.trs.police.risk.domain.entity.RiskPersonWorkRecord;
import com.trs.police.risk.service.RiskPersonOperateService;

import java.time.LocalDateTime;

/**
 * 风险人员抽象类
 *
 * @param <DTO> 泛型dto
 * @param <T> 泛型返回数据
 */
public abstract class AbstractRiskOperate<DTO,T> implements RiskPersonOperateService<DTO,T> {
    @Override
    public abstract T doOperate(DTO dto);

    /**
     * 是否可以继续操作
     *
     * @param dto 参数
     * @return 结果
     */
    public abstract RiskPerson canOperate(DTO dto);

    /**
     * 操作
     *
     * @param dto 参数
     * @param t 参数
     * @return 结果
     */
    public abstract RiskPerson operate(DTO dto,T t);

    /**
     * 保存工作记录
     *
     * @param riskPerson  风险人员
     * @param currentUser 当前使用者
     * @param detail      操作细节
     * @param code        操作代码
     * @return 风险人员工作记录
     */
    public RiskPersonWorkRecord buildWorkRecord(RiskPerson riskPerson, CurrentUser currentUser, String detail, int code) {
        RiskPersonWorkRecord riskPersonWorkRecord = new RiskPersonWorkRecord();
        riskPersonWorkRecord.setRiskPersonId(riskPerson.getId());
        riskPersonWorkRecord.setOperateType(code);
        riskPersonWorkRecord.setOperateDetail(detail);
        riskPersonWorkRecord.setCreateTime(LocalDateTime.now());
        if(currentUser!=null){
            riskPersonWorkRecord.setCreateUserId(currentUser.getId());
            riskPersonWorkRecord.setCreateDeptId(currentUser.getDeptId());
            riskPersonWorkRecord.setUpdateUserId(currentUser.getId());
            riskPersonWorkRecord.setUpdateDeptId(currentUser.getDeptId());
        }
        riskPersonWorkRecord.setUpdateTime(LocalDateTime.now());
        return riskPersonWorkRecord;
    }


    /**
     * 更新责任单位
     *
     * @param pushDept   推送/退回 部门信息
     * @param riskPerson 风险人员
     * @return 风险人员
     */
    public RiskPerson updateResponsibleDept(DeptDto pushDept, RiskPerson riskPerson) {
        SimpleDeptVO simpleDeptVO = new SimpleDeptVO();
        simpleDeptVO.setDeptId(pushDept.getId());
        simpleDeptVO.setDeptCode(pushDept.getCode());
        simpleDeptVO.setDeptName(pushDept.getName());
        simpleDeptVO.setShortName(pushDept.getShortName());
        simpleDeptVO.setDistrictCode(pushDept.getDistrictCode());
        simpleDeptVO.setDistrictName(pushDept.getDistrictName());
        simpleDeptVO.setDistrictMainName(pushDept.getDistrictMainName());
        simpleDeptVO.setDistrictShortName(pushDept.getDistrictShortName());
        riskPerson.setResponsibleDept(simpleDeptVO);
        return riskPerson;
    }
}
