package com.trs.police.risk.service.impl;

import com.trs.police.common.core.constant.enums.LogGroup;
import com.trs.police.common.core.constant.log.Operation;
import com.trs.police.common.core.dto.log.LogContext;
import com.trs.police.risk.aspect.OperationLog;
import com.trs.police.risk.converter.LogConverter;
import com.trs.police.risk.domain.entity.OperationLogEntity;
import com.trs.police.risk.message.RiskMessageSource;
import com.trs.police.risk.service.OperationLogService;
import com.trs.police.risk.service.RiskLogService;
import com.trs.police.risk.service.WarningConfigLogService;
import javax.annotation.Resource;

import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2023/6/6 15:03
 */
@Service
@Slf4j
@EnableBinding(RiskMessageSource.class)
public class OperationLogServiceImpl implements OperationLogService {

    public static final String NEW_OPERATION = "生成操作记录 :";

    @Resource
    private RiskLogService riskLogService;

    @Resource
    private WarningConfigLogService warningConfigLogService;

    @Resource
    private RiskMessageSource riskMessageSource;

    private final LogConverter converter = LogConverter.LOG_CONVERTER;

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void createOperatorLog(Long targetObjectId, String ipAddress, OperationLog operationLog, Object newObj,
        Object oldObj) {
        OperationLogEntity operationLogEntity = new OperationLogEntity();
        operationLogEntity.setOperation(operationLog.operation());
        operationLogEntity.setRelatedId(targetObjectId);
        operationLogEntity.setModule(operationLog.operateModule());
        operationLogEntity.setIpAddress(ipAddress);
        setDetail(targetObjectId, operationLog, newObj, operationLogEntity);

        // 推送省厅日志消息
        sendProvinceMessage(operationLogEntity);
    }

    private void sendProvinceMessage(OperationLogEntity operationLogEntity) {
        Boolean pushEnable = BeanFactoryHolder.getEnv().getProperty("com.trs.police.provinceLog.pushEnable", Boolean.class, false);
        if (pushEnable) {
            LogContext logContext = buildProvinceMessage(operationLogEntity);
            riskMessageSource.provinceLogOutPut().send(MessageBuilder.withPayload(logContext).build());
        }
    }

    private LogContext buildProvinceMessage(OperationLogEntity operationLogEntity) {
        LogContext logContext = converter.logDo2Context(operationLogEntity);
        LogGroup logGroup = operationLogEntity.getOperation().equals(Operation.QUERY) ? LogGroup.INFORMATION_SEARCH : LogGroup.GROUP_DEFAULT;
        logContext.setLogGroup(logGroup.getValue());
        boolean hasDetail = com.trs.common.utils.StringUtils.isNotEmpty(operationLogEntity.getDetail()) && !"--".equals(operationLogEntity.getDetail());
        logContext.setOperateResult(hasDetail ? "1" : "0");
        logContext.setInquireContent(hasDetail ? operationLogEntity.getDetail() : null);
        logContext.setCode("200");
        logContext.setNumId(operationLogEntity.getId());
        logContext.setCreateTime(operationLogEntity.getCreateTime());
        logContext.setTerminalIp(operationLogEntity.getIpAddress());
        logContext.setOperateName(operationLogEntity.getOperation().getName() + operationLogEntity.getModule().getCnName());
        logContext.setUserId(operationLogEntity.getCreateUserId());
        logContext.setDeptId(operationLogEntity.getCreateDeptId());
        return logContext;
    }

    /**
     * 详情设置
     *
     * @param targetObjectId     id
     * @param operationLog       操作记录
     * @param newObj             新值
     * @param operationLogEntity 操作记录
     */
    private void setDetail(Long targetObjectId, OperationLog operationLog, Object newObj,
        OperationLogEntity operationLogEntity) {
        switch (operationLog.operateModule()) {
            case RISK_WARNING_CONFIG:
                warningConfigLogService.createOperateLog(targetObjectId, operationLog, newObj, operationLogEntity);
                break;
            case RISK:
                riskLogService.createOperateLog(targetObjectId, operationLog, newObj, operationLogEntity);
                break;
            default:
                break;
        }
    }



}
