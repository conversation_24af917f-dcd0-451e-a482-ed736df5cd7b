package com.trs.police.risk.domain.vo.push;

import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/9/13 09:30
 */
@AllArgsConstructor
@Data
public class ScoreDisplayVO {

    /**
     * 名称
     */
    private String name;
    /**
     * 分值
     */
    private String score;
    /**
     * 内容
     */
    private List<Item> items;
    /**
     * 子节点
     */
    private List<ScoreDisplayVO> children;

    public ScoreDisplayVO() {
        this.children = new ArrayList<>();
        this.items = new ArrayList<>();
    }


    public ScoreDisplayVO(String name, String score) {
        this.name = name;
        this.score = score;
        this.children = new ArrayList<>();
        this.items = new ArrayList<>();
    }


    public ScoreDisplayVO(String name, String score, List<ScoreDisplayVO> children) {
        this.name = name;
        this.score = score;
        this.children = children;
    }

    /**
     * 内容
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Item {

        /**
         * 名称
         */
        private String name;
        /**
         * 类型：jq/clue
         */
        private String type;
        /**
         * 编码
         */
        private String code;
        /**
         * 分值
         */
        private String score;

        public Item(String name, String score) {
            this.name = name;
            this.score = score;
        }
    }

}
