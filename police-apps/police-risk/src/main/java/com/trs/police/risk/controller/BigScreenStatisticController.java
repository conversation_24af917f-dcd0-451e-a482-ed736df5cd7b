package com.trs.police.risk.controller;

import com.trs.police.risk.domain.dto.RiskStatisticDTO;
import com.trs.police.risk.domain.dto.StatisticContext;
import com.trs.police.risk.service.statistic.*;
import com.trs.police.statistic.domain.DTO.NoParamsDTO;
import com.trs.police.statistic.domain.VO.CountStatisticVO;
import com.trs.police.statistic.domain.bean.NoOtherValue;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 矛盾纠纷预防与化解大屏统计
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("statistic")
public class BigScreenStatisticController {

    @Autowired
    private RiskLevelDistributionStatisticServiceImpl riskLevelDistributionStatisticService;

    @Autowired
    private RiskTypeDistributionStatisticServiceImpl riskTypeDistributionStatisticService;

    @Autowired
    private RiskStatusDistributionStatisticServiceImpl riskStatusDistributionStatisticService;

    @Autowired
    private RiskHandledDistributionStatisticServiceImpl riskHandledDistributionStatisticService;

    @Autowired
    private RiskTopStatisticServiceImpl riskTopStatisticService;

    @Autowired
    private RiskAreaStatisticServiceImpl riskAreaStatisticService;

    @Autowired
    private RiskPcsStatisticServiceImpl riskPoliceStationStatisticService;

    /**
     * 矛盾纠纷与化解-风险等级分布
     *
     * @param dto dto
     * @return 统计结果
     */
    @PostMapping("riskLevelDistribution")
    public CountStatisticVO<NoOtherValue> riskLevelDistribution(RiskStatisticDTO dto) {
        NoParamsDTO noParamsDTO = new NoParamsDTO();
        BeanUtils.copyProperties(dto, noParamsDTO);
       return riskLevelDistributionStatisticService.doStatistic(new StatisticContext<>(noParamsDTO,dto));
    }

    /**
     * 矛盾纠纷与化解-风险类别分布
     *
     * @param dto dto
     * @return 统计结果
     */
    @PostMapping("riskTypeDistribution")
    public CountStatisticVO<NoOtherValue> riskTypeDistribution(RiskStatisticDTO dto) {
        NoParamsDTO noParamsDTO = new NoParamsDTO();
        BeanUtils.copyProperties(dto, noParamsDTO);
        return riskTypeDistributionStatisticService.doStatistic(new StatisticContext<>(noParamsDTO,dto));
    }

    /**
     * 矛盾纠纷与化解-风险处理分布
     *
     * @param dto dto
     * @return 统计结果
     */
    @PostMapping("riskStatusDistribution")
    public CountStatisticVO<NoOtherValue> riskStatusDistribution(RiskStatisticDTO dto) {
        NoParamsDTO noParamsDTO = new NoParamsDTO();
        BeanUtils.copyProperties(dto, noParamsDTO);
        return riskStatusDistributionStatisticService.doStatistic(new StatisticContext<>(noParamsDTO,dto));
    }

    /**
     * 矛盾纠纷与化解-风险办结分布
     *
     * @param dto dto
     * @return 统计结果
     */
    @PostMapping("riskHandledDistribution")
    public CountStatisticVO<NoOtherValue> riskHandledDistribution(RiskStatisticDTO dto) {
        NoParamsDTO noParamsDTO = new NoParamsDTO();
        BeanUtils.copyProperties(dto, noParamsDTO);
        return riskHandledDistributionStatisticService.doStatistic(new StatisticContext<>(noParamsDTO,dto));
    }

    /**
     * 风险顶部统计
     *
     * @param dto dto
     * @return {@link CountStatisticVO}<{@link NoOtherValue}>
     */
    @GetMapping("riskTop")
    public CountStatisticVO<NoOtherValue> riskTop(RiskStatisticDTO dto) {
        return riskTopStatisticService.doStatistic(new StatisticContext<>(new NoParamsDTO(), dto));
    }

    /**
     * 风险区域统计
     *
     * @param dto dto
     * @return {@link CountStatisticVO}<{@link NoOtherValue}>
     */
    @GetMapping("riskArea")
    public CountStatisticVO<NoOtherValue> riskArea(RiskStatisticDTO dto) {
        return riskAreaStatisticService.doStatistic(new StatisticContext<>(new NoParamsDTO(), dto));
    }

    /**
     * 风险派出所统计
     *
     * @param dto dto
     * @return {@link CountStatisticVO}<{@link NoOtherValue}>
     */
    @GetMapping("riskPoliceStation")
    public CountStatisticVO<NoOtherValue> riskPoliceStation(RiskStatisticDTO dto) {
        return riskPoliceStationStatisticService.doStatistic(new StatisticContext<>(new NoParamsDTO(), dto));
    }

}