package com.trs.police.risk.domain.entity;

import com.trs.police.risk.constant.enums.RiskPersonOperateTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/9/25 10:18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PushDetail {
    /**
     * 推送/退回原因
     */
    private String reason;
    /**
     * 原始部门Id
     */
    private Long sourceDeptId;
    /**
     * 目标部门ID
     */
    private Long targetDeptId;
    /**
     * 原始部门名称
     */
    private String sourceDeptName;
    /**
     * 目标部门名称
     */
    private String targetDeptName;

    /**
     * 内容
     */
    private String content;

    /**
     * 设置内容
     *
     * @param riskEnum 参数
     */
    public void setContent(RiskPersonOperateTypeEnum riskEnum){
        this.content = riskEnum.getName()+ "至" + this.targetDeptName + "\n" + this.reason;
    };
}
