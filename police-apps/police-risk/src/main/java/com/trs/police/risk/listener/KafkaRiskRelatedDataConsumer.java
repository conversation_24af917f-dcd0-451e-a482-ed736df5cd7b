package com.trs.police.risk.listener;

import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.risk.domain.vo.push.MessagePushVO;
import com.trs.police.risk.domain.vo.push.RiskProcessAnalysisVO;
import com.trs.police.risk.properties.KafkaRiskConsumerProperties;
import com.trs.police.risk.service.impl.MessageProcessServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.DependsOn;
import org.springframework.kafka.annotation.KafkaHandler;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * kafka 高风险相关业务数据消费
 *
 * <AUTHOR>
 * @date 2023/06/30
 */
@Component
@Slf4j
@ConditionalOnBean(value = KafkaRiskConsumerProperties.class)
@ConditionalOnProperty(value = "kafka.risk.relatedDataStartup", havingValue = "true")
@DependsOn("kafkaKerberosAutoConfigure")
public class KafkaRiskRelatedDataConsumer {

    private final MessageProcessServiceImpl messageProcessService;

    public KafkaRiskRelatedDataConsumer(MessageProcessServiceImpl messageProcessService) {
        this.messageProcessService = messageProcessService;
    }

    /**
     * 接受风险分析消息
     *
     * @param messages 消息
     * @param ack      ack
     */
    @KafkaHandler
    @KafkaListener(id = "riskProcessAnalysisConsumer", topics = "riskProcessAnalysis", groupId = "riskProcessAnalysisGroup",
            concurrency = "1")
    public void receiveProcessAnalysisData(List<String> messages, Acknowledgment ack) {
        long startTime = System.currentTimeMillis();
        //执行消费行为
        try {
            messages.forEach(this::processAnalysis);
            log.info("riskProcessAnalysisConsumer本次消费数据入库完毕！共耗时:{}ms,共处理{}条数据", System.currentTimeMillis() - startTime, messages.size());
        } catch (Exception e) {
            log.error("KafkaRiskProcessAnalysisConsumer消费失败:", e);
        }
        //手动提交
        ack.acknowledge();
    }


    /**
     * 接收更新风险分数消息
     *
     * @param messages 消息
     * @param ack      ack
     */
    @KafkaHandler
    @KafkaListener(id = "riskUpdateScoreConsumer", topics = "riskUpdateScore", groupId = "riskUpdateScoreGroup",
            concurrency = "1")
    public void receiveUpdateScoreData(List<String> messages, Acknowledgment ack) {
        long startTime = System.currentTimeMillis();
        //执行消费行为
        try {
            messages.forEach(this::updateScore);
            log.info("riskUpdateScoreConsumer本次消费数据入库完毕！共耗时:{}ms,共处理{}条数据", System.currentTimeMillis() - startTime, messages.size());
        } catch (Exception e) {
            log.error("KafkaRiskProcessAnalysisConsumer消费失败:", e);
        }
        //手动提交
        ack.acknowledge();
    }

    /**
     * 执行单条数据风险分析
     *
     * @param message 记录
     */
    private void processAnalysis(String message) {
        try {
            if (StringUtils.isBlank(message)) {
                log.error("receive risk blank!");
            }
            log.info("接收到风险消息：{}", message);
            RiskProcessAnalysisVO riskProcessAnalysisVO = JsonUtil.parseObject(message, RiskProcessAnalysisVO.class);
            if (riskProcessAnalysisVO != null) {
                log.info("解析消息信息成功！内容：{}", riskProcessAnalysisVO);
                messageProcessService.processAnalysis(riskProcessAnalysisVO);
            }
        } catch (Exception e) {
            log.error("执行消费发生异常", e);
        }
    }

    /**
     * 执行单条数据消费
     *
     * @param message 记录
     */
    private void updateScore(String message) {
        if (StringUtils.isBlank(message)) {
            log.error("receive risk blank!");
        }
        log.info("接收到风险消息：{}", message);
        MessagePushVO messagePushVO = JsonUtil.parseObject(message, MessagePushVO.class);
        if (messagePushVO != null) {
            log.info("解析消息信息成功！内容：{}", messagePushVO);
            try {
                messageProcessService.updateScore(messagePushVO);
            } catch (TRSException e) {
                log.info(e.getMessage());
            } catch (RuntimeException e) {
                log.info("", e);
            }
        }
    }
}
