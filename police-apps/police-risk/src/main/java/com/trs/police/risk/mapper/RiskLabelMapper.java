package com.trs.police.risk.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.common.core.vo.CodeNameVO;
import com.trs.police.risk.domain.entity.RiskLabel;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @date 2023/5/30 17:12
 */
@Mapper
public interface RiskLabelMapper extends BaseMapper<RiskLabel> {

    /**
     * 获取风险标签-树
     *
     * @return 风险标签-树
     */
    List<CodeNameVO> getTree();

    /**
     * 获取风险标签-树
     *
     * @return 风险标签-树
     */
    List<RiskLabel> getTreeConfig();


    /**
     * 获取标签路径名称
     *
     * @param labelPath 标签路径数组
     * @return 标签路径名称
     */
    List<String> getLabelPathName(@Param("labelPath") List<List<Long>> labelPath);

    /**
     * 获取根节点
     *
     * @return 根节点
     */
    @ResultMap("mybatis-plus_RiskLabel")
    @Select("select * from t_risk_label where pid is null")
    List<RiskLabel> getRoots();

    /**
     * 根据父节点获取子节点
     *
     * @param pid 父节点id
     * @return 子节点
     */
    @ResultMap("mybatis-plus_RiskLabel")
    @Select("select * from t_risk_label where pid = #{pid}")
    List<RiskLabel> getByPid(@Param("pid") Long pid);

    /**
     * 获取所有子节点
     *
     * @param paths 路径
     * @return 子节点
     */
    List<Long> getAllByPaths(@Param("paths") List<Long> paths);
}
