package com.trs.police.risk.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.risk.domain.vo.traceableData.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/3 17:39
 */
@Mapper
public interface TraceableDataClueMapper extends TraceableDataBaseMapper {


    @Override
    TraceableDataBaseVO getByCode(@Param("code") String code,@Param("feedbackTimeOffsetEnabled")boolean feedbackTimeOffsetEnabled);


    @Override
    TraceableDataScoreVO getScoreByCode(@Param("code") String code);

    @Override
    void updateScore(@Param("data") TraceableDataScoreVO traceableDataScoreVO);

    /**
     * 查询溯源数据列表
     *
     * @param riskId 风险id
     * @param tel    电话号码
     * @param params 筛选参数
     * @param idNumber 身份证号
     * @param timeOffsetEnabled 是否增加8小时
     * @param page 分页参数
     * @return 溯源数据列表
     */
    @Override
    Page<TraceableDataListVO> getRiskTraceableData(@Param("riskId") Long riskId, @Param("tel") String tel, @Param("params") ListParamsRequest params,@Param("idNumber") String idNumber
            ,@Param("timeOffsetEnabled")boolean timeOffsetEnabled,Page<TraceableDataListVO> page);

    @Override
    List<String> getRiskTraceableDataTagCode(@Param("riskId") Long id);

    /**
     * 根据风险id查询线索的具体来源
     *
     * @param id 风险id
     * @return 线索具体来源
     */
    ClueSourceDetailVO getCurrentSourceDetailByRiskId(@Param("riskId") Long id);

    /**
     * 获取溯源数据总数
     *
     * @param riskId 风险id
     * @param tel    电话号码
     * @param params 筛选参数
     * @param idNumber 身份证号
     * @param timeOffsetEnabled 是否增加8小时
     * @return 溯源数据总数
     */
    @Override
    Long getRiskTraceableDataTotal(@Param("riskId")Long riskId,
                                   @Param("tel")String tel,
                                   @Param("params")ListParamsRequest params,
                                   @Param("idNumber")String idNumber,
                                   @Param("timeOffsetEnabled")boolean timeOffsetEnabled);

    /**
     * 获取反馈信息
     *
     * @param code 编号
     * @param feedbackTimeOffsetEnabled 时间是否偏移
     * @return 反馈信息
     */
    @Override
    List<FeedbackVO> getFeedbacks(@Param("code") String code,
                                  @Param("feedbackTimeOffsetEnabled")boolean feedbackTimeOffsetEnabled);

    /**
     * 根据tel或者idNumber获取总数
     *
     * @param riskId 风险id
     * @param tel    电话号码
     * @param params 筛选参数
     * @param idNumber 身份证号
     * @return 溯源数据列表
     */
    @Override
    Long getRiskTraceableDataTotalV2(@Param("riskId")Long riskId,
                                     @Param("tel")String tel,
                                     @Param("params")ListParamsRequest params,
                                     @Param("idNumber")String idNumber);
}
