<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.risk.mapper.RiskStatisticsMapper">

    <select id="riskTotalCount" resultType="java.lang.Long">
        SELECT COUNT(distinct r.id)
        FROM t_risk r
        left join t_risk_traceable_data_relation re on re.risk_id = r.id
        left join t_profile_sthy s on s.jjdbh = re.code
        <where>
            r.deleted= 0
            <if test="dto.startTime != null and dto.startTime != ''">
                AND s.bjsj >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND s.bjsj &lt;= #{dto.endTime}
            </if>
            <if test="dto.areaCode != null and dto.areaCode != ''">
                AND r.responsible_dept ->> '$.deptCode' LIKE CONCAT(#{dto.areaCode}, '%' )
            </if>
        </where>
    </select>

    <select id="riskStatusStatistics" resultType="com.trs.police.risk.domain.vo.RiskStatisticVO">
        SELECT
            r.risk_status AS riskStatus,
            COUNT(distinct r.id) AS count
        FROM t_risk r
        left join t_risk_traceable_data_relation re on re.risk_id = r.id
        left join t_profile_sthy s on s.jjdbh = re.code
        <where>
            r.deleted= 0
            <if test="dto.startTime != null and dto.startTime != ''">
                AND s.bjsj >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND s.bjsj &lt;= #{dto.endTime}
            </if>
            <if test="dto.areaCode != null and dto.areaCode != ''">
                AND r.responsible_dept ->> '$.deptCode' LIKE CONCAT(#{dto.areaCode}, '%' )
            </if>
        </where>
        GROUP BY r.risk_status
    </select>

    <select id="riskAreaStatistics" resultType="com.trs.police.risk.domain.vo.RiskStatisticVO">
        SELECT
            LEFT(r.responsible_dept ->> '$.deptCode',6) AS areaCode,
            SUM(CASE WHEN r.risk_status = 8 THEN 1 ELSE 0 END) AS zbzCount,
            SUM(CASE WHEN r.risk_status = 9 THEN 1 ELSE 0 END) AS ybjCount,
            COUNT(distinct r.id) AS count
        FROM t_risk r
        left join t_risk_traceable_data_relation re on re.risk_id = r.id
        left join t_profile_sthy s on s.jjdbh = re.code
        <where>
            r.deleted= 0
            <if test="dto.startTime != null and dto.startTime != ''">
                AND s.bjsj >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND s.bjsj &lt;= #{dto.endTime}
            </if>
        </where>
        GROUP BY areaCode
    </select>

    <select id="riskPcsStatistics" resultType="com.trs.police.risk.domain.vo.RiskStatisticVO">
        SELECT
            deptCode,
            COUNT(DISTINCT CASE WHEN risk_status = 3 THEN CONCAT(id, '-' ,risk_status) END) AS dqsCount,
            COUNT(DISTINCT CASE WHEN risk_status = 6 THEN CONCAT(id, '-' ,risk_status) END) AS dypCount,
            COUNT(DISTINCT CASE WHEN risk_status = 8 THEN CONCAT(id, '-' ,risk_status) END) AS zbzCount,
            COUNT(DISTINCT CASE WHEN risk_status = 9 THEN CONCAT(id, '-' ,risk_status) END) AS ybjCount,
            COUNT(DISTINCT CASE WHEN risk_level = 1 THEN CONCAT(id, '-' ,risk_status) END) AS redCount,
            COUNT(DISTINCT CASE WHEN risk_level = 2 THEN CONCAT(id, '-' ,risk_status) END) AS yellowCount,
            COUNT(DISTINCT CASE WHEN risk_level = 3 THEN CONCAT(id, '-' ,risk_status) END) AS blueCount,
            COUNT(DISTINCT id) AS count
        FROM (
            SELECT jt.deptCode,r.risk_status,r.risk_level,r.id
            FROM t_risk r
             left join t_risk_traceable_data_relation re on re.risk_id = r.id
             left join t_profile_sthy s on s.jjdbh = re.code,
            JSON_TABLE(r.handle_dept, '$[*]' COLUMNS (deptCode VARCHAR(255) PATH '$.deptCode')) AS jt
            <where>
                r.deleted = 0
                <if test="dto.startTime != null and dto.startTime != ''">
                    AND s.bjsj >= #{dto.startTime}
                </if>
                <if test="dto.endTime != null and dto.endTime != ''">
                    AND s.bjsj &lt;= #{dto.endTime}
                </if>
                <if test="dto.areaCode != null and dto.areaCode != ''">
                    AND LEFT(r.responsible_dept ->> '$.deptCode',6) = #{dto.areaCode}
                </if>
            </where>
            ) AS extracted_deptCodes
        GROUP BY deptCode;
    </select>
</mapper>