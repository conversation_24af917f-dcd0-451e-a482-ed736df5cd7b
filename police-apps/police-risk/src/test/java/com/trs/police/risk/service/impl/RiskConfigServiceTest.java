package com.trs.police.risk.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.common.utils.JsonUtils;
import com.trs.police.common.openfeign.starter.vo.DictVO;
import com.trs.police.risk.RiskApp;
import com.trs.police.risk.domain.dto.ModelStatusDTO;
import com.trs.police.risk.domain.vo.RuleConfigBasicInfoVO;
import com.trs.police.risk.service.BasicModelConfigService;
import com.trs.police.risk.service.RiskConfigService;
import com.trs.web.builder.base.RestfulResultsV2;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.ArrayList;

/**
 * <AUTHOR>
 * @since 2025/2/11 10:18
 */
@Slf4j
@SpringBootTest(classes = RiskApp.class)
public class RiskConfigServiceTest {

    @Resource
    private RiskConfigService riskConfigService;

    @Resource
    private BasicModelConfigService basicModelConfigService;

    /**
     * 测试打开风险预警模型
     */
    @Test
    public void testSwitchModelStatusOpen(){
        RestfulResultsV2<String> resultsV2 = basicModelConfigService.switchModel(new ModelStatusDTO("risk_model", true, "jq"));
        System.out.println(JsonUtils.toOptionalJson(resultsV2).get());
    }

    /**
     * 测试关闭风险预警模型
     */
    @Test
    public void testSwitchModelStatusClose(){
        RestfulResultsV2<String> resultsV2 = basicModelConfigService.switchModel(new ModelStatusDTO("risk_model", false, null));
        System.out.println(JsonUtils.toOptionalJson(resultsV2).get());
    }

    /**
     * 测试模型编码为空
     */
    @Test
    public void testSwitchModelCodeIsNull(){
        RestfulResultsV2<String> resultsV2 = basicModelConfigService.switchModel(new ModelStatusDTO(null, false, null));
        System.out.println(JsonUtils.toOptionalJson(resultsV2).get());
    }

    /**
     * 测试模型开关状态为空
     */
    @Test
    public void testSwitchModelOpenIsNull(){
        RestfulResultsV2<String> resultsV2 = basicModelConfigService.switchModel(new ModelStatusDTO("risk_model", null, null));
        System.out.println(JsonUtils.toOptionalJson(resultsV2).get());
    }

    /**
     * 测试参数都是空
     */
    @Test
    public void testSwitchModelAllParamsIsNull(){
        RestfulResultsV2<String> resultsV2 = basicModelConfigService.switchModel(new ModelStatusDTO(null, null, null));
        System.out.println(JsonUtils.toOptionalJson(resultsV2).get());
    }

    @Test
    public void getRelationDataRuleDetail() {
        RestfulResultsV2<JsonNode> resultsV2 = riskConfigService.tagRelationDataRuleDetail();
        System.out.println(JsonUtils.toOptionalJson(resultsV2).get());
    }

    @Test
    public void getTagRuleDetail() {
        RestfulResultsV2<JsonNode> resultsV2 = riskConfigService.tagRuleDetail(79);
        System.out.println(JsonUtils.toOptionalJson(resultsV2).get());
    }

    @Test
    public void testSaveOrUpdateTagRule() {
        String rule = "{\"score\":888,\"filter\":{\"filters\":[{\"conditions\":[{\"type\":\"any\",\"field\":\"data.$nr\",\"params\":[\"家庭纠纷\",\"打妈妈\",\"抢小孩\",\"抢孩子\",\"抢娃儿\",\"砍儿子\"],\"fieldSelect\":[],\"paramsSelect\":[],\"function\":\"contain\"}]},{\"conditions\":[{\"type\":\"any\",\"field\":\"data.$fknr\",\"params\":[\"家庭纠纷\",\"打妈妈\",\"抢小孩\",\"抢孩子\",\"抢娃儿\",\"砍儿子\"],\"fieldSelect\":[],\"paramsSelect\":[],\"function\":\"contain\"}]},{\"relation\":\"and\",\"conditions\":[{\"type\":\"means\",\"field\":\"data.$nr\",\"params\":[\"带走\",\"抢\"],\"fieldSelect\":[],\"paramsSelect\":[],\"function\":\"contain\"},{\"type\":\"relation\",\"field\":\"data.$nr\",\"params\":[\"孙子\",\"孙女\",\"外孙\",\"外孙女\",\"儿子\",\"女儿\",\"小孩\",\"孩子\",\"娃儿\"],\"fieldSelect\":[],\"paramsSelect\":[],\"function\":\"contain\"}]},{\"relation\":\"and\",\"conditions\":[{\"type\":\"means\",\"field\":\"data.$fknr\",\"params\":[\"带走\",\"抢\"],\"fieldSelect\":[],\"paramsSelect\":[],\"function\":\"contain\"},{\"type\":\"relation\",\"field\":\"data.$fknr\",\"params\":[\"孙子\",\"孙女\",\"外孙\",\"外孙女\",\"儿子\",\"女儿\",\"小孩\",\"孩子\",\"娃儿\"],\"function\":\"contain\"}]},{\"relation\":\"and\",\"conditions\":[{\"type\":\"means\",\"field\":\"data.$nr\",\"params\":[\"不管我\",\"不管娃儿\",\"不管孩子\",\"不管儿子\",\"抓扯\",\"抓伤\",\"抓坏\",\"纠纷\",\"抢去\",\"抢走\",\"抢我\",\"抢东西\",\"抢了\",\"抢他\",\"抢她\",\"吵架\",\"闹事\",\"砸\",\"威胁\",\"骚扰\",\"争吵\",\"纠缠\",\"动手\",\"据为己有\",\"砍东西\",\"乱砍\",\"砍死\",\"砍我\",\"砍人\",\"被砍\",\"砍了\",\"砍他\",\"砍她\",\"砍门\",\"砍伤\",\"砍我\",\"掐\",\"闹矛盾\",\"找麻烦\",\"找我的麻烦\",\"耍无赖\",\"虐待\",\"反锁\",\"不开门\",\"关起\",\"关在\",\"囚禁\",\"锁在家\",\"调解\",\"协商\",\"刀\",\"骂\",\"破坏\",\"拉扯\",\"冲突\",\"尾随\",\"刺伤\",\"赖着不走\",\"欺负\",\"打来\",\"打倒\",\"殴打\",\"虐打\",\"打我\",\"打了我\",\"打架\",\"打人\",\"打起了\",\"打起来了\",\"打他\",\"打伤\",\"打死\",\"打人\",\"暴打\",\"打来\",\"击打\",\"打他\",\"打她\",\"出轨\",\"推了\",\"推倒\",\"杀他\",\"杀她\",\"杀我\",\"杀人\",\"杀了我\",\"杀死\",\"整我\",\"收拾\"],\"fieldSelect\":[],\"paramsSelect\":[],\"function\":\"contain\"},{\"type\":\"relation\",\"field\":\"data.$nr\",\"params\":[\"爸\",\"父亲\",\"儿子\",\"女儿\",\"亲戚\",\"婆婆\",\"儿媳妇\",\"妈\",\"家里的人\",\"家里人\",\"舅\",\"姐姐\",\"弟弟\",\"妹妹\",\"女婿\",\"孙子\",\"兄弟\",\"姐妹\",\"妹夫\",\"家里有人\",\"父母\",\"继母\",\"继父\",\"侄\",\"母亲\",\"老汉\",\"哥哥\",\"表哥\",\"大哥\",\"亲哥\",\"堂哥\",\"哥嫂\",\"二哥\",\"三哥\",\"表姐\",\"老爹\",\"幺妹\"],\"fieldSelect\":[],\"paramsSelect\":[],\"function\":\"contain\"}]},{\"relation\":\"and\",\"conditions\":[{\"type\":\"means\",\"field\":\"data.$fknr\",\"params\":[\"不管我\",\"不管娃儿\",\"不管孩子\",\"不管儿子\",\"抓扯\",\"抓伤\",\"抓坏\",\"纠纷\",\"抢去\",\"抢走\",\"抢我\",\"抢东西\",\"抢了\",\"抢他\",\"抢她\",\"吵架\",\"闹事\",\"砸\",\"威胁\",\"骚扰\",\"争吵\",\"纠缠\",\"动手\",\"据为己有\",\"砍东西\",\"乱砍\",\"砍死\",\"砍我\",\"砍人\",\"被砍\",\"砍了\",\"砍他\",\"砍她\",\"砍门\",\"砍伤\",\"砍我\",\"掐\",\"闹矛盾\",\"找麻烦\",\"找我的麻烦\",\"耍无赖\",\"虐待\",\"反锁\",\"不开门\",\"关起\",\"关在\",\"囚禁\",\"锁在家\",\"调解\",\"协商\",\"刀\",\"骂\",\"破坏\",\"拉扯\",\"冲突\",\"尾随\",\"刺伤\",\"赖着不走\",\"欺负\",\"打来\",\"打倒\",\"殴打\",\"虐打\",\"打我\",\"打了我\",\"打架\",\"打人\",\"打起了\",\"打起来了\",\"打他\",\"打伤\",\"打死\",\"打人\",\"暴打\",\"打来\",\"击打\",\"打他\",\"打她\",\"出轨\",\"推了\",\"推倒\",\"杀他\",\"杀她\",\"杀我\",\"杀人\",\"杀了我\",\"杀死\",\"整我\",\"收拾\"],\"fieldSelect\":[],\"paramsSelect\":[],\"function\":\"contain\"},{\"type\":\"relation\",\"field\":\"data.$fknr\",\"params\":[\"爸\",\"父亲\",\"儿子\",\"女儿\",\"亲戚\",\"婆婆\",\"妈\",\"家里的人\",\"家里人\",\"舅\",\"姐姐\",\"弟弟\",\"妹妹\",\"女婿\",\"孙子\",\"兄弟\",\"姐妹\",\"妹夫\",\"家里有人\",\"父母\",\"继母\",\"继父\",\"侄\",\"母亲\",\"老汉\",\"哥哥\",\"表哥\",\"大哥\",\"亲哥\",\"堂哥\",\"哥嫂\",\"二哥\",\"三哥\",\"表姐\",\"老爹\",\"幺妹\"],\"function\":\"contain\"}]},{\"relation\":\"and\",\"conditions\":[{\"type\":\"any\",\"field\":\"data.$nr\",\"params\":[\"被\"],\"fieldSelect\":[],\"paramsSelect\":[],\"function\":\"contain\"},{\"type\":\"means\",\"field\":\"data.$nr\",\"params\":[\"打了\"],\"fieldSelect\":[],\"paramsSelect\":[],\"function\":\"contain\"},{\"type\":\"relation\",\"field\":\"data.$nr\",\"params\":[\"爸\",\"父亲\",\"儿子\",\"女儿\",\"亲戚\",\"婆婆\",\"儿媳妇\",\"妈\",\"家里的人\",\"家里人\",\"舅\",\"姐姐\",\"弟弟\",\"妹妹\",\"女婿\",\"孙子\",\"兄弟\",\"姐妹\",\"妹夫\",\"家里有人\",\"家里的人\",\"父母\",\"继母\",\"继父\",\"侄\",\"母亲\",\"老汉\",\"哥哥\",\"表哥\",\"大哥\",\"亲哥\",\"堂哥\",\"哥嫂\",\"二哥\",\"三哥\",\"表姐\",\"老爹\",\"幺妹\"],\"fieldSelect\":[],\"paramsSelect\":[],\"function\":\"contain\"}]},{\"relation\":\"and\",\"conditions\":[{\"type\":\"any\",\"field\":\"data.$fknr\",\"params\":[\"被\"],\"fieldSelect\":[],\"paramsSelect\":[],\"function\":\"contain\"},{\"type\":\"means\",\"field\":\"data.$fknr\",\"params\":[\"打了\"],\"fieldSelect\":[],\"paramsSelect\":[],\"function\":\"contain\"},{\"type\":\"relation\",\"field\":\"data.$fknr\",\"params\":[\"爸\",\"父亲\",\"儿子\",\"女儿\",\"二婚\",\"亲戚\",\"婆婆\",\"儿媳妇\",\"妈\",\"家里的人\",\"家里人\",\"舅\",\"姐姐\",\"弟弟\",\"妹妹\",\"女婿\",\"孙子\",\"兄弟\",\"姐妹\",\"妹夫\",\"家里有人\",\"家里的人\",\"父母\",\"继母\",\"继父\",\"侄\",\"母亲\",\"老汉\",\"哥哥\",\"表哥\",\"大哥\",\"亲哥\",\"堂哥\",\"哥嫂\",\"二哥\",\"三哥\",\"表姐\",\"老爹\",\"幺妹\"],\"fieldSelect\":[],\"paramsSelect\":[],\"function\":\"contain\"}]}],\"relation\":\"or\",\"conditions\":[{\"type\":\"none\",\"field\":\"data.$jqlb\",\"params\":[\"家庭纠纷\"],\"fieldSelect\":[],\"paramsSelect\":[],\"function\":\"equal\"}]},\"tagName\":\"纠纷类型/家庭纠纷\",\"parentId\":1,\"priority\":3,\"isRelation\":true}";
        log.info(JsonUtils.toOptionalJson(riskConfigService.addOrUpdateTagRule(rule)).get());
    }

    /**
     * 测试关联数据规则保存或修改
     */
    @Test
    public void testSaveOrUpdateRelationDataRule() {
        String rule = "{\"relationDataScope\":\"jq;12345;xs\",\"timeScope\":\"1year\",\"rules\":[{\"score\":2,\"tagFilter\":{\"conditions\":[{\"field\":\"data.$tag\",\"function\":\"contain\",\"type\":\"none\",\"params\":[\"婚恋家庭纠纷\",\"邻里纠纷\",\"琐事纠纷\"]}],\"relation\":\"and\"},\"name\":\"超高频率预警\",\"ruleFilter\":{\"conditions\":[{\"field\":\"data.$tagMatchTimes\",\"function\":\">=\",\"type\":\"none\",\"params\":[\"3\"]}]},\"timeScope\":\"30day\",\"priority\":3,\"group\":\"frequency\"},{\"score\":1,\"tagFilter\":{\"conditions\":[{\"field\":\"data.$tag\",\"function\":\"contain\",\"type\":\"none\",\"params\":[\"婚恋家庭纠纷\",\"邻里纠纷\",\"琐事纠纷\"]}],\"relation\":\"and\"},\"name\":\"高频率预警\",\"ruleFilter\":{\"conditions\":[{\"field\":\"data.$tagMatchTimes\",\"function\":\">=\",\"type\":\"none\",\"params\":[\"3\"]}]},\"timeScope\":\"60day\",\"priority\":2,\"group\":\"frequency\"},{\"score\":0.5,\"tagFilter\":{\"conditions\":[{\"field\":\"data.$tag\",\"function\":\"contain\",\"type\":\"none\",\"params\":[\"婚恋家庭纠纷\",\"邻里纠纷\",\"琐事纠纷\"]}],\"relation\":\"and\"},\"name\":\"中高频率预警\",\"ruleFilter\":{\"conditions\":[{\"field\":\"data.$tagMatchTimes\",\"function\":\">=\",\"type\":\"none\",\"params\":[\"3\"]}]},\"timeScope\":\"90day\",\"priority\":1,\"group\":\"frequency\"},{\"score\":-2,\"tagFilter\":{\"conditions\":[{\"field\":\"data.$tag\",\"function\":\"contain\",\"type\":\"none\",\"params\":[\"职业/出租车司机\"]}],\"relation\":\"and\"},\"ruleFilter\":{\"conditions\":[{\"field\":\"data.$tagMatchTimes\",\"function\":\">=\",\"type\":\"none\",\"params\":[\"3\"]}]},\"priority\":0,\"tagName\":\"多次命中出租车标签\",\"group\":\"denoise\"},{\"score\":-1,\"tagFilter\":{\"conditions\":[{\"field\":\"data.$tag\",\"function\":\"contain\",\"type\":\"none\",\"params\":[\"职业/物管\"]}],\"relation\":\"and\"},\"ruleFilter\":{\"conditions\":[{\"field\":\"data.$tagMatchTimes\",\"function\":\">=\",\"type\":\"none\",\"params\":[\"3\"]}]},\"priority\":0,\"tagName\":\"多次命中物管标签\",\"group\":\"denoise\"},{\"score\":-1,\"tagFilter\":{\"conditions\":[{\"field\":\"data.$tag\",\"function\":\"contain\",\"type\":\"none\",\"params\":[\"职业/保安\"]}],\"relation\":\"and\"},\"ruleFilter\":{\"conditions\":[{\"field\":\"data.$tagMatchTimes\",\"function\":\">=\",\"type\":\"none\",\"params\":[\"3\"]}]},\"priority\":0,\"tagName\":\"多次命中保安标签\",\"group\":\"denoise\"},{\"score\":-1,\"tagFilter\":{\"conditions\":[{\"field\":\"data.$tag\",\"function\":\"contain\",\"type\":\"none\",\"params\":[\"职业/停车收费员\"]}],\"relation\":\"and\"},\"ruleFilter\":{\"conditions\":[{\"field\":\"data.$tagMatchTimes\",\"function\":\">=\",\"type\":\"none\",\"params\":[\"3\"]}]},\"priority\":0,\"tagName\":\"多次命中停车收费员标签\",\"group\":\"denoise\"},{\"score\":-1,\"tagFilter\":{\"conditions\":[{\"field\":\"data.$tag\",\"function\":\"contain\",\"type\":\"none\",\"params\":[\"职业/市场管理\"]}],\"relation\":\"and\"},\"ruleFilter\":{\"conditions\":[{\"field\":\"data.$tagMatchTimes\",\"function\":\">=\",\"type\":\"none\",\"params\":[\"3\"]}]},\"priority\":0,\"tagName\":\"多次命中市场管理标签\",\"group\":\"denoise\"}],\"contentTagScore\":{\"score\":\"${thisTagScore} * ${thisTagScoreWeight} + ${otherTagScore} * ${otherTagScoreWeight}\",\"thisTagScoreWeight\":\"0.8\",\"otherTagScoreWeight\":\"0.2\"}}";
        log.info(JsonUtils.toOptionalJson(riskConfigService.addRelationDataRule(rule)).get());
    }

    public static void main(String[] args) {
        testGetColumnName();
        //testAddOrUpdateSuccess();
        //testGetRuleConfigBasicInfoSuccess();
    }

    //@Test
    public static void testGetColumnName() {
        ArrayList<DictVO> list = new ArrayList<>();
        DictVO dictVO = new DictVO();
        dictVO.setName("报警内容");
        dictVO.setDictDesc("data.$nr");

        DictVO dictVO2 = new DictVO();
        dictVO2.setName("警情类别");
        dictVO2.setDictDesc("data.$jqlb");

        DictVO dictVO3 = new DictVO();
        dictVO3.setName("反馈信息");
        dictVO3.setDictDesc("data.$fknr");

        list.add(dictVO);
        list.add(dictVO2);
        list.add(dictVO3);
        RestfulResultsV2<DictVO> ok = RestfulResultsV2.ok(list);
        log.info("ok:{}", JsonUtils.toOptionalJson(ok).get());
    }


    public static void testAddOrUpdateSuccess() {
        RestfulResultsV2<String> ok = RestfulResultsV2.ok("成功");
        log.info("ok:{}", JsonUtils.toOptionalJson(ok).get());
    }

    public static void testGetRuleConfigBasicInfoSuccess() {
        RuleConfigBasicInfoVO ruleConfigBasicInfoVO = new RuleConfigBasicInfoVO();
        ruleConfigBasicInfoVO.setModelName("风险预警模型");
        ruleConfigBasicInfoVO.setModelCategory("风险模型");
        ruleConfigBasicInfoVO.setWarnModel("风险预警模型");
        ruleConfigBasicInfoVO.setDataScope("jq;12345");
        ruleConfigBasicInfoVO.setRiskScoreFormula("风险分值 = 内容标签分分值 + 关联数据分值");
        RestfulResultsV2<RuleConfigBasicInfoVO> ok = RestfulResultsV2.ok(ruleConfigBasicInfoVO);
        log.info("ok:{}", JsonUtils.toOptionalJson(ok).get());
    }


    public static void testGetTagRuleList() {
        RuleConfigBasicInfoVO ruleConfigBasicInfoVO = new RuleConfigBasicInfoVO();
        ruleConfigBasicInfoVO.setModelName("风险预警模型");
        ruleConfigBasicInfoVO.setModelCategory("风险模型");
        ruleConfigBasicInfoVO.setWarnModel("风险预警模型");
        ruleConfigBasicInfoVO.setDataScope("jq;12345");
        ruleConfigBasicInfoVO.setRiskScoreFormula("风险分值 = 内容标签分分值 +关联数据分值");
    }

}
