package com.trs.police.comparison.data.extract.domain.DTO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.UUID;

/**
 * 请求预览轨迹数据的请求参数
 *
 * <AUTHOR>
 * @since 2024/8/20 16:45
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ViewTrackDataDTO implements Serializable {

    /**
     * kafka访问信息
     */
    @NotEmpty(message = "kafka 访问信息不能为空")
    private String bootstrapServers;

    /**
     * kakfa主题名称
     */
    @NotEmpty(message = "kafka topic 不能为空")
    private String topic;

    /**
     * 消费者组名称
     */
    private String group = UUID.randomUUID().toString();

}
