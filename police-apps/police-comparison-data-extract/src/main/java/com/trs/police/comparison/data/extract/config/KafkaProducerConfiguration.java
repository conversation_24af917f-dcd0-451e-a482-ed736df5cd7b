package com.trs.police.comparison.data.extract.config;

import com.google.common.collect.Lists;
import com.trs.police.comparison.data.extract.config.properties.KafkaProduceProperties;
import com.trs.police.comparison.data.extract.constants.KafkaSecurityConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.Properties;
import java.util.stream.Collectors;

/**
 * kafka实例初始化
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class KafkaProducerConfiguration {

    @Resource
    private KafkaProduceProperties produceProperties;

    @PostConstruct
    private void initSecurity() {
        if (StringUtils.isNotBlank(produceProperties.getKrb5Path())
                && StringUtils.isNotBlank(produceProperties.getKeytabPath())
                && StringUtils.isNotBlank(produceProperties.getPrincipal())) {

            String jaasContent = Lists.newArrayList(
                    "KafkaClient {",
                    "com.sun.security.auth.module.Krb5LoginModule required",
                    "useKeyTab=true",
                    "keyTab=\"" + produceProperties.getKeytabPath() + "\"",
                    "principal=\"" + produceProperties.getPrincipal() + "\"",
                    "useTicketCache=false",
                    "storeKey=true",
                    "debug=true;",
                    "};"
            ).stream().collect(Collectors.joining(System.getProperty("line.separator")));

            log.info("jaasContent is {}", jaasContent);

            final String jaasPath =
                    System.getProperty("java.io.tmpdir") + File.separator + System.getProperty("user.name") + ".jaas.conf";
            try {
                FileUtils.write(new File(jaasPath), jaasContent, "UTF-8", false);
            } catch (IOException e) {
                throw new RuntimeException("创建jaas文件失败", e);
            }
            System.setProperty("java.security.auth.login.config", jaasPath);
            log.info("jaasPath is {}", jaasPath);
            System.setProperty("java.security.krb5.conf", produceProperties.getKrb5Path());
            log.info("kafka krb5Path is {}", produceProperties.getKrb5Path());
        } else {
            log.info("当前环境不需要初始化 kafka 安全配置");
        }
    }


    /**
     * 初始化kafka producer
     *
     * @return kafka producer
     */
    @Bean(name = "producer")
    public KafkaProducer<String, String> kafkaProducer() {

        final Properties properties = new Properties();
        properties.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, produceProperties.getBootstrapServers());
        properties.put(ProducerConfig.CLIENT_ID_CONFIG, "trs_data_extract_" + System.currentTimeMillis());
        properties.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        properties.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());

        if (StringUtils.isNotBlank(produceProperties.getKrb5Path())) {
            properties.setProperty(KafkaSecurityConstants.SECURITY_PROTOCOL, produceProperties.getSecurityProtocol());
            properties.setProperty(KafkaSecurityConstants.SASL_KERBEROS_SERVICE_NAME, produceProperties.getSaslKerberosServiceName());
            properties.setProperty(KafkaSecurityConstants.SASL_MECHANISM, produceProperties.getSaslMechanism());
        }
        return new KafkaProducer<>(properties);
    }

}
