package com.trs.police.comparison.data.extract.processor;

import com.alibaba.fastjson2.JSONObject;
import com.trs.police.comparison.data.extract.config.ConfigInfo;
import com.trs.police.comparison.data.extract.task.IDataTask;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 带数据回填流程处理器
 * 生成主键
 * 数据回填
 * 轨迹推送
 *
 * <AUTHOR>
 * @since 2024/6/20 16:51
 */
@Component
public class KafkaExtractJobProcessor implements IJobProcessor{


    private final List<IDataTask> sortedTasks;

    public KafkaExtractJobProcessor(List<IDataTask> tasks){
        //按照处理顺序排序  order 越小的排在前面
        sortedTasks = tasks.stream()
                .sorted(Comparator.comparingInt(IDataTask::getOrder))
                .collect(Collectors.toList());
    }

    /**
     * 按照流程处理数据
     *
     * @param list 原数据
     * @param config 配置信息
     */
    @Override
    public void process(List<JSONObject> list, ConfigInfo config) throws Exception {
        if (CollectionUtils.isEmpty(sortedTasks)) {
            throw new RuntimeException("执行的处理器为空");
        }
        for (IDataTask task : sortedTasks) {
            task.processData(list, config);
        }
    }

    /**
     * 按照指定的流程处理数据
     *
     * @param list         数据
     * @param config 配置
     * @throws Exception 异常
     */
    @Override
    public void testProcess(List<JSONObject> list, ConfigInfo config) throws Exception {
        for (IDataTask task : sortedTasks) {
            task.testProcessData(list, config);
        }
    }
}
