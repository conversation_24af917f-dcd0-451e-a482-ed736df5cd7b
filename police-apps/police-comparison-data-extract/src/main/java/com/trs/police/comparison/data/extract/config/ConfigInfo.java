package com.trs.police.comparison.data.extract.config;

import com.trs.police.comparison.data.extract.domain.DTO.KafkaInfoAddDTO;
import lombok.Data;
import org.apache.kafka.clients.consumer.KafkaConsumer;

import javax.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 数据源配置
 *
 * <AUTHOR>
 */
@Data
public class ConfigInfo {

    private List<KafkaConsumer<String, String>> consumers = new ArrayList<>();

    /**
     * 数据源名称
     */
    @NotEmpty(message = "数据源名称不能为空")
    private String name;

    @NotEmpty(message = "数据源英文名不能为空")
    private String enName;

    /**
     * 活动时间是否需要加8小时
     */
    private boolean needPlusEightHours;

    private boolean needMarkActivityType;

    /**
     * 推送云墙的数据源英文名
     */
    private String yqEnName;

    private HashMap<String, String> yqFieldJsonPath = new HashMap<>(0);

    /**
     * 云墙时间格式转换
     */
    private HashMap<String, String> yqTimeFieldConfig = new HashMap<>(0);

    private HashMap<String, String> yqFieldBackFill = new HashMap<>(0);

    /**
     * 特征值归一化类型
     */
    private String identifierNormalizationType;

    @NotEmpty(message = "特征值类型不能为空")
    private String identifierType;

    @NotEmpty(message = "感知源类型不能为空")
    private String sensingType;

    /**
     * 数据分类
     */
    private String dataClassification;

    @NotEmpty(message = "kafka topic 不能为空")
    private String topic;

    private String group;

    /**
     * 不同环境的kafka信息 唯一标识 nacos配置
     */
    private String kafkaInfoId;

    /**
     * kafka信息 前端传入
     */
    private KafkaInfoAddDTO mqInfo;

    /**
     * 数据数据jsonPath
     */
    private String arrayNodeJsonPath;

    /**
     * 获取标记的数据来源jsonPath
     */
    private String trackSourceFromFlagJsonPath;

    /**
     * 活动类型jsonPath
     */
    private String activityTypeJsonPath;

    /**
     * 存在json中字段内容为字符串，字符串实际上可以解析为json的情况, 配置存在此种字段的jsonPath, 特意处理为正常jsonpath
     */
    private List<String> stringJsonPaths = new ArrayList<>();


    /**
     * 轨迹推送配置字段
     */
    private List<TrackFieldMapping> fieldMappingList = new ArrayList<>();

    /**
     * 配置为json path
     */
    @NotEmpty(message = "主键生成字段不能为空")
    private List<String> keyFields = new ArrayList<>();

    private String keyFieldName = "zjlid";

    /**
     * 过滤条件配置
     */
    private List<FilterConfig> filterList = new ArrayList<>();

    /**
     * 字段映射, 开启数据存储时需配置, 决定建立的iceberg表的字段, 若修改将清空表中原有数据并重建表
     */
    private HashMap<String, String> fieldJsonPath = new HashMap<>(0);
}