package com.trs.police.guangan.dto;

import com.trs.police.common.core.utils.JsonUtil;
import org.junit.jupiter.api.Test;

class TokenDTOTest {

    @Test
    void isExpire() {
        var token = JsonUtil.parseObject("{}", Token.class);
        System.out.println(TokenDTO.of(
                token,
                System.currentTimeMillis()
        ).isExpire());
        System.out.println(TokenDTO.of(
                token,
                System.currentTimeMillis() - (2 * 60 * 60 * 1000L)
        ).isExpire());
    }
}