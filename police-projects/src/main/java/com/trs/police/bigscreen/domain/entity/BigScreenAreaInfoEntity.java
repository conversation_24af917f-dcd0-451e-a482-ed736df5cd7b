package com.trs.police.bigscreen.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 地点信息
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-11-25 14:06:53
 */
@Data
@TableName("tb_common_bigscreen_area_info")
public class BigScreenAreaInfoEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Date createTime = new Date();

    private String thirdId;
    /**
     * 巡区名称
     */
    private String name;
    /**
     * 巡区类型
     */
    private String typeCode;
    /**
     * 巡区类型
     */
    private String typeValueJson;
    private String typeValue;
    /**
     * 编码
     */
    private String districtCode;
    /**
     * 单位名
     */
    private String deptName;
    /**
     * 描绘方式
     */
    private String portrayModeName;
    /**
     * 描绘方式
     */
    private String portrayModeCode;
    /**
     * 描述数据
     */
    private String portrayModeData;
}
