package com.trs.police.bigscreen.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.bigscreen.domain.dto.WorkInstructionDTO;
import com.trs.police.bigscreen.domain.entity.WorkInstructionEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @author: dingkeyu
 * @date: 2024/09/12
 * @description:
 */
@Mapper
public interface WorkInstructionMapper extends BaseMapper<WorkInstructionEntity> {

    /**
     * 下发指令列表
     *
     * @param dto dto
     * @param deptCode deptCode
     * @param page page
     * @return {@link Page}<{@link WorkInstructionEntity}>
     */
    Page<WorkInstructionEntity> xfZlList(@Param("dto")WorkInstructionDTO dto, @Param("deptCode") String deptCode, Page<WorkInstructionEntity> page);
}
