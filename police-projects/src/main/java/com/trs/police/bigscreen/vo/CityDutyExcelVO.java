package com.trs.police.bigscreen.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.trs.common.pojo.BaseVO;
import com.trs.police.common.core.dto.UserDto;
import lombok.Data;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 州（市）公安局值班表导出VO
 *
 * <AUTHOR> zhang.yang  E-mail: <EMAIL>
 * @since 创建时间：2024/12/3 17:57
 * @version 1.0
 * @since 1.0
 */
@Data
public class CityDutyExcelVO extends BaseVO {

    private String unitName;

    private String districtCode;

    /**
     * 值班日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String dutyTime;

    /**
     * 录入时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String crTime;

    /**
     * 联系电话（值班电话）
     */
    private String dutyPhone;

    /**
     * 值班领导主班_姓名
     */
    private String zbldZbName;

    private String zbldZbPost;

    private String zbldZbPhone;

    /**
     * 值班领导副班_姓名
     */
    private String zbldFbName;

    private String zbldFbPost;

    private String zbldFbPhone;

    /**
     * 指挥长主班_姓名（职务补充字段暂缺相关逻辑，待后续补充）
     */
    private String zhzZbName;

    private String zhzZbPost;

    private String zhzZbPhone;

    /**
     * 指挥长副班_姓名
     */
    private String zhzFbName;

    private String zhzFbPost;

    private String zhzFbPhone;

    /**
     * 指挥调度专班_姓名（调度岗）
     */
    private String zhddzbName;

    private UserDto firstDdgUser;

    private String dataIds;
}
