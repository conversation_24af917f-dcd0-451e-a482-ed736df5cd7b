package com.trs.police.bigscreen.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.police.bigscreen.domain.entity.BigScreenDutyUserEntity;
import com.trs.police.bigscreen.mapper.BigScreenDutyUserMapper;
import com.trs.police.bigscreen.vo.DutyUserSyncVo;
import com.trs.police.common.core.mapper.SyncTaskMapper;
import com.trs.police.zg.utils.QinWuUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.Optional;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：数据同步服务
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/9/5 17:54
 * @since 1.0
 */
public abstract class BaseDutyUserSyncService extends BaseSyncService<DutyUserSyncVo, DutyUserSyncVo> {

    private final BigScreenDutyUserMapper bigScreenDutyUserMapper;

    protected final QinWuUtils qinWuUtils;

    public BaseDutyUserSyncService(
            BigScreenDutyUserMapper bigScreenDutyUserMapper,
            SyncTaskMapper syncTaskMapper,
            RedisTemplate<String, Object> redisTemplate
    ) {
        super(syncTaskMapper, redisTemplate);
        this.bigScreenDutyUserMapper = bigScreenDutyUserMapper;
        this.qinWuUtils = QinWuUtils.getInstance();
    }

    /**
     * save<BR>
     *
     * @param out 参数
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/5 18:04
     */
    @Override
    public void save(DutyUserSyncVo out) {
        // 移除历史数据
        bigScreenDutyUserMapper.delete(
                new QueryWrapper<BigScreenDutyUserEntity>()
                        .lambda()
                        .eq(BigScreenDutyUserEntity::getDutyTime, out.getDutyTime())
                        .eq(BigScreenDutyUserEntity::getDistrictCode, out.getDistrictCode())
        );
        // 插入新数据
        Optional.of(out)
                .map(DutyUserSyncVo::getUsers)
                .filter(CollectionUtils::isNotEmpty)
                .ifPresent(it -> it.forEach(bigScreenDutyUserMapper::insert));
    }

    /**
     * convert<BR>
     *
     * @param dutyUserSyncVo 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/5 18:04
     */
    @Override
    public DutyUserSyncVo convert(DutyUserSyncVo dutyUserSyncVo) {
        return dutyUserSyncVo;
    }
}
