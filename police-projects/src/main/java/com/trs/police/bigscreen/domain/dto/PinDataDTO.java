package com.trs.police.bigscreen.domain.dto;

import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.police.common.core.entity.BigScreenPinDataEntity;
import com.trs.police.common.core.entity.CurrentUser;
import lombok.Data;

import static com.trs.common.base.PreConditionCheck.checkNotEmpty;
import static com.trs.common.base.PreConditionCheck.checkNotNull;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/6/7 15:56
 * @since 1.0
 */
@Data
public class PinDataDTO extends UnPinDataDTO {

    /**
     * 需要钉住的对象类型
     */
    private String objType;

    /**
     * 对象ID
     */
    private String objId;

    /**
     * 1：钉住
     * 0：取消钉住
     */
    private Integer pinFlag;

    /**
     * 钉住在大屏哪个模块
     * 今日重点关注-重大案事件：jrzdgz_zdasj
     */
    private String pinModule;

    /**
     * 上屏展示的标题内容
     */
    private String showTitle;

    /**
     * 数据日期（yyyy-MM-dd HH:mm:ss）
     */
    private String dataTime;

    /**
     * 经度
     */
    private Double jd;

    /**
     * 纬度
     */
    private Double wd;

    /**
     * 上屏开始时间（yyyy-MM-dd HH:mm:ss）
     */
    private String startTime;

    /**
     * 上屏结束时间（yyyy-MM-dd HH:mm:ss）
     */
    private String endTime;

    /**
     * 自定义内容（JSON格式）
     * 不同对象类型可能需要有一些特殊的参数需要存储
     */
    private String customContent;

    @Override
    protected boolean checkParams() throws ServiceException {
        checkNotNull(getPinFlag(), new ParamInvalidException("钉住标识不能为空"));
        checkNotEmpty(getShowTitle(), new ParamInvalidException("上屏展示的标题内容不能为空"));
        return super.checkParams();
    }

    /**
     * toEntity<BR>
     *
     * @param currentUser 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/7 16:24
     */
    public BigScreenPinDataEntity toEntity(CurrentUser currentUser) {
        BigScreenPinDataEntity entity = new BigScreenPinDataEntity();
        entity.setDistrictCode(currentUser.getDept().getDistrictCode());
        entity.setObjType(getObjType());
        entity.setObjId(getObjId());
        entity.setPinModule(getPinModule());
        return entity;
    }
}
