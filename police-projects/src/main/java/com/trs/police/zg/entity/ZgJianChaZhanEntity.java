package com.trs.police.zg.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.datasource.starter.typehandler.GeometryTypeHandler;
import com.trs.police.zg.anno.ZiGongField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

/**
 * @ClassName BigScreenJianChaZhanEntity
 * @Description 自贡大屏 - 检查站
 * <AUTHOR>
 * @Date 2024/12/24 10:05
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "tb_projects_bigscreen_jianchazhan", autoResultMap = true)
public class ZgJianChaZhanEntity extends BaseZgJingWuSourceEntity {

    private String id;

    private String jczmc;

    /**
     * 检察站级别
     * 01：省级
     * 02：市级
     * 03：县级
     */
    private String jczjb;

    /**
     * 检查站类型
     * 01：固定检查站
     * 02：临时检查站
     * 03：移动检查站
     */
    private String jczlx;

    private String xxdz;

    private String xzqhdm;

    @ZiGongField(clazz = Double.class)
    private Double jd;

    @ZiGongField(clazz = Double.class)
    private Double wd;

    @TableField(jdbcType = JdbcType.OTHER, typeHandler = GeometryTypeHandler.class)
    private String zb;

    private String zbhash;

    private String code;

    /**
     * 道路类型
     * 02：高速
     * 03：国道
     * 04：快速
     * 06：省道
     * 07：县道
     * 08：乡道
     * 100：城市道路
     * 20：村道
     * 99：其他
     */
    private String dllx;

    private String fzr;

    private String lxdh;

    /**
     * 附件id，多值逗号分隔
     */
    private String fjid;

    /**
     * 全景图
     */
    private String qjfjid;

    /**
     * 执勤类型
     * 1：公安检查站
     * 2：临时治安卡点
     */
    private String zqlx;

    /**
     * 是否启动
     * 0：否
     * 1：是
     */
    private String sfqd;

}
