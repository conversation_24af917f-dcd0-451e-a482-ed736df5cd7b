package com.trs.police.zg.vo;

import com.trs.common.pojo.BaseVO;
import com.trs.police.common.core.vo.GeometryVO;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * @ClassName BaseJingwuAreaSourceVo
 * @Description 警务资源相关 点位信息VO
 * <AUTHOR>
 * @Date 2024/12/24 17:24
 **/
@Getter
@Setter
public class BaseJingwuSourceVo extends BaseVO {

    /**
     * 本地数据ID
     */
    private Long dataId;
    
    private Date crTime;
    
    private Date updateTime;

    /**
     * 第三方数据id
     */
    private String id;

    /**
     * 名称
     */
    private String name;

    /**
     * 类型
     */
    private String type;

    /**
     * 级别
     */
    private String level;

    /**
     * 地址信息
     */
    private String address;

    /**
     * 经度
     */
    private Double jd;

    /**
     * 纬度
     */
    private Double wd;

    /**
     * 坐标信息
     */
    private String zb;

    private String zbhash;

    /**
     * 行政区划代码
     */
    private String xzqhdm;

    /**
     * 区域位置信息
     */
    private List<GeometryVO> geometries;


    /*--------------------下方是统一部分信息--------------*/
    private String ssbmid;

    private String ssbm;

    private String ssbmdm;

    private String ssxgajid;

    private String ssxgaj;

    private String ssxgajdm;

    private String sssgajid;

    private String sssgaj;

    private String sssgajdm;

    /**
     * 数据来源
     * 1：PC
     * 2：手机端
     */
    private String xtSjly;

    /**
     * 数据状态
     * 0：注销
     * 1：正常
     * -1：封存
     */
    private String xtSjzt;

    /**
     * 删除标识
     * 0：未删除
     * 1：已删除
     */
    private String xtScbz;

    private String xtCjip;

    private Date xtCjsj;

    private String xtCjrId;

    private String xtCjr;

    private String xtCjbmdm;

    private String xtCjbmmc;

    private String xtZhgxip;

    private Date xtZhgxsj;

    private String xtZhgxr;

    private String xtZhgxrid;

    private String xtZhgxbmdm;

    private String xtZhgxbm;

    private String bz;

}
