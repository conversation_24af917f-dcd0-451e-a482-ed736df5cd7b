package com.trs.police.zg.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 离婚实体
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class ZgLiHunVO implements Serializable  {

    private Long id;

    /**
     * 区域代码
     */
    private String qydm;

    /**
     * 离婚登记日期
     */
    private String lhdjrq;

    /**
     * 离婚证字号
     */
    private String lhzzh;

    /**
     * 备注
     */
    private String bz;

    /**
     * 男方身份证号码
     */
    private String nfsfzhm;

    /**
     * 男方离婚登记证印刷号
     */
    private String nflhdjzysh;

    /**
     * 男方姓名
     */
    private String nfxm;

    /**
     * 男方民族
     */
    private String nfmz;

    /**
     * 男方住址
     */
    private String nfzz;

    /**
     * 男方邮政编码
     */
    private String nfyzbm;

    /**
     * 男方电话
     */
    private String nfdh;

    /**
     * 男方国籍
     */
    private String nfgj;

    /**
     * 男方职业
     */
    private String nfzy;

    /**
     * 男方文化程度
     */
    private String nfwhcd;

    /**
     * 男方是否再婚
     */
    private String nfsfzh;

    /**
     * 女方身份证号码
     */
    private String nvfsfzhm;

    /**
     * 女方离婚登记证印刷号
     */
    private String nvflhdjzysh;

    /**
     * 女方姓名
     */
    private String nvfxm;

    /**
     * 女方民族
     */
    private String nvfmz;

    /**
     * 女方住址
     */
    private String nvfzz;

    /**
     * 女方邮政编码
     */
    private String nvfyzbm;

    /**
     * 女方电话
     */
    private String nvfdh;

    /**
     * 女方国籍
     */
    private String nvfgj;

    /**
     * 女方职业
     */
    private String nvfzy;

    /**
     * 女方文化程度
     */
    private String nvfwhcd;

    /**
     * 女方是否再婚
     */
    private String nvfsfzh;

    /**
     * 操作标识
     */
    private String depActionFlag;

    /**
     * 操作时间
     */
    private String depActionTime;
}
