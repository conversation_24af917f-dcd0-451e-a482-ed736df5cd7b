package com.trs.police.zg.vo;

import com.trs.police.zg.anno.ZiGongField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 自贡信访记录
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class ZgXfxsVO implements Serializable {

    /**
     * 信访件编号
     */
    private String xfjbh;

    /**
     * 信访形式
     */
    private String xfxs;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 性别
     */
    private String xb;

    /**
     * 联系电话
     */
    private String lxdh;

    /**
     * 证件号码
     */
    private String zjhm;

    /**
     * 信访目的
     */
    private String xfmd;

    /**
     * 内容分类
     */
    private String nrfl;

    /**
     * 全国初次标志
     */
    private String ccbz;

    /**
     * 概况
     */
    private String gk;

    /**
     * 纳入满意度评价标志
     */
    private String pjbz;

    /**
     * 集体访（联名信）标志
     */
    private String jtfbz;

    /**
     * 登记人
     */
    private String djr;

    /**
     * 登记机构
     */
    private String djjg;

    /**
     * 登记时间
     */
    @ZiGongField(clazz = Date.class)
    private Date djsj;

    /**
     * 问题属地
     */
    private String wtsd;

    /**
     * 是否依法逐级走访
     */
    private String sfzjzf;

    /**
     * 信访人数
     */
    private String xfrs;

    /**
     * 对信访部门的评价
     */
    private String dxfbmpj;

    /**
     * 对责任部门的评价
     */
    private String dzrbmpj;

    /**
     * 信访部门评价状态
     */
    private String xfbmpjzt;

    /**
     * 责任单位评价状态
     */
    private String zrdwpjzt;

    /**
     * 数据标识
     */
    @ZiGongField(name = "dep_action_flag")
    private String depActionFlag;

    /**
     * 数据抽取时间
     */
    @ZiGongField(name = "dep_action_time", clazz = Date.class)
    private Date depActionTime;

    /**
     * 首次入库时间
     */
    @ZiGongField(name = "dep_firstenter_time", clazz = Date.class)
    private Date depFirstenterTime;
}
