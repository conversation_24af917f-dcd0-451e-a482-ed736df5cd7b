package com.trs.police.zg.vo;

import com.trs.police.zg.anno.ZiGongField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * zg从业人员实体
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class ZgCongYeRenYuanVO {

    /**
     * 信息主键编号
     */
    private String xxzjbh;

    /**
     * 业务主键
     */
    private String ywid;

    /**
     * 从业人员姓名
     */
    private String cyryxm;

    /**
     * 从业人员证件号码
     */
    private String cyryzjhm;

    /**
     * 从业人员出生日期
     */
    @ZiGongField(clazz = Date.class)
    private Date cyrycsrq;

    /**
     * 从业人员国籍
     */
    private String cyrygj;

    /**
     * 从业人员民族
     */
    private String cyrymz;

    /**
     * 从业人员职业类型
     */
    private String cyryzylx;

    /**
     * 从业人员联系电话
     */
    private String cyrylxdh;

    /**
     * 从业人员家庭住址
     */
    private String cyryjtzz;

    /**
     * 从业人员学历
     */
    private String cyryxl;

    /**
     * 从业人员毕业学校
     */
    private String cyrybyxx;

    /**
     * 从业单位名称
     */
    private String cydwmc;

    /**
     * 从业单位地址
     */
    private String cydwdz;

    /**
     * 所在职务
     */
    private String szzy;

    /**
     * 所在部门
     */
    private String szbm;

    /**
     * 从业开始日期
     */
    @ZiGongField(clazz = Date.class)
    private Date cyksrq;

    /**
     * 从业结束日期
     */
    @ZiGongField(clazz = Date.class)
    private Date cyjsrq;

    /**
     * 首次入库时间
     */
    @ZiGongField(clazz = Date.class)
    private Date scrksj;

    /**
     * 信息备注
     */
    private String xxbz;

    /**
     * 信息来源地区划
     */
    private String xxlydqh;

    /**
     * 信息来源描述
     */
    private String xxlyms;

    /**
     * 数据来源编号
     */
    private String sjlybh;

    /**
     * 数据标识
     */
    @ZiGongField(name = "dep_action_flag")
    private String depActionFlag;

    /**
     * 首次入库时间
     */
    @ZiGongField(name = "dep_firstenter_time", clazz = Date.class)
    private Date depFirstenterTime;

    /**
     * 数据抽取时间
     */
    @ZiGongField(name = "dep_action_time", clazz = Date.class)
    private Date depActionTime;

    /**
     * 业务来源描述
     */
    private String ywlyms;

    /**
     * 行政区划
     */
    private String xzqh;

    /**
     * 从业人员性别
     */
    private String cyryxb;

    /**
     * 从业资格证号
     */
    private String cyzgzh;

    /**
     * 老档案号
     */
    private String ldah;

    /**
     * 新档案号
     */
    private String xdah;

    /**
     * 发证日期
     */
    @ZiGongField(clazz = Date.class)
    private Date fzrq;

    /**
     * 资格证初领日期
     */
    @ZiGongField(clazz = Date.class)
    private Date clrq;

    /**
     * 有效期起
     */
    @ZiGongField(clazz = Date.class)
    private Date yxqstart;

    /**
     * 有效期止
     */
    @ZiGongField(clazz = Date.class)
    private Date yxqend;

    /**
     * 证书状态
     */
    private String zszt;
}
