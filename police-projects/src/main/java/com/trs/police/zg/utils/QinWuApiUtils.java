package com.trs.police.zg.utils;

import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.police.zg.entity.*;
import com.trs.police.zg.vo.*;
import com.trs.web.builder.util.BeanFactoryHolder;
import io.vavr.Tuple2;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.trs.common.base.PreConditionCheck.checkNotEmpty;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> zhang.yang  E-mail: <EMAIL>
 * @since ：2025/3/19 15:40
 * @version 1.0
 * @since 1.0
 */
@Component
public class QinWuApiUtils extends QinWuUtils {

    @Override
    public String key() {
        return "api";
    }

    /**
     * qwglXfbbZd<BR>
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/12/18 18:08
     */
    @Override
    public Tuple2<Long, List<XunFangBaoBeiZhongDuanVo>> qwglXfbbZd(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        var url = BeanFactoryHolder.getEnv().getProperty("com.trs.bigscreen.sync.zg.qwbb.xfbb_zd");
        checkNotEmpty(url, new ParamInvalidException("Url不能为空"));
        return doHttp(url, "510300380000-0100-00028", pageNum, pageSize, where, XunFangBaoBeiZhongDuanVo.class);
    }

    /**
     * qwglZbbb<BR>
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/12/13 20:11
     */
    @Override
    public Tuple2<Long, List<ZhiBanBaoBeiVo>> qwglZbbb(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        var url = BeanFactoryHolder.getEnv().getProperty("com.trs.bigscreen.sync.zg.qwbb.qwgl_zbbb");
        checkNotEmpty(url, new ParamInvalidException("Url不能为空"));
        return doHttp(url, "510300380000-0100-00038", pageNum, pageSize, where, ZhiBanBaoBeiVo.class);
    }

    /**
     * qwglXfbb<BR>
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/12/17 14:14
     */
    @Override
    public Tuple2<Long, List<XunFangBaoBeiVo>> qwglXfbb(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        var url = BeanFactoryHolder.getEnv().getProperty("com.trs.bigscreen.sync.zg.qwbb.qwgl_xfbb");
        checkNotEmpty(url, new ParamInvalidException("Url不能为空"));
        return doHttp(url, "510300380000-0100-00032", pageNum, pageSize, where, XunFangBaoBeiVo.class);
    }

    /**
     * xfbbCl<BR>
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/12/17 14:30
     */
    @Override
    public Tuple2<Long, List<XunFangBaoBeiCheLiangVo>> xfbbCl(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        var url = BeanFactoryHolder.getEnv().getProperty("com.trs.bigscreen.sync.zg.qwbb.xfbb_cl");
        checkNotEmpty(url, new ParamInvalidException("Url不能为空"));
        return doHttp(url, "510300380000-0100-00029", pageNum, pageSize, where, XunFangBaoBeiCheLiangVo.class);
    }

    /**
     * xfbbCl<BR>
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/12/17 14:30
     */
    @Override
    public Tuple2<Long, List<XunFangBaoBeiRenYuanVo>> xfbbRy(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        var url = BeanFactoryHolder.getEnv().getProperty("com.trs.bigscreen.sync.zg.qwbb.xfbb_ry");
        checkNotEmpty(url, new ParamInvalidException("Url不能为空"));
        return doHttp(url, "510300380000-0100-00031", pageNum, pageSize, where, XunFangBaoBeiRenYuanVo.class);
    }

    /**
     * zbbbRy<BR>
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/12/13 20:11
     */
    @Override
    public Tuple2<Long, List<ZhiBanBaoBeiRenYuanVo>> zbbbRy(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        var url = BeanFactoryHolder.getEnv().getProperty("com.trs.bigscreen.sync.zg.qwbb.zbbb_ry");
        checkNotEmpty(url, new ParamInvalidException("Url不能为空"));
        return doHttp(url, "510300380000-0100-00039", pageNum, pageSize, where, ZhiBanBaoBeiRenYuanVo.class);
    }

    /**
     * xfll<BR>
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/12/13 18:19
     */
    @Override
    public Tuple2<Long, List<XunFangLiLiangVo>> xfll(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        var url = BeanFactoryHolder.getEnv().getProperty("com.trs.bigscreen.sync.zg.qwbb.xfll");
        checkNotEmpty(url, new ParamInvalidException("Url不能为空"));
        return doHttp(url, "510300380000-0100-00024", pageNum, pageSize, where, XunFangLiLiangVo.class);
    }

    /**
     * wzSb<BR>
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/12/13 18:19
     */
    @Override
    public Tuple2<Long, List<SheBeiLiShiWeiZhiVo>> wzSb(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        var url = BeanFactoryHolder.getEnv().getProperty("com.trs.bigscreen.sync.zg.qwbb.wz_sb");
        checkNotEmpty(url, new ParamInvalidException("Url不能为空"));
        return doHttp(url, "510300380000-0100-00040", pageNum, pageSize, where, SheBeiLiShiWeiZhiVo.class);
    }

    /**
     * xfcl<BR>
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/12/13 18:19
     */
    @Override
    public Tuple2<Long, List<XunFangCheLiangVo>> xfcl(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        var url = BeanFactoryHolder.getEnv().getProperty("com.trs.bigscreen.sync.zg.qwbb.xfcl");
        checkNotEmpty(url, new ParamInvalidException("Url不能为空"));
        return doHttp(url, "510300380000-0100-00019", pageNum, pageSize, where, XunFangCheLiangVo.class);
    }

    /**
     * xfcl<BR>
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/12/13 18:19
     */
    @Override
    public Tuple2<Long, List<QinWuGuanLiXunFangQuYuVo>> qwglXfqy(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        var url = BeanFactoryHolder.getEnv().getProperty("com.trs.bigscreen.sync.zg.qwbb.qwgl_xfqy");
        checkNotEmpty(url, new ParamInvalidException("Url不能为空"));
        return doHttp(url, "510300380000-0100-00035", pageNum, pageSize, where, QinWuGuanLiXunFangQuYuVo.class);
    }

    /**
     * 获取警务站数据
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    条件
     * @return 结果
     * @throws ServiceException 相关异常
     */
    @Override
    public Tuple2<Long, List<ZgJingWuZhanEntity>> getJwz(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        var url = BeanFactoryHolder.getEnv().getProperty("com.trs.bigscreen.sync.zg.qwbb.jwz");
        checkNotEmpty(url, new ParamInvalidException("Url不能为空"));
        return doHttp(url, "510300380000-0100-00023", pageNum, pageSize, where, ZgJingWuZhanEntity.class);
    }

    /**
     * 获取快反点数据
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    条件
     * @return 结果
     * @throws ServiceException 相关异常
     */
    @Override
    public Tuple2<Long, List<ZgKuaiFanDianEntity>> getKfd(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        var url = BeanFactoryHolder.getEnv().getProperty("com.trs.bigscreen.sync.zg.qwbb.kfd");
        checkNotEmpty(url, new ParamInvalidException("Url不能为空"));
        return doHttp(url, "510300380000-0100-00021", pageNum, pageSize, where, ZgKuaiFanDianEntity.class);
    }

    /**
     * 获取必巡点数据
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    条件
     * @return 结果
     * @throws ServiceException 相关异常
     */
    @Override
    public Tuple2<Long, List<ZgBiXunDianEntity>> getBxd(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        var url = BeanFactoryHolder.getEnv().getProperty("com.trs.bigscreen.sync.zg.qwbb.bxd");
        checkNotEmpty(url, new ParamInvalidException("Url不能为空"));
        return doHttp(url, "510300380000-0100-00037", pageNum, pageSize, where, ZgBiXunDianEntity.class);
    }

    /**
     * 获取必巡线数据
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    条件
     * @return 结果
     * @throws ServiceException 相关异常
     */
    @Override
    public Tuple2<Long, List<ZgBiXunXianEntity>> getBxx(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        var url = BeanFactoryHolder.getEnv().getProperty("com.trs.bigscreen.sync.zg.qwbb.bxx");
        checkNotEmpty(url, new ParamInvalidException("Url不能为空"));
        return doHttp(url, "510300380000-0100-00041", pageNum, pageSize, where, ZgBiXunXianEntity.class);
    }

    /**
     * 获取巡防区域数据
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    条件
     * @return 结果
     * @throws ServiceException 相关异常
     */
    @Override
    public Tuple2<Long, List<ZgXunFangQuYuEntity>> getXfqy(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        var url = BeanFactoryHolder.getEnv().getProperty("com.trs.bigscreen.sync.zg.qwbb.xfqy");
        checkNotEmpty(url, new ParamInvalidException("Url不能为空"));
        return doHttp(url, "510300380000-0100-00026", pageNum, pageSize, where, ZgXunFangQuYuEntity.class);
    }

    /**
     * 检查站
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    条件
     * @return 结果
     * @throws ServiceException 相关异常
     */
    @Override
    public Tuple2<Long, List<ZgJianChaZhanEntity>> getJcz(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        var url = BeanFactoryHolder.getEnv().getProperty("com.trs.bigscreen.sync.zg.qwbb.jcz");
        checkNotEmpty(url, new ParamInvalidException("Url不能为空"));
        return doHttp(url, "510300380000-0100-00030", pageNum, pageSize, where, ZgJianChaZhanEntity.class);
    }

    /**
     * 获取圈层数据
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    条件
     * @return 结果
     * @throws ServiceException 相关异常
     */
    @Override
    public Tuple2<Long, List<ZgQuanCengEntity>> getQc(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        var url = BeanFactoryHolder.getEnv().getProperty("com.trs.bigscreen.sync.zg.qwbb.qc");
        checkNotEmpty(url, new ParamInvalidException("Url不能为空"));
        return doHttp(url, "510300380000-0100-00027", pageNum, pageSize, where, ZgQuanCengEntity.class);
    }

    /**
     * 获取报备数据
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    条件
     * @return 结果
     * @throws ServiceException 相关异常
     */
    @Override
    public Tuple2<Long, List<ZgBaoBeiEntity>> getBb(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        var url = BeanFactoryHolder.getEnv().getProperty("com.trs.bigscreen.sync.zg.qwbb.qwgl_xfbb");
        checkNotEmpty(url, new ParamInvalidException("Url不能为空"));
        return doHttp(url, "510300380000-0100-00056", pageNum, pageSize, where, ZgBaoBeiEntity.class);
    }

    /**
     * qwglXfbbCl<BR>
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    参数
     * @return 结果
     * @throws ServiceException 异常
     */
    @Override
    public Tuple2<Long, List<XunFangBaoBeiCheLiangVo>> qwglXfbbCl(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        var url = BeanFactoryHolder.getEnv().getProperty("com.trs.bigscreen.sync.zg.qwbb.xfbb_cl");
        checkNotEmpty(url, new ParamInvalidException("Url不能为空"));
        return doHttp(url, "510300380000-0100-00029", pageNum, pageSize, where, XunFangBaoBeiCheLiangVo.class);
    }

    /**
     * qwglXfbbRy<BR>
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    参数
     * @return 结果
     * @throws ServiceException 异常
     */
    @Override
    public Tuple2<Long, List<XunFangBaoBeiRenYuanVo>> qwglXfbbRy(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        var url = BeanFactoryHolder.getEnv().getProperty("com.trs.bigscreen.sync.zg.qwbb.xfbb_ry");
        checkNotEmpty(url, new ParamInvalidException("Url不能为空"));
        return doHttp(url, "510300380000-0100-00031", pageNum, pageSize, where, XunFangBaoBeiRenYuanVo.class);
    }

    /**
     * qwglXfbbQx<BR>
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    参数
     * @return 结果
     * @throws ServiceException 异常
     */
    @Override
    public Tuple2<Long, List<XunFangBaoBeiQiXieVo>> qwglXfbbQx(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        var url = BeanFactoryHolder.getEnv().getProperty("com.trs.bigscreen.sync.zg.qwbb.xfbb_qx");
        checkNotEmpty(url, new ParamInvalidException("Url不能为空"));
        return doHttp(url, "510300380000-0100-00031", pageNum, pageSize, where, XunFangBaoBeiQiXieVo.class);
    }

    /**
     * qwglJingZu<BR>
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    参数
     * @return 结果
     * @throws ServiceException 异常
     */
    @Override
    public Tuple2<Long, List<JingZuVo>> qwglJingZu(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        var url = BeanFactoryHolder.getEnv().getProperty("com.trs.bigscreen.sync.zg.qwbb.jz");
        checkNotEmpty(url, new ParamInvalidException("Url不能为空"));
        return doHttp(url, "510300380000-0100-00034", pageNum, pageSize, where, JingZuVo.class);
    }

}
