package com.trs.police.st.portal.domain.dto.response;

import com.trs.police.st.portal.constant.enums.CalenderInfoTypeEnum;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * 获取日历详情返回
 */

@Data
public class GetCalenderDetailResponse {

    /**
     * 日历主键
     */
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 开始日期
     */
    private LocalDate startDate;

    /**
     * 结束日期
     */
    private LocalDate endDate;

    /**
     * 当前日期
     */
    private LocalDate date;

    /**
     * 类型
     */
    private CalenderInfoTypeEnum type;

    /**
     * 备注
     */
    private String remark;

    /**
     * 调班日
     */
    private List<LocalDate> adjustDate;

    /**
     * 指挥日志id，为空则代表当天没有日志
     */
    private Long commandLogId;
}
