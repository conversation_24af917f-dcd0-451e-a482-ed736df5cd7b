package com.trs.police.st.portal.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import lombok.Data;

import java.time.LocalDate;

/**
 * 日历导出DTO
 *
 */

@Data
//@ColumnWidth(20)
//@HeadRowHeight(40)
//@ContentRowHeight(60)
@ExcelIgnoreUnannotated
public class CalenderExportDto {

    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonFormat(pattern = "yyyy/MM/dd")
    @ColumnWidth(13)
    @ExcelProperty("日期")
    private LocalDate date;

    /**
     * 应急
     */
    @ExcelProperty("勤务等级")
    private String level;

    /**
     * 五一国际劳动节
     */
    @ExcelProperty("节假日")
    private String holiday;

    /**
     * 已提交/未提交
     */
    @ExcelProperty("指挥日志")
    private String log;

    /**
     * 违法乱纪活动集中整顿日
     * 日期：2025-01-22至2025-01-28
     * 备注：这里是备注内容xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
     */
    @ExcelProperty("敏感日")
//    @ColumnWidth(30)
    private String sensitiveDay;

    /**
     * 张三
     * （xx职务）
     * 13600122210"
     */
    @ExcelProperty("值班领导（主班）")
    private String zbldzb;

    @ExcelProperty("值班领导（副班）")
    private String zbldfb;

    @ExcelProperty("指挥长（主班）")
    private String zhzzb;

    @ExcelProperty("指挥长（副班）")
    private String zhzfb;

    @ExcelProperty("调度岗")
    private String ddg;

    @ExcelProperty("技术岗")
    private String jsg;

    @ExcelProperty("治安岗")
    private String zag;
}
