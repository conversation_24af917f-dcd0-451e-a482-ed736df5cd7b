package com.trs.police.st.portal.service.impl;

import com.alibaba.fastjson.JSON;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.st.portal.domain.dto.WritingLogDTO;
import com.trs.police.st.portal.domain.dto.WritingLogSearchDTO;
import com.trs.police.st.portal.domain.entity.BaseCommandLogEntity;
import com.trs.police.st.portal.domain.vo.FormInfoVO;
import com.trs.police.st.portal.service.AbsCommandLogAnalysis;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: dingkeyu
 * @date: 2024/11/30
 * @description:
 * @param <T>  T
 */
@Component
public abstract class BaseFormInfoAnalysis<T extends BaseCommandLogEntity> implements AbsCommandLogAnalysis<WritingLogSearchDTO, WritingLogDTO> {

    @Override
    public void writingLog(WritingLogDTO dto) throws Exception {
        FormInfoVO formInfoVO = dto.getFormInfoVO();
        T entity = createEntity();
        entity.setId(dto.getId());
        entity.setCreateTime(StringUtils.isEmpty(dto.getDutyTime()) ? new Date() : TimeUtils.stringToDate(dto.getDutyTime()));
        for (FormInfoVO.FormVO formVO : formInfoVO.getFormVO()) {
            Field field = entity.getClass().getDeclaredField(formVO.getCode());
            field.setAccessible(true);
            field.set(entity, JSON.toJSONString(formVO.getValues()));
        }
        saveOrUpdate(entity);
    }

    protected List<FormInfoVO.FormVO > getFormVO(T entity) {
        List<FormInfoVO.FormVO> formVos = new ArrayList<>();
        Class<?> instance = entity.getClass();
        Field[] fields = instance.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                // 获取字段值并添加到列表中
                String value = (String) field.get(entity);
                FormInfoVO.FormVO formVO = new FormInfoVO.FormVO();
                formVO.setCode(field.getName());
                formVO.setName(getDictByCode(field.getName()).getName());
                formVO.setValues(JSON.parseArray(value, String.class));
                formVO.setFlag(value == null ? Boolean.FALSE : Boolean.TRUE);
                formVO.setOrder(getDictByCode(field.getName()).getShowNumber());
                formVos.add(formVO);
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        return formVos;
    }

    @Override
    public void delete(Long id) {

    }

    @Override
    public void export(HttpServletResponse response, WritingLogSearchDTO dto) {

    }

    protected List<FormInfoVO.FormVO> initFormVO(Map<String, DictDto> dictMap) {
        List<FormInfoVO.FormVO> list = new ArrayList<>();
        dictMap.forEach((k, v) -> {
            FormInfoVO.FormVO formVO = new FormInfoVO.FormVO(k, v.getName(), false, v.getShowNumber(), null);
            list.add(formVO);
        });
        return list.stream()
                .sorted(Comparator.comparing(FormInfoVO.FormVO::getOrder))
                .collect(Collectors.toList());
    }

    protected abstract T createEntity();

    protected abstract void saveOrUpdate(T entity);

    protected abstract DictDto getDictByCode(String code);
}
