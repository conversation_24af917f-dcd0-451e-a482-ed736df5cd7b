package com.trs.police.st.portal.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.trs.police.common.core.handler.typehandler.JsonToStringListHandler;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 指挥日志-值班接警情况表
 */

@Data
@TableName(value = "t_command_log_receive_incident", autoResultMap = true)
public class CommandLogReceiveIncidentEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建用户id
     */
    @TableField(fill = FieldFill.INSERT, value = "create_user_id")
    private Long createUserId;

    /**
     * 创建部门id
     */
    @TableField(fill = FieldFill.INSERT, value = "create_dept_id")
    private Long createDeptId;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT, value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新用户id
     */
    @TableField(fill = FieldFill.INSERT_UPDATE, value = "update_user_id")
    private Long updateUserId;

    /**
     * 更新部门id
     */
    @TableField(fill = FieldFill.INSERT_UPDATE, value = "update_dept_id")
    private Long updateDeptId;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE, value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 接警时间
     */
    private LocalDateTime receiveTime;

    /**
     * 办理时限
     */
    private LocalDateTime untilTime;

    /**
     * 接警地域
     */
    private String receiveDistrict;

    /**
     * 责任单位/人
     */
    private String dutyObject;

    /**
     * 标题
     */
    private String title;

    /**
     * 处置情况
     */
    @TableField(typeHandler = JsonToStringListHandler.class)
    private List<String> disposal;

    /**
     * 是否办结
     */
    private Boolean ifFinished;

    /**
     * 是否盯办
     */
    private Boolean ifMonitor;

    /**
     * 备注
     */
    @TableField(typeHandler = JsonToStringListHandler.class)
    private List<String> remark;

    /**
     * 是否关注
     */
    private Boolean ifFocus;

    /**
     * 记录id，由前端生成
     */
    private String recordId;

    /**
     * 是否合并
     */
    private Boolean isMerged;

    /**
     * 合并的父id
     */
    private String mergedParentId;

    /**
     * 主表id
     */
    private Long infoId;

    /**
     * 是否往期
     */
    private Boolean ifPast;
}
