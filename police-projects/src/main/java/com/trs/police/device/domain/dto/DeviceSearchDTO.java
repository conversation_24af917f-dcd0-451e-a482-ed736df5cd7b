package com.trs.police.device.domain.dto;

import com.trs.common.exception.ServiceException;
import com.trs.common.pojo.BaseListDTO;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.core.vo.GeometryVO;
import lombok.Data;

import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> zhang.yang  E-mail: <EMAIL>
 * @since 创建时间：2025/2/24 17:02
 * @version 1.0
 * @since 1.0
 */
@Data
public class DeviceSearchDTO extends BaseListDTO {

    private String deviceNo;

    private String deviceType;

    private String geometries;

    private String searchField;

    private String searchValue;

    private String districtCode;

    @Override
    protected boolean checkParams() throws ServiceException {
        return true;
    }

    /**
     * makeGeometries<BR>
     *
     * @return 结果
     * @since 创建时间：2024/8/27 16:06
     */
    public List<GeometryVO> makeGeometries() {
        if (StringUtils.isEmpty(getGeometries())) {
            return null;
        }
        return JsonUtil.parseArray(getGeometries(), GeometryVO.class);
    }
}
