package com.trs.police.gx.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Data;

@Data
public class ExportTicketsInfoVO {

    @ExcelProperty("演出时间")
    @ColumnWidth(30)
    @ContentStyle(horizontalAlignment= HorizontalAlignmentEnum.CENTER)
    private String gameTime;

    @ExcelProperty("场次时间")
    @ColumnWidth(30)
    @ContentStyle(horizontalAlignment= HorizontalAlignmentEnum.CENTER)
    private String matchTime;

    @ExcelProperty("场馆名称")
    @ColumnWidth(30)
    @ContentStyle(horizontalAlignment= HorizontalAlignmentEnum.CENTER)
    private String venueName;

    @ExcelProperty("座位：排")
    @ColumnWidth(20)
    @ContentStyle(horizontalAlignment= HorizontalAlignmentEnum.CENTER)
    private Integer seatRow;

    @ExcelProperty("座位：号")
    @ColumnWidth(20)
    @ContentStyle(horizontalAlignment= HorizontalAlignmentEnum.CENTER)
    private Integer seatNO;

    @ExcelProperty("票单ID")
    @ColumnWidth(20)
    @ContentStyle(horizontalAlignment= HorizontalAlignmentEnum.CENTER)
    private String ticketNO;

    @ExcelProperty("票名名称")
    @ColumnWidth(20)
    @ContentStyle(horizontalAlignment= HorizontalAlignmentEnum.CENTER)
    private String ticketName;

    @ExcelProperty("姓名")
    @ColumnWidth(20)
    @ContentStyle(horizontalAlignment= HorizontalAlignmentEnum.CENTER)
    private String userName;

    @ExcelProperty("证件号")
    @ColumnWidth(20)
    @ContentStyle(horizontalAlignment= HorizontalAlignmentEnum.CENTER)
    private String userIdNumber;

}
