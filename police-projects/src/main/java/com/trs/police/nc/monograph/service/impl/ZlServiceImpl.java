package com.trs.police.nc.monograph.service.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.nc.monograph.dto.CommandDTO;
import com.trs.police.nc.monograph.dto.CommandWspzxx;
import com.trs.police.nc.monograph.service.ZlService;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;

import javax.net.ssl.SSLContext;
import java.security.cert.X509Certificate;

import java.util.Map;

import java.util.HashMap;

/**
 * 指令服务实现类
 *
 * <AUTHOR>
 */
@Component
@ConditionalOnProperty(name = "ys.fight.collaboration.approval.version", havingValue = "nc", matchIfMissing = true)
public class ZlServiceImpl implements ZlService {

    private RestTemplate restTemplate = restTemplate();

    @Override
    public String getnavigation(String idCard) {
        // 获取服务地址
        String baseUrl = getServiceUrl(); // url 从配置中获取

        // 拼接完整 URL
        String requestUrl = String.format("%s/center-smp-northface-service/api/userInfo/systemSortByIdentitySign?identitySign=%s", baseUrl, idCard);

        // 调用接口并接收原始响应字符串
        return restTemplate.getForObject(requestUrl, String.class);
    }

    @Override
    public String getToken() {
        // 构建请求参数
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("clientType", 1);
        requestBody.put("identitySign", AuthHelper.getCurrentUser().getIdNumber());
        requestBody.put("passWord", "111111");
        requestBody.put("terminalTag", "");

        // 获取服务地址
        String baseUrl = getServiceUrl();
        String url = String.format("%s/commandcenter-uaa-service/auth/login/dev", baseUrl);

        // 发送 POST 请求
        return restTemplate.postForObject(url, requestBody, String.class);
    }


    @Override
    public CommandWspzxx getWspzxx() {
        // 获取服务地址
        String baseUrl = getServiceUrl(); // url 从配置中获取

        // 拼接完整 URL
        String requestUrl = baseUrl + "/commandcenter-ifs-order-new/officialNumber/message/order?xxzlxDm=0103&zlflDm=1";

        // 调用接口并接收原始响应字符串
        String response = restTemplate.getForObject(requestUrl, String.class);

        // 解析 JSON 响应
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            // 先解析为通用结构
            JsonNode rootNode = objectMapper.readTree(response);
            JsonNode dataNode = rootNode.get("data");

            // 反序列化为 CommandWspzxx 对象
            return objectMapper.treeToValue(dataNode, CommandWspzxx.class);
        } catch (Exception e) {
            throw new TRSException("Failed to parse response", e);
        }
    }

    @Override
    public String sendCommand(CommandDTO commandDTO, Map<String, byte[]> files) {
        // 创建请求头，设置Content-Type为multipart/form-data
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        // 构建请求体
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();

        // 添加JSON数据
        body.add("jsonData", JSON.toJSONString(commandDTO));

        // 添加文件数据
        if (files != null && !files.isEmpty()) {
            for (Map.Entry<String, byte[]> entry : files.entrySet()) {
                String filename = entry.getKey();
                byte[] fileContent = entry.getValue();
                ByteArrayResource resource = new ByteArrayResource(fileContent) {
                    @Override
                    public String getFilename() {
                        return filename;
                    }
                };
                body.add("files", resource);
            }
        }

        // 创建请求实体
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

        // 发送请求
        String url = getServiceUrl() + "/commandcenter-ifs-order-new/api/message/order";
        ResponseEntity<String> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                String.class
        );
        // 返回响应
        return response.getBody();
    }

    private String getServiceUrl() {
        return BeanFactoryHolder.getEnv().getProperty("com.trs.monograph.zl.url");
    }

    private RestTemplate restTemplate() {
        try {
            // 创建一个信任所有证书的 TrustStrategy
            TrustStrategy acceptingTrustStrategy = (X509Certificate[] chain, String authType) -> true;

            // 创建 SSLContext 并使用我们的 TrustStrategy
            SSLContext sslContext = org.apache.http.ssl.SSLContexts.custom()
                    .loadTrustMaterial(null, acceptingTrustStrategy)
                    .build();

            // 创建不验证主机名的 SSL Socket Factory
            SSLConnectionSocketFactory csf = new SSLConnectionSocketFactory(sslContext, NoopHostnameVerifier.INSTANCE);

            // 创建 HttpClient
            CloseableHttpClient httpClient = HttpClients.custom()
                    .setSSLSocketFactory(csf)
                    .build();

            // 使用自定义的 HttpClient 创建 HttpComponentsClientHttpRequestFactory
            HttpComponentsClientHttpRequestFactory requestFactory =
                    new HttpComponentsClientHttpRequestFactory();
            requestFactory.setHttpClient(httpClient);

            // 创建并返回 RestTemplate
            return new RestTemplate(requestFactory);
        } catch (Exception e) {
            throw new RuntimeException("Failed to create RestTemplate", e);
        }
    }
}
