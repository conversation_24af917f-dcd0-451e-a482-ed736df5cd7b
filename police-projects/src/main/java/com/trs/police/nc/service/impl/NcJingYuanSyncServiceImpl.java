package com.trs.police.nc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.police.bigscreen.domain.entity.BigScreenJingYuanEntity;
import com.trs.police.bigscreen.mapper.BigScreenJingYuanMapper;
import com.trs.police.bigscreen.service.BaseJingYuanSyncService;
import com.trs.police.common.core.mapper.SyncTaskMapper;
import com.trs.police.nc.domain.entity.UserGpsEntity;
import com.trs.police.nc.mapper.UserGpsEntityMapper;
import io.vavr.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 警员信息同步
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-11-21 16:45:49
 */
@Slf4j
@Service
@ConditionalOnProperty(value = "com.trs.bigscreen.system.area", havingValue = "nc")
public class NcJingYuanSyncServiceImpl extends BaseJingYuanSyncService<UserGpsEntity> {

    private UserGpsEntityMapper userGpsEntityMapper;

    public NcJingYuanSyncServiceImpl(
            UserGpsEntityMapper userGpsEntityMapper,
            BigScreenJingYuanMapper jingYuanMapper,
            SyncTaskMapper syncTaskMapper,
            RedisTemplate<String, Object> redisTemplate
    ) {
        super(jingYuanMapper, syncTaskMapper, redisTemplate);
        this.userGpsEntityMapper = userGpsEntityMapper;
    }

    @Override
    public Tuple2<List<UserGpsEntity>, String> findInData(String startTime, String endTime) {
        return new Tuple2<>(userGpsEntityMapper.selectList(
                new QueryWrapper<UserGpsEntity>().lambda()), null);
    }

    @Override
    public BigScreenJingYuanEntity convert(UserGpsEntity in) {
        BigScreenJingYuanEntity entity = new BigScreenJingYuanEntity();
        // 单位名
        entity.setUnitName(in.getOrgName());
        // 地域编码
        entity.setDistrictCode("511300000000");
        // 值守组
//        entity.setZsz();
        // 出警状态
        entity.setCjzt(1);
        // 报备状态
        entity.setBbzt(string2integer(in.getReportStatus()));
        // 在线状态
        entity.setZxzt(string2integer(in.getIsOnline()));
        // 是否可用
        entity.setStatus(1);
        // 当前经度
        //南充市的第三方库把经纬度搞反了
//        entity.setDqwzJd(in.getLongitude());
        entity.setDqwzJd(in.getLatitude());
        // 当前纬度
//        entity.setDqwzWd(in.getLatitude());
        entity.setDqwzWd(in.getLongitude());
        // 位置名称
//        entity.setDqwzMc();
        // 最后上报时间
        entity.setLastReportTime(Objects.nonNull(in.getGpsDatetime()) ? in.getGpsDatetime() : new Date());
        // 姓名
        entity.setXm(in.getStaffName());
        // 电话
        entity.setDh(in.getStaffMobile());
        // 警号
        entity.setJh(in.getStaffCode());
        // 人员类型
        entity.setRylx(in.getStaffType());
        // 出警数
        entity.setCjs(1);
        return entity;
    }

    private Integer string2integer(String string) {
        try {
            return Integer.valueOf(string);
        } catch (Exception e) {
            log.error("转换异常{}", e.getMessage(), e);
            return 1;
        }
    }


    @Override
    public String key() {
        return "NcJingYuan";
    }

    @Override
    public String desc() {
        return "南充JY信息同步";
    }
}
