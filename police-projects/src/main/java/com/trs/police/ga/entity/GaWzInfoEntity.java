package com.trs.police.ga.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.datasource.starter.typehandler.GeometryTypeHandler;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 暂未开通（广安）
 *
 * <AUTHOR> zhang.yang  E-mail: <EMAIL>
 * @since 创建时间：2025/3/20 15:20
 * @version 1.0
 * @since 1.0
 */
@Data
@TableName("zg_tb_wz_sblswz")
public class GaWzInfoEntity implements Serializable {

    @TableId
    private String id;

    @TableField
    private String sjly;

    @TableField
    private String sjlydm;

    @TableField
    private String ywid;

    @TableField
    private String sbid;

    @TableField
    private String sbmc;

    @TableField
    private String yhId;

    @TableField
    private String yhXm;

    @TableField
    private String yhSfzh;

    @TableField
    private String jzId;

    @TableField
    private String jzMc;

    @TableField
    private String dwrq;

    @TableField
    private String dwsj;

    @TableField
    private Double jd;

    @TableField
    private Double wd;

    @TableField(jdbcType = JdbcType.OTHER, typeHandler = GeometryTypeHandler.class)
    private String zb;

    @TableField
    private String zbhash;

    @TableField
    private String wztgz;

    @TableField
    private String guo;

    @TableField
    private String sheng;

    @TableField
    private String shi;

    @TableField
    private String csbm;

    @TableField
    private String ssqx;

    @TableField
    private String qxbm;

    @TableField
    private String dzxz;

    @TableField
    private String dzjc;

    @TableField
    private String dwbz;

    @TableField
    private Integer xfsc;

    @TableField
    private Integer xflc;

    @TableField
    private String ssbmid;

    @TableField
    private String ssbm;

    @TableField
    private String ssbmdm;

    @TableField
    private String ssxgajid;

    @TableField
    private String ssxgaj;

    @TableField
    private String ssxgajdm;

    @TableField
    private String sssgajid;

    @TableField
    private String sssgaj;

    @TableField
    private String sssgajdm;

    @TableField
    private String xtSjly;

    @TableField
    private String xtSjzt;

    @TableField
    private String xtScbz;

    @TableField
    private String xtCjip;

    @TableField
    private Timestamp xtCjsj;

    @TableField
    private String xtCjrId;

    @TableField
    private String xtCjr;

    @TableField
    private String xtCjbmdm;

    @TableField
    private String xtCjbmmc;

    @TableField
    private String xtZhgxip;

    @TableField
    private Timestamp xtZhgxsj;

    @TableField
    private String xtZhgxrid;

    @TableField
    private String xtZhgxr;

    @TableField
    private String xtZhgxbmdm;

    @TableField
    private String xtZhgxbm;

    @TableField
    private String bz;
}
