package com.trs.police.guangan.service.impl;


import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.trs.police.bigscreen.domain.entity.BigScreenJingYuanEntity;
import com.trs.police.bigscreen.mapper.BigScreenJingYuanMapper;
import com.trs.police.common.core.mapper.SyncTaskMapper;
import com.trs.police.guangan.domain.entity.ChongQingDataEntity;
import com.trs.police.guangan.vo.ChongQingKafkaVo;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/9/18 20:19
 * @since 1.0
 */
@Service
@ConditionalOnProperty(value = "com.trs.bigscreen.system.area", havingValue = "ga")
public class ChongQingJingYuanSyncService extends ChongQingDataSyncService<BigScreenJingYuanEntity> {

    private final BigScreenJingYuanMapper jingYuanMapper;

    public ChongQingJingYuanSyncService(
            ChongQingQueryServiceImpl chongQingQueryService,
            BigScreenJingYuanMapper jingYuanMapper,
            SyncTaskMapper syncTaskMapper,
            RedisTemplate<String, Object> redisTemplate
    ) {
        super(chongQingQueryService, syncTaskMapper, redisTemplate);
        this.jingYuanMapper = jingYuanMapper;
    }

    /**
     * supportDeviceClass<BR>
     *
     * @param deviceClass 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/18 20:22
     */
    @Override
    public Boolean supportDeviceClass(Integer deviceClass) {
        return List.of(2, 3, 6, 7).contains(deviceClass);
    }

    /**
     * convert<BR>
     *
     * @param o        参数
     * @param qingData 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/5 18:04
     */
    @Override
    public BigScreenJingYuanEntity convert(ChongQingKafkaVo o, ChongQingDataEntity qingData) {
        BigScreenJingYuanEntity entity = new BigScreenJingYuanEntity();
        entity.setUnitName(qingData.getDwmc());
        // 按照XMKFB-4315要求还原地域编码
        entity.setDistrictCode(qingData.getSjcjlyd());
        entity.setZsz(qingData.getDwmc());
        entity.setCjzt(1);
        entity.setBbzt(1);
        entity.setZxzt(1);
        entity.setStatus(1);
        entity.setDqwzJd(o.getLon());
        entity.setDqwzWd(o.getLat());
        entity.setDqwzMc(qingData.getDwmc());
        entity.setLastReportTime(new Date(o.makeTime()));
        entity.setXm(qingData.getLxrMc());
        entity.setJh(o.getDeviceCode());
        entity.setRylx(qingData.getSbeLb01Mc());
        entity.setCjs(1);
        return entity;
    }

    /**
     * save<BR>
     *
     * @param entity 参数
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/5 18:04
     */
    @Override
    public void save(BigScreenJingYuanEntity entity) {
        var opt = new LambdaQueryChainWrapper<>(jingYuanMapper)
                .eq(BigScreenJingYuanEntity::getJh, entity.getJh())
                .oneOpt();
        entity.setUpdateTime(new Date());
        if (opt.isPresent()) {
            entity.setId(opt.get().getId());
            entity.setCrTime(opt.get().getCrTime());
            jingYuanMapper.updateById(entity);
        } else {
            jingYuanMapper.insert(entity);
        }
        jingYuanMapper.updateDqwzPoint(entity.getId());
    }

    @Override
    public String key() {
        return "ChongQingJingYuanSyncService";
    }

    @Override
    public String desc() {
        return "警员数据同步";
    }
}
