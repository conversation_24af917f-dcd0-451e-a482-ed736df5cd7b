package com.trs.police.guangan.service.impl;

import com.trs.police.bigscreen.domain.entity.BigScreenJingCheEntity;
import com.trs.police.bigscreen.mapper.BigScreenJingCheMapper;
import com.trs.police.bigscreen.service.BaseJingCheSyncService;
import com.trs.police.common.core.mapper.SyncTaskMapper;
import com.trs.police.guangan.domain.entity.GaJingWuZiYuanEntity;
import com.trs.police.guangan.mapper.GaJingWuZiYuanMapper;
import io.vavr.Tuple2;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/9/6 14:31
 * @since 1.0
 */
@Service
@ConditionalOnProperty(value = "com.trs.bigscreen.system.area", havingValue = "ga")
public class GaJingCheSyncService extends BaseJingCheSyncService<GaJingWuZiYuanEntity> {

    private final GaJingWuZiYuanMapper mapper;

    public GaJingCheSyncService(
            GaJingWuZiYuanMapper mapper,
            BigScreenJingCheMapper jingCheMapper,
            SyncTaskMapper syncTaskMapper,
            RedisTemplate<String, Object> redisTemplate
    ) {
        super(jingCheMapper, syncTaskMapper, redisTemplate);
        this.mapper = mapper;
    }

    /**
     * findInData<BR>
     *
     * @param startTime 参数
     * @param endTime   参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/5 18:05
     */
    @Override
    public Tuple2<List<GaJingWuZiYuanEntity>, String> findInData(String startTime, String endTime) {
        return new Tuple2<>(
                mapper.queryList(2),
                "a.GPSID is not null and a.GPSLB = 2 and t.REALTIME > SYSDATE-1/24/12;"
        );
    }

    /**
     * convert<BR>
     *
     * @param in 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/5 18:04
     */
    @Override
    public BigScreenJingCheEntity convert(GaJingWuZiYuanEntity in) {
        BigScreenJingCheEntity entity = new BigScreenJingCheEntity();
        entity.setUnitName(in.getSydwmc());
        entity.setDistrictCode(in.getSydwdm().substring(0, 6));
        entity.setZsz(in.getSydwmc());
        entity.setCjzt(1);
        entity.setBbzt(1);
        entity.setZxzt(1);
        entity.setStatus(1);
        entity.setDqwzJd(in.getX());
        entity.setDqwzWd(in.getY());
        entity.setDqwzMc(in.getSydwmc());
        entity.setLastReportTime(in.getRealtime());
        entity.setCphm(in.getBh());
        return entity;
    }

    @Override
    public String key() {
        return "GaJingChe";
    }

    @Override
    public String desc() {
        return "广安JC信息同步";
    }
}
