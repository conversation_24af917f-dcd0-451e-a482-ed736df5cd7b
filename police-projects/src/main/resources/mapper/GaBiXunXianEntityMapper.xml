<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.ga.mapper.GaBiXunXianEntityMapper">

    <select id="doPageSelect" resultType="com.trs.police.ga.entity.GaBiXunXianEntity">
        SELECT
        id,
        ss_xfqy_id as ssXfqyId,
        bxx_mc as bxxMc,
        bxx_lx as bxxLx,
        jz_id as jzId,
        xzqhdm,
        ST_AsText(zb) AS zb,
        ssbmid,
        ssbm,
        ssbmdm,
        ssxgajid,
        ssxgaj,
        ssxgajdm,
        sssgajid,
        sssgaj,
        sssgajdm,
        xt_sjly as xtSjly,
        xt_sjzt as xtSjzt,
        xt_scbz as xtScbz,
        xt_cjip as xtCjip,
        xt_cjsj as xtCjsj,
        xt_cjr_id as xtCjrId,
        xt_cjr as xtCjr,
        xt_cjbmdm as xtCjbmdm,
        xt_cjbmmc as xtCjbmmc,
        xt_zhgxip as xtZhgxip,
        xt_zhgxsj as xtZhgxsj,
        xt_zhgxrid as xtZhgxrid,
        xt_zhgxr as xtZhgxr,
        xt_zhgxbmdm as xtZhgxbmdm,
        xt_zhgxbm as xtZhgxbm,
        bz
        FROM
        v_tb_jcgl_bxx
        WHERE 1 = 1
        <if test="condition != null and condition != ''">
            and ${condition}
        </if>
        ORDER BY xt_cjsj
    </select>

    <select id="selectCount" resultType="java.lang.Long">
        SELECT
        count(1)
        FROM
        v_tb_jcgl_bxx
        WHERE 1 = 1
        <if test="condition != null and condition != ''">
            and ${condition}
        </if>
    </select>
</mapper>