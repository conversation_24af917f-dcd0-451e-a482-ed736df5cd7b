<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.nc.monograph.mapper.MonographCommentMapper">

    <select id="selectByIds" resultType="com.trs.police.nc.monograph.entity.MonographComment">
        select update_time,monograph_id,directory_id,comment,create_user_name,create_dept_name
        from t_monograph_comment
        where monograph_id = #{dto.monographId} and directory_id in
        <if test="dto.directoryIdList != null and dto.directoryIdList.size()>0">
            <foreach collection="dto.directoryIdList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>
</mapper>