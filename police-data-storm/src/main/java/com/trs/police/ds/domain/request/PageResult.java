package com.trs.police.ds.domain.request;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import lombok.Data;

/**
 * 分页结果
 *
 * @param <T> 封装的分页查询结果类型
 * <AUTHOR>
 */
@Data
public class PageResult<T> {

    private int pageNumber;
    private int pageSize;
    private long total;


    private List<T> items;


    private PageResult() {

    }

    /**
     * 由List构造PageResult
     *
     * @param items      {@link List}
     * @param pageNumber 页号
     * @param total      总数
     * @param pageSize   每页大小
     * @param <T>        泛型参数
     * @return {@link PageResult}
     */
    public static <T> PageResult<T> of(List<T> items, int pageNumber, long total, int pageSize) {

        final PageResult<T> pageResult = new PageResult<>();

        pageResult.setItems(items);
        pageResult.setPageNumber(pageNumber);
        pageResult.setTotal(total);
        pageResult.setPageSize(pageSize);

        return pageResult;
    }

    /**
     * 由List构造PageResult, 在此时分页
     *
     * @param items      {@link List}
     * @param pageParams {@link PageParams}
     * @param <T>        泛型参数
     * @return {@link PageResult}
     */
    public static <T> PageResult<T> of(List<T> items, PageParams pageParams) {
        return PageResult.of(pageList(items, pageParams), pageParams.getPageNumber(), items.size(),
            pageParams.getPageSize());
    }

    /**
     * list分页
     *
     * @param items list
     * @param pageParams 分页参数
     * @param <T> 类型
     * @return 结果
     */
    public static <T> List<T> pageList(List<T> items, PageParams pageParams) {
        final int beginIndex = pageParams.getOffset();
        if(items.size() < beginIndex) {
            return Collections.emptyList();
        }
        final int endIndex = Math.min(pageParams.getOffset() + pageParams.getPageSize(), items.size());
        return items.subList(beginIndex, endIndex);
    }

    /**
     * 创建空的PageResult
     *
     * @param pageNumber 页码
     * @param pageSize   每页大小
     * @param <T>        泛型参数
     * @return {@link PageResult}
     */
    public static <T> PageResult<T> empty(int pageNumber, int pageSize) {
        return PageResult.of(new ArrayList<>(), pageNumber, 0, pageSize);
    }

    /**
     * 创建空的PageResult
     *
     * @param pageParams 分页参数
     * @param <T>        泛型参数
     * @return {@link PageResult}
     */
    public static <T> PageResult<T> empty(PageParams pageParams) {
        return PageResult.of(new ArrayList<>(), pageParams.getPageNumber(), 0, pageParams.getPageSize());
    }
}
