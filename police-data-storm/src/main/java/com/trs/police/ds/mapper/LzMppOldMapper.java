package com.trs.police.ds.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.trs.police.ds.domain.request.DataStormRequest;
import com.trs.police.ds.domain.vo.MeasureDimension;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/26
 */
@Mapper
@DS("lz-mpp-old")
public interface LzMppOldMapper {


    /**
     * 禁毒卷宗分析
     *
     * @param request 请求参数
     * @return 数量
     */
    String getXsajCount(@Param("request") DataStormRequest request);

    /**
     * 禁毒卷宗分析
     *
     * @param request 请求参数
     * @return 数量
     */
    String getXzajCount(@Param("request") DataStormRequest request);

    /**
     * 吸毒人员排行
     *
     * @param request 请求参数
     * @return 数量
     */
    List<MeasureDimension> getXdryphb(@Param("request") DataStormRequest request);

    /**
     * 获取毒品种类
     *
     * @param drugName 毒品名字
     * @param request 参数
     * @return 数量
     */
    Integer getXddpzl(@Param("drugName") String drugName, @Param("request") DataStormRequest request);
}
