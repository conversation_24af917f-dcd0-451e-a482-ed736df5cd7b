package com.trs.police.ds.typehandler;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.trs.police.ds.utils.JsonUtil;
import java.sql.CallableStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * json转List Long
 *
 * <AUTHOR>
 * @date 2022/6/22 10:00
 */
public class JsonToLongListHandler extends JacksonTypeHandler {

    @Override
    protected List<Long> parse(String json) {
        if (StringUtils.isBlank(json)) {
            return new ArrayList<>();
        }
        return JsonUtil.parseArray(json, Long.class);
    }

    public JsonToLongListHandler(Class<?> type) {
        super(type);
    }

    @Override
    public List<Long> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return rs.getString(columnName) == null ? new ArrayList<>() : parse(rs.getString(columnName));
    }

    @Override
    public List<Long> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return rs.getString(columnIndex) == null ? new ArrayList<>() : parse(rs.getString(columnIndex));
    }

    @Override
    public List<Long> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return cs.getString(columnIndex) == null ? new ArrayList<>() : parse(cs.getString(columnIndex));
    }
}
