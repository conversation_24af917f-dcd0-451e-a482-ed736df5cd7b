package com.trs.police.ds.json.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.trs.police.ds.utils.JsonUtil;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/06/19
 */
public class StringToListDeserializer extends JsonDeserializer<List<Object>> {

    @Override
    public List<Object> deserialize(JsonParser parser, DeserializationContext context) throws IOException {
        String temp = parser.getValueAsString();
        return JsonUtil.parseArray(temp,Object.class);
    }
}