package com.trs.police.ds.controller;

import com.trs.police.ds.properties.GxUrlConfigurationProperties;
import com.trs.police.ds.service.GxService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/1/10
 */
@RestController
@RequestMapping("/gx")
public class GxController {

    @Resource
    GxService gxService;

    @Resource
    private GxUrlConfigurationProperties urlProperties;

    /**
     * 根据身份证号查询常驻人口信息
     *
     * @param request 参数
     * @return 数据
     */
    @PostMapping("/getResidentPeopleByCertNum")
    public Object getResidentPeopleByCertNum(@RequestBody Map<String, String> request) {
        String url = urlProperties.getGetResidentPeopleByCertNum();
        return gxService.requestSjjz(url, request);
    }

    /**
     * 根据身份证号查询人员轨迹v1
     *
     * @param request 参数
     * @return 数据
     */
    @PostMapping("/findPersonTrack")
    public Object findPersonTrack(@RequestBody Map<String, String> request) {
        String url = urlProperties.getFindPersonTrack();
        return gxService.requestSjjz(url, request);
    }

    /**
     * 根据身份证号查询人员居住轨迹
     *
     * @param request 参数
     * @return 数据
     */
    @PostMapping("/getLiveTrackByCertNum")
    public Object getLiveTrackByCertNum(@RequestBody Map<String, String> request) {
        String url = urlProperties.getGetLiveTrackByCertNum();
        return gxService.requestSjjz(url, request);
    }

    /**
     * 根据身份证号查询同户人员
     *
     * @param request 参数
     * @return 数据
     */
    @PostMapping("/getSameHouseholdByCertNum")
    public Object getSameHouseholdByCertNum(@RequestBody Map<String, String> request) {
        String url = urlProperties.getGetSameHouseholdByCertNum();
        return gxService.requestSjjz(url, request);
    }

    /**
     * 根据身份证号查询人员基本信息
     *
     * @param request 参数
     * @return 数据
     */
    @PostMapping("/getPersonInfoByIdCard")
    public Object getPersonInfoByIdCard(@RequestBody Map<String, String> request) {
        String url = urlProperties.getGetPersonInfoByIdCard();
        return gxService.requestSjjz(url, request);
    }

    /**
     * 根据身份证号查询全国情报重点人员
     *
     * @param request 参数
     * @return 数据
     */
    @PostMapping("/getKeyPersonnelByCertNum")
    public Object getKeyPersonnelByCertNum(@RequestBody Map<String, String> request) {
        String url = urlProperties.getGetKeyPersonnelByCertNum();
        return gxService.requestSjjz(url, request);
    }

    /**
     * 根据身份证号查询刑事案件相关人员
     *
     * @param request 参数
     * @return 数据
     */
    @PostMapping("/getInvolveCasePersonByCertNum")
    public Object getInvolveCasePersonByCertNum(@RequestBody Map<String, String> request) {
        String url = urlProperties.getGetInvolveCasePersonByCertNum();
        return gxService.requestSjjz(url, request);
    }

    /**
     * 身份证号查询涉毒前科人员
     *
     * @param request 参数
     * @return 数据
     */
    @PostMapping("/zjhmsdqkry")
    public Object zjhmsdqkry(@RequestBody Map<String, String> request) {
        String url = urlProperties.getZjhmsdqkry();
        return gxService.requestSjjz(url, request);
    }

    /**
     * 根据身份证号查询行政案件相关人员
     *
     * @param request 参数
     * @return 数据
     */
    @PostMapping("/getXzajAjxgryByCertNum")
    public Object getXzajAjxgryByCertNum(@RequestBody Map<String, String> request) {
        String url = urlProperties.getGetXzajAjxgryByCertNum();
        return gxService.requestSjjz(url, request);
    }

    /**
     * 根据身份证号查询全国在逃人员登记信息
     *
     * @param request 参数
     * @return 数据
     */
    @PostMapping("/getFugitivesByCertNum")
    public Object getFugitivesByCertNum(@RequestBody Map<String, String> request) {
        String url = urlProperties.getGetFugitivesByCertNum();
        return gxService.requestSjjz(url, request);
    }

    /**
     * 根据身份证号查询同航班人员信息
     *
     * @param request 参数
     * @return 数据
     */
    @PostMapping("/getSameFlightPersonByCertNum")
    public Object getSameFlightPersonByCertNum(@RequestBody Map<String, String> request) {
        String url = urlProperties.getGetSameFlightPersonByCertNum();
        return gxService.requestSjjz(url, request);
    }

    /**
     * 根据身份证号查询同上网人员信息
     *
     * @param request 参数
     * @return 数据
     */
    @PostMapping("/getSameOnlinePersonByCertNum")
    public Object getSameOnlinePersonByCertNum(@RequestBody Map<String, String> request) {
        String url = urlProperties.getGetSameOnlinePersonByCertNum();
        return gxService.requestSjjz(url, request);
    }

    /**
     * 根据身份证号查询全省违法犯罪人员信息
     *
     * @param request 参数
     * @return 数据
     */
    @PostMapping("/OffendersByCertNum")
    public Object offendersByCertNum(@RequestBody Map<String, String> request) {
        String url = urlProperties.getOffendersByCertNum();
        return gxService.requestSjjz(url, request);
    }

    /**
     * 根据身份证号查询全省吸毒人员信息
     *
     * @param request 参数
     * @return 数据
     */
    @PostMapping("/getDrugPersonByCertNum")
    public Object getDrugPersonByCertNum(@RequestBody Map<String, String> request) {
        String url = urlProperties.getGetDrugPersonByCertNum();
        return gxService.requestSjjz(url, request);
    }

    /**
     * 根据身份证号查询全省涉毒人员信息
     *
     * @param request 参数
     * @return 数据
     */
    @PostMapping("/getDrugRelatedPersonByCertNum")
    public Object getDrugRelatedPersonByCertNum(@RequestBody Map<String, String> request) {
        String url = urlProperties.getGetDrugRelatedPersonByCertNum();
        return gxService.requestSjjz(url, request);
    }

    /**
     * 根据身份证号查询前科信息
     *
     * @param request 参数
     * @return 数据
     */
    @PostMapping("/getCriminalRecordByCertNum")
    public Object getCriminalRecordByCertNum(@RequestBody Map<String, String> request) {
        String url = urlProperties.getGetCriminalRecordByCertNum();
        return gxService.requestSjjz(url, request);
    }

    /**
     * 车牌号+车辆类型查询接口
     *
     * @param request 参数
     * @return 数据
     */
    @PostMapping("/cphcplxcx")
    public Object cphcplxcx(@RequestBody Map<String, String> request) {
        String url = urlProperties.getCphcplxcx();
        return gxService.requestSjjz(url, request);
    }
}
