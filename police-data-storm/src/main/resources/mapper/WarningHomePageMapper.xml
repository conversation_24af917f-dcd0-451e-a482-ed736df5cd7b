<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.ds.mapper.WarningHomepageMapper">

    <resultMap id="warningListVoMap" type="com.trs.police.ds.domain.vo.homepage.warning.WarningHomePageListVO">
        <id property="id" column="id"/>
        <result property="type" column="type"
            typeHandler="com.trs.police.ds.typehandler.JsonToStringListHandler"/>
        <result property="warningLevel" column="warningLevel"/>
        <result property="name" column="name"/>
        <result property="warningTime" column="warningTime"/>
    </resultMap>

    <select id="groupWarningList" resultMap="warningListVoMap">
        select w.id,
               g.name as                                                    name,
               (select JSON_ARRAYAGG(l.name) from t_profile_label l where l.module='group' and l.id member of(g.group_label)) as type,
               max(w.warning_time) as warningTime,
               w.warning_level as warningLevel
        from t_warning w
            join t_profile_group g
        on w.group_id=g.id
        where w.group_id is not null
          and (w.warning_time between #{timeParams.beginTime}and #{timeParams.endTime})
        group by w.group_id
        order by w.warning_time desc
        limit 1000
    </select>
    <select id="personWarningList" resultMap="warningListVoMap">
        select w.id,
               p.name as                                                    name,
               (select JSON_ARRAYAGG(l.name) from t_profile_label l where l.module='person' and  l.id member of(p.person_label)) as type,
               max(w.warning_time) as warningTime,
               w.warning_level as warningLevel
        from t_warning w
            join t_warning_track t
        on w.id=t.warning_id
            join t_profile_person p on t.person_id = p.id
        where w.warning_type='person'
          and (w.warning_time between #{timeParams.beginTime}and #{timeParams.endTime})
          group by t.person_id
        order by w.warning_time desc
            limit 1000
    </select>
    <select id="areaWarningList" resultMap="warningListVoMap">
        select w.id,
               a.name as                                                    name,
               (select JSON_ARRAYAGG(d.name) from t_dict d where d.type='control_warning_source_category' and  d.code =a.category) as type,
               max(w.warning_time) as warningTime,
               w.warning_level as warningLevel
        from t_warning w
                 join t_control_important_area a on  a.id member of (w.area_id)
        where w.warning_type='area'
          and (w.warning_time between #{timeParams.beginTime}and #{timeParams.endTime})
        group by w.area_id
        order by w.warning_time desc
            limit 1000
    </select>
    <select id="statisticsWarning" resultType="com.trs.police.ds.domain.vo.CodeNameCountVO">
        select w.warning_type as name,
               count(0) as count
        from t_warning w
        where (w.warning_time between #{timeParams.beginTime}and #{timeParams.endTime})
        group by w.warning_type
    </select>
    <select id="warningSourceStatistic" resultType="com.trs.police.ds.domain.request.KeyValueTypeVO">
        SELECT
            ( SELECT d.NAME FROM t_dict d WHERE d.type = 'control_warning_source_type' AND d.CODE = s.type ) AS 'KEY',
            count( 0 ) AS 'VALUE'
        FROM
            t_warning w
                JOIN t_warning_track t ON w.id = t.warning_id
                JOIN t_control_warning_source s ON t.source_id = s.unique_key
        where (w.warning_time between #{timeParams.beginTime}and #{timeParams.endTime})
        GROUP BY
            s.type
    </select>
    <select id="controlStatistic" resultType="com.trs.police.ds.domain.request.KeyValueTypeVO">
        select monitor_level as 'key',
        count(0) as 'value'
        from t_control_monitor
        where monitor_status = 5
          and deleted != 1
          and (create_time between #{timeParams.beginTime}and #{timeParams.endTime})
        group by monitor_level
    </select>
    <select id="personLabelStatistic" resultType="com.trs.police.ds.domain.request.KeyValueTypeVO">
        SELECT  (SELECT l.name FROM t_profile_label l where l.id=jt.label_id) as 'key',
                count(0)  as 'value'
        FROM t_warning w , JSON_TABLE(w.person_label, '$[*]' COLUMNS (label_id JSON PATH '$')) jt
        GROUP BY jt.label_id
    </select>
    <select id="warningPoint" resultType="com.trs.police.ds.domain.vo.homepage.warning.WarningPointVO">
        (select w.id,
               p.name as                                                    name,
               'person' as type,
               max(w.warning_time) as warningTime,
               t.latitude as latitude,
               t.longitude as longitude
        from t_warning w
            join t_warning_track t
        on w.id=t.warning_id
            join t_profile_person p on t.person_id = p.id
        where w.warning_type='person'
          and (w.warning_time between #{timeParams.beginTime}and #{timeParams.endTime})
        group by t.person_id
        order by w.warning_time desc
        limit 100)
        union all
        (select w.id,
               g.name as                                                    name,
               'group' as type,
               max(w.warning_time) as warningTime,
               t.latitude as latitude,
               t.longitude as longitude
        from t_warning w
            join t_warning_track t on w.id=t.warning_id
            join t_profile_group g on w.group_id=g.id
        where w.group_id is not null
          and (w.warning_time between #{timeParams.beginTime}and #{timeParams.endTime})
        group by w.group_id
        order by w.warning_time desc
        limit 100)
        union all
        (select w.id,
               a.name as                                                    name,
              'area' as type,
               max(w.warning_time) as warningTime,
            t.latitude as latitude,
            t.longitude as longitude
        from t_warning w
                 join t_warning_track t on w.id=t.warning_id
                 join t_control_important_area a on  a.id member of (w.area_id)
        where w.warning_type='area'
          and (w.warning_time between #{timeParams.beginTime}and #{timeParams.endTime})
        group by w.area_id
        order by w.warning_time desc
        limit 100)
    </select>
    <select id="regularWarningList"
        resultType="com.trs.police.ds.domain.vo.homepage.regular.RegularWarningListVO">
        select w.id as id,
        w.content as content,
        (select group_concat(title) from t_control_monitor_warning_model where id member of (w.model_id)) as model,
        (SELECT name FROM t_dict t1 where type='control_warning_source_type' and code = t.source_type) as sourceType,
        t.address as address,
        w.warning_time as warningTime,
        w.warning_level as warningLevel
        from t_warning w
        join t_warning_track t on w.id=t.warning_id
        where (w.warning_time between #{timeParams.beginTime}and #{timeParams.endTime})
        and w.control_type=2
        <if test="warningLevel ==null">
            and w.warning_level=#{warningLevel}
        </if>
    </select>
    <select id="personLabelStatistics" resultType="com.trs.police.ds.domain.request.KeyValueTypeVO">
        SELECT  (SELECT l.name FROM t_profile_label l where l.id=jt.label_id) as 'key',
            count(0)  as 'value'
        FROM t_profile_person w , JSON_TABLE(w.person_label, '$[*]' COLUMNS (label_id JSON PATH '$')) jt
        where (w.create_time between #{timeParams.beginTime}and #{timeParams.endTime})
        GROUP BY jt.label_id
    </select>
</mapper>