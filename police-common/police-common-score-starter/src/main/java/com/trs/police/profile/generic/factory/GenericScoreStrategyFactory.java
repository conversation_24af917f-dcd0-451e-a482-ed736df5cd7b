package com.trs.police.profile.generic.factory;

import com.trs.police.profile.generic.strategy.GenericScoreStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 通用积分策略工厂
 *
 * @author: AI Assistant
 * @date: 2025/01/27
 * @description: 管理和创建通用积分计算策略
 */
@Slf4j
@Component
public class GenericScoreStrategyFactory {

    /**
     * 策略名称映射
     */
    private final Map<String, GenericScoreStrategy> strategyByName = new HashMap<>();
    /**
     * 规则类型映射
     */
    private final Map<String, GenericScoreStrategy> strategyByRuleType = new HashMap<>();
    /**
     * 复合键映射（规则类型 + 适用范围）
     */
    private final Map<String, GenericScoreStrategy> strategyByCompositeKey = new HashMap<>();
    @Autowired
    private List<GenericScoreStrategy> strategies;
    /**
     * 按优先级排序的策略列表
     */
    private List<GenericScoreStrategy> prioritizedStrategies = new ArrayList<>();

    /**
     * 初始化策略映射
     */
    @PostConstruct
    public void initStrategies() {
        if (strategies != null) {
            // 按优先级排序
            prioritizedStrategies = strategies.stream()
                    .sorted((s1, s2) -> Integer.compare(s2.getPriority(), s1.getPriority()))
                    .collect(Collectors.toList());

            for (GenericScoreStrategy strategy : strategies) {
                // 按策略名称注册
                if (StringUtils.hasText(strategy.getStrategyName())) {
                    strategyByName.put(strategy.getStrategyName(), strategy);
                    log.info("注册积分策略：{}", strategy.getStrategyName());
                }

                // 按规则类型注册
                if (StringUtils.hasText(strategy.getSupportedRuleType())) {
                    strategyByRuleType.put(strategy.getSupportedRuleType(), strategy);
                    log.info("注册规则类型策略：{} -> {}", strategy.getSupportedRuleType(), strategy.getStrategyName());
                }

                // 按复合键注册（规则类型 + 适用范围）
                if (StringUtils.hasText(strategy.getSupportedRuleType()) && strategy.getApplicableScopes() != null) {
                    for (String scope : strategy.getApplicableScopes()) {
                        String compositeKey = strategy.getSupportedRuleType() + ":" + scope;
                        strategyByCompositeKey.put(compositeKey, strategy);
                        log.info("注册复合键策略：{} -> {}", compositeKey, strategy.getStrategyName());
                    }
                }
            }
        }
        log.info("通用积分策略工厂初始化完成，共注册{}个策略", strategyByName.size());
    }

    /**
     * 根据规则名称、规则类型和适用范围获取策略
     *
     * @param ruleName 规则名称
     * @param ruleType 规则类型
     * @param scope    适用范围
     * @return 积分策略
     */
    public GenericScoreStrategy getStrategy(String ruleName, String ruleType, String scope) {
        GenericScoreStrategy strategy = null;

        // 1. 优先按复合键查找（规则类型 + 适用范围）
        if (StringUtils.hasText(ruleType) && StringUtils.hasText(scope)) {
            String compositeKey = ruleType + ":" + scope;
            strategy = strategyByCompositeKey.get(compositeKey);
            if (strategy != null) {
                log.debug("根据复合键找到策略：{} -> {}", compositeKey, strategy.getStrategyName());
                return strategy;
            }
        }

        // 2. 按规则名称查找
        if (StringUtils.hasText(ruleName)) {
            strategy = strategyByName.get(ruleName);
            if (strategy != null) {
                log.debug("根据规则名称找到策略：{} -> {}", ruleName, strategy.getStrategyName());
                return strategy;
            }
        }

        // 3. 按规则类型查找
        if (StringUtils.hasText(ruleType)) {
            strategy = strategyByRuleType.get(ruleType);
            if (strategy != null) {
                log.debug("根据规则类型找到策略：{} -> {}", ruleType, strategy.getStrategyName());
                return strategy;
            }
        }

        // 4. 遍历所有策略，使用supports方法判断
        for (GenericScoreStrategy s : prioritizedStrategies) {
            if (s.supports(ruleName, ruleType, scope)) {
                log.debug("通过supports方法找到策略：{},{},{} -> {}", ruleName, ruleType, scope, s.getStrategyName());
                return s;
            }
        }

        log.warn("未找到匹配的积分策略，规则名称：{}，规则类型：{}，适用范围：{}", ruleName, ruleType, scope);
        return null;
    }

    /**
     * 根据策略名称获取策略
     *
     * @param strategyName 策略名称
     * @return 积分策略
     */
    public GenericScoreStrategy getStrategyByName(String strategyName) {
        return strategyByName.get(strategyName);
    }

    /**
     * 根据规则类型获取策略
     *
     * @param ruleType 规则类型
     * @return 积分策略
     */
    public GenericScoreStrategy getStrategyByRuleType(String ruleType) {
        return strategyByRuleType.get(ruleType);
    }

    /**
     * 获取支持指定范围的所有策略
     *
     * @param scope 适用范围
     * @return 策略列表
     */
    public List<GenericScoreStrategy> getStrategiesByScope(String scope) {
        if (!StringUtils.hasText(scope)) {
            return new ArrayList<>();
        }

        return strategies.stream()
                .filter(strategy -> {
                    String[] scopes = strategy.getApplicableScopes();
                    return scopes != null && Arrays.asList(scopes).contains(scope);
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取支持指定规则类型的所有策略
     *
     * @param ruleType 规则类型
     * @return 策略列表
     */
    public List<GenericScoreStrategy> getStrategiesByRuleType(String ruleType) {
        if (!StringUtils.hasText(ruleType)) {
            return new ArrayList<>();
        }

        return strategies.stream()
                .filter(strategy -> ruleType.equals(strategy.getSupportedRuleType()))
                .collect(Collectors.toList());
    }

    /**
     * 获取所有已注册的策略
     *
     * @return 策略列表
     */
    public List<GenericScoreStrategy> getAllStrategies() {
        return new ArrayList<>(prioritizedStrategies);
    }

    /**
     * 检查是否存在指定的策略
     *
     * @param ruleName 规则名称
     * @param ruleType 规则类型
     * @param scope    适用范围
     * @return 是否存在
     */
    public boolean hasStrategy(String ruleName, String ruleType, String scope) {
        return getStrategy(ruleName, ruleType, scope) != null;
    }

    /**
     * 获取策略统计信息
     *
     * @return 策略统计信息
     */
    public Map<String, Object> getStrategyStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalStrategies", strategies != null ? strategies.size() : 0);
        stats.put("strategiesByName", strategyByName.size());
        stats.put("strategiesByRuleType", strategyByRuleType.size());
        stats.put("strategiesByCompositeKey", strategyByCompositeKey.size());

        if (strategies != null) {
            Map<String, Object> strategyDetails = new HashMap<>();
            for (GenericScoreStrategy strategy : strategies) {
                Map<String, Object> detail = new HashMap<>();
                detail.put("description", strategy.getDescription());
                detail.put("ruleType", strategy.getSupportedRuleType());
                detail.put("scopes", strategy.getApplicableScopes());
                detail.put("priority", strategy.getPriority());
                detail.put("supportsBatch", strategy.supportsBatchCalculation());
                strategyDetails.put(strategy.getStrategyName(), detail);
            }
            stats.put("strategyDetails", strategyDetails);
        }

        // 按适用范围分组统计
        Map<String, Long> scopeStats = new HashMap<>();
        if (strategies != null) {
            for (GenericScoreStrategy strategy : strategies) {
                if (strategy.getApplicableScopes() != null) {
                    for (String scope : strategy.getApplicableScopes()) {
                        scopeStats.put(scope, scopeStats.getOrDefault(scope, 0L) + 1);
                    }
                }
            }
        }
        stats.put("strategiesByScope", scopeStats);

        // 按规则类型分组统计
        Map<String, Long> ruleTypeStats = strategies != null
                ? strategies.stream()
                        .filter(s -> StringUtils.hasText(s.getSupportedRuleType()))
                        .collect(Collectors.groupingBy(
                                GenericScoreStrategy::getSupportedRuleType,
                                Collectors.counting()
                        )) : new HashMap<>();
        stats.put("strategiesByRuleTypeCount", ruleTypeStats);

        return stats;
    }

    /**
     * 重新加载策略
     */
    public void reloadStrategies() {
        strategyByName.clear();
        strategyByRuleType.clear();
        strategyByCompositeKey.clear();
        prioritizedStrategies.clear();

        initStrategies();

        log.info("策略重新加载完成");
    }
}
