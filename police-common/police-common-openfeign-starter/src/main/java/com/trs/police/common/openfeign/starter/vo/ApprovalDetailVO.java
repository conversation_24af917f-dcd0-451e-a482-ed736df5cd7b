package com.trs.police.common.openfeign.starter.vo;

import com.trs.police.common.core.vo.approval.RelatedService;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import com.trs.police.common.core.vo.permission.SimpleUserVO;
import com.trs.police.common.core.vo.permission.UserCardVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 审批详情
 *
 * <AUTHOR>
 */
@Data
public class ApprovalDetailVO implements Serializable {

    private static final long serialVersionUID = 3721482717996009642L;
    /**
     * id
     */
    private Long id;
    /**
     * 流程编号
     */
    private String processCode;
    /**
     * 审批标题
     */
    private String title;
    /**
     * 发起人部门
     */
    private String createDept;
    /**
     * 审批状态
     */
    private Integer status;
    /**
     * 修改原因
     */
    private String modifyReason;
    /**
     * 节点详情
     */
    private List<Node> nodes;
    /**
     * 关联业务
     */
    private RelatedService relatedService;

    /**
     * 是否可以操作
     */
    private Boolean canProcess;

    /**
     * 审批类型
     */
    private String type;

    /**
     * 是否支持完结
     */
    private Boolean supportFinished;

    /**
     * 是否是最后的节点
     */
    private Boolean isLastNode;

    /**
     * 是否需要选择审批人
     */
    private Boolean needSelectApprover;


    /**
     * 审批人
     */
    private List<SimpleUserVO> approver;

    /**
     * 是否动态选择审批人
     */
    private Boolean dynamicApprover;

    /**
     * 动态选择审批人策略
     */
    private String approverStrategy;

    /**
     * 操作
     */
    private Integer actionCode;

    /**
     * 节点类型
     */
    private Integer nodeType;

    /**
     * 当前审批节点配置id
     */
    private Long currentNodeConfigId;

    /**
     * 配置信息
     */
    private NodeConfigDetailVO configDetail;

    /**
     * 发起审批表单内容
     */
    private String startFormContent;

    /**
     * 节点
     */
    @Data
    public static class Node implements Serializable {

        private static final long serialVersionUID = 8438813207490105683L;
        /**
         * 节点id
         */
        private Long id;
        /**
         * 节点名称
         */
        private String name;
        /**
         * 节点状态
         */
        private String status;

        /**
         * 审批状态（明文）
         */
        private String approvalStatusName;
        /**
         * 最后更新时间
         */
        private String time;

        @Deprecated
        private Integer isSampleOrg;

        private Integer isSameOrg;


        /**
         * 审批人意见详情
         */
        private List<ApproverCommentDetail> commentDetails;

        /**
         * 配置id
         */
        private Long configId;

    }

    /**
     * 审批意见
     */
    @Data
    public static class ApproverCommentDetail implements Serializable {

        private static final long serialVersionUID = -3795769959087708043L;

        private Long id;
        /**
         * 审批人
         */
        private UserCardVO approver;
        /**
         * 操作时间
         */
        private String time;
        /**
         * 操作时间(yyyy-MM-dd HH:mm:ss)
         */
        private String fullTime;
        /**
         * 审批结果
         */
        private String result;
        /**
         * 审批意见
         */
        private String comment;
        /**
         * 附件
         */
        private List<FileInfoVO> attachments;

        /**
         * 备注
         */
        private String remark;

        /**
         * 审批人员类型
         */
        private String type;
    }
}
