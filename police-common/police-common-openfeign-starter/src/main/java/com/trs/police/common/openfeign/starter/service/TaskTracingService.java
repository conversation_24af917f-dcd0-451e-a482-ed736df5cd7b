package com.trs.police.common.openfeign.starter.service;

import com.trs.police.common.core.constant.PoliceMicroserviceNameConstant;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.TodoTaskVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 挂账盯办
 *
 * <AUTHOR>
 */
@FeignClient(name = PoliceMicroserviceNameConstant.SERVICE_NAME_TASK_TRACING)
public interface TaskTracingService {

    /**
     * 查询待办列表
     *
     * @param pageParams 分页
     * @return 结果
     */
    @PostMapping("/todo")
    PageResult<TodoTaskVO> getTaskTodo(@RequestBody PageParams pageParams);
}
