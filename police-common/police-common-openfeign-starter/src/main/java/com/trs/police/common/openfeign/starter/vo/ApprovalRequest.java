package com.trs.police.common.openfeign.starter.vo;

import com.trs.police.common.core.dto.approval.ApprovalNodeFieldParam;
import com.trs.police.common.core.vo.permission.UserDeptVO;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 发起审批请求
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApprovalRequest implements Serializable {

    private static final long serialVersionUID = 7933740431866772307L;
    /**
     * 审批模板
     */
    private String approvalConfigName;
    /**
     * 审批操作信息
     */
    private ApprovalActionVO approvalActionVO;

    /**
     * 操作用户
     */
    private UserDeptVO user;

    /**
     * 目标用户（目标部门） 需要目标部门审批时传值
     */
    private UserDeptVO targetUser;

    /**
     * 目标部门 目标部门对应多个的时候使用
     */
    private List<UserDeptVO> targetUserList;

    /**
     * 选择的审批者 (仅针对于第一级 后续流程无法使用)
     */
    private List<UserDeptVO> selectApprover;

    /**
     * 动态节点参数 key 节点类型编码 value 动态参数
     */
    private Map<String, List<ApprovalNodeFieldParam>> dynamicNodeParams;

    /**
     * 添加参数
     *
     * @param key key
     * @param value vakue
     */
    public void addDynamicNodeParams(String key, List<ApprovalNodeFieldParam> value) {
        if (dynamicNodeParams == null) {
            dynamicNodeParams = new java.util.HashMap<>();
        }
        dynamicNodeParams.put(key, value);
    }
}
