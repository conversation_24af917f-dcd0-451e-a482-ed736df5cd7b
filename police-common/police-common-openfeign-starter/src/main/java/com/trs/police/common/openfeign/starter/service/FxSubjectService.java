package com.trs.police.common.openfeign.starter.service;

import com.trs.police.common.core.constant.PoliceMicroserviceNameConstant;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * fx专题feign服务
 *
 * <AUTHOR>
 * @date 2024/04/25
 */
@FeignClient(name = PoliceMicroserviceNameConstant.SERVICE_NAME_FX_SUBJECT)
public interface FxSubjectService {

    /**
     * 更新fx专题-模型预警的预警状态
     *
     * @param id fx专题预警id
     * @param warningStatus 预警状态
     * @return 是否更新成功 true 成功 false 失败
     */
    @GetMapping(value = "/public/warning/model/updateWarningStatus")
    Boolean updateWarningStatus(@RequestParam("id") Long id, @RequestParam("warningStatus") Integer warningStatus);

    /**
     * 添加关联关系
     *
     * @param warnId id
     * @param idCard idCard
     */
    @PostMapping("/public/warning/addWarnRelation")
    void addWarnRelation(@RequestParam("warnId") Long warnId, @RequestParam("idCard") String idCard);

}
