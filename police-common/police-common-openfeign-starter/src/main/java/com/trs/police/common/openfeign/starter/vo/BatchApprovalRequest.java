package com.trs.police.common.openfeign.starter.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 批量发起审批的请求
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class BatchApprovalRequest implements Serializable {

    /**
     * 异常时是否退出
     */
    private Boolean exitOnError;

    /**
     * 审批请求
     */
    private List<ApprovalRequest> approvalRequests;

    public BatchApprovalRequest(Boolean exitOnError, List<ApprovalRequest> approvalRequests) {
        this.exitOnError = exitOnError;
        this.approvalRequests = approvalRequests;
    }
}
