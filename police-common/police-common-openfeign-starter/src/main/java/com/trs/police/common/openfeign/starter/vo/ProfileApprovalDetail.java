package com.trs.police.common.openfeign.starter.vo;

import com.trs.police.common.core.constant.enums.ApprovalStatusEnum;


import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 审批详情
 */
@Data
@NoArgsConstructor
public class ProfileApprovalDetail {

    /**
     * 状态 {@link ApprovalStatusEnum}
     */
    private Integer status;

    /**
     * 状态名称
     */
    private String statusName;

    /**
     * 最后的审批意见
     */
    private String finalApprovalComment;

    /**
     * 审批的用户id
     */
    private List<Long> approvalUserId;

    /**
     * 标题
     */
    private String title;

    /**
     * 审批id
     */
    private Long approvalId;

    /**、
     * 构建审批详情
     *
     * @param status 状态
     * @return 详情
     */
    public static ProfileApprovalDetail of(Integer status) {
        ProfileApprovalDetail detail = new ProfileApprovalDetail();
        detail.setStatus(status);
        return detail;
    }
}
