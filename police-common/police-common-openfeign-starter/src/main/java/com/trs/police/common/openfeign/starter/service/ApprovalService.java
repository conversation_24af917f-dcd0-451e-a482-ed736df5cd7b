package com.trs.police.common.openfeign.starter.service;

import com.trs.police.common.core.constant.PoliceMicroserviceNameConstant;
import com.trs.police.common.core.dto.approval.TemplateQueryDTO;
import com.trs.police.common.core.entity.ResponseMessage;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.TodoTaskVO;
import com.trs.police.common.core.vo.approval.ApprovalInfoVO;
import com.trs.police.common.core.vo.approval.NodeResultEntityVO;
import com.trs.police.common.core.vo.approval.ProcessPreviewVO;
import com.trs.police.common.core.vo.approval.TemplateVO;
import com.trs.police.common.core.vo.permission.UserDeptActionVO;
import com.trs.police.common.core.vo.permission.UserDeptVO;
import com.trs.police.common.openfeign.starter.vo.ApprovalDetailVO;
import com.trs.police.common.openfeign.starter.vo.ApprovalListVO;
import com.trs.police.common.openfeign.starter.vo.ApprovalRequest;
import com.trs.police.common.openfeign.starter.vo.BatchApprovalRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 审批open feign接口
 *
 * <AUTHOR>
 */
@FeignClient(name = PoliceMicroserviceNameConstant.SERVICE_NAME_APPROVAL)
public interface ApprovalService {

    /**
     * 查询审批列表
     *
     * @param request 请求
     * @param type 类型
     * @return 列表
     */
    @PostMapping("/list/{type}")
    PageResult<ApprovalListVO> getApprovalList(@RequestBody ListParamsRequest request, @PathVariable("type") String type);

    /**
     * 查询审批详情
     *
     * @param approvalId 审批id
     * @return 审批详情
     */
    @GetMapping("/detail/batch")
    List<ApprovalDetailVO> getApprovalBatch(@RequestParam("approvalId") String approvalId);

    /**
     * 发起审批
     *
     * @param approvalRequest 参数
     * @return 审批id
     */
    @PostMapping(value = "/public/startApprovalWithResult", consumes = MediaType.APPLICATION_JSON_VALUE)
    String startApprovalWithResult(@RequestBody ApprovalRequest approvalRequest);

    /**
     * 批量发起审批 按顺序返回审批结果
     *
     * @param approvalRequest 请求
     * @return 审批结果
     */
    @PostMapping(value = "/public/startApprovalBatch", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseMessage startApprovalBatch(@RequestBody BatchApprovalRequest approvalRequest);

    /**
     * 发起审批
     *
     * @param approvalRequest 参数
     * @return 审批id
     */
    @PostMapping(value = "/public/fistApprover", consumes = MediaType.APPLICATION_JSON_VALUE)
    List<UserDeptVO> fistApprover(@RequestBody ApprovalRequest approvalRequest);

    /**
     * 获取审批流程预览
     *
     * @param approvalRequest 审批参数 {@link ProcessPreviewVO} ResponseMessage.ok(List  ProcessPreviewVO )
     * @return 审批流程预览
     */
    @PostMapping(value = "/public/getApprovalProcessPreview", consumes = MediaType.APPLICATION_JSON_VALUE)
    String getApprovalProcessPreview(@RequestBody ApprovalRequest approvalRequest);


    /**
     * 获取第一个审批节点的审批用户V2,,需要返回报错信息，避免直接在页面报错
     *
     * @param approvalRequest 审批配置
     * @return 审批用户列表
     */
    @PostMapping(value = "/public/fistApproverV2", consumes = MediaType.APPLICATION_JSON_VALUE)
    String fistApproverV2(@RequestBody ApprovalRequest approvalRequest);

    /**
     * 发起审批
     *
     * @param approvalRequest 参数
     * @return 审批id
     */
    @PostMapping(value = "/public/start", consumes = MediaType.APPLICATION_JSON_VALUE)
    Long startApproval(@RequestBody ApprovalRequest approvalRequest);


    /**
     * 获取发起审批产生的审批人
     *
     * @param approvalRequest 参数
     * @return 审批人
     */
    @PostMapping(value = "/public/getStartApprovers", consumes = MediaType.APPLICATION_JSON_VALUE)
    UserDeptVO[] getStartApprovers(@RequestBody ApprovalRequest approvalRequest);

    /**
     * 查询关联审批列表
     *
     * @param service 业务模块名称
     * @param id      业务id
     * @param action  操作类型
     * @return 审批列表
     */
    @GetMapping("/public/approvals")
    List<ApprovalInfoVO> getApprovals(@RequestParam("service") String service, @RequestParam("id") Long id, @RequestParam("action") String action);

    /**
     * 撤回所有相关审批
     *
     * @param service 关联业务类型code
     * @param id      关联业务id
     */
    @GetMapping("/public/cancel/all")
    void cancelAllApprovals(@RequestParam("service") String service, @RequestParam("id") Long id);

    /**
     * 撤回所有相关审批
     *
     * @param service 关联业务类型code
     * @param id      关联业务id
     * @param action 具体某个操作
     */
    @GetMapping("/public/cancel/action")
    void cancelAllApprovalsAction(@RequestParam("service") String service, @RequestParam("id") Long id, @RequestParam("action") String action);

    /**
     * 根据id查询
     *
     * @param id 审批id
     * @return 审批
     */
    @GetMapping("/{id}")
    ApprovalInfoVO selectById(@PathVariable("id") Long id);

    /**
     * 查询审批表单项
     *
     * @param id 审批id
     * @return 结果
     */
    @GetMapping("/{id}/form")
    String getStartFormContent(@PathVariable("id") Long id);

    /**
     * 查询审批人信息
     *
     * @param relatedId 关联id
     * @param relatedService 关联类型
     * @return UserDeptActionVO
     */
    @GetMapping("/public/approver/{relatedService}/{relatedId}")
    UserDeptActionVO getApprover(@PathVariable("relatedService") String relatedService, @PathVariable("relatedId") Long relatedId);

    /**
     * 查询待办列表
     *
     * @param pageParams fy
     * @return 结果
     */
    @PostMapping("/todo")
    PageResult<TodoTaskVO> getApprovalTodo(@RequestBody PageParams pageParams);

    /**
     * 获取节点信息
     *
     * @param nodeIds 节点ids
     * @return 结果
     */
    @GetMapping("/public/approval/getNodes/{nodeIds}")
    List<NodeResultEntityVO> getNodesByNodeId(@PathVariable("nodeIds") String nodeIds);

    /**
     * 获取审批详情
     *
     * @param approvalId 审批id
     * @return 结果
     */
    @GetMapping("public/{approvalId}/detail")
    ApprovalDetailVO getApproval(@PathVariable("approvalId") Long approvalId);

    /**
     * 获取审批模板是否启用
     *
     * @param templateName 审批模板名称
     * @return {@link Boolean }
     */
    @GetMapping("/public/findEnableByTemplateName")
    Boolean findEnableByTemplateName(@RequestParam("templateName") String templateName);

    /**
     * 获取到模板
     *
     * @param dto dto
     * @return 模板
     */
    @PostMapping("/public/getTemplate")
    TemplateVO getTemplate(@RequestBody TemplateQueryDTO dto);

}
