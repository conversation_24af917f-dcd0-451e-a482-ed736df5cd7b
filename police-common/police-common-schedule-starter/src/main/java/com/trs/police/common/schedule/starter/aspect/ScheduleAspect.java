package com.trs.police.common.schedule.starter.aspect;

import com.trs.police.common.core.utils.AspectUtil;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.core.vo.message.ScheduleMessageVO;
import com.trs.police.common.core.vo.message.ScheduleResultVO;
import com.trs.police.common.schedule.starter.annotation.ScheduleResult;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;

import static com.trs.police.common.schedule.starter.constant.ExchangeConstant.SCHEDULE_RESULT_EXCHANGE;

/**
 * schedule运行结果处理切面
 *
 * <AUTHOR>
 */
@Aspect
@Configuration
@Slf4j
public class ScheduleAspect {

    @Resource
    private RabbitTemplate rabbitTemplate;

    /**
     * 切点
     */
    @Pointcut("@annotation(com.trs.police.common.schedule.starter.annotation.ScheduleResult)")
    public void pointCut() {
        //ignore
    }

    /**
     * 处理返回信息
     *
     * @param joinPoint      参数
     * @param scheduleResult 运行结果
     */
    @AfterReturning("pointCut()&& @annotation(scheduleResult)")
    public void sendReturningMessage(JoinPoint joinPoint, ScheduleResult scheduleResult) {
        try {
            Map<String, Object> params = AspectUtil.generateParams(joinPoint);
            ScheduleMessageVO message = JsonUtil.parseSpecificObject(params.get("message"), ScheduleMessageVO.class);
            pushScheduleResultToMq(message, true);
        } catch (Exception e) {
            log.error("定时任务处理结果生成失败！", e);
        }

    }

    /**
     * 处理异常信息
     *
     * @param joinPoint      参数
     * @param scheduleResult 运行结果
     */
    @AfterThrowing("pointCut()&& @annotation(scheduleResult)")
    public void sendThrowingMessage(JoinPoint joinPoint, ScheduleResult scheduleResult) {
        try {
            Map<String, Object> params = AspectUtil.generateParams(joinPoint);
            Object data = params.get("message");
            ScheduleMessageVO message = JsonUtil.parseSpecificObject(data, ScheduleMessageVO.class);
            if (Objects.isNull(message)) {
                log.warn("数据[{}]转换失败", data);
                return;
            }
            pushScheduleResultToMq(message, false);
        } catch (Exception e) {
            log.error("定时任务处理结果生成失败！", e);
        }

    }

    private void pushScheduleResultToMq(ScheduleMessageVO message, Boolean result) {
        ScheduleResultVO schedule = new ScheduleResultVO();
        schedule.setJobId(message.getJobId());
        schedule.setModule(message.getModule());
        schedule.setOperation(message.getOperation());
        schedule.setExecuteTime(LocalDateTime.now());
        schedule.setSuccess(result);
        String messageStr = JsonUtil.toJsonString(schedule);
        rabbitTemplate.convertAndSend(SCHEDULE_RESULT_EXCHANGE, "", messageStr);
        log.info("向交换机 [{}] 发送消息：{}", SCHEDULE_RESULT_EXCHANGE, messageStr);
    }
}
