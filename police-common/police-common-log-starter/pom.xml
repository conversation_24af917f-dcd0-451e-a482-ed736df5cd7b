<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>police-common</artifactId>
        <groupId>com.trs.police</groupId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>police-common-log-starter</artifactId>
    <description>操作记录模块</description>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.trs.police</groupId>
            <artifactId>police-common-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>

        <dependency>
            <groupId>com.trs.police</groupId>
            <artifactId>police-common-openfeign-starter</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>

</project>
