package com.trs.police.common.core.vo;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class RangeVoTest {

    @Test
    void inRange() {
        RangeVo rangeVo = new RangeVo();
        rangeVo.setRange("45-");
        rangeVo.setName("高风险");
        Assertions.assertTrue(rangeVo.inRange(50.0));
        rangeVo.setParsed(false);
        rangeVo.setRange("-45");
        Assertions.assertFalse(rangeVo.inRange(50.0));
        rangeVo.setParsed(false);
        rangeVo.setRange("-50");
        Assertions.assertFalse(rangeVo.inRange(50.0));
        rangeVo.setParsed(false);
        rangeVo.setRange("44-51");
        Assertions.assertTrue(rangeVo.inRange(50.0));
        rangeVo.setParsed(false);
        rangeVo.setRange("-51");
        Assertions.assertTrue(rangeVo.inRange(50.0));
        rangeVo.setParsed(false);
        rangeVo.setRange("");
        Assertions.assertFalse(rangeVo.inRange(50.0));
    }
}