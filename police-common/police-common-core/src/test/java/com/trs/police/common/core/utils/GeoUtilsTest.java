package com.trs.police.common.core.utils;

import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Polygon;
import org.locationtech.proj4j.*;

import java.util.Arrays;

import static com.trs.police.common.core.utils.GeoUtils.*;

/**
 * *@author:wen.wen
 * *@create 2024-10-10 12:27
 **/
@Slf4j
public class GeoUtilsTest {

    private static final GeometryFactory GEOMETRY_FACTORY = new GeometryFactory();

    private static final CoordinateReferenceSystem WGS_84 = new CRSFactory().createFromName("epsg:4326");
    private static final CoordinateReferenceSystem MERCATOR = new CRSFactory().createFromName("epsg:3857");

    /**
     * test
     *
     * @param args arguments
     */
    public static void main(String[] args) {
        String p1 = wgs84ToMercator("POINT(105.477618 28.890501)");
        log.info(p1);
        String p2 = wgs84ToMercator("POINT(105.498475 28.884949)");
        log.info(p2);
        int height = 50;

        // 矩形
        Coordinate c1 = wktToCoordinate(p1);
        Coordinate c2 = wktToCoordinate(p2);
        Coordinate[] coordinates = lineToRectangleCoordinates(c1, c2, height);
        Polygon polygon = GEOMETRY_FACTORY.createPolygon(
                Arrays.stream(coordinates).map(c -> convert(c, MERCATOR, WGS_84)).toArray(Coordinate[]::new));
        //输出wkt
        log.info(polygon.toText());
    }

    @NotNull
    private static Coordinate convert(Coordinate srcPoint, CoordinateReferenceSystem srcCrs,
                                      CoordinateReferenceSystem targetCrs) {
        // 创建坐标转换对象
        CoordinateTransform transform = new BasicCoordinateTransform(srcCrs, targetCrs);
        // 定义原始WGS84坐标点
        ProjCoordinate srcCoordinate = new ProjCoordinate(srcPoint.getX(), srcPoint.getY());
        // 目标Mercator坐标点
        ProjCoordinate targetCoordinate = new ProjCoordinate();
        // 进行坐标转换
        transform.transform(srcCoordinate, targetCoordinate);
        // 输出WKT字符串
        return new Coordinate(doubleScale(targetCoordinate.x), doubleScale(targetCoordinate.y));
    }

    @Test
    void pointInInlineGeometry() {
        System.out.println(GeoUtils.getInstance().pointInInlineGeometry(
                GeoUtils.makePointByLongitudeLatitude(104.070379, 30.572357)
        ));
        System.out.println(GeoUtils.getInstance().pointInInlineGeometry(
                GeoUtils.makePointByLongitudeLatitude(104.070379, -30.572357),
                it -> "no"
        ));
        var d = GeoUtils.getInstance().pointInInlineGeometry(
                GeoUtils.makePointByLongitudeLatitude(104.070379, 30.572357),
                it -> true,
                it -> false
        );
        System.out.println(d);
    }
}
