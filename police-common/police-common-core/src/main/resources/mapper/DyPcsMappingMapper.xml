<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.common.core.mapper.DyPcsMappingMapper">

    <select id="findAllDept" resultType="com.trs.police.common.core.vo.permission.DeptVO">
        SELECT
        id as deptId,
        name as deptName,
        pid as pid,
        code as deptCode,
        district_code as areaCode,
        short_name as shortName,
        (select t_district.name from t_district where t_district.code=t_dept.district_code) as path
        FROM t_dept
    </select>

    <select id="findByPcsbm" resultType="com.trs.police.common.core.entity.DyPcsMappingEntity">
        select * from tb_dy_pcs_mapping
        <where>
            pcsbm = #{pcsbm}
        </where>
    </select>

    <select id="findByQxDyShortCode"
        resultType="com.trs.police.common.core.entity.DyPcsMappingEntity">
        select * from tb_dy_pcs_mapping
        <where>
            <if test="qxDyShortCode != null and qxDyShortCode.length() > 0">
                AND qxbm like '${qxDyShortCode}%'
            </if>
        </where>
    </select>

</mapper>
