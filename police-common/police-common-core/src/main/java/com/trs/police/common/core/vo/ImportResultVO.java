package com.trs.police.common.core.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/5 10:25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ImportResultVO {

    /**
     * 总数
     */
    private Integer allCount;
    /**
     * 成功数
     */
    private Integer successCount;
    /**
     * 失败数
     */
    private Integer failCount;

    /**
     * 导入失败的数据信息
     */
    private List<ImportFailVO> importFailVOList;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 构造器
     *
     * @param allCount     allCount
     * @param successCount successCount
     * @param failCount    failCount
     * @param file         file
     */
    public ImportResultVO(Integer allCount, Integer successCount, Integer failCount) {
        this.allCount = allCount;
        this.successCount = successCount;
        this.failCount = failCount;
    }

    /**
     * 构造器
     *
     * @param allCount         allCount
     * @param successCount     successCount
     * @param failCount        failCount
     * @param importFailVOList importFailVOList
     */
    public ImportResultVO(Integer allCount, Integer successCount, Integer failCount, List<ImportFailVO> importFailVOList) {
        this.allCount = allCount;
        this.successCount = successCount;
        this.failCount = failCount;
        this.importFailVOList = importFailVOList;
    }
}
