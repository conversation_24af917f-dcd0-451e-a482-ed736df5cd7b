package com.trs.police.common.core.vo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.trs.police.common.core.configure.PoliceCloudCoreAutoConfigure.LocalDateTimeDeserializer;
import com.trs.police.common.core.configure.PoliceCloudCoreAutoConfigure.LocalDateTimeSerializer;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.vo.permission.SimpleUserVO;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/3/27 16:45
 */
@Data
@NoArgsConstructor
public class OperateVO implements Serializable {

    private static final long serialVersionUID = 4291789785186035793L;
    private SimpleUserVO user;


    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime operateTime;

    /**
     * 填入审计字段
     *
     * @param currentUser 当前用户信息
     */
    public void fillAuditFields(CurrentUser currentUser) {
        this.user = new SimpleUserVO(currentUser);
        this.operateTime = LocalDateTime.now();
    }

    /**
     * 填入审计字段
     */
    public void fillAuditFields() {
        this.user = AuthHelper.getNotNullSimpleUser();
        this.operateTime = LocalDateTime.now();
    }

    /**
     * 新建、包含时间和当前用户
     *
     * @return 操作信息
     */
    public static OperateVO newInstance() {
        OperateVO operateVO = new OperateVO();
        operateVO.fillAuditFields();
        return operateVO;
    }

    public OperateVO(CurrentUser currentUser) {
        this.user = new SimpleUserVO(currentUser);
        this.operateTime = LocalDateTime.now();
    }
}
