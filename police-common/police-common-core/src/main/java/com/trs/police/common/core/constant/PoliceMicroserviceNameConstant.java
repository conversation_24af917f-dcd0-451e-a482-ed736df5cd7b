package com.trs.police.common.core.constant;

/**
 * 微服务名常量
 *
 * <AUTHOR>
 */
public class PoliceMicroserviceNameConstant {

    private PoliceMicroserviceNameConstant() {
    }

    /**
     * 单点登录服务
     */
    public static final String SERVICE_NAME_AUTHORIZE = "authorize";
    /**
     * 对象存储服务
     */
    public static final String SERVICE_NAME_OSS = "oss";
    /**
     * 基于spring boot admin 的监控服务,监控各个微服务是否正常
     */
    public static final String SERVICE_NAME_MONITOR = "monitor";
    /**
     * 微服务网关
     */
    public static final String SERVICE_NAME_GATEWAY = "gateway";
    /**
     * 全局通用服务
     */
    public static final String SERVICE_NAME_GLOBAL = "global";
    /**
     * 用户角色模块
     */
    public static final String SERVICE_NAME_PERMISSION = "permission";
    /**
     * 审批模块
     */
    public static final String SERVICE_NAME_APPROVAL = "approval";
    /**
     * 日志模块
     */
    public static final String SERVICE_NAME_LOG = "log";
    /**
     * 合成作战
     */
    public static final String SERVICE_NAME_FIGHT = "fight";
    /**
     * 消息中心
     */
    public static final String SERVICE_NAME_MESSAGE = "message";
    /**
     * 管控中心
     */
    public static final String SERVICE_NAME_CONTROL = "control";
    /**
     * 档案
     */
    public static final String SERVICE_NAME_PROFILE = "profile";
    /**
     * 挂账盯办
     */
    public static final String SERVICE_NAME_TASK_TRACING = "task-tracing";
    /**
     * 风险
     */
    public static final String SERVICE_NAME_RISK = "risk";
    /**
     * 定时任务
     */
    public static final String SERVICE_NAME_SCHEDULE = "schedule";

    /**
     * 全文检索
     */
    public static final String SERVICE_NAME_SEARCH = "search";

    /**
     * 统计
     */
    public static final String SERVICE_NAME_STATISTIC = "statistic";
    /**
     * fx专题
     */
    public static final String SERVICE_NAME_FX_SUBJECT = "fx-subject";

    /**
     * 情指行
     */
    public static final String SERVICE_NAME_INTELLIGENCE = "intelligence";

    /**
     * 门户
     */
    public static final String PORTAL_SERVICE = "ulportal";

    /**
     * projects
     */
    public static final String PROJECTS = "projects";

    /**
     * 预警订阅模块
     */
    public static final String SERVICE_NAME_COMPARISON = "comparison";
}
