package com.trs.police.common.core.json.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.trs.police.common.core.vo.IdNameVO;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2022/06/19
 */
public class IdToEntityDeserializer extends JsonDeserializer<IdNameVO> {

    @Override
    public IdNameVO deserialize(JsonParser parser, DeserializationContext context) throws IOException {
        long id = parser.getValueAsLong();
        return new IdNameVO(id, null);
    }
}