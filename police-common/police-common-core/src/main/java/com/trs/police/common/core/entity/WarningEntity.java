package com.trs.police.common.core.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.trs.police.common.core.constant.enums.ControlTypeEnum;
import com.trs.police.common.core.constant.enums.MonitorLevelEnum;
import com.trs.police.common.core.handler.typehandler.JsonToLongListHandler;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 预警表(Warning)数据访问类
 *
 * <AUTHOR>
 * @since 2022-08-11 14:04:37
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "t_warning", autoResultMap = true)
public class WarningEntity implements Serializable {

    private static final long serialVersionUID = -64749959707901065L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT, value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新用户id
     */
    @TableField(fill = FieldFill.UPDATE, value = "update_user_id")
    private Long updateUserId;

    /**
     * 更新部门id
     */
    @TableField(fill = FieldFill.UPDATE, value = "update_dept_id")
    private Long updateDeptId;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE, value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 预警类型
     */
    private String warningType;

    /**
     * 预警级别
     */
    private MonitorLevelEnum warningLevel;

    /**
     * 预警详情
     */
    private String content;

    /**
     * 预警时间
     */
    private LocalDateTime warningTime;

    /**
     * 布控id
     */
    private Long monitorId;

    /**
     * 模型id
     */
    @TableField(typeHandler = JsonToLongListHandler.class)
    private List<Long> modelId;
    /**
     * 群体预警群体id
     */
    private Long groupId;

    /**
     * 管控类型 1=布控 2=常控
     */
    private ControlTypeEnum controlType;

    /**
     * 活动时间
     */
    private LocalDateTime activityTime;
    /**
     * 活动地点
     */
    private String activityAddress;

    /**
     * 预警关联的人的标签，用于统计
     */
    @TableField(typeHandler = JsonToLongListHandler.class)
    private List<Long> personLabel;
    /**
     * 命中的区域id
     */
    @TableField(typeHandler = JsonToLongListHandler.class)
    private List<Long> areaId;

    /**
     * 命中的场所code
     */
    @TableField(typeHandler = JsonToLongListHandler.class)
    private List<Long> placeCode;

    /**
     * fx预警状态
     */
    private Integer fxWarningStatus;

    /**
     * 命中专题-码表type:hit_subject_scene
     */
    private Long hitSubject;

    /**
     * 命中专题的场景-码表type:hit_subject_scene
     */
    private Long hitSubjectScene;

    /**
     * 发文字号 云控only
     */
    private String fwzh;

    /**
     * 首次处置反馈时限 云控only
     */
    private LocalDateTime scczfksx;

    /**
     * 预警指令ID 云控only
     */
    private String ywzjid;

    /**
     * 布控指令编号 云控only
     */
    private String lkzlbh;

    /**
     * 签收时限 云控only
     */
    private LocalDateTime qssx;

    /**
     * 活动类型 云控only
     */
    private String hdlx;

    /**
     * 处置措施要求 云控only
     */
    private String czcsyq;

    /**
     * 发布责任单位名称 云控only
     */
    private String fbzrdw;

    /**
     * 发布责任单位代码 云控only
     */
    private String fbzrdwjgdm;

    /**
     * 活动发起地点区划 云控only
     */
    private String hdfsddqh;

    /**
     * 活动发生地所属社会场所代码 云控only
     */
    private String hdfsddshcsdm;

    /**
     * 活动发生地所属社会场所 云控only
     */
    private String hdfsddshcs;

    /**
     * 预警平台
     */
    private Integer warningPlatform;

    /**
     * 布控平台 1：本地平台 2：云控平台 冗余自t_control_monitor
     */
    @TableField(typeHandler = JsonToLongListHandler.class)
    private List<Long> monitorPlatform;

    /**
     * 布控人单位id
     */
    private Long monitorPersonUnit;

    /**
     * 布控人单位代码
     */
    private String monitorUnitCode;
}