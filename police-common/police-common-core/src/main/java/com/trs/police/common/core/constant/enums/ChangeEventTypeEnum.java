package com.trs.police.common.core.constant.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/10/28 16:16
 */
public enum ChangeEventTypeEnum {
    /**
     * 增
     */
    CREATE("c"),
    /**
     * 改
     */
    UPDATE("u"),
    /**
     * 删除
     */
    DELETE("d"),
    ;
    @Getter
    private final String type;

    ChangeEventTypeEnum(String type) {
        this.type = type;
    }

    /**
     * 枚举类转换
     *
     * @param type 类型
     * @return {@link ChangeEventTypeEnum}
     */
    public static ChangeEventTypeEnum of(String type) {
        if (StringUtils.isNotBlank(type)) {
            for (ChangeEventTypeEnum typeEnum : ChangeEventTypeEnum.values()) {
                if (typeEnum.type.equals(type)) {
                    return typeEnum;
                }
            }
        }
        return null;
    }
}
