package com.trs.police.common.core.vo.control;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * WorkRecordDTO
 *
 * <AUTHOR>
 * @Date 2023/6/29 16:30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WorkRecordDTO implements Serializable {

    private static final long serialVersionUID = -1181913030651861709L;

    /**
     * 人员id
     */
    private Long personId;

    /**
     * 人员状态
     */
    private Integer status;

    /**
     * 工作方式
     */
    private Long workMethod;

    /**
     * 工作详情
     */
    private String workDetail;

    /**
     * 记录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
