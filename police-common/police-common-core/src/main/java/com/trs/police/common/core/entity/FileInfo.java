package com.trs.police.common.core.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.constant.enums.FileTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 文件信息
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "t_file_info")
public class FileInfo extends AbstractBaseEntity {

    private static final long serialVersionUID = 5718834031711163036L;

    /**
     * 文件名称
     */
    @TableField(value = "`name`")
    private String name;

    /**
     * 文件扩展名
     */
    @TableField(value = "ext")
    private String ext;

    /**
     * 文件大小(单位byte)
     */
    @TableField(value = "`size`")
    private Long size;

    /**
     * 文件类型
     */
    @TableField(value = "content_type")
    private FileTypeEnum contentType;

    /**
     * 文件sm3国密摘要
     */
    @TableField(value = "sm3")
    private String sm3;

    /**
     * 文件md5国密摘要
     */
    @TableField(value = "md5")
    private String md5;

    /**
     * 文件存储路径
     */
    @TableField(value = "`path`")
    private String path;

    /**
     * 文件状态,0 临时文件,1 永久文件
     */
    @TableField(value = "`status`")
    private Byte status;
    /**
     * 视频文件预览图片url
     */
    private String previewImage;

    /**
     * url
     */
    @TableField(value = "`url`")
    private String url;

    /**
     * 音视频时长(ms)
     */
    @TableField(value = "duration")
    private Long duration;

    @TableField(value = "pdf_url")
    private String pdfUrl;
}
