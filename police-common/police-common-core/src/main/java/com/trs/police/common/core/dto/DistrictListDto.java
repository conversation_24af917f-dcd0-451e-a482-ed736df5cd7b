package com.trs.police.common.core.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> yanghy
 * @date : 2022/10/12 11:12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DistrictListDto {

    /**
     * 主键
     */
    private Long id;
    /**
     * 码值
     */
    private String code;
    /**
     * 显示名称
     */
    private String name;

    private String shortName;
    /**
     * 上级代码(同一类型码表本身码值的上下关系)
     */
    private String pCode;
    /**
     * 级别
     */
    private Integer level;

    /**
     * 中心点
     */
    private String center;

    /**
     * 轮廓
     */
    private String contour;
    /**
     * path
     */
    private String path;
}
