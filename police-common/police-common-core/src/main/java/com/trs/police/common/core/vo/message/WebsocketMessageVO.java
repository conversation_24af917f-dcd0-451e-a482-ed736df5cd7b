package com.trs.police.common.core.vo.message;

import com.trs.police.common.core.constant.enums.MessageTypeEnum;
import com.trs.police.common.core.constant.message.WebsocketMessageTypeConstant;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.vo.permission.SimpleDeptVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/6 9:29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WebsocketMessageVO implements Serializable {

    private static final long serialVersionUID = -6283250104749468691L;

    /**
     * 前端使用的唯一id
     */
    private String unrealId;

    private Long crDeptId;

    private SimpleDeptVO crDept;

    /**
     * 消息id
     */
    private Long id;
    /**
     * 当前系统信息
     */
    private Channel systemInfo;
    /**
     * 合成id（或情指行中的chatId）
     */
    private Long conversationId;
    /**
     * {@link WebsocketMessageTypeConstant}
     */
    private String type;
    /**
     * 发送时间
     */
    private LocalDateTime sendTime;
    /**
     * 发送人信息
     */
    private CurrentUser sender;
    /**
     * 回复消息id
     */
    private Long referId;
    /**
     * 被回复次数
     */
    private Integer referCount;
    /**
     * 回复的消息内容
     */
    private WebsocketMessageVO referMessage;
    /**
     * 是否已读
     */
    private Boolean read;
    /**
     * 是否已撤回
     */
    private Boolean withdraw;
    /**
     * 消息内容
     */
    private Message message;
    /**
     * 关键信息
     */
    private Boolean important;

    /**
     * 是否是工作要求
     */
    private Boolean work;

    private Boolean isRecalled;

    /**
     * 数据来源 不会向来源方推送该消息
     */
    private Integer dataSource;

    private String sourcePrimaryKey;

    /**
     * 消息pojo类
     */
    @Data
    public static class Message implements Serializable {

        private static final long serialVersionUID = 2558976058424227886L;
        /**
         * 消息类型 文本/文档/音视频.etc
         * {@link MessageTypeEnum}
         */
        private List<String> types;
        /**
         * 消息内容
         */
        private String content;
        /**
         * 用于显示的纯文本消息内容
         */
        private String simpleContent;
        /**
         * 关联业务消息状态
         */
        private Integer status;
        /**
         * 关联人员档案身份证号
         */
        private List<String> idNumbers;
        /**
         * 关联车辆档案车牌号
         */
        private List<String> carNumbers;
        /**
         * 艾特列表
         */
        private List<Long> atIds;
        /**
         * 文件列表
         */
        private List<FileVO> files;
        /**
         * 关联业务id eg.战果
         */
        private List<Long> relatedModuleIds;
    }

    /**
     * 文件
     */
    @Data
    public static class FileVO implements Serializable {

        private static final long serialVersionUID = 3662797650886181937L;
        /**
         * 文件id
         */
        private Long id;
        /**
         * 文件类型
         */
        private String type;
        /**
         * 文件url
         */
        private String url;
        /**
         * 视频封面图url
         */
        private String coverUrl;
        /**
         * 文件名
         */
        private String name;
    }


}
