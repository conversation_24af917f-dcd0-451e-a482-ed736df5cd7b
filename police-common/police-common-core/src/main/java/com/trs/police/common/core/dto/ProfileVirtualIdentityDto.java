package com.trs.police.common.core.dto;

import com.trs.police.common.core.vo.profile.ListSourceVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/5/15 10:52
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProfileVirtualIdentityDto {

    /**
     * 人员id
     */
    private Long personId;
    /**
     * 证件号码
     */
    private String virtualNumber;
    /**
     * 虚拟身份类型
     */
    private Long type;
    /**
     * 来源
     */
    private ListSourceVO source;

}
