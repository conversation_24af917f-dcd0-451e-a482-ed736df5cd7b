package com.trs.police.common.core.ybss.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.trs.common.base.PreConditionCheck;
import com.trs.police.common.core.config.LnOneStandardThreeSensesInterfaceConfig;
import com.trs.police.common.core.utils.OkHttpUtil;
import com.trs.police.common.core.utils.StringUtil;
import com.trs.police.common.core.vo.PersonLiveInfoVO;
import com.trs.police.common.core.vo.PersonPhotoVO;
import com.trs.police.common.core.ybss.service.OneStandardThreeSensesService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 陇南一标三识服务类
 * *@author:tang.shuai
 * *@create 2024-07-08
 **/
@Service
@Slf4j
public class LnOneStandardThreeSensesServiceImpl implements OneStandardThreeSensesService {

    private static final String AUTHORIZATION = "Digest username=\"face\",realm=\"asit1400\"";

    private final LnOneStandardThreeSensesInterfaceConfig config;

    public LnOneStandardThreeSensesServiceImpl(LnOneStandardThreeSensesInterfaceConfig config) {
        this.config = config;
    }

    private final OkHttpUtil okHttpUtil = OkHttpUtil.getInstance();

    @Override
    public PersonPhotoVO getPersonPhoto(String idNumber) {

        PreConditionCheck.checkArgument(!StringUtil.isEmpty(idNumber), "身份证号码不能为空");
        //2.封装请求体
        Map<String, String> headersMap = new HashMap<>();
        headersMap.put("appKey", config.getAppKey());
        headersMap.put("senderId", config.getSenderId());
        headersMap.put("serviceResourceId", config.getServiceResourceId());
        headersMap.put("userId", config.getUserId());
        headersMap.put("Authorization", AUTHORIZATION);
        String url = String.format("%s/%s", config.getUrl(), String.format("rest/static-faceb?idNumbers=%s", idNumber));
        //3.执行请求
        String result = okHttpUtil.getData(url, headersMap);
        //4.解析结构
        JSONObject photoInfo = JSON.parseObject(result);
        if (Objects.isNull(photoInfo) || StringUtils.isEmpty(photoInfo.getString("rows"))) {
            log.error("陇南人像接口返回异常,异常信息为:{}", photoInfo);
            return null;
        }
        JSONArray rows = photoInfo.getJSONArray("rows");
        JSONObject person = rows.getJSONObject(0).getJSONObject("person");
        Long personId = person.getLong("id");
        if (Objects.isNull(personId)) {
            log.error("陇南人像接口返回异常,异常信息为:{}", personId);
            return null;
        }
        String photo = getPhoto(personId);
        PersonPhotoVO personPhotoVO = new PersonPhotoVO();
        personPhotoVO.setXm(person.getString("name"));
        personPhotoVO.setSfzh(idNumber);
        personPhotoVO.setZp(photo);

        return personPhotoVO;
    }

    /**
     * 获取照片
     *
     * @param personId personId
     * @return String
     */
    private String getPhoto(Long personId) {
        try {
            // 构造 URL
            String url = String.format("%s/%s", config.getPhotoUrl(), String.format("rest/face/static-image/binary?personId=%s", personId));
            // 创建 OkHttpClient
            OkHttpClient client = new OkHttpClient();
            // 创建请求
            Request request = new Request.Builder()
                    .url(url)
                    .build();
            log.info("url请求结果:{}", request);
            // 执行请求
            try (Response response = client.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    byte[] imageBytes = response.body().bytes();
                    // 转换为 Base64
                    String encodeToString = Base64.getEncoder().encodeToString(imageBytes);
                    log.info("Base64编码后的图片：{}", encodeToString);
                    return encodeToString;
                } else {
                    throw new RuntimeException("Failed to get image: " + response.message());
                }
            }
        } catch (Exception e) {
            log.error("请求异常", e);
            return null;
        }
    }

    /**
     * 获取人员居住信息
     *
     * @param idNumber idNumber
     * @return PersonLiveInfoVO
     */
    @Override
    public PersonLiveInfoVO requestPersonLiveInfo(String idNumber) {
        return null;
    }

    @Override
    public String key() {
        return "lnOneStandardThreeSensesServiceImpl";
    }

    @Override
    public String desc() {
        return "陇南一标三识服务类";
    }
}
