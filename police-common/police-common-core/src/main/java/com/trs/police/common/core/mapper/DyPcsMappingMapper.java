package com.trs.police.common.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.common.core.entity.DyPcsMappingEntity;
import com.trs.police.common.core.vo.permission.DeptVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * DyPcsMappingMapper
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/12/20 17:24
 * @since 1.0
 */
@Mapper
public interface DyPcsMappingMapper extends BaseMapper<DyPcsMappingEntity> {

    /**
     * findAllDept<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/12/20 17:42
     */
    List<DeptVO> findAllDept();

    /**
     * findByPcsbm<BR>
     *
     * @param pcsbm 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/12/20 17:53
     */
    DyPcsMappingEntity findByPcsbm(String pcsbm);

    /**
     * findByQxDy<BR>
     *
     * @param qxDyShortCode 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/12/20 18:33
     */
    List<DyPcsMappingEntity> findByQxDyShortCode(@Param("qxDyShortCode") String qxDyShortCode);

    /**
     * 获取所有的mapping配置信息
     *
     * @param namePrefix pcs名称前缀
     * @return 结果
     */
    @Select("select * from tb_dy_pcs_mapping where pcsmc like concat(concat(#{namePrefix}), '%')")
    List<DyPcsMappingEntity> findByPcsmcPrefix(@Param("namePrefix") String namePrefix);
}
