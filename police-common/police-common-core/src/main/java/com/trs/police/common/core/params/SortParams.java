package com.trs.police.common.core.params;

import java.io.Serializable;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 排序参数
 *
 * <AUTHOR>
 */
@Data
public class SortParams implements Serializable {

    private static final long serialVersionUID = -8588554261102324168L;

    /**
     * 升序
     */
    private static final String ASC = "asc";

    private static final String ASCENDING = "ascending";

    /**
     * 降序
     */
    private static final String DESCENDING = "descending";

    private static final String DESC = "desc";
    /**
     * 排序字段
     */
    private String sortField;
    /**
     * 排序方向
     */
    private String sortDirection;

    /**
     * 获取处理后的值
     *
     * @return {@link String}
     */
    public String getProcessedValue() {
        if (StringUtils.isNotBlank(sortDirection) && (DESCENDING.equalsIgnoreCase(sortDirection)
            || DESC.equalsIgnoreCase(sortDirection))) {
            return DESC;
        } else {
            return ASC;
        }
    }

    /**
     * isDesc<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/2/19 20:34
     */
    public Boolean isDesc() {
        return !isAsc();
    }

    /**
     * isAsc<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/2/19 20:34
     */
    public Boolean isAsc() {
        return ASC.equals(getProcessedValue());
    }

    /**
     * 根据sortDirection比较两个值
     *
     * @param i1 值1
     * @param i2 值2
     * @return 结果
     */
    public int getCompare(Integer i1, Integer i2) {
        if (getProcessedValue().equals(ASC)) {
            return i1.compareTo(i2);
        } else {
            return -i1.compareTo(i2);
        }
    }

    /**
     * 根据sortDirection比较两个值
     *
     * @param i1 值1
     * @param i2 值2
     * @return 结果
     */
    public int getCompare(Double i1, Double i2) {
        if (getProcessedValue().equals(ASC)) {
            return i1.compareTo(i2);
        } else {
            return -i1.compareTo(i2);
        }
    }

    /**
     * 根据sortDirection比较两个值
     *
     * @param i1 值1
     * @param i2 值2
     * @return 结果
     */
    public int getCompare(Long i1, Long i2) {
        if (getProcessedValue().equals(ASC)) {
            return i1.compareTo(i2);
        } else {
            return -i1.compareTo(i2);
        }
    }

}
