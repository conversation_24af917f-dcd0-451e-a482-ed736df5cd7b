package com.trs.police.common.core.dto.approval;

import com.trs.police.common.core.constant.ApprovalNodeFieldPramConstant;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 自定义节点参数
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class ApprovalNodeFieldParam implements Serializable {

    /**
     * 字段名 目前仅支持childDeptType {@link ApprovalNodeFieldPramConstant}
     */
    private String fieldName;

    /**
     * 字段值 可以是多值，如果传了多值，单个节点创建多个审批人（或签关系）  <br>
     * 强制建议单个审批节点最多传一次多值，否则报错（因为两两组合，组合结果会指数增长，不利于流程控制）
     */
    private List<String> value;

    public ApprovalNodeFieldParam(String fieldName, List<String> value) {
        this.fieldName = fieldName;
        this.value = value;
    }
}
