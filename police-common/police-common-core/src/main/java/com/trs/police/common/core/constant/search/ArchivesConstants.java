package com.trs.police.common.core.constant.search;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName ArchivesConstants
 * @Description 档案相关常量信息
 * <AUTHOR>
 * @Date 2023/10/9 9:50
 **/
public class ArchivesConstants {

    /**
     * 业务库真实索引前缀
     */
    public static final String THEME_PREFIX = "theme_";

    /**
     * 档案类型-全部
     */
    public static final String ARCHIVES_TYPE_ALL = "all";

    /**
     * 档案类型-人员
     */
    public static final String ARCHIVES_TYPE_PERSON = "person";

    /**
     * 档案类型-车辆
     */
    public static final String ARCHIVES_TYPE_CAR = "car";

    /**
     * 档案类型-手机
     */
    public static final String ARCHIVES_TYPE_PHONE = "phone";

    /**
     * 档案类型-企业
     */
    public static final String ARCHIVES_TYPE_COMPANY = "company";

    /**
     * 业务档案
     */
    public static final String ARCHIVES_TYPE_BUSINESS = "business";
    public static final String ARCHIVES_TYPE_BUSINESS_FOR_CLUE = "businessForClue";
    public static final String ARCHIVES_TYPE_BUSINESS_FOR_JQ = "businessForJQ";
    public static final String ARCHIVES_TYPE_BUSINESS_FOR_XT = "businessForXT";
    public static final String ARCHIVES_TYPE_BUSINESS_FOR_BK = "businessForBK";

    /**
     * 档案类型-案件
     */
    public static final String ARCHIVES_TYPE_CASE = "case";

    /**
     * 档案类型-列表
     */
    public static final List<String> ARCHIVES_INDEX_NAMES = Arrays.asList(
            "archive_person", "archive_vehicle",
            "archive_case", "archive_phone"
    );

    /**
     * 特殊类型字段映射map
     */
    public static final Map<String, String> ARCHIVES_SPECIAL_FIELDS_KEY_MAP = new HashMap<>() {{
        put("标签信息", "tag_name");
        put("车辆信息", "plate_no");
        put("电话信息", "phone_no");
        put("地址信息", "address");
        put("驾驶证信息", "class");
        put("护照信息", "passport_no");
        put("相关人员", "zjhm");
        put("警情信息", "jqbh");
    }};

    /**
     * 人员顺序字段
     */
    public static final List<String> PERSON_ARCHIVES_FIELD_LIST = Arrays.asList(
            "xm", "zjhm", "cym", "xb", "csrq", "mz", "sg", "hyzk",
            "jg", "whcd", "zzmm", "byzk", "zjxy", "xx", "zy", "fwcs", "xzdz", "tags", "cars", "phones", "addresses",
            "driving_licences", "passports"
    );

    /**
     * 人员档案字段与中文名称映射信息
     */
    public static final Map<String, String> ARCHIVES_FIELD_EN_ZH_MAP_PERSON = new HashMap<>() {{
        put("xm", "姓名");
        put("zjhm", "证件号码");
        put("cym", "曾用名");
        put("xb", "性别");
        put("mz", "民族");
        put("csrq", "出生日期");
        put("jg", "籍贯");
        put("zy", "职业");
        put("fwcs", "服务场所");
        put("whcd", "文化程度");
        put("hyzk", "婚姻状况");
        put("byzk", "兵役状况");
        put("zjxy", "宗教信仰");
        put("zzmm", "政治面貌");
        put("xx", "血型");
        put("sg", "身高");
        put("xzdz", "现住地址");
        put("tags", "标签信息");
        put("cars", "车辆信息");
        put("phones", "电话信息");
        put("addresses", "地址信息");
        put("driving_licences", "驾驶证信息");
        put("passports", "护照信息");
    }};

    /**
     * 车辆顺序字段
     */
    public static final List<String> CAR_ARCHIVES_FIELD_LIST = Arrays.asList(
            "czxm", "clwybs", "czsfzhm", "hpzl", "jdchlj",
            "jdcltgg", "nyzl", "clph", "jdcwlkgd", "jdcsyq", "jdcwlkcd", "dz", "clsbdm", "hbdbqk", "clzbzl", "hdzkrs",
            "fdjgl", "jdczj", "jdcwlkkd", "syxz", "fzjg", "lxdh", "jdccchgzbh", "fdjpl", "csys", "jdczs", "fzrz", "dabh",
            "jdchdfs", "tags", "ccrq", "ccdjrq", "jdcqlj", "clxh", "zzsmc", "jbr", "hphm", "wfzt", "fdjbh", "cscd",
            "wzclr");

    /**
     * 车辆档案字段与中文名称映射信息
     */
    public static final Map<String, String> ARCHIVES_FIELD_EN_ZH_MAP_CAR = new HashMap<>() {{
        put("hpzl", "号牌种类");
        put("hphm", "号牌号码");
        put("syxz", "使用性质");
        put("czxm", "车主姓名");
        put("czsfzhm", "车主身份证号码");
        put("lxdh", "联系电话");
        put("fzjg", "发证机关");
        put("jbr", "经办人");
        put("dz", "地址");
        put("dabh", "档案编号");
        put("fzrz", "发证日期");
        put("ccdjrq", "初次登记日期");
        put("ccrq", "出厂日期");
        put("clsbdm", "车辆识别代码");
        put("wfzt", "违法状态");
        put("jdcsyq", "机动车所有权");
        put("jdchdfs", "机动车获得方式");
        put("fdjpl", "发动机排量");
        put("fdjgl", "发动机功率");
        put("jdccchgzbh", "机动车出厂合格证编号");
        put("fdjbh", "发动机编号");
        put("jdcltgg", "机动车轮胎规格");
        put("jdchlj", "机动车后轮距");
        put("jdcqlj", "机动车前轮距");
        put("jdczs", "机动车轴数");
        put("jdczj", "机动车轴距");
        put("clxh", "车辆型号");
        put("jdcwlkkd", "机动车外轮廓宽度");
        put("jdcwlkgd", "机动车外轮廓高度");
        put("jdcwlkcd", "机动车外轮廓长度");
        put("cscd", "车身长度");
        put("hdzkrs", "核定载客人数");
        put("clph", "车辆品牌");
        put("csys", "车身颜色");
        put("nyzl", "能源种类");
        put("hbdbqk", "环保达标情况");
        put("clwybs", "车辆唯一标识");
        put("clzbzl", "车辆整备质量");
        put("zzsmc", "制造商名称");
        // obj
        put("tags", "标签信息");
        // obj
        put("wzclr", "违章处理人");
    }};

    /**
     * 案件顺序字段
     */
    public static final List<String> CASE_ARCHIVES_FIELD_LIST = Arrays.asList(
            "ajmc", "ajbh", "ajlb", "slsj", "jqxx",
            "asjfsdd", "jyaq", "ajzt", "xgry", "tags"
    );

    /**
     * 案件档案字段与中文名称映射信息
     */
    public static final Map<String, String> ARCHIVES_FIELD_EN_ZH_MAP_CASE = new HashMap<>() {{
        put("ajmc", "案件名称");
        put("ajbh", "案件编号");
        put("asjfsdd", "案事件发生地点");
        put("ajzt", "案件状态");
        put("jyaq", "简要案情");
        put("ajlb", "案件类别");
        put("slsj", "受理时间");
        // obj
        put("jqxx", "警情信息");
        // obj
        put("xgry", "相关人员");
        // obj
        put("tags", "标签信息");
    }};

    /**
     * 手机档案顺序字段
     */
    public static final List<String> PHONE_ARCHIVES_FIELD_LIST = Arrays.asList(
            "sjhm", "jzzjhm", "rwsj", "zt", "djdz",
            "yys", "gsd", "jzxm", "tjsj", "misis", "imeis", "glr", "tags");

    /**
     * 手机档案字段与中文名称映射信息
     */
    public static final Map<String, String> ARCHIVES_FIELD_EN_ZH_MAP_PHONE = new HashMap<>() {{
        put("sjhm", "手机号码");
        put("yys", "运营商");
        put("gsd", "归属地");
        put("rwsj", "入网时间");
        put("zt", "状态");
        put("tjsj", "受理时间");
        put("jzzjhm", "机主证件号码");
        put("jzxm", "机主姓名");
        put("djdz", "登记地址");
        put("glr", "关联人");
        put("misis", "imsi");
        put("imeis", "imei");
        put("tags", "标签信息");
    }};

    public static final List<String> COMPANY_ARCHIVES_FIELD_LIST = Arrays.asList(
            "company_type", "reg_number", "city", "revoke_reason", "description", "industry",
            "trs_id", "products", "actual_address", "contributed_capital", "administrative_penalties_num",
            "province", "have_administrative_penalties", "abnormal_operation_reason", "tax_number",
            "abnormal_operation_reason_present", "have_abnormal_operation_present", "website_states",
            "company_status", "credit_number", "have_abnormal_operation", "trs_source_from",
            "website", "address", "abnormal_operation_num", "juridical_person", "regit_capital",
            "administrative_penalties_type", "business_scope", "reg_authority", "company_name",
            "org_code", "products_cc", "industry_cc"
    );

    /**
     * 全量更新，类似于删除后插入
     */
    public static final String FULL_UPDATE = "fullUpdate";

    /**
     * 增量更新，只更新传递来的字段
     */
    public static final String INCREMENTAL_UPDATE = "incrementalUpdate";

    /**
     * 删除行为
     */
    public static final String DELETE = "delete";

    /**
     * 标签搜索类型，包含全部
     */
    public static final String SEARCH_LABELS_TYPE_ALL = "ALL";

    /**
     * 标签搜索类型，包含任意一个
     */
    public static final String SEARCH_LABELS_TYPE_ANY = "ANY";

    /**
     * 未知
     */
    public static final String CN_WEIZHI = "未知";

    public static final String ZHONG_TAI = "zhongtai";
}
