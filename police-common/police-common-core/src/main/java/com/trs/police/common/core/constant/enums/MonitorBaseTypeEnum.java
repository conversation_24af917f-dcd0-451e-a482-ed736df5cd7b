package com.trs.police.common.core.constant.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.Objects;
import lombok.Getter;

/**
 * 布控类型 （人员|群体）
 *
 * <AUTHOR> yanghy
 * @date : 2022/8/29 17:21
 */
public enum MonitorBaseTypeEnum {

    /**
     * 人员
     */
    PERSON(1, "person", "人员"),
    /**
     * 群体
     */
    GROUP(2, "group", "群体"),
    /**
     * 区域
     */
    AREA(3,"area", "区域");


    @JsonValue
    @Getter
    @EnumValue
    private final Integer code;

    @Getter
    private final String enName;

    @Getter
    private final String name;

    MonitorBaseTypeEnum(Integer code, String enName, String name) {
        this.code = code;
        this.name = name;
        this.enName = enName;
    }

    /**
     * code转换枚举
     *
     * @param code 编码
     * @return 枚举
     */
    @JsonCreator
    public static MonitorBaseTypeEnum codeOf(Integer code) {
        if (Objects.nonNull(code)) {
            for (MonitorBaseTypeEnum typeEnum : MonitorBaseTypeEnum.values()) {
                if (code.equals(typeEnum.getCode())) {
                    return typeEnum;
                }
            }
        }
        return null;
    }

    /**
     * enName转换枚举
     *
     * @param enName 编码
     * @return 枚举
     */
    @JsonCreator
    public static MonitorBaseTypeEnum nameOf(String enName) {
        if (Objects.nonNull(enName)) {
            for (MonitorBaseTypeEnum typeEnum : MonitorBaseTypeEnum.values()) {
                if (enName.equals(typeEnum.getEnName())) {
                    return typeEnum;
                }
            }
        }
        return null;
    }
}
