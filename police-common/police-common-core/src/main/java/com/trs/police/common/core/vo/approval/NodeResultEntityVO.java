package com.trs.police.common.core.vo.approval;

import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 审批步骤结果表(ApprovalNodeResult)数据访问类
 *
 * <AUTHOR>
 * @since 2022-06-15 09:58:09
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NodeResultEntityVO extends AbstractBaseEntity implements Serializable {

    private static final long serialVersionUID = -75193892391422819L;

    /**
     * 审批流程主键
     */
    private Long processId;
    /**
     * 节点id
     */
    private Long nodeId;
    /**
     * 审批用户id
     */
    private Long userId;
    /**
     * 审批用户部门
     */
    private Long deptId;
    /**
     * 审批结果
     */
    private Integer result;
    /**
     * 审批表单内容
     */
    private MonitorFormData nodeFormContent;
}