package com.trs.police.common.core.entity.approval;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.Data;

/**
 * @author: dingkeyu
 * @date: 2024/11/25
 * @description:
 */
@Data
@TableName(value = "t_approval_template_edit_record")
public class ApprovalTemplateEditRecord extends AbstractBaseEntity {

    @TableField("target_id")
    private Integer targetId;

    @TableField("source_id")
    private Integer sourceId;
}
