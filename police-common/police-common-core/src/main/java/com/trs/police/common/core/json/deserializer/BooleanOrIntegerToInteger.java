package com.trs.police.common.core.json.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.trs.common.utils.StringUtils;

import java.io.IOException;
import java.util.Objects;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2025</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2025/4/8 17:16
 * @since 1.0
 */
public class BooleanOrIntegerToInteger extends JsonDeserializer<Integer> {

    private final Integer defaultEmptyValue;
    private final Integer defaultNullValue;

    public BooleanOrIntegerToInteger() {
        this(null, null);
    }

    public BooleanOrIntegerToInteger(Integer defaultEmptyValue, Integer defaultNullValue) {
        this.defaultEmptyValue = defaultEmptyValue;
        this.defaultNullValue = defaultNullValue;
    }

    @Override
    public Integer deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
        String value = jsonParser.getValueAsString();
        if (StringUtils.isInt(value)) {
            return Integer.parseInt(value);
        } else if (Objects.equals(Boolean.TRUE.toString(), value)) {
            return 1;
        } else if (Objects.equals(Boolean.FALSE.toString(), value)) {
            return 0;
        } else if (Objects.isNull(value)) {
            return defaultNullValue;
        } else {
            return defaultEmptyValue;
        }
    }

    /**
     * <p>Title:        TRS</p>
     * <p>Copyright:    Copyright (c) 2004-2025</p>
     * <p>Company:      www.trs.com.cn</p>
     * 类描述：
     *
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @version 1.0
     * @date 创建时间：2025/4/8 17:50
     * @since 1.0
     */
    public static class NullOrEmptyToZero extends BooleanOrIntegerToInteger {
        public NullOrEmptyToZero() {
            super(0, 0);
        }
    }

    /**
     * <p>Title:        TRS</p>
     * <p>Copyright:    Copyright (c) 2004-2025</p>
     * <p>Company:      www.trs.com.cn</p>
     * 类描述：
     *
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @version 1.0
     * @date 创建时间：2025/4/8 17:50
     * @since 1.0
     */
    public static class EmptyToZero extends BooleanOrIntegerToInteger {
        public EmptyToZero() {
            super(0, null);
        }
    }
}
