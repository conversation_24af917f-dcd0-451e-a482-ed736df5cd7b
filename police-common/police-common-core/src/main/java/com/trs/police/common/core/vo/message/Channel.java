package com.trs.police.common.core.vo.message;

import static com.trs.police.common.core.constant.message.SystemMessageTypeConstant.MESSAGE_TYPE_CHANNEL_INFO;
import static com.trs.police.common.core.constant.message.SystemMessageTypeConstant.MESSAGE_TYPE_CHANNEL_OPERATION;
import static com.trs.police.common.core.constant.message.SystemMessageTypeConstant.MESSAGE_TYPE_MODULE_COMPOSITE;
import static com.trs.police.common.core.constant.message.SystemMessageTypeConstant.MESSAGE_TYPE_MODULE_INFO;
import static com.trs.police.common.core.constant.message.SystemMessageTypeConstant.MESSAGE_TYPE_SYSTEM_FIGHT;
import static com.trs.police.common.core.constant.message.SystemMessageTypeConstant.MESSAGE_TYPE_SYSTEM_MESSAGE;

import java.io.Serializable;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用于描述连接channel
 *
 * <AUTHOR>
 * @date 2022/04/01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Channel implements Serializable {

    private static final long serialVersionUID = 2651123191983330393L;

    /**
     * 系统名称：例如 fight
     */
    private String system;

    /**
     * 模块名称: 例如 composite
     */
    private String module;

    /**
     * 频道名称：例如 message
     */
    private String channelName;

    /**
     * 频道id：例如 12345
     */
    private String channelId;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        Channel channel = (Channel) o;
        return Objects.equals(system, channel.system) && Objects.equals(module, channel.module)
            && Objects.equals(channelName, channel.channelName) && Objects.equals(channelId,
            channel.channelId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(system, module, channelName, channelId);
    }

    /**
     * 获取消息中心channel
     *
     * @return 消息中心channel
     */
    public static Channel getMessageCenterChannel() {
        return new Channel(MESSAGE_TYPE_SYSTEM_MESSAGE, MESSAGE_TYPE_MODULE_INFO, MESSAGE_TYPE_CHANNEL_INFO, null);
    }

    /**
     * 获取合成作战聊天
     *
     * @return 消息中心channel
     */
    public static Channel getCompositeChannel() {
        return new Channel(MESSAGE_TYPE_SYSTEM_FIGHT, MESSAGE_TYPE_MODULE_COMPOSITE,MESSAGE_TYPE_CHANNEL_OPERATION, null);
    }

    /**
     * 获取合成作战聊天
     *
     * @return 消息中心channel
     */
    public static Channel getSearchChannel() {
        return new Channel("search", "list","count", null);
    }
}
