package com.trs.police.common.core.vo.permission;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 用户数据权限信息
 * 保存用户所拥有的各种具体数据权限值，用于检查数据权限时与所需的数据权限进行比对
 *
 * <AUTHOR>
 */
@Data
public class DataPermissionInfo implements Serializable {

    private static final long serialVersionUID = -7016161368377677048L;

    /**
     * 用户id
     */
    private Long userId;
    /**
     * 用户姓名
     */
    private String userName;
    /**
     * 用户身份证号
     */
    private String userIdNumber;
    /**
     * 部门id
     */
    private Long deptId;
    /**
     * 部门代码
     */
    private String deptCode;
    /**
     * 本部门及以下id列表
     */
    private List<Long> deptIds;
    /**
     * 本部门及以下code列表
     */
    private List<String> deptCodes;
    /**
     * 地区id
     */
    private Long districtId;
    /**
     * 地区代码
     */
    private String districtCode;
    /**
     * 本地区及以下id列表
     */
    private List<Long> districtIds;
    /**
     * 本地区及以下code列表
     */
    private List<String> districtCodes;

    /**
     * 数据权限名称
     */
    private String permission;

    /**
     * 档案数据权限
     */
    private ProfilePermission profilePermission;

    /**
     * 指定警种
     */
    private List<Long> policeKinds;

    /**
     * 获取指定字段值
     *
     * @param fieldName 字段名
     * @return 字段值
     * @throws NoSuchFieldException 找不到字段异常
     * @throws IllegalAccessException 非法访问异常
     */
    public Object getField(String fieldName) throws NoSuchFieldException, IllegalAccessException {
        return this.getClass().getDeclaredField(fieldName).get(this);
    }

    /**
     * 档案数据权限
     */
    @Data
    public static class ProfilePermission implements Serializable{

        private static final long serialVersionUID = -7016161368377677049L;

        /**
         * 人员标签
         */
        private List<Long> personLabelIds;
        /**
         * 群体标签
         */
        private List<Long> groupLabelIds;
        /**
         * 事件标签
         */
        private List<Long> eventLabelIds;
        /**
         * 线索标签
         */
        private List<Long> clueLabelIds;
        /**
         * 案件标签
         */
        private List<Long> caseLabelIds;
        /**
         * 警情标签
         */
        private List<Long> jqLabelIds;

        /**
         * 人员标签
         */
        private Boolean personIsNull;
        /**
         * 群体标签
         */
        private Boolean groupIsNull;
        /**
         * 事件标签
         */
        private Boolean eventIsNull;
        /**
         * 线索标签
         */
        private Boolean clueIsNull;
        /**
         * 案件标签
         */
        private Boolean caseIsNull;
        /**
         * 警情标签
         */
        private Boolean jqIsNull;
    }
}
