package com.trs.police.common.core.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.handler.typehandler.JsonToLongListHandler;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 人员档案公安管控
 *
 * <AUTHOR>
 * @since 2022/11/17 10:51
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_profile_person_police_control", autoResultMap = true)
public class ProfilePersonPoliceControlEntity extends AbstractBaseEntity {

    private static final long serialVersionUID = -5221266505670207828L;
    /**
     * 人员id
     */
    private Long personId;

    /**
     * 责任分局code
     */
    private String controlBureau;
    /**
     * 责任分局领导id
     */
    private Long controlBureauLeader;
    /**
     * 责任警种部门id
     */
    private String controlPolice;

    /**
     * 责任警种部门领导id
     */
    private Long controlPoliceLeader;

    /**
     * 责任派出所id
     */
    private String controlStation;
    /**
     * 责任派出所领导id
     */
    private Long controlStationLeader;
    /**
     * 责任民警
     */
    @TableField(typeHandler = JsonToLongListHandler.class)
    private List<Long> controlPerson;

    /**
     * 管控警种
     */
    private Long policeKind;

    /**
     * 技术布控情况
     */
    @TableField(typeHandler = JsonToLongListHandler.class)
    private List<Long> technologyControl;
}
