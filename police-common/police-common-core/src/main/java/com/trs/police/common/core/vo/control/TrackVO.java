package com.trs.police.common.core.vo.control;

import lombok.Data;

/**
 * 轨迹vo
 *
 * <AUTHOR>
 */
@Data
public class TrackVO {

    /**
     * id
     */
    private String id;
    /**
     * 经度
     */
    private Double lng;
    /**
     * 纬度
     */
    private Double lat;
    /**
     * 时间
     */
    private String time;
    /**
     * 地址
     */
    private String address;

    /**
     * 感知源id
     */
    private String sourceId;

    /**
     * 感知源名称
     */
    private String gzymc;

    /**
     * 感知源类型英文
     */
    private String gzylx;

    /**
     * 活动时间
     */
    private String activityTime;

    /**
     * 活动地址
     */
    private String activityAddress;

    /**
     * 预警时间
     */
    private String warningTime;

    /**
     * 预警内容
     */
    private String content;

    /**
     * 预警id
     */
    private String warningId;

    /**
     * 辖区
     */
    private String controlStation;
}
