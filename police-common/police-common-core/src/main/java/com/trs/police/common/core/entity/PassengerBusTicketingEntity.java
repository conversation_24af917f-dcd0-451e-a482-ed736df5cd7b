package com.trs.police.common.core.entity;

import com.trs.db.sdk.annotations.TableField;
import com.trs.db.sdk.annotations.TableName;
import com.trs.db.sdk.pojo.BaseRecordDO;
import lombok.Data;

/**
 * @ClassName dws_gzyq_hz_bus_passenger_transport_ticket_purchase
 * @Description 客运汽车售票信息实体类
 * <AUTHOR>
 * @Date 2024/2/18 9:42
 **/
@Data
@TableName("dws_gzyq_hz_passenger_bus_ticketing")
public class PassengerBusTicketingEntity extends BaseRecordDO {

    /**
     * 活动发生行政区划
     */
    @TableField
    private String hdfsddxzqh;

    /**
     * 轨迹信息编号
     */
    @TableField
    private String gjxxbh;

    /**
     * 对象编号
     */
    @TableField
    private String dxbh;

    /**
     * 活动发生时间
     */
    @TableField
    private String hdfssj;

    /**
     * 对象属性
     */
    @TableField
    private String dxsx;

    /**
     * 活动发生地点
     */
    @TableField
    private String hdfsdd;

    /**
     * 活动发生涉案场所
     */
    @TableField
    private String hdfsdssshcs;

    /**
     * 活动发生涉案场所代码
     */
    @TableField
    private String hdfsdssshcsdm;

    /**
     * 活动发生地点经度
     */
    @TableField
    private Double hdfsdjd;

    /**
     * 活动发生地点纬度
     */
    @TableField
    private Double hdfsdwd;

    /**
     * 活动共享信息 - 航班号
     */
    @TableField(value = "hdxgxx.hbh")
    private String hdxgxxHbh;

    /**
     * 活动共享信息 - 证件号码
     */
    @TableField(value = "hdxgxx.zjhm")
    private String hdxgxxZjhm;

    /**
     * 活动共享信息 - 起飞时间
     */
    @TableField(value = "hdxgxx.qfsj")
    private String hdxgxxQfsj;

    /**
     * 活动共享信息 - 机票状态
     */
    @TableField(value = "hdxgxx.jpzt")
    private String hdxgxxJpzt;

    /**
     * 活动共享信息 - 到达时间
     */
    @TableField(value = "hdxgxx.ddsj")
    private String hdxgxxDdsj;

    /**
     * 活动共享信息 - 目的地经度
     */
    @TableField(value = "hdxgxx.mddjd")
    private Double hdxgxxMddjd;

    /**
     * 活动共享信息 - 目的地纬度
     */
    @TableField(value = "hdxgxx.mddwd")
    private Double hdxgxxMddwd;

    /**
     * 活动共享信息 - 目的地行政区划
     */
    @TableField(value = "hdxgxx.mddxzqh")
    private String hdxgxxMddxzqh;

    /**
     * 活动共享信息 - 目的地
     */
    @TableField(value = "hdxgxx.mdd")
    private String hdxgxxMdd;

    /**
     * 活动共享信息 - 目的地代码
     */
    @TableField(value = "hdxgxx.mdddm")
    private String hdxgxxMdddm;

    /**
     * 活动共享信息 - 目的地地点
     */
    @TableField(value = "hdxgxx.mdddd")
    private String hdxgxxMdddd;

    /**
     * 信息类别
     */
    @TableField
    private String dtxxlb;

    /**
     * 活动发生涉案公安机关
     */
    @TableField
    private String hdfsdssgajg;

    /**
     * 活动发生涉案公安机关代码
     */
    @TableField
    private String hdfsdssgajgdm;

    /**
     * 活动信息明细
     */
    @TableField
    private String hdxxms;

    /**
     * 动态信息提供机构
     */
    @TableField
    private String dtxxtgjg;

    /**
     * 动态信息提供机构代码
     */
    @TableField
    private String dtxxtgjgdm;
}
