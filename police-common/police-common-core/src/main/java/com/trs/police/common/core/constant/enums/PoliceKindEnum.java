package com.trs.police.common.core.constant.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Objects;
import java.util.Optional;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/5/16 12:18
 * @since 1.0
 */
public enum PoliceKindEnum {
    QINGZHI(1, "情指", "情报指挥中心"),
    ZHENGBAO(2, "政保", "政治安全保卫"),
    JINGZHEN(3, "经侦", "经济犯罪侦查"),
    ZHIAN(4, "治安", "治安管理"),
    XINGZHEN(5, "刑侦", "刑事侦查"),
    CHURUJING(6, "出入境", "出入境管理"),
    WANGAN(7, "网安", "网络安全保卫"),
    JIZHEN(8, "技侦", "技术侦查"),
    SHIYAOHUAN(9, "食药环", "食品药品环境犯罪侦查"),
    JINDU(10, "禁毒", "禁毒缉毒"),
    FAZHI(11, "法制", "法制"),
    FANKONG(12, "反恐", "反恐怖"),
    TEJING(13, "特警", "特警"),
    JIAOJING(14, "交警", "交通警察"),
    JICHANG(15, "机场", "机场"),
    SHENLIN(16, "森林", "森林警察"),
    JULIUSUO(17, "拘留所", "拘留所"),
    KANSHOUSUO(18, "看守所", "看守所"),
    DUCHA(19, "督察", "警务督察"),
    KEXIN(20, "科信", "科技信息化"),
    JINGBAO(21, "警保", "警务保障"),
    JIGUANDANGWEI(22, "机关党委", "机关党委"),
    ZHENGZHIBU(23, "政治部", "政治部"),
    XUANCHUANBU(24, "宣传部", "宣传部"),
    JIANGUAN(25, "监管", "监所管理"),
    JIJIANWEI(26, "纪检委", "纪检委"),
    OHTHER(99, "其他", "其他");

    @JsonValue
    @Getter
    @EnumValue
    private final Integer code;

    @Getter
    private final String name;

    @Getter
    private final String desc;

    PoliceKindEnum(Integer code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    /**
     * code转换枚举
     *
     * @param code 编码
     * @return 枚举
     */
    @JsonCreator
    public static PoliceKindEnum codeOf(Integer code) {
        if (Objects.nonNull(code)) {
            for (PoliceKindEnum typeEnum : PoliceKindEnum.values()) {
                if (code.equals(typeEnum.getCode())) {
                    return typeEnum;
                }
            }
        }
        return null;
    }

    /**
     * code 转枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static Optional<PoliceKindEnum> nullableCodeOf(Integer code) {
        if (Objects.nonNull(code)) {
            for (PoliceKindEnum typeEnum : PoliceKindEnum.values()) {
                if (code.equals(typeEnum.getCode())) {
                    return Optional.of(typeEnum);
                }
            }
        }
        return null;
    }
}
