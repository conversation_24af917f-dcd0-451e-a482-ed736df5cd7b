package com.trs.police.common.core.dto;

import java.util.Map;
import lombok.Data;

/**
 * 预警轨迹信息
 *
 * <AUTHOR>
 */
@Data
public class WarningDTO {

    /**
     * 轨迹类型英文名
     */
    private String enName;
    /**
     * 轨迹类型中文
     */
    private String name;
    /**
     * 预警时间
     */
    private String eventTime;
    /**
     * 命中信息
     */
    private HitInfo hitInfo;
    /**
     * 标识符
     */
    private String identifier;
    /**
     * 标识符类型
     */
    private Integer identifierType;
    /**
     * 感知源信息
     */
    private Source sensingMessage;
    /**
     * 轨迹详情
     */
    private Map<String, Object> trackDetail;
    /**
     * 用户名
     */
    private String userName;
}
