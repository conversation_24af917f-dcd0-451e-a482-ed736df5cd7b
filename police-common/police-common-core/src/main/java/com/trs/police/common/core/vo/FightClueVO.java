package com.trs.police.common.core.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 作战关联线索vo
 *
 * <AUTHOR>
 * @date 2024/03/21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FightClueVO implements Serializable {

    private static final long serialVersionUID = 6838218765723708111L;

    /**
     * 线索档案id，线索池id
     */
    private Long id;

    /**
     * 线索档案名称或线索池名称
     */
    private String name;

    /**
     * 线索档案编码或线索池编码
     */
    private String code;

    /**
     * 来源
     * {@link com.trs.police.fight.constant.FightCompositeConstant#FIGHT_CLUE_RELATION_CLUE} 1:线索档案
     * {@link com.trs.police.fight.constant.FightCompositeConstant#FIGHT_CLUE_RELATION_CLUE_POOL} 2:线索池
     */
    private Integer dataSource;

    /**
     * 线索池类型
     */
    private String type;

    /**
     * 合成id
     */
    private Long compositeId;

}
