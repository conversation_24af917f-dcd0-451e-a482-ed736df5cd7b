package com.trs.police.common.core.vo.profile;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 警情反馈vo
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class JqFkxxCommonVO implements Serializable {

    /**
     * 主键
     */
    private Long id;
    /**
     * 行政区划单位
     */
    private String xzqhdm;
    /**
     * 反馈单编号
     */
    private String fkdbh;//HANDLENUM
    /**
     * 接警单编号
     */
    private String jjdbh; //RECEIVENUM
    /**
     * 派警单编号
     */
    private String pjdbh;
    /**
     * 反馈单位代码
     */
    private String fkdwdm;
    /**
     * 反馈员编号
     */
    private String fkybh;
    /**
     * 反馈员姓名
     */
    private String fkyxm;
    /**
     * 反馈时间
     */
    private LocalDateTime fksj;
    /**
     * 实际出警时间
     */
    private LocalDateTime cjsj01;
    /**
     * 到达现场时间
     */
    private LocalDateTime ddxcsj;
    /**
     * 现场处理完毕时间
     */
    private LocalDateTime xcclwbsj;
    /**
     * 警情类别代码
     */
    private String jqlbdm;
    /**
     * 警情类型代码
     */
    private String jqlxdm;
    /**
     * 警情细类代码
     */
    private String jqxldm;
    /**
     * 警情子类代码
     */
    private String jqzldm;
    /**
     * 警情类别代码
     */
    private String jqlbmc;
    /**
     * 警情类型代码
     */
    private String jqlxmc;
    /**
     * 警情细类代码
     */
    private String jqxlmc;
    /**
     * 警情子类代码
     */
    private String jqzlmc;
    /**
     * 警情发生时间
     */
    private LocalDateTime jqfssj;
    /**
     * 报警地址
     */
    private String bjdz;
    /**
     * 警情地址
     */
    private String jqdz;
    /**
     * 反馈定位x坐标（处置现场位置）
     */
    private Double fkdwxzb;
    /**
     * 反馈定位y坐标（处置现场位置）
     */
    private Double fkdwyzb;
    /**
     * 出警处置情况
     */
    private String cjczqk;
    /**
     * 警情处理结果代码
     */
    private String jqcljgdm;
    /**
     * 警情处理结果说明
     */
    private String jqcljgsm;
    /**
     * 抓获人数
     */
    private Integer zhrs;
    /**
     * 涉案人数
     */
    private Integer sars;
    /**
     * 逃跑人数
     */
    private Integer tprs;
    /**
     * 警情处理状态代码
     */
    private String jqclztdm;
    /**
     * 创建时间
     */
    private LocalDateTime cjsj;
    /**
     * 更新时间
     */
    private LocalDateTime gxsj;
    /**
     * 乡镇（街道）
     */
    private String jdxz;
    /**
     * 属地村社
     */
    private String sdcs;
    /**
     *
     */
    private String sfqz;
    /**
     * 移交单位代码
     */
    private String yjdwdm;
    /**
     * 移交单位联系人名称
     */
    private String yjdwlxr;
    /**
     * 移交单位反馈情况
     */
    private String yjdwfkqk;
    /**
     * 移交单位反馈情况说明
     */
    private String yjdwfkqksm;
    /**
     * 案件编号
     */
    private String ajbh;
    /**
     * 警情标签
     */
    private String nrbq;
    /**
     * 警情部位代码
     */
    private String jqbwdm;
    /**
     * 警情发生地域
     */
    private String jqfsdy;
    /**
     * 警力代码
     */
    private String forcedm;
    /**
     * 警力类型
     */
    private String forcetype;
    /**
     * 当前业务状态
     */
    private String dqywzt;
    /**
     * 反馈单位短码
     */
    private String fkdwdmbs;
    /**
     * 反馈单位名称
     */
    private String fkdwmc;
    /**
     * 提交单位短码
     */
    private String tjdwdmbs;
    /**
     * 无效标识
     */
    private String wxbs;
    /**
     * 移交单位名称
     */
    private String yjdwmc;
    /**
     * 移交单位短码
     */
    private String yjdwdmbs;
    /**
     * 调整后管辖单位代码
     */
    private String tzhgxdwdm;
    /**
     * 调整后管辖单位名称
     */
    private String tzhgxdwmc;
    /**
     * 调整后管辖单位短码
     */
    private String tzhgxdwdmbs;
    /**
     * 移交单位联系人代码
     */
    private String yjdwlxrdm;
    /**
     * 过程反馈标识
     */
    private Integer gcfkbs;

    private Integer fklx;

    /**
     * 签收时间
     */
    private LocalDateTime qssj;

    /**
     * 操作类别编号
     */
    private String czlbbh;

    /**
     * 处置反馈时间
     */
    private LocalDateTime czfksj;

    /**
     * 抄送时间
     */
    private LocalDateTime cssj;

    /**
     * 接警时间
     */
    private LocalDateTime jjsj;

    /**
     * 推送时间
     */
    private LocalDateTime tssj;

    /**
     * 结案时间
     */
    private LocalDateTime jasj;
}
