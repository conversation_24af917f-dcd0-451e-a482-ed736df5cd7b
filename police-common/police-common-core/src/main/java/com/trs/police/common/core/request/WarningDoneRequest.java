package com.trs.police.common.core.request;

import com.trs.police.common.core.vo.oss.FileInfoVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 预警完结请求
 *
 * <AUTHOR>
 */
@Data
public class WarningDoneRequest implements Serializable {

    private static final long serialVersionUID = -1692986085203942998L;

    /**
     * 处置完结备注
     */
    private String comment;
    /**
     * 附件
     */
    private List<FileInfoVO> attachments;
    /**
     * 是否处置
     */
    private Boolean isHandle;
    /**
     * 不处置原因
     */
    private Integer notHandleReason;
    /**
     * 其他原因
     */
    private String notHandleDescription;
    /**
     * 处置措施 1:已抓获 2:未抓获 3:已撤网 4:管控 5:其他
     */
    private Integer handleMeasure;

}
