package com.trs.police.common.core.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.constant.enums.ApprovalStatusEnum;
import com.trs.police.common.core.constant.enums.CompositeRoleEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 合成与参与用户的关联表
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "t_fight_composite_user_relation")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FightCompositeUserRelation extends AbstractBaseEntity {

    private static final long serialVersionUID = -5706056415308037045L;

    /**
     * 合成主键
     */
    @TableField(value = "composite_id")
    private Long compositeId;

    /**
     * 用户id
     */
    @TableField(value = "user_id")
    private Long userId;

    /**
     * 该用户在此合成中的角色
     */
    @TableField(value = "`role`")
    private CompositeRoleEnum role;

    /**
     * 参与部门
     */
    @TableField(value = "dept_id")
    private Long deptId;

    /**
     * 状态 1 待审批 2 审批中 3 审批通过
     */

    @TableField(value = "status")
    private Integer status;

    /**
     * 省厅情指接收状态
     */
    @TableField(value = "receive_status")
    private Integer receiveStatus;

    /**
     * 审批id
     */
    @TableField(value = "approval_id")
    private Long approvalId;

    /**
     * 来源 0 创建或者编辑时选择 1 仅预案 2 云墙同步
     */
    @TableField(value = "user_source")
    private Integer userSource;


    public FightCompositeUserRelation(Long compositeId, Long userId, CompositeRoleEnum role, Long deptId) {
        this.compositeId = compositeId;
        this.userId = userId;
        this.role = role;
        this.deptId = deptId;
        this.status = ApprovalStatusEnum.PASSED.getCode();
    }

    public FightCompositeUserRelation(Long compositeId, Long userId, CompositeRoleEnum role, Long deptId, Integer status) {
        this.compositeId = compositeId;
        this.userId = userId;
        this.role = role;
        this.deptId = deptId;
        this.status = status;
    }


    /**
     * 构造唯一值
     *
     * @return 唯一值
     */
    public String buildKey() {
        return new StringBuilder()
                .append(userId)
                .append("-")
                .append(deptId)
                .toString();
    }
}
