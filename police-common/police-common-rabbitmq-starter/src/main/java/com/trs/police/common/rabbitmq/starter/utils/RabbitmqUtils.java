package com.trs.police.common.rabbitmq.starter.utils;

import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.core.vo.message.DelayMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.MessageDeliveryMode;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

/**
 * mq工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class RabbitmqUtils {

    private RabbitmqUtils() {
    }

    /**
     * 发送mq消息
     *
     * @param rabbitTemplate template
     * @param exchange       交换机
     * @param messageStr     消息
     */
    public static void sendMessageToMq(RabbitTemplate rabbitTemplate, String exchange, String messageStr) {
        rabbitTemplate.convertAndSend(exchange, "", messageStr);
        log.info("向交换机 [{}] 发送消息：{}", exchange, messageStr);
    }

    /**
     * 向延迟交换机推送消息
     *
     * @param rabbitTemplate template
     * @param exchange       交换机 type=x-delayed-message才能支持延迟消息
     * @param message        消息内容
     */
    public static void sendDelayMessage(RabbitTemplate rabbitTemplate, String exchange, DelayMessage message) {
        rabbitTemplate.convertAndSend(exchange, "", JsonUtil.toJsonString(message),
                ms -> {
                    MessageProperties mp = ms.getMessageProperties();
                    mp.setDeliveryMode(MessageDeliveryMode.PERSISTENT);
                    mp.setDelay(message.getDelayLength());
                    return ms;
                });
        log.info("向交换机 [{}] 发送消息：{}", exchange, message);
    }

    /**
     * 发送延迟消息
     *
     * @param rabbitTemplate template
     * @param exchange       交换机 type=x-delayed-message才能支持延迟消息
     * @param message        消息
     * @param delay          延迟时间 单位:毫秒
     */
    public static void sendDelayMessage(RabbitTemplate rabbitTemplate, String exchange, String message, Integer delay) {
        rabbitTemplate.convertAndSend(exchange, "", message,
                ms -> {
                    MessageProperties mp = ms.getMessageProperties();
                    mp.setDeliveryMode(MessageDeliveryMode.PERSISTENT);
                    mp.setDelay(delay);
                    return ms;
                });
        log.info("向交换机 [{}] 发送延迟消息：{}", exchange, message);
    }

    /**
     * 处理接收消息
     *
     * @param message 消息字符串
     * @param clazz   类型
     * @param <T>     类型
     * @return 结果
     */
    public static <T> T getMessage(String message, Class<T> clazz) {
        log.info("接收到消息：{}", message);
        T result = JsonUtil.parseObject(message, clazz);
        if (result == null) {
            throw new TRSException("消息解析失败！");
        } else {
            return result;
        }
    }
}
